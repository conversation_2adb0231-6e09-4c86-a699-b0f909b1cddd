package com.shuyun.node.api.demo.service;

import com.shuyun.motor.client.proxy.annotation.Resource;
import com.shuyun.motor.client.proxy.annotation.ResourceGroup;
import com.shuyun.motor.client.proxy.annotation.Return;
import com.shuyun.motor.client.proxy.annotation.Var;
import com.shuyun.node.api.constant.MsServer;
import com.shuyun.node.api.demo.dto.DemoDto;

/**
 * <AUTHOR>
 * @Description DemoResource 对外的接口client端
 *
 * @date 2019/6/14
 */


@ResourceGroup(service =  MsServer.CUSTOMIZED_NODE )
public interface INodeDemoService {

    @Resource(
            apiVersion = MsServer.API_VERSION,
            method = Resource.HttpMethod.GET,
            resource = "/api/demo/{id}" ,
            params = {@Resource.Param(name="id",exp = "id") }
    )
    @Return({DemoDto.class})
    DemoDto getDemo(@Var("id") Long id);


    @Resource(
            apiVersion = MsServer.API_VERSION,
            method = Resource.HttpMethod.DELETE,
            resource = "/api/demo/{id}" ,
            params = {@Resource.Param(name="id",exp = "id") }
    )
    @Return({Void.class})
    void delteDemo(@Var("id") Long id);

    @Resource(
            apiVersion = MsServer.API_VERSION,
            method = Resource.HttpMethod.POST,
            resource = "/api/demo",
            body = @Resource.Body(exp = "demoDto")
    )
    @Return(Void.class)
    void createDemo( @Var("demoDto") DemoDto demoDto);
}
