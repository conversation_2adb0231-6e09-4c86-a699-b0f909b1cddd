package com.shuyun.node.api.demo.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.shuyun.node.api.enums.SexEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @date 2019/4/12
 */
@Data
@ApiModel("演示类")
public class DemoDto {
    @ApiModelProperty(value = "姓名")
    private String name;
    @ApiModelProperty(value = "地址")
    private String address;
    @ApiModelProperty(value = "手机")
    private String mobile;
    @ApiModelProperty(value = "性别")
    @JsonProperty("sex")
    private SexEnum sex;
    @ApiModelProperty(value = "年龄")
    private Integer age;
}
