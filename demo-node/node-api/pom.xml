<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.shuyun.kylin.customized.crm</groupId>
        <artifactId>demo-node</artifactId>
        <version>1.5.0-SNAPSHOT</version>
    </parent>
    <artifactId>node-api</artifactId>
    <packaging>jar</packaging>
    <properties>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.shuyun</groupId>
            <artifactId>motor-client-annotation</artifactId>
            <version>${motor.version}</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}-${project.parent.version}</finalName>
    </build>

</project>