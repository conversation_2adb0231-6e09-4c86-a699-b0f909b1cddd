from hub.shuyun.com/base/java:oracle-java-8-jdk

MAINTAINER YT "<<EMAIL>>"

WORKDIR /demo-node
COPY node-server/target/demo-node.jar demo-node.jar
ADD  node-server/target/classes /demo-node/classes
ADD  node-server/target/dependency /demo-node/dependency

EXPOSE 8080
CMD ["java", "-jar","-XX:HeapDumpPath=/var/log/demo-node/heap.bin","-XX:+PrintGCDetails","-XX:+PrintGCTimeStamps","-Xloggc:/var/log/demo-node/heap_trace.txt","demo-node.jar", "server", "application.yml"]
