application:
  server:
    name: demo-node

server:
  servlet:
    context-path: /demo-node/v1
  undertow:
      io-threads: 16
      worker-threads: 256
      buffer-size: 1024
      direct-buffers: true

logging:
  config: classpath:logback-spring.xml
  level:
    com:
      shuyun:
        crmep:
          customized: INFO
spring:
    jackson:
        default-property-inclusion: non_null
    cloud:
      stream:
        bindings:
          kafka_input:  #channelName
            destination: test.kafka.stream #kafka topic
            #设置消息的类型
            contentType: application/json
            nativeDecoding: true
            group: group-k
          kafka_output:   #channelName
              destination: test.kafka.stream #kafka topic
              #设置消息的类型
              contentType: application/json
              nativeDecoding: true
              group: group-k                   #分组
          kafka_output1:   #channelName
            destination: test.kafka.stream1 #kafka topic
            #设置消息的类型
            contentType: application/json
            nativeDecoding: true

        kafka:
          binder:
            brokers: ${kafka.brokers:192.168.110.128:9092}
            configuration:
              auto:
                offset:
                  reset: latest
            minPartitionCount: ${kafka.min.partition.count:1}
            autoAddPartitions: true
            consumer-properties:
              session.timeout.ms: ${kafka.session.timeout.ms:60000}
              max.poll.records: ${kafka.max.poll.records:1}
              auto.commit.interval.ms: 2000
              max.poll.interval.ms: ${kafka.max.poll.interval.ms:600000}

system:
  kafka:
    enabled: false

