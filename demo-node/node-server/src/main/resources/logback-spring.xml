<?xml version="1.0" encoding="UTF-8" ?>
<configuration scan="true" scanPeriod="600 seconds" debug="false">
    <contextName>demo-node</contextName>

    <property name="ROOT_LOG_LEVEL" value="${ROOT_LOG_LEVEL:-INFO}"/>
    <property name="APP_VERSION" value="${APP_VERSION:-1.0.0}"/>
    <property name="ENVIRONMENT" value="${ENVIRONMENT:-dev}"/>
    <property name="SANDBOX" value="${SANDBOX:-base}"/>
    <property name="MESOS_TASK_ID" value="${MESOS_TASK_ID:-001}"/>
    <property name="SERVICE_NAME" value="${SERVICE_NAME:-demo-node}" />
    <property name="LOG_PATTERN" value="[%d{yyyy-MM-dd HH:mm:ss.SSS,GMT+8}] [%-5level] [%class{0}:%method:%line] [%t] [-[%msg]-] %n"/>
    <property name="FILE_LOG_PATTERN_" value="${FILE_LOG_PATTERN:-${LOG_PATTERN}}"/>
    <property name="LOG_PATTERN"  value="[%d{yyyy-MM-dd HH:mm:ss.SSS,GMT+8}] [%-5level] [%class{0}:%method:%line] [%t] [-[%msg]-] %n"/>
    <property name="FILE_LOG_PATTERN" value="${FILE_LOG_PATTERN_}"/>
    <property name="time" value="${bySecond}"/>
    <property name="system.environment" value="${system.environment:-${ENVIRONMENT}}" />
    <property name="system.logging.format" value="${system.logging.format:-${LOG_PATTERN}}"/>
    <property name="system.logging.dir" value="${system.logging.dir:-/data/log}"/>
    <property name="system.logging.bakdir" value="${system.logging.bakdir:-/data/logbak}"/>

    <property name="system.logging.bakfileMaxFileSize" value="${system.logging.bakfileMaxFileSize:-300MB}" />
    <property name="system.logging.async.queueSize" value="${system.logging.async.queueSize:-10240}" />
    <property name="system.logging.async.neverBlock" value="${system.logging.async.neverBlock:-true}" />
    <property name="system.logging.async.includeCallerData" value="${system.logging.async.includeCallerData:-true}" />

    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>

    <!-- override org/springframework/boot/logging/logback/defaults.xml FILE_LOG_PATTERN configuration -->


    <!-- 控制台设置 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoder 默认配置为PatternLayoutEncoder -->
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{5}-%L : %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${system.logging.format}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>


    <!-- override org/springframework/boot/logging/logback/defaults.xml FILE_LOG_PATTERN configuration -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>
            ${system.logging.dir}/${SERVICE_NAME}/${APP_VERSION}/${ENVIRONMENT}/${SANDBOX}/${MESOS_TASK_ID}/${SERVICE_NAME}.log
        </file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>
                ${system.logging.bakdir}/${SERVICE_NAME}/${APP_VERSION}/${ENVIRONMENT}/${SANDBOX}/${MESOS_TASK_ID}/${SERVICE_NAME}.log.%d{yyyyMMdd}
            </fileNamePattern>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </layout>
    </appender>

    <appender name="ASYNC_CONSOLE" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>${system.logging.async.queueSize}</queueSize>
        <neverBlock>${system.logging.async.neverBlock}</neverBlock>
        <includeCallerData>${system.logging.async.includeCallerData}</includeCallerData>
        <appender-ref ref="CONSOLE"/>
    </appender>
    <appender name="ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>${system.logging.async.queueSize}</queueSize>
        <neverBlock>${system.logging.async.neverBlock}</neverBlock>
        <includeCallerData>${system.logging.async.includeCallerData}</includeCallerData>
        <appender-ref ref="FILE"/>
    </appender>


    <!-- * 通配符 设置log打印级别 对所有类有效TRACE, DEBUG, INFO, WARN, ERROR, ALL 和 OFF-->
    <root level="${ROOT_LOG_LEVEL}">
        <appender-ref ref="ASYNC_CONSOLE"/>
        <appender-ref ref="ASYNC_FILE"/>
    </root>
</configuration>