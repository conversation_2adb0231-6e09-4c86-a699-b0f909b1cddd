--// product-selector init
-- Migration SQL that makes the change goes here.

delete from `tenant_configuration`
where `key` in ('extTfilterDemo.image.repository','extTfilterDemo.spectrum.key','extTfilterDemo.spectrum.secret');

insert into `tenant_configuration` ( `key`, `schema`, `value`, `region`)
values ( 'extTfilterDemo.image.repository', 'node-processor', 'node-demo-processor', 'default');
insert into `tenant_configuration` ( `key`, `schema`, `value`, `region`)
values ( 'extTfilterDemo.spectrum.key', 'node-processor', 'guest-r', 'default');
insert into `tenant_configuration` ( `key`, `schema`, `value`, `region`)
values ( 'extTfilterDemo.spectrum.secret', 'node-processor', 'F655E3DFF41A45218937D8AE26869B12', 'default');



--//@ UNDO
-- SQL to undo the change goes here.

