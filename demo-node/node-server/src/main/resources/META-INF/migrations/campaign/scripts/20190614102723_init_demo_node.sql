--// product-selector init
-- Migration SQL that makes the change goes here.

delete from workflow_meta_node where type = 'extTfilterDemo';
INSERT INTO `workflow_meta_node`(type,name,viewable,draggable,removable,copyable,source_style,version,out_min,out_max,in_min,in_max,tips)
VALUES ('extTfilterDemo','Demo节点',1,1,1,1,'manual','CUSTOM',1,-1,1,1,'Demo节点');

delete from workflow_meta_connect_rule where source = 'extTfilterDemo';
delete from workflow_meta_connect_rule where target = 'extTfilterDemo';
INSERT INTO `workflow_meta_connect_rule` (source,target,is_prefix) 
VALUES ('tfilterAttrQuery','extTfilterDemo',0); 

delete from workflow_meta_node_group where node_type = 'extTfilterDemo';
INSERT INTO `workflow_meta_node_group` (group_name, node_type, sort_no)
VALUES ('top','extTfilterDemo',13);


--//@ UNDO
-- SQL to undo the change goes here.

