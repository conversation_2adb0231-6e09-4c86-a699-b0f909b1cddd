--// product-selector init
-- Migration SQL that makes the change goes here.


/** 新建数据库迁移日志表*/
-- DROP TABLE IF EXISTS `CHANGELOG`;

CREATE TABLE IF NOT EXISTS `CHANGELOG` (
  `ID` decimal(20,0) NOT NULL,
  `APPLIED_AT` varchar(25) COLLATE utf8_bin NOT NULL,
  `DESCRIPTION` varchar(255) COLLATE utf8_bin NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

--//@UNDO
-- SQL to undo the change goes here.
DROP TABLE IF EXISTS `CHANGELOG`;

