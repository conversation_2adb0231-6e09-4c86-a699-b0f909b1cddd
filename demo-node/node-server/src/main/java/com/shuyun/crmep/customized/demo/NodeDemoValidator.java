package com.shuyun.crmep.customized.demo;

import com.shuyun.crmep.customized.demo.domain.NodeDemo;
import com.shuyun.crmep.customized.demo.service.IDemoService;
import com.shuyun.crmep.customized.support.impl.AbstractNodeValidator;
import com.shuyun.crmep.customized.support.impl.DefaultNodeValidateContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description Demo节点校验检查类
 * @date 2019/6/7
 */
@Component("nodeDemeValidator")
public class NodeDemoValidator extends AbstractNodeValidator {
    private static final String TYPE= "tDemo";
    @Autowired
    private IDemoService demoService;

    @Override
    public void validate(DefaultNodeValidateContext context) {
        NodeDemo demo = demoService.getById(context.getNodeId());
        // for test mark check
        if(demo == null || demo.getId() == null){
            addError(context,context.getNodeId(),"请配置演示节点");
            return;
        }

        // TODO -- 可以根据业务需要增加其它的校验逻辑

    }



    @Override
    public String type() {
        return NodeDemo.TYPE;
    }
}
