package com.shuyun.crmep.customized.demo.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.shuyun.node.api.enums.SexEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 前端页面数据展示类
 * @date 2019/6/7
 */
@Data
@ApiModel("演示类")
public class NodeDemoVo {
    @ApiModelProperty(value = "节点ID")
    private Long id;
    @ApiModelProperty(value = "姓名")
    private String name;
    @ApiModelProperty(value = "地址")
    private String address;
    @ApiModelProperty(value = "手机")
    private String mobile;
    @ApiModelProperty(value = "性别")
    @JsonProperty("sex")
    private SexEnum sex;
    @ApiModelProperty(value = "年龄")
    private Integer age;
    @ApiModelProperty(value="节点类型")
    private String type;
}
