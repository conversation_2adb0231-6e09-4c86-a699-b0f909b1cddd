package com.shuyun.crmep.customized.demo.resource.web;

import com.shuyun.ccms.campaign.model.Node;
import com.shuyun.ccms.campaign.resource.api.mcjaxrs.CampaignWorkflowNodeResource;
import com.shuyun.crmep.customized.base.enums.MicroServiceEnums;
import com.shuyun.crmep.customized.demo.domain.NodeDemo;
import com.shuyun.crmep.customized.demo.service.IDemoService;
import com.shuyun.crmep.customized.demo.vo.NodeDemoVo;
import com.shuyun.epassport.sdk.register.RequiresPermissions;
import com.shuyun.kylin.starter.swagger.annotation.WebApi;
import com.shuyun.node.api.enums.SexEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;
import java.util.Random;

/**
 * <AUTHOR>
 * @Description Demo节点
 * @date 2019/6/7
 */


@RestController
@WebApi
@RequestMapping("/demo")
@Api(value = "显示范例",description = "Demo web相关接口,定制后需要将此接口失效")
@RequiresPermissions(value = {"marketing.demo.node.visit"})
public class DemoWebResource {
    @Autowired
    private IDemoService demoService;

    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询Demo" )
    public NodeDemoVo getNodeDemo(@PathVariable("id") Long id){
        NodeDemoVo nodeDemoVo = new NodeDemoVo();
        NodeDemo demo = demoService.getById(id);
        CampaignWorkflowNodeResource campaignWorkflowNodeResource = new CampaignWorkflowNodeResource();
        campaignWorkflowNodeResource.setServiceName_(MicroServiceEnums.CAMPAIGN.getServerName());
        Node node = campaignWorkflowNodeResource.getNode(id);
        nodeDemoVo.setName(node.getName());
        nodeDemoVo.setType(node.getType());
        nodeDemoVo.setId(node.getId());
        if(demo == null){
            demo = new NodeDemo();
            demo.setId(id);

            demo.setName(node.getName());
            Random random = new Random(100);
            Integer value = random.nextInt();
            demo.setAge(value);
            if(value%2 ==0 ){
                demo.setSex(SexEnum.F);
            }else{
                demo.setSex(SexEnum.M);
            }
            demo.setName("test"+value);
            demo.setMobile("139344556666");
            demo.setAddress("上海");
            demoService.save(demo);
        }
        return nodeDemoVo;
    }

    // 以下节点只是Demo
    @PostMapping
    @ApiOperation(value = "新增Demo", tags = {})
    public NodeDemoVo saveDemo(@RequestBody  NodeDemoVo demoVo){
        NodeDemo nodeDemo = new NodeDemo();
        NodeDemoVo nodeDemoVo = Optional.ofNullable(demoVo.getMobile()).map(dmVo -> {
            demoVo.setAddress("上海");
            Random random = new Random(100);
            Integer value = random.nextInt();
            demoVo.setAge(value);
            if(value%2 ==0 ){
                demoVo.setSex(SexEnum.F);
            }else{
                demoVo.setSex(SexEnum.M);
            }
            demoVo.setName("test"+value);
            demoVo.setMobile("139344556666");
            return demoVo;
        }).orElse(demoVo);
        BeanUtils.copyProperties(nodeDemoVo,nodeDemo);
        demoService.save(nodeDemo);
        return demoVo;
    }


    @DeleteMapping("/{id}")
    @ApiOperation(value = "根据id删除Demo", tags = {})
    public void deleteNodeDemo(Long id){
        demoService.removeById(id);
    }


    @PutMapping
    @ApiOperation(value = "修改Demo" )
    public NodeDemoVo update(@RequestBody NodeDemoVo demoVo){
        NodeDemo nodeDemo = new NodeDemo();
        BeanUtils.copyProperties(demoVo,nodeDemo);
        demoService.updateById(nodeDemo);
        return demoVo;
    }


}
