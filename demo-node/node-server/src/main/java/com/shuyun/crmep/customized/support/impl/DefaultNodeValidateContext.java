package com.shuyun.crmep.customized.support.impl;

import com.google.common.collect.Sets;
import com.shuyun.crmep.customized.support.Mergeable;
import com.shuyun.crmep.customized.support.NodeValidateConstraintViolation;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.HashMap;
import java.util.Set;

/**
 * Created by Administrator on 2015/11/13.
 */
public class DefaultNodeValidateContext implements Cloneable , Mergeable<DefaultNodeValidateContext> {

    private Long nodeId;
    private HashMap<String, Comparable> attributes = new HashMap<String, Comparable>();
    private Set<NodeValidateConstraintViolation> constraintViolations = null;
    private ConflictStrategy conflictStrategy = ConflictStrategy.GRATER;
    private Set<String> attributeNames;


    public DefaultNodeValidateContext(){}
    private DefaultNodeValidateContext(HashMap<String, Comparable> paramMap) {
        this.attributes = paramMap;
    }

    public DefaultNodeValidateContext(Long nodeId) {
        this.nodeId = nodeId;
    }

//    @Override
    public Long getNodeId() {
        return nodeId;
    }

    public void setNodeId(Long nodeId) {
        this.nodeId = nodeId;
    }

    public void setConflictStrategy(ConflictStrategy conflictStrategy) {
        this.conflictStrategy = conflictStrategy;
    }

//    @Override
    public Set<String> getAttributeNames() {
        return attributes.keySet();
    }


//    @Override
    @SuppressWarnings("unchecked")
    public <T extends Comparable<T>> T getAttribute(String key) {
        return (T) attributes.get(key);
    }

//    @Override
    @SuppressWarnings("unchecked")
    public  void setAttribute(String key, Comparable<?> value) {
        if(value==null){
            return ;
        }
        Comparable original = attributes.get(key);
        if (original != null) {
            int c = original.compareTo(value);
            switch (conflictStrategy) {
                case GRATER:
                    if (c < 0) {
                        attributes.put(key, value);
                    }
                    break;
                case SMALLER:
                    if (c > 0) {
                        attributes.put(key, value);
                    }
                    break;
            }
        } else {
            attributes.put(key, value);
        }
    }

//    @Override
    public void addConstraintViolation(
            NodeValidateConstraintViolation constraintViolation) {
        if(constraintViolations==null){
            constraintViolations = Sets.newHashSet();
        }
        constraintViolations.add(constraintViolation);
    }

//    @Override
    public Set<NodeValidateConstraintViolation> getConstraintViolations() {
        return constraintViolations==null? Collections.<NodeValidateConstraintViolation>emptySet():Collections.unmodifiableSet(constraintViolations);
    }

//    @Override
    public DefaultNodeValidateContext clone() {
        DefaultNodeValidateContext newContext = new DefaultNodeValidateContext(
                (HashMap<String, Comparable>) attributes.clone());
        newContext.setNodeId(nodeId);
        return newContext;
    }

    @Override
    public DefaultNodeValidateContext merge(DefaultNodeValidateContext secondary) {
        Assert.notNull(secondary);
        DefaultNodeValidateContext returned = this.clone();
        Set<String> names = secondary.getAttributeNames();
        for(String name:names){
            Comparable attribute = secondary.getAttribute(name);
            returned.setAttribute(name, attribute);
        }
        return returned;
    }

    @Override
    public String toString() {
        return "DefaultNodeValidateContext [nodeId=" + nodeId + ", attributes="
                + attributes + "]";
    }


    /**
     * 给同一个参数设置值的冲突选择策略
     * <AUTHOR>
     *
     */
    public static enum ConflictStrategy{GRATER,SMALLER};

}
