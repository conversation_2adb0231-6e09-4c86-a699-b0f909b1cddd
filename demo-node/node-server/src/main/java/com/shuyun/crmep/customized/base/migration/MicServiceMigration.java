package com.shuyun.crmep.customized.base.migration;

import com.shuyun.crmep.customized.base.migration.mysql.MultiMigrationSetupFactory;
import com.shuyun.motor.common.cons.App;
import com.shuyun.motor.common.cons.PropsUtil;
import com.shuyun.motor.migration.MigrationSetup;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.datasource.pooled.PooledDataSource;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Description  仅执行其它微服务库 migration 初始化脚本
 * @date 2019/6/13
 */
@Slf4j
public class MicServiceMigration {

    public static void executeMigration() {
        String driver = App.getDbDriverClass();
        String host = PropsUtil.getSys("database.host");
        String port = PropsUtil.getSys("database.port");
        String environment = PropsUtil.getSys("system.environment");
        String password = App.getDbPassword();
        String user = App.getDbUsername();

        log.info("执行其它微服务库script开始 " );

        if(null == host || null == driver || password== null || user==null ){
            log.info("migration execute skip");
            return;
        }

        //执行定制模式连接非demo-node服务库的migration

        //jdbc:mysql://${database.host}:${database.port}/${database.schema.name}?useUnicode=true&characterEncoding=utf8&autoReconnect=true&allowMultiQueries=true&zeroDateTimeBehavior=convertToNull
        String defaultProfile = PropsUtil.getSysOrEnv("customized.migration.profile","campaign,ccms_bpm_adapter_service");

        String[] profiles = defaultProfile.split(",");
        Arrays.stream(profiles).forEach(profile ->{
            StringBuffer schemaName = new StringBuffer();
            schemaName.append(environment).append("_").append(profile);
            String url = getUrl(host,port,schemaName.toString());
            initMigration(schemaName.toString(),profile,url,driver,user,password);
        });
        log.info("执行其它微服务库script结束");
    }

    private static String getUrl(String host, String port, String schemaName){
        String url = "jdbc:mysql://${database.host}:${database.port}/${database.schema.name}?useUnicode=true&characterEncoding=utf8&autoReconnect=true&allowMultiQueries=true&zeroDateTimeBehavior=convertToNull";
        return  url.replaceAll("\\$\\{database.host}",host)
                .replaceAll("\\$\\{database.port}",port)
                .replaceAll("\\$\\{database.schema.name}",schemaName);
    }


    private static void initMigration(String schemaName, String profile, String url, String driver, String user,  String password ){
        log.info("执行库:{} migriation script开始. url:{}",schemaName,url);
        PooledDataSource dataSource = new PooledDataSource(driver,url , user, password);
        dataSource.setDefaultAutoCommit(true);
        try {
            MigrationSetup migrationSetup = MultiMigrationSetupFactory.defaultMultiMigrationSetup();
            migrationSetup.execute(dataSource, profile);
        } catch (Exception e) {
             throw new RuntimeException(e);
        } finally {
            dataSource.forceCloseAll();
        }
        log.info("执行库:{} migriation script结束",schemaName);
    }
}
