package com.shuyun.crmep.customized.support.impl;



import com.shuyun.crmep.customized.support.NodeDataCloneException;
import com.shuyun.crmep.customized.support.NodeDataHandler;

import java.util.Map;

/**
 * This class provides a skeletal implementation of the <tt>NodeHandler</tt>
 * interface, to minimize the effort required to implement this interface. <p>
 *
 * <AUTHOR>
 */
public abstract class AbstractNodeDataHandler implements NodeDataHandler {

	@Override
	public Object clone(Long nodeId, Long newNodeId) {
		throw new UnsupportedOperationException();	
	}

	@Override
	public void delete(Long nodeId) {
		throw new UnsupportedOperationException();	
	}

	/**
	 * 复制节点特殊配置
	 * @param nodeId
	 * @param newNodeId
	 * @param oldNewNodeIds
	 * @throws NodeDataCloneException
	 */
	@Override
	public Object clone(Long nodeId, Long newNodeId, Map<Long, Long> oldNewNodeIds) throws NodeDataCloneException {
		return null;
	}
}
