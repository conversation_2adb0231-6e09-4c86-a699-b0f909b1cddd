package com.shuyun.crmep.customized.base.migration.mysql;

import com.shuyun.motor.migration.ComplexMigrationLoader;
import com.shuyun.motor.migration.DefaultMigrationSetup;
import com.shuyun.motor.migration.MigrationSetup;
import org.apache.ibatis.migration.DataSourceConnectionProvider;
import org.apache.ibatis.migration.MigrationException;
import org.apache.ibatis.migration.operations.UpOperation;
import org.apache.ibatis.migration.options.DatabaseOperationOption;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.net.URL;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Enumeration;
import java.util.Objects;

/**
 * 支持执行多个目录migration，目录之间以逗号分隔
 * <AUTHOR>
 */
public class MultiMigrationSetup implements MigrationSetup {
    private static final Logger logger = LoggerFactory.getLogger(DefaultMigrationSetup.class);

    @Override
    public void execute(DataSource dataSource, String profile) throws Exception {
        Objects.requireNonNull(profile, "migration profile未配置，请检查!");
        String[] profiles = profile.split(",");
        for (String p : profiles) {
            logger.info("开始执行profile:{}", p);
            doIt(dataSource, p);
        }
    }

    /**
     * @param dataSource 数据源
     * @param profile    执行migration的profile（development|production）
     * @throws Exception
     */
    public void doIt(DataSource dataSource, String profile) throws Exception {
        String migrationPath = new StringBuilder("META-INF/migrations/").append(profile).append("/scripts/").toString();
        Enumeration<URL> paths = DefaultMigrationSetup.class.getClassLoader().getResources(migrationPath);
        ComplexMigrationLoader migrationLoader = new ComplexMigrationLoader(migrationPath, paths);
        migrationLoader.init();

        DataSourceConnectionProvider dataSourceConnectionProvider = new DataSourceConnectionProvider(dataSource);

        // changelog table is Exists?
        if (!checkChangelogExists(dataSourceConnectionProvider)) {
            createChangelogTable(dataSourceConnectionProvider);
        }

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        PrintStream printStream = new PrintStream(byteArrayOutputStream);
        logger.info("Migrating **Pending** SQL scripts");

        DatabaseOperationOption databaseOperationOption = new DatabaseOperationOption();
        databaseOperationOption.setSendFullScript(false);
        databaseOperationOption.setAutoCommit(true);

        new PendingOperationWaringIgnore().operate(dataSourceConnectionProvider, migrationLoader, databaseOperationOption, printStream);
        new UpOperation().operate(dataSourceConnectionProvider, migrationLoader, databaseOperationOption, printStream);
        logger.info(new String(byteArrayOutputStream.toByteArray()));
        printStream.close();
    }

    private static void createChangelogTable(DataSourceConnectionProvider dataSourceConnectionProvider) {
        Connection connection = null;
        Statement stmt = null;
        try {
            connection = dataSourceConnectionProvider.getConnection();
            String sql = "CREATE TABLE changelog ( " +
                        "ID NUMERIC(20,0) NOT NULL, " +
                        "APPLIED_AT VARCHAR(25) NOT NULL, " +
                        "DESCRIPTION VARCHAR(255) NOT NULL, " +
                        "PRIMARY KEY(ID) )";
            stmt = connection.createStatement();
            stmt.execute(sql);
        } catch (SQLException e) {
            throw new MigrationException("create changelog table fail : ", e);
        } finally {
            close(stmt);
            close(connection);
        }
    }

    /**
     * @param dataSourceConnectionProvider
     */
    private static boolean checkChangelogExists(DataSourceConnectionProvider dataSourceConnectionProvider) {
        Connection connection = null;
        Statement stmt = null;
        try {
            connection = dataSourceConnectionProvider.getConnection();
            stmt = connection.createStatement();

            stmt.executeQuery("SELECT count(1) FROM changelog");
            return true;
        } catch (SQLException e) {
            String msg = e.getMessage();
            if (null != msg) {
                logger.info("changelog table check fail : {}", msg);
            } else {
                logger.info("changelog table check fail : ", e);
            }
            return false;
        } finally {
            close(stmt);
            close(connection);
        }
    }

    private static void close(Connection connection) {
        try {
            if (null != connection) {
                connection.close();
            }
        } catch (Exception e) {
            logger.error("close sql connection error : ", e);
        }
    }

    private static void close(Statement statement) {
        try {
            if (null != statement) {
                statement.close();
            }
        } catch (Exception e) {
            logger.error("close sql statement error : ", e);
        }
    }
}