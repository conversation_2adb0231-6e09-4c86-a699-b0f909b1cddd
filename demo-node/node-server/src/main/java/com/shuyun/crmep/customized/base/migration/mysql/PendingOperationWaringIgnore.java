package com.shuyun.crmep.customized.base.migration.mysql;

import org.apache.ibatis.migration.Change;
import org.apache.ibatis.migration.ConnectionProvider;
import org.apache.ibatis.migration.MigrationException;
import org.apache.ibatis.migration.MigrationLoader;
import org.apache.ibatis.migration.hook.HookContext;
import org.apache.ibatis.migration.hook.MigrationHook;
import org.apache.ibatis.migration.operations.DatabaseOperation;
import org.apache.ibatis.migration.operations.ScriptRunner;
import org.apache.ibatis.migration.options.DatabaseOperationOption;
import org.apache.ibatis.migration.utils.Util;

import java.io.PrintStream;
import java.io.Reader;
import java.sql.Connection;
import java.util.*;

/**
 * @Author: wayne
 */
public class PendingOperationWaringIgnore extends DatabaseOperation {
    public PendingOperationWaringIgnore operate(ConnectionProvider connectionProvider, MigrationLoader migrationsLoader,
                                    DatabaseOperationOption option, PrintStream printStream) {
        return operate(connectionProvider, migrationsLoader, option, printStream, null);
    }

    public PendingOperationWaringIgnore operate(ConnectionProvider connectionProvider, MigrationLoader migrationsLoader,
                                    DatabaseOperationOption option, PrintStream printStream, MigrationHook hook) {
//        try {
//            if (option == null) {
//                option = new DatabaseOperationOption();
//            }
//            if (!changelogExists(connectionProvider.getConnection(), option)) {
//                throw new MigrationException("Change log doesn't exist, no migrations applied.  Try running 'up' instead.");
//            }
//            List<Change> pending = getPendingChanges(connectionProvider, migrationsLoader, option);
//            int stepCount = 0;
//            Map<String, Object> hookBindings = new HashMap<>();
//            println(printStream, "WARNING: Running pending migrations out of order can create unexpected results.");
//            ScriptRunner runner = getScriptRunner(connectionProvider, option, printStream);
//            Reader scriptReader = null;
//            try {
//                for (Change change : pending) {
//                    if (stepCount == 0 && hook != null) {
//                        hookBindings.put(MigrationHook.HOOK_CONTEXT, new HookContext(connectionProvider, runner, null));
//                        hook.before(hookBindings);
//                    }
//                    if (hook != null) {
//                        hookBindings.put(MigrationHook.HOOK_CONTEXT, new HookContext(connectionProvider, runner, change.clone()));
//                        hook.beforeEach(hookBindings);
//                    }
//                    println(printStream, Util.horizontalLine("Applying: " + change.getFilename(), 80));
//                    scriptReader = migrationsLoader.getScriptReader(change, false);
//                    runner.runScript(scriptReader);
//                    insertChangelog(change, connectionProvider, option);
//                    println(printStream);
//                    if (hook != null) {
//                        hookBindings.put(MigrationHook.HOOK_CONTEXT, new HookContext(connectionProvider, runner, change.clone()));
//                        hook.afterEach(hookBindings);
//                    }
//                    stepCount++;
//                }
//                if (stepCount > 0 && hook != null) {
//                    hookBindings.put(MigrationHook.HOOK_CONTEXT, new HookContext(connectionProvider, runner, null));
//                    hook.after(hookBindings);
//                }
//                return this;
//            } catch (Exception e) {
//                throw new MigrationException("Error executing command.  Cause: " + e, e);
//            } finally {
//                if (scriptReader != null) {
//                    scriptReader.close();
//                }
//                runner.closeConnection();
//            }
//        } catch (Throwable e) {
//            throw new MigrationException("Error executing command.  Cause: " + e, e);
//        }

        try (Connection con = connectionProvider.getConnection()) {
            if (option == null) {
                option = new DatabaseOperationOption();
            }
            if (!changelogExists(con, option)) {
                throw new MigrationException("Change log doesn't exist, no migrations applied.  Try running 'up' instead.");
            }
            List<Change> pending = getPendingChanges(con, migrationsLoader, option);
            int stepCount = 0;
            Map<String, Object> hookBindings = new HashMap<String, Object>();
            println(printStream, "WARNING: Running pending migrations out of order can create unexpected results.");
            Reader scriptReader = null;
            try {
                ScriptRunner runner = getScriptRunner(con, option, printStream);
                for (Change change : pending) {
                    if (stepCount == 0 && hook != null) {
                        hookBindings.put(MigrationHook.HOOK_CONTEXT, new HookContext(connectionProvider, runner, null));
                        hook.before(hookBindings);
                    }
                    if (hook != null) {
                        hookBindings.put(MigrationHook.HOOK_CONTEXT, new HookContext(connectionProvider, runner, change.clone()));
                        hook.beforeEach(hookBindings);
                    }
                    println(printStream, Util.horizontalLine("Applying: " + change.getFilename(), 80));
                    scriptReader = migrationsLoader.getScriptReader(change, false);
                    runner.runScript(scriptReader);
                    insertChangelog(change, con, option);
                    println(printStream);
                    if (hook != null) {
                        hookBindings.put(MigrationHook.HOOK_CONTEXT, new HookContext(connectionProvider, runner, change.clone()));
                        hook.afterEach(hookBindings);
                    }
                    stepCount++;
                }
                if (stepCount > 0 && hook != null) {
                    hookBindings.put(MigrationHook.HOOK_CONTEXT, new HookContext(connectionProvider, runner, null));
                    hook.after(hookBindings);
                }
                return this;
            } catch (Exception e) {
                throw new MigrationException("Error executing command.  Cause: " + e, e);
            } finally {
                if (scriptReader != null) {
                    scriptReader.close();
                }
            }
        } catch (Throwable e) {
            while (e instanceof MigrationException) {
                e = e.getCause();
            }
            throw new MigrationException("Error executing command.  Cause: " + e, e);
        }
    }

//    protected ScriptRunner getScriptRunner(ConnectionProvider connectionProvider, DatabaseOperationOption option,
//                                           PrintStream printStream) {
//        try {
//            ScriptRunnerWarningIgnore scriptRunner = new ScriptRunnerWarningIgnore(connectionProvider.getConnection());
//            scriptRunner.setStopOnError(option.isStopOnError());
//            scriptRunner.setThrowWarning(option.isThrowWarning());
//            scriptRunner.setEscapeProcessing(false);
//            scriptRunner.setThrowWarning(false);
//            scriptRunner.setAutoCommit(option.isAutoCommit());
//            scriptRunner.setDelimiter(option.getDelimiter());
//            scriptRunner.setFullLineDelimiter(option.isFullLineDelimiter());
//            scriptRunner.setSendFullScript(option.isSendFullScript());
//            scriptRunner.setRemoveCRs(option.isRemoveCRs());
//            return scriptRunner;
//        } catch (Exception e) {
//            throw new MigrationException("Error creating ScriptRunner.  Cause: " + e, e);
//        }
//    }

    private List<Change> getPendingChanges(Connection con, MigrationLoader migrationsLoader,
                                           DatabaseOperationOption option) {
        List<Change> pending = new ArrayList<Change>();
        List<Change> migrations = migrationsLoader.getMigrations();
        List<Change> changelog = getChangelog(con, option);
        for (Change change : migrations) {
            int index = changelog.indexOf(change);
            if (index < 0) {
                pending.add(change);
            }
        }
        Collections.sort(pending);
        return pending;
    }
}
