package com.shuyun.crmep.customized.demo;

import com.shuyun.crmep.customized.demo.domain.NodeDemo;
import com.shuyun.crmep.customized.demo.service.IDemoService;
import com.shuyun.crmep.customized.demo.vo.NodeDemoVo;
import com.shuyun.crmep.customized.support.impl.AbstractNodeDataHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 定义节点复制、删除逻辑
 * @date 2019/6/7
 */
@Component("nodeDemoHandler")
@Slf4j
public class NodeDemoHandler extends AbstractNodeDataHandler {
    @Autowired
    private IDemoService demoService;

    // 节点复制
    @Override
    public Object clone(Long nodeId, Long newNodeId) {
        try {
            log.info("Demo节点复制开始. nodeId:{},newNodeId:{}",nodeId,newNodeId);
            NodeDemo nodeDemo = demoService.getById(nodeId);
            NodeDemo nodeDemoNew = new NodeDemo();

            if (nodeDemo != null) {
                BeanUtils.copyProperties(nodeDemo, nodeDemoNew);
                nodeDemoNew.setId(newNodeId);
                demoService.save(nodeDemoNew);
                NodeDemo result = demoService.getById(newNodeId);
                NodeDemoVo nodeDemoVo = new NodeDemoVo();
                BeanUtils.copyProperties(result, nodeDemoVo);
                return nodeDemoVo;
            }
            log.info("Demo节点复制结束");
        }catch (Exception e){
            log.error("Demo节点复制失败.",e);
            throw e;
        }
        return null;
    }

    // 节点删除
    @Override
    public void delete(Long nodeId) {
        NodeDemo nodeDemo = demoService.getById(nodeId);
        if (null != nodeDemo) {
            demoService.removeById(nodeId);
        }
    }


    @Override
    public String type() {
        return NodeDemo.TYPE;
    }

}
