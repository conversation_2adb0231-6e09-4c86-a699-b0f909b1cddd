package com.shuyun.crmep.customized.base.context;

import com.shuyun.crmep.customized.base.util.VisitorInfoUtil;
import com.shuyun.pip.component.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerRequestFilter;
import java.io.IOException;
import java.util.Base64;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 拦截URL请求
 * @date 2019/6/11
 */
@Slf4j
public class UrlOncePerRequestFilter /*implements ContainerRequestFilter*/ extends OncePerRequestFilter {
    private static final String AUTHORIZATION = "Authorization";
    private static final String USER_ID = "sub";
    private static final String USER_NAME = "name";

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        log.info("拦截请求url: {} 开始",request.getRequestURI());
        String requestUri = request.getRequestURI();
        if (request.getMethod().equals(HttpMethod.OPTIONS.toString())) {
            log.info("url:{}, OPTIONS请求不做处理.", requestUri);
            filterChain.doFilter(request, response);
            return;
        }
        String authorization = getHeader(request, AUTHORIZATION);
        if(StringUtils.isNotEmpty(authorization)){
            VisitorInfoUtil.setUserId(parserAuth(authorization, USER_ID));
            VisitorInfoUtil.setAuthoration(authorization);
            VisitorInfoUtil.setUserName(parserAuth(authorization, USER_NAME));
        }else{
            log.info("拦截请求url: {} authorization为空,不做值设置",request.getRequestURI());
        }

        filterChain.doFilter(request, response);
        log.info("拦截请求url: {} 结束",request.getRequestURI());
    }

    private String parserAuth(String authorization, String filed) {
        try {
            String payload = authorization.split("\\.")[1];
            String json = new String(Base64.getUrlDecoder().decode(payload)).replaceAll("/", "_");
            Map jsonObject = JsonUtils.parse(json, Map.class);
            return (String) jsonObject.get(filed);
        } catch (Exception e) {
            log.info("authorization:{}不存在{}字段", authorization, filed);
        }
        return null;
    }


    private String getHeader(HttpServletRequest request , String headerName )  {
         String uid = request.getHeader(headerName);
         if(StringUtils.isEmpty(uid)){
             uid = null;
         }
        return uid;
    }


    /*@Override
    public void filter(ContainerRequestContext request) throws IOException {
        log.info("拦截请求url: {} 开始",request.getUriInfo().getPath());
        String requestUri = request.getUriInfo().getPath();
        if (request.getMethod().equals(HttpMethod.OPTIONS.toString())) {
            log.info("url:{}, OPTIONS请求不做处理.", requestUri);
            return;
        }
        String authorization = getHeader(request, AUTHORIZATION);
        if(StringUtils.isNotEmpty(authorization)){
            VisitorInfoUtil.setUserId(parserAuth(authorization, USER_ID));
            VisitorInfoUtil.setAuthoration(authorization);
            VisitorInfoUtil.setUserName(parserAuth(authorization, USER_NAME));
        }else{
            log.info("拦截请求url: {} authorization为空,不做值设置",request.getUriInfo().getPath());
        }

        log.info("拦截请求url: {} 结束",request.getUriInfo().getPath());
    }*/
}
