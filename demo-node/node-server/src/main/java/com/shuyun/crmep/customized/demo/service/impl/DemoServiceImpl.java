package com.shuyun.crmep.customized.demo.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shuyun.crmep.customized.demo.domain.NodeDemo;
import com.shuyun.crmep.customized.demo.mapper.NodeDemoMapper;
import com.shuyun.crmep.customized.demo.service.IDemoService;
import com.shuyun.node.api.demo.dto.DemoDto;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description
 * @date 2019/4/12
 */
@Service
public class DemoServiceImpl extends ServiceImpl<NodeDemoMapper, NodeDemo>  implements IDemoService  {
  /*  @Autowired
    @Qualifier("guavaCache")
    private ICache<NodeDemo> guavaCache;*/


   /* @Autowired
    @Qualifier("redisCache")
    private ICache<Demo> redisCache;*/

    @Autowired
    private NodeDemoMapper demoMapper;

/*    @Override
    public void createDemo(DemoDto demoDto) {
        Demo demo = new Demo();
        BeanUtils.copyProperties(demoDto,demo);
        demo.setCreateBy(1L);
        demo.setCreateTime(new Date());
        int cnt = demoMapper.insert(demo);
        System.out.println(cnt);
    }

    @Override
    public void delteDemo(Long id) {

    }*/

    @Override
    public DemoDto getDemoByCache(Long id) {
        // guava cache
      /*  NodeDemo demo = guavaCache.get(id.toString());
       DemoDto demoDto = new DemoDto();
       demo = Optional.ofNullable(demo)
               .orElseGet(() ->{
                   NodeDemo dm = demoMapper.selectById(id);
                  guavaCache.put(id.toString(),dm);
                  return dm;
               } );
*/
        // redis cache
      /*  Demo demo = redisCache.get(id.toString());
        DemoDto demoDto = new DemoDto();
        demo = Optional.ofNullable(demo)
                .orElseGet(() ->{
                    Demo dm = demoMapper.selectByPrimaryKey(id);
                    redisCache.put(id.toString(),dm);
                    return dm;
                } );*/
     //  BeanUtils.copyProperties(demo,demoDto);

     //  return demoDto;
        return null;
    }


    @Override
    public DemoDto getDemoByName(String name){
        DemoDto demoDto = new DemoDto();
        NodeDemo dm = demoMapper.getDemoByName(name);
        BeanUtils.copyProperties(dm,demoDto);
        return demoDto;
    }
}
