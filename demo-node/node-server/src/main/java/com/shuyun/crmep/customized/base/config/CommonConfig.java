package com.shuyun.crmep.customized.base.config;

import com.shuyun.crmep.customized.base.context.UrlOncePerRequestFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Description 注册filter, migration
 * @date 2019/6/14
 */
@Configuration
public class CommonConfig {

    // 注册filter
    @Bean
    public FilterRegistrationBean urlOncePerRequestFilterRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean(new UrlOncePerRequestFilter());
        registration.addUrlPatterns("/*");
        return registration;
    }



}
