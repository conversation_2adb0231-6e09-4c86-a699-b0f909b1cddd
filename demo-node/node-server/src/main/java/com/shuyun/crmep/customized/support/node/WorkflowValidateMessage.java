package com.shuyun.crmep.customized.support.node;


public class WorkflowValidateMessage {
	
	private Long nodeId;
	private String message;	
	 
	public WorkflowValidateMessage(){}
	
	private WorkflowValidateMessage(Long nodeId, String message) {
		super();
		this.message = message;
		this.nodeId = nodeId;
	}
	
	public static  WorkflowValidateMessage newError(Long nodeId,String message){
		return new WorkflowValidateMessage(nodeId,message);
	}
	
	public Long getNodeId() {
		return nodeId;
	}
	
	public void setNodeId(Long nodeId) {
		this.nodeId = nodeId;
	}
	
	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	@Override
	public String toString() {
		return "WorkflowValidateMessage [nodeId=" + nodeId + ", message="
				+ message + "]";
	}
	
	
}
