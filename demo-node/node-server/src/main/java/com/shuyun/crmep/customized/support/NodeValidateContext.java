package com.shuyun.crmep.customized.support;

import java.util.Set;


/**
 * 验证上下文
 * <AUTHOR>
 *
 */
@SuppressWarnings("rawtypes")
public interface NodeValidateContext extends Cloneable ,Mergeable<NodeValidateContext> {

	/**
	 * 当前节点ID
	 * 
	 * @return
	 */
	Long getNodeId();
	
	/**
	 * 参数列表
	 * @return
	 */
	Set<String> getAttributeNames();
	
	/**
	 * 获取一个参数
	 * @param name
	 * @return
	 */
	<T extends Comparable<T>> T getAttribute(String name);
	
	/**
	 * 设置一个参数
	 * <br/>可通过这种方式向后面的节点传递验证需要的信息
	 * @see ConflictStrategy
	 * @param name
	 * @param value
	 */
	 void setAttribute(String name, Comparable<?> value);
	
	/**
	 * 添加一个约束违背
	 * @param constraintViolation
	 */
	void addConstraintViolation(NodeValidateConstraintViolation constraintViolation);
	
	/**
	 * 获取当前验证中检测到的约束违背
	 * @return
	 */
	Set<NodeValidateConstraintViolation> getConstraintViolations();
	
	/**
	 * 给同一个参数设置值的冲突选择策略
	 * <AUTHOR>
	 *
	 */
	public static enum ConflictStrategy{GRATER,SMALLER};

}
