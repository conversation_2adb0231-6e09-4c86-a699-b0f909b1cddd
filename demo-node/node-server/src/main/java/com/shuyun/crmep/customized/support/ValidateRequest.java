package com.shuyun.crmep.customized.support;


import com.shuyun.crmep.customized.support.impl.DefaultNodeValidateContext;
import com.shuyun.crmep.customized.support.node.Node;

/**
 * Created by Administrator on 2016/1/4.
 */
public class ValidateRequest {

    private Node node;
    private DefaultNodeValidateContext validateContext;

    public ValidateRequest(){}

    public ValidateRequest(Node node, DefaultNodeValidateContext validateContext) {
        this.node = node;
        this.validateContext = validateContext;
    }

    public Node getNode() {
        return node;
    }

    public DefaultNodeValidateContext getValidateContext() {
        return validateContext;
    }

    public void setNode(Node node) {
        this.node = node;
    }

    public void setValidateContext(DefaultNodeValidateContext validateContext) {
        this.validateContext = validateContext;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((node == null) ? 0 : node.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        ValidateRequest other = (ValidateRequest) obj;
        if (node == null) {
            if (other.node != null)
                return false;
        } else if (!node.equals(other.node))
            return false;
        return true;
    }
}
