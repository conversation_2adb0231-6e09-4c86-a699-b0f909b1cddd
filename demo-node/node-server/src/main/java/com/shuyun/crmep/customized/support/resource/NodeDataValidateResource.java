package com.shuyun.crmep.customized.support.resource;

import com.google.common.base.Function;
import com.google.common.collect.Lists;

import com.shuyun.crmep.customized.support.*;
import com.shuyun.crmep.customized.support.impl.DefaultNodeValidateContext;
import com.shuyun.crmep.customized.support.node.Node;
import com.shuyun.crmep.customized.support.node.WorkflowValidateMessage;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.*;
import static com.shuyun.crmep.customized.support.enums.NodeValidateContextAttribute.FIRST_SCHEDULE_TIME;
import static com.shuyun.crmep.customized.support.enums.NodeValidateContextAttribute.FIRST_END_TIME;


/**
 * Created by Administrator on 2015/11/12.
 */
@RestController
@RequestMapping("/node/validator")
public class NodeDataValidateResource {

    private static Logger logger = LoggerFactory.getLogger(NodeDataValidateResource.class);

    @Autowired
    private NodeValidatorFactoryService validatorFactoryService;

    /**
     * 验证节点，并将错误放到validateContext中
     * @param validateContext 验证上下文
     * @return
     */
    @PostMapping("/validate/{nodeType}")
    @Produces(MediaType.APPLICATION_JSON)
    public DefaultNodeValidateContext validate(@PathVariable("nodeType") String nodeType,
                                               @RequestBody  DefaultNodeValidateContext validateContext){
        logger.info("验证节点开始.nodeType:{}",nodeType);
        NodeValidator nodeValidator = validatorFactoryService.getNodeValidator(nodeType);
        if (nodeValidator == null) {
			logger.error("Validator for NodeType:{} not Found!!!!!!!!!!",nodeType);
		}else{
            nodeValidator.validate(validateContext);
        }
        logger.info("验证节点结束");
        return validateContext;
    }

    /**
     * 更新验证上下文（设置或者更新上下文中的属性供后面的节点验证使用）
     * <br/>比如等待节点可以设置活动是否周期性，后面的等待需要使用
     * @param validateContext
     */
    @PostMapping("/update/{nodeType}")
    @Produces(MediaType.APPLICATION_JSON)
    public DefaultNodeValidateContext update(@PathVariable("nodeType") String nodeType,
                       DefaultNodeValidateContext validateContext){
        NodeValidator nodeValidator = validatorFactoryService.getNodeValidator(nodeType);
        if (nodeValidator == null) {
            logger.error("Validator for NodeType:{} not Found!!!!!!!!!!",nodeType);
        }else{
            nodeValidator.update(validateContext);
        }

        return validateContext;
    }

    @PostMapping("/validate/nodes")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public List<WorkflowValidateMessage> validateWorkflow(@RequestBody  ValidateEntity validateEntity){
        logger.info("验证Nodes开始");
        List<WorkflowValidateMessage> allMessages = Lists.newArrayList();

        Map<Long,DefaultNodeValidateContext> nodeValidateMap = new HashMap<Long,DefaultNodeValidateContext>();


        Queue<ValidateRequest> validateQueue = validateEntity.getValidateQueue() ;
     //   Map<Long, List<Long>> nodeMutiIndgreeMap = validateEntity.getNodeMutiIndgreeMap() ;
        //从第一个节点  开始节点 验证
  /*      ValidateRequest startRequest = validateQueue.poll();
        Node startNode = startRequest.getNode();
        DefaultNodeValidateContext startContext = startRequest.getValidateContext();
        startContext.setAttribute(FIRST_SCHEDULE_TIME.name(), new Date());*/
        // 开始、时间节点不会走到定制节点逻辑，所以不需要考虑开始节点

        while (!validateQueue.isEmpty()) {
            ValidateRequest request = validateQueue.poll();
            DefaultNodeValidateContext context = request.getValidateContext();
          //  context = context.merge(startContext);
            request.setValidateContext(context);

            List<WorkflowValidateMessage> nodeValidateMessages = doValidate(request);

            if (CollectionUtils.isNotEmpty(nodeValidateMessages)) {
                allMessages.addAll(nodeValidateMessages);
            }

            Node node = request.getNode();
            context = request.getValidateContext();
       /*     Date endTime = context.getAttribute(FIRST_END_TIME.name());
            context.setAttribute(FIRST_SCHEDULE_TIME.name(), endTime);

            List<Long> indgreeNodeIds = nodeMutiIndgreeMap.get(node.getId());//入度大于1的节点需要合并验证上下文信息
            if(CollectionUtils.isNotEmpty(indgreeNodeIds)){
                for (Long indgreeNodeId:indgreeNodeIds){
                    DefaultNodeValidateContext indgreeContext = nodeValidateMap.get(indgreeNodeId);

                    if(null!=indgreeContext){
                        indgreeContext.setAttribute(FIRST_SCHEDULE_TIME.name(), endTime);
                        context = context.merge(indgreeContext);
                    }
                }
            }*/

         //   startContext = startContext.merge(context) ;

            nodeValidateMap.put(node.getId(),context);

        }
        logger.info("验证Nodes结束");
        return allMessages;
    }

    private List<WorkflowValidateMessage> doValidate(ValidateRequest request){

        Node node = request.getNode();
        DefaultNodeValidateContext context = request.getValidateContext();

        NodeValidator nodeValidator = validatorFactoryService.getNodeValidator(node.getType());
        if (nodeValidator == null) {
            logger.error("Validator for NodeType:{} not Found!!!!!!!!!!",node.getType());
        }else{
            Set<NodeValidateConstraintViolation> msg = null;
            try {
                nodeValidator.validate(context);
                msg = context.getConstraintViolations();
                Date scheduleTime = context.getAttribute(FIRST_SCHEDULE_TIME.name());
                Date endTime = context.getAttribute(FIRST_END_TIME.name());
                if(endTime==null){
                    context.setAttribute(FIRST_END_TIME.name(), scheduleTime);
                }
                logger.info("{} Node:{} Context updated:{}",
                        new Object[] { nodeValidator.getClass().getSimpleName(),node.getId(),context });
            } catch (Exception e) {
                logger.error("{}  Node:{},update Context:{},throws:{}",
                        new Object[] { nodeValidator.getClass().getSimpleName(),node.getId(),context, e.toString() });
                e.printStackTrace();
            }

            try {
                nodeValidator.update(context);
                Date scheduleTime = context.getAttribute(FIRST_SCHEDULE_TIME.name());
                Date endTime = context.getAttribute(FIRST_END_TIME.name());
                if(endTime==null){
                    context.setAttribute(FIRST_END_TIME.name(), scheduleTime);
                }
                logger.info("{} Node:{} Context updated:{}",
                        new Object[] { nodeValidator.getClass().getSimpleName(),node.getId(),context });
            } catch (Exception e) {
                logger.error("{}  Node:{},update Context:{},throws:{}",
                        new Object[] { nodeValidator.getClass().getSimpleName(),node.getId(),context, e.toString() });
                e.printStackTrace();
            }

            if (CollectionUtils.isNotEmpty(msg)) {
                return Lists.transform(Lists.newArrayList(msg), new Function<NodeValidateConstraintViolation, WorkflowValidateMessage>() {
                    @Override
                    public WorkflowValidateMessage apply(
                            NodeValidateConstraintViolation input) {
                        return WorkflowValidateMessage.newError(input.getNodeId(), input.getMessage());
                    }

                });
            }
        }
        return Collections.emptyList();

    }
}
