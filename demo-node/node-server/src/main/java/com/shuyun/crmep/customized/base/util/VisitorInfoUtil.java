package com.shuyun.crmep.customized.base.util;

/**
 * <AUTHOR>
 * @Description 线程内部值
 * @date 2019/6/12
 */
public class VisitorInfoUtil {
    private static final ThreadLocal<String> threadUserId = new ThreadLocal<String>();
    private static final ThreadLocal<String> threadUsername = new ThreadLocal<String>();
    private static final ThreadLocal<String> threadAuthoration = new ThreadLocal<String>();


    public static String getUserId() {
        return  threadUserId.get();
    }

    public static void setUserId(String userId) {
        threadUserId.set(userId);
    }

    public static String getAuthoration() {
        return (String)threadAuthoration.get();
    }

    public static void setAuthoration(String authoration) {
        threadAuthoration.set(authoration);
    }

    public static void setUserName(String userName){
        threadUsername.set(userName);
    }


}
