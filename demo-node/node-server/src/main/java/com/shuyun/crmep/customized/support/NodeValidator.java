package com.shuyun.crmep.customized.support;


import com.shuyun.crmep.customized.support.impl.DefaultNodeValidateContext;

/**
 * Created by Administrator on 2015/7/3.
 */
public interface NodeValidator extends NodeSupport{
    /**
     * 验证节点，并将错误放到validateContext中
     * @param validateContext 验证上下文
     * @return
     */
    void validate(DefaultNodeValidateContext validateContext);

    /**
     * 更新验证上下文（设置或者更新上下文中的属性供后面的节点验证使用）
     * <br/>比如等待节点可以设置活动是否周期性，后面的等待需要使用
     * <br/>
     * @param validateContext
     */
    void update(DefaultNodeValidateContext validateContext);
}
