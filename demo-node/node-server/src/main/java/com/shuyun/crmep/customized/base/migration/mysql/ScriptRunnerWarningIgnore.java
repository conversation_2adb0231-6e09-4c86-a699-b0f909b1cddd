//package com.shuyun.crmep.customized.base.migration.mysql;
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.ibatis.jdbc.RuntimeSqlException;
//import org.apache.ibatis.jdbc.ScriptRunner;
//
//import java.io.BufferedReader;
//import java.io.Reader;
//import java.sql.*;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//
///**
// * @Author: wayne
// */
//@Slf4j
//public class ScriptRunnerWarningIgnore extends ScriptRunner {
//    private static final String LINE_SEPARATOR = System.getProperty("line.separator", "\n");
//
//    private static final String DEFAULT_DELIMITER = ";";
//
//    private static final Pattern DELIMITER_PATTERN = Pattern.compile("^\\s*((--)|(//))?\\s*(//)?\\s*@DELIMITER\\s+([^\\s]+)", Pattern.CASE_INSENSITIVE);
//
//    private final Connection connection;
//
//    private boolean stopOnError;
//    private boolean throwWarning;
//    private boolean autoCommit;
//    private boolean sendFullScript;
//    private boolean removeCRs;
//    private boolean escapeProcessing = true;
//
//    private String delimiter = DEFAULT_DELIMITER;
//    private boolean fullLineDelimiter;
//
//    public ScriptRunnerWarningIgnore(Connection connection) {
//        super(connection);
//        this.connection = connection;
//    }
//
//    @Override
//    public void setStopOnError(boolean stopOnError) {
//        this.stopOnError = stopOnError;
//    }
//
//    @Override
//    public void setThrowWarning(boolean throwWarning) {
//        this.throwWarning = throwWarning;
//    }
//
//    @Override
//    public void setAutoCommit(boolean autoCommit) {
//        this.autoCommit = autoCommit;
//    }
//
//    @Override
//    public void setSendFullScript(boolean sendFullScript) {
//        this.sendFullScript = sendFullScript;
//    }
//
//    @Override
//    public void setRemoveCRs(boolean removeCRs) {
//        this.removeCRs = removeCRs;
//    }
//
//    /**
//     * @since 3.1.1
//     */
//    @Override
//    public void setEscapeProcessing(boolean escapeProcessing) {
//        this.escapeProcessing = escapeProcessing;
//    }
//
//    @Override
//    public void setDelimiter(String delimiter) {
//        this.delimiter = delimiter;
//    }
//
//    @Override
//    public void setFullLineDelimiter(boolean fullLineDelimiter) {
//        this.fullLineDelimiter = fullLineDelimiter;
//    }
//
//    @Override
//    public void runScript(Reader reader) {
//        setAutoCommit();
//
//        try {
//            if (sendFullScript) {
//                executeFullScript(reader);
//            } else {
//                executeLineByLine(reader);
//            }
//        } finally {
//            rollbackConnection();
//        }
//    }
//
//    private void executeFullScript(Reader reader) {
//        StringBuilder script = new StringBuilder();
//        try {
//            BufferedReader lineReader = new BufferedReader(reader);
//            String line;
//            while ((line = lineReader.readLine()) != null) {
//                script.append(line);
//                script.append(LINE_SEPARATOR);
//            }
//            String command = script.toString();
//            log.info("command:{}", command);
//            executeStatement(command);
//            commitConnection();
//        } catch (Exception e) {
//            String message = "Error executing: " + script;
//            log.error(message, e);
//            throw new RuntimeSqlException(message, e);
//        }
//    }
//
//    private void executeLineByLine(Reader reader) {
//        StringBuilder command = new StringBuilder();
//        try {
//            BufferedReader lineReader = new BufferedReader(reader);
//            String line;
//            while ((line = lineReader.readLine()) != null) {
//                handleLine(command, line);
//            }
//            commitConnection();
//            checkForMissingLineTerminator(command);
//        } catch (Exception e) {
//            String message = "Error executing: " + command;
//            log.error(message, e);
//            throw new RuntimeSqlException(message, e);
//        }
//    }
//
//    @Override
//    public void closeConnection() {
//        try {
//            connection.close();
//        } catch (Exception e) {
//            // ignore
//        }
//    }
//
//    private void setAutoCommit() {
//        try {
//            if (autoCommit != connection.getAutoCommit()) {
//                connection.setAutoCommit(autoCommit);
//            }
//        } catch (Throwable t) {
//            throw new RuntimeSqlException("Could not set AutoCommit to " + autoCommit + ". Cause: " + t, t);
//        }
//    }
//
//    private void commitConnection() {
//        try {
//            if (!connection.getAutoCommit()) {
//                connection.commit();
//            }
//        } catch (Throwable t) {
//            throw new RuntimeSqlException("Could not commit transaction. Cause: " + t, t);
//        }
//    }
//
//    private void rollbackConnection() {
//        try {
//            if (!connection.getAutoCommit()) {
//                connection.rollback();
//            }
//        } catch (Throwable t) {
//            // ignore
//        }
//    }
//
//    private void checkForMissingLineTerminator(StringBuilder command) {
//        if (command != null && command.toString().trim().length() > 0) {
//            throw new RuntimeSqlException("Line missing end-of-line terminator (" + delimiter + ") => " + command);
//        }
//    }
//
//    private void handleLine(StringBuilder command, String line) throws SQLException {
//        String trimmedLine = line.trim();
//        if (lineIsComment(trimmedLine)) {
//            Matcher matcher = DELIMITER_PATTERN.matcher(trimmedLine);
//            if (matcher.find()) {
//                delimiter = matcher.group(5);
//            }
//            log.info(trimmedLine);
//        } else if (commandReadyToExecute(trimmedLine)) {
//            command.append(line, 0, line.lastIndexOf(delimiter));
//            command.append(LINE_SEPARATOR);
//            log.info(command.toString());
//            executeStatement(command.toString());
//            command.setLength(0);
//        } else if (trimmedLine.length() > 0) {
//            command.append(line);
//            command.append(LINE_SEPARATOR);
//        }
//    }
//
//    private boolean lineIsComment(String trimmedLine) {
//        return trimmedLine.startsWith("//") || trimmedLine.startsWith("--");
//    }
//
//    private boolean commandReadyToExecute(String trimmedLine) {
//        // issue #561 remove anything after the delimiter
//        return !fullLineDelimiter && trimmedLine.contains(delimiter) || fullLineDelimiter && trimmedLine.equals(delimiter);
//    }
//
//    private void executeStatement(String command) throws SQLException {
//        boolean hasResults = false;
//        Statement statement = connection.createStatement();
//        statement.setEscapeProcessing(escapeProcessing);
//        String sql = command;
//        if (removeCRs) {
//            sql = sql.replaceAll("\r\n", "\n");
//        }
//        if (stopOnError) {
//            hasResults = statement.execute(sql);
//            if (throwWarning) {
//                // In Oracle, CRATE PROCEDURE, FUNCTION, etc. returns warning
//                // instead of throwing exception if there is compilation error.
//                SQLWarning warning = statement.getWarnings();
//                if (warning != null) {
//                    throw warning;
//                }
//            }
//        } else {
//            try {
//                hasResults = statement.execute(sql);
//            } catch (SQLException e) {
//                String message = "Error executing: " + command;
//                log.error(message, e);
//            }finally {
//                try {
//                    statement.close();
//                } catch (Exception e) {
//                    // Ignore to workaround a bug in some connection pools
//                }
//            }
//        }
//        printResults(statement, hasResults);
//    }
//
//    private void printResults(Statement statement, boolean hasResults) {
//        ResultSet rs = null;
//        try {
//            if (hasResults) {
//                rs = statement.getResultSet();
//                if (rs != null) {
//                    ResultSetMetaData md = rs.getMetaData();
//                    int cols = md.getColumnCount();
//                    for (int i = 0; i < cols; i++) {
//                        String name = md.getColumnLabel(i + 1);
//                        log.info(name + "\t");
//                    }
//                    while (rs.next()) {
//                        for (int i = 0; i < cols; i++) {
//                            String value = rs.getString(i + 1);
//                            log.info(value + "\t");
//                        }
//                    }
//                }
//            }
//        } catch (SQLException e) {
//            log.error("Error printing results: " + e.getMessage(), e);
//        }finally {
//            try{
//                if(rs!= null) {
//                    rs.close();
//                }
//            }catch (Exception e){
//                e.printStackTrace();
//            }
//        }
//    }
//}
