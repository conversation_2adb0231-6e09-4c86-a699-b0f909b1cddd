package com.shuyun.crmep.customized.support.impl;


import com.shuyun.crmep.customized.support.NodeValidateConstraintViolation;
import com.shuyun.crmep.customized.support.NodeValidator;

public abstract class AbstractNodeValidator implements NodeValidator {

	@Override
	public void validate(DefaultNodeValidateContext context) {
		throw new UnsupportedOperationException();
	}
	
	@Override
	public void update(DefaultNodeValidateContext context){
		//do nothing
	}
	
	protected final void addError(DefaultNodeValidateContext context,Long nodeId,String message){
		context.addConstraintViolation(new NodeValidateConstraintViolation(nodeId, message));
	}
	
}