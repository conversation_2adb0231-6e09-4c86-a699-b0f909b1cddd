package com.shuyun.crmep.customized.support;

import java.util.List;
import java.util.Map;
import java.util.Queue;

/**
 * Created by Administrator on 2016/1/4.
 */
public class ValidateEntity {
    Queue<ValidateRequest> validateQueue ;
    Map<Long,List<Long>> nodeMutiIndgreeMap ;

    public ValidateEntity(){}

    public ValidateEntity(Queue<ValidateRequest> validateQueue, Map<Long, List<Long>> nodeMutiIndgreeMap){
        this.validateQueue = validateQueue ;
        this.nodeMutiIndgreeMap = nodeMutiIndgreeMap ;
    }

    public Queue<ValidateRequest> getValidateQueue() {
        return validateQueue;
    }

    public void setValidateQueue(Queue<ValidateRequest> validateQueue) {
        this.validateQueue = validateQueue;
    }

    public Map<Long, List<Long>> getNodeMutiIndgreeMap() {
        return nodeMutiIndgreeMap;
    }

    public void setNodeMutiIndgreeMap(Map<Long, List<Long>> nodeMutiIndgreeMap) {
        this.nodeMutiIndgreeMap = nodeMutiIndgreeMap;
    }
}
