package com.shuyun.crmep.customized;

import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;
import com.shuyun.pip.BaseApplication;
import com.shuyun.pip.autoconfiguration.ReloadableMessageSourceAutoConfiguration;
import com.shuyun.pip.autoconfigure.LockerAutoConfiguration;
import com.shuyun.pip.autoconfigure.PipAutoConfiguration;
import com.shuyun.pip.autoconfigure.RedisLockerAutoConfigure;
import com.shuyun.pip.autoconfigure.ValidatorConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration;

/**
 * <AUTHOR>
 * @date 2019/4/06
 */

@SpringBootApplication(
        scanBasePackages = {"com.shuyun.kylin.starter","com.shuyun.crmep.customized"},
        exclude = {MultipartAutoConfiguration.class, DataSourceAutoConfiguration.class, LockerAutoConfiguration.class,
                MybatisPlusAutoConfiguration.class,RedisLockerAutoConfigure.class, ReloadableMessageSourceAutoConfiguration.class,
                  ValidatorConfiguration.class, PipAutoConfiguration.class})
public class CustomizedApplication extends BaseApplication {
    private static Logger logger = LoggerFactory.getLogger(CustomizedApplication.class);
    public CustomizedApplication() {
        super(CustomizedApplication.class);
    }

    public static void main(String[] args) {
        logger.info("定制节点启动开始...");
        new CustomizedApplication().run(args);
        logger.info("定制节点启动结束...");
    }
}
