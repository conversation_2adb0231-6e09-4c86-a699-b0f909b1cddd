package com.shuyun.crmep.customized.base.stream.kafka.producer;


import com.shuyun.crmep.customized.base.stream.kafka.messaging.KafkaSource;
import com.shuyun.node.api.demo.dto.DemoDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.integration.support.MessageBuilder;
import com.shuyun.crmep.customized.base.stream.kafka.messaging.KafkaSink;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description kafka生产者
 * @date 2019/5/15
 */

@Component
 @EnableBinding({KafkaSource.class, KafkaSink.class})
@ConditionalOnExpression("${system.kafka.enabled:false}")
public class KafkaProducer {
     @Autowired
    private KafkaSource kafkaSource;

    public void send(DemoDto user){
      kafkaSource.output().send(MessageBuilder.withPayload(user).build());
        //    output.send(MessageBuilder.withPayload(user).build());
        System.out.println("发送用户信息结束." + user.toString() );
        //  return user.toString();

    }

    public void sendOutput(DemoDto user){
        kafkaSource.output1().send(MessageBuilder.withPayload(user).build());
        //    output.send(MessageBuilder.withPayload(user).build());
        System.out.println("发送用户信息结束." + user.toString() );
        //  return user.toString();

    }
}
