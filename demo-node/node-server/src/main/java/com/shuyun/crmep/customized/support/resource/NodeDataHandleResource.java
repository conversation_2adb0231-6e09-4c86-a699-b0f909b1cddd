package com.shuyun.crmep.customized.support.resource;


import com.shuyun.crmep.customized.support.NodeDataHandler;
import com.shuyun.crmep.customized.support.NodeDataHandlerFactoryService;
import com.shuyun.crmep.customized.support.node.NodeStart;
import com.shuyun.crmep.customized.support.node.OldAndNewNodeVO;
import com.shuyun.support.common.exception.WrongArgException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

/**
 * Created by Administrator on 2015/7/4.
 */
@RestController
@RequestMapping("/node/handler")
public class NodeDataHandleResource {
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    public static final String NODE_TYPE_BLANK = "tflowblank";//空白节点的类型

    @Autowired
    private NodeDataHandlerFactoryService handlerFactoryService;

    @PostMapping("/{nodeId}/{newNodeId}/{nodeType}")
    public void clone(@PathVariable("nodeId") Long nodeId,
                      @PathVariable("newNodeId") Long newNodeId,
                      @PathVariable("nodeType") String nodeType){
        logger.info("复制节点开始.nodeId:{},newNodeId:{},nodeType:{}",nodeId,newNodeId,nodeType);
        if (NodeStart.TYPE.equals(nodeType)){//开始接口 无对应实体数据表 不需要clone数据
            return ;
        }
        NodeDataHandler handler = handlerFactoryService.getNodeDataHandler(nodeType);
        if(null!=handler){
            handler.clone(nodeId,newNodeId);
        }else{
            throw new WrongArgException(nodeType+":此节点类型的NodeDataHandler不存在");
        }
        logger.info("复制节点结束");
    }

    @PostMapping("/{nodeId}/{newNodeId}/{nodeType}/special")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public void clone(@PathVariable("nodeId") Long nodeId,
                      @PathVariable("newNodeId") Long newNodeId,
                      @PathVariable("nodeType") String nodeType,
                      @RequestBody OldAndNewNodeVO vo){
        logger.info("special复制节点开始.nodeId:{},newNodeId:{},nodeType:{}",nodeId,newNodeId,nodeType);
        if (NodeStart.TYPE.equals(nodeType)){//开始接口 无对应实体数据表 不需要clone数据
            return ;
        }
        NodeDataHandler handler = handlerFactoryService.getNodeDataHandler(nodeType);
        if(null!=handler){
            handler.clone(nodeId,newNodeId, vo.getMaps());
        }else{
            throw new WrongArgException(nodeType+":此节点类型的NodeDataHandler不存在");
        }
        logger.info("special复制节点结束");
    }


    @DeleteMapping("/{nodeId}/{nodeType}")
    public void delete(@PathVariable("nodeId") Long nodeId,
                       @PathVariable("nodeType") String nodeType){
        logger.info("删除节点开始.nodeId:{}, nodeType:{}",nodeId, nodeType);
        if (NodeStart.TYPE.equals(nodeType)||NODE_TYPE_BLANK.equals(nodeType)){//开始接口 无对应实体数据表 不需要delete数据
            return ;
        }
        NodeDataHandler handler = handlerFactoryService.getNodeDataHandler(nodeType);
        logger.info("删除节点获取handler结束.handler:{}",handler);
        if(null!=handler){
            handler.delete(nodeId);
        }else{
            throw new WrongArgException(nodeType+":此节点类型的NodeDataHandler不存在");
        }
    }
}
