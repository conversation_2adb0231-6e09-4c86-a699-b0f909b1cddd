package com.shuyun.crmep.customized.base.enums;

/**
 * <AUTHOR>
 * @Description 微服务名
 * @date 2019/6/13
 */
public enum MicroServiceEnums {
    CAMPAIGN("campaign/v1") ,//v1是接口版本号，应与SERVICE文件的的API_VERSION一致
    NODE("node/v1"),
    CHANNEL("channel/1.0"),
    SYSTEM_CONFIGURATION("system-configuration/v1"),
    COMMON_COMPONENT("common-component/v1"),

    ;

    private String serverName;

    public String getServerName(){
        return this.serverName;
    }

    MicroServiceEnums(String serverName){
        this.serverName = serverName;
    }
}
