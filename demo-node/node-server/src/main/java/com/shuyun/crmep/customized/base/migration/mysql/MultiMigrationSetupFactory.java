package com.shuyun.crmep.customized.base.migration.mysql;

import com.shuyun.motor.migration.MigrationSetupFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 支持包括多migration目录
 *
 * <AUTHOR>
 */
public class MultiMigrationSetupFactory extends MigrationSetupFactory {
    private static final Logger logger = LoggerFactory.getLogger(MultiMigrationSetupFactory.class);
    private static final MultiMigrationSetup multiMigrationSetup = new MultiMigrationSetup();

    public static MultiMigrationSetup defaultMultiMigrationSetup() {
        logger.info("使用multiMigration开始处理....");
        return multiMigrationSetup;
    }
}