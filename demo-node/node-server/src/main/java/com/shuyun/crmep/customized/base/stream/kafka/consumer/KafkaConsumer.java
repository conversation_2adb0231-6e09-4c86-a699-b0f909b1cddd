package com.shuyun.crmep.customized.base.stream.kafka.consumer;

import com.google.gson.Gson;
import com.shuyun.crmep.customized.base.stream.kafka.messaging.KafkaSink;
import com.shuyun.crmep.customized.base.stream.kafka.messaging.KafkaSource;
import com.shuyun.node.api.demo.dto.DemoDto;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @Description 此类定义kafka的消费者,从kafka中接受消息,并对接受的消息做业务处理
 * @date 2019/5/15
 */

@Component
  @EnableBinding({KafkaSink.class, KafkaSource.class})
@ConditionalOnExpression("${system.kafka.enabled:false}")
public class KafkaConsumer {

     @StreamListener(KafkaSink.KAFKA_INPUT)
    public void processUser(DemoDto user) throws InterruptedException {
        Gson gson = new Gson();

        // TODO -- 此处做业务处理
         // 需要注意，若此处处理慢，会导致这个topic阻塞
        System.out.println("接受到消息 KAFKA: " + gson.toJson(user));
        Thread.sleep(100000L);
    }
}
