package com.shuyun.crmep.customized.support;

/**
 * Created by Administrator on 2015/11/17.
 */
public class NodeValidateConstraintViolation {
    private Long nodeId;
    private String message;

    public NodeValidateConstraintViolation(){}

    public NodeValidateConstraintViolation(Long nodeId, String message) {
        this.message = message;
        this.nodeId = nodeId;
    }

    public String getMessage() {
        return message;
    }

    public Long getNodeId() {
        return nodeId;
    }

    public void setNodeId(Long nodeId) {
        this.nodeId = nodeId;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return "Message [nodeId=" + nodeId + ", message=" + message + "]";
    }
}
