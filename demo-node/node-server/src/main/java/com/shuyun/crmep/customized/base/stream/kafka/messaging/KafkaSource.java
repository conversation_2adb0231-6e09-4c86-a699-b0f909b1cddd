package com.shuyun.crmep.customized.base.stream.kafka.messaging;

import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @Description 此类定义Kafka的发送队列
 * @date 2019/5/15
 */
@Component
@ConditionalOnExpression("${system.kafka.enabled:false}")
public interface KafkaSource {
    String KAFKA_OUTPUT = "kafka_output";
    String KAFKA_OUTPUT1 = "kafka_output1";

    @Output(KAFKA_OUTPUT)
    MessageChannel output();

    @Output(KAFKA_OUTPUT1)
    MessageChannel output1();
}
