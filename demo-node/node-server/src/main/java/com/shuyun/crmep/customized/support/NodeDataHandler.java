package com.shuyun.crmep.customized.support;

import java.util.Map;

/**
 * Created by wwc on 2015/5/12.
 */
public interface NodeDataHandler extends NodeSupport{
    /**
     * 复制节点配置
     *
     * @param nodeId
     * @param newNodeId
     */
//    public void clone(Long nodeId, Long newNodeId) throws NodeDataCloneException;
    public Object clone(Long nodeId, Long newNodeId);


    /**
     * 删除节点配置
     *
     * @param nodeId
     */
    public void delete(Long nodeId);

//     public String getNodeType();

    /**
     * 生成执行定时器
     * @param nodeId
     * @return
     */
//    public TaskTimer toTimer(Long nodeId);


    /**
     * 复制节点特殊配置
     * @param nodeId
     * @param newNodeId
     * @param oldNewNodeIds
     * @throws NodeDataCloneException
     */
    public Object clone(Long nodeId, Long newNodeId, Map<Long, Long> oldNewNodeIds) throws NodeDataCloneException;
}
