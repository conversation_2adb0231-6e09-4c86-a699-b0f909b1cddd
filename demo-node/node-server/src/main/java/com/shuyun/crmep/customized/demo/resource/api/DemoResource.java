package com.shuyun.crmep.customized.demo.resource.api;

import com.google.common.collect.Lists;
import com.shuyun.crmep.customized.demo.domain.NodeDemo;
import com.shuyun.crmep.customized.demo.service.IDemoService;
import com.shuyun.kylin.starter.swagger.annotation.OpenApi;
import com.shuyun.node.api.demo.dto.DemoDto;
import com.shuyun.node.api.enums.SexEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.Random;


/**
 * <AUTHOR>
 * @Description Demo resource
 *      用于范例显示
 * @date 2019/4/12
 */
@RestController
@OpenApi
@RequestMapping("/api/demo")
@Api(value = "显示范例1",description = "Demo相关接口,定制后需要将此接口失效")
public class DemoResource {
    @Autowired
    private IDemoService demoService;

/*    @Autowired
    private KafkaProducer kafkaProducer;*/

    @PostMapping
    @ApiOperation(value = "新增Demo", tags = {})
    public void createDemo(@RequestBody DemoDto demoDto){
        NodeDemo demo = new NodeDemo();
        DemoDto nodeDemoVo = Optional.ofNullable(demoDto.getMobile()).map(dmVo -> {
            demoDto.setAddress("上海");
            Random random = new Random(100);
            Integer value = random.nextInt();
            demoDto.setAge(value);
            if(value%2 ==0 ){
                demoDto.setSex(SexEnum.F);
            }else{
                demoDto.setSex(SexEnum.M);
            }
            demoDto.setName("test"+value);
            demoDto.setMobile("139344556666");
            return demoDto;
        }).orElse(demoDto);

        BeanUtils.copyProperties(nodeDemoVo,demo);
        demoService.save(demo);
    }



    @DeleteMapping("/{id}")
    @ApiOperation(value = "修改Demo", tags = {})
    public void delteDemo(@PathVariable("id") Long id){
        demoService.removeById(id);
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "获取Demo", tags = {})
    public DemoDto getDemo(@PathVariable("id") Long id){
        NodeDemo demo = demoService.getById(id);
        DemoDto demoDto = new DemoDto();
        if(demo == null){
            demo = new NodeDemo();
            demo.setId(id);
        }
        BeanUtils.copyProperties(demo,demoDto);
        return demoDto;
    }

    @GetMapping("/info")
    @ApiOperation(value = "获取Demo信息", tags = {})
    public DemoDto getDemoInfo( DemoDto demoDto){
        return demoService.getDemoByName(demoDto.getName());
    }

    @GetMapping("/infos")
    @ApiOperation(value = "获取所有Demo信息", tags = {})
    public List<DemoDto> getDemoInfos(){
        List<NodeDemo> nodeDemos = demoService.list();
       return Optional.ofNullable(nodeDemos).map(nodeDemoList -> {
            List<DemoDto> demoDtos = Lists.newArrayList();
            nodeDemoList.stream().forEach(nodeDemo ->{
                            DemoDto demoDto = new DemoDto();
                            BeanUtils.copyProperties(nodeDemo,demoDto);
                            demoDtos.add(demoDto);

                    }

                    );
            return demoDtos;
        }).orElse(null);
    }

/*
    @PostMapping("/kafka")
    @ApiOperation(value = "用kafka发送Demo信息", tags = {})
    public Map<String,String> sendDemo(@RequestBody DemoDto demoDto) {
        Map<String, String> map = Maps.newHashMap();
       kafkaProducer.send(demoDto);
        return map;
    }*/
}
