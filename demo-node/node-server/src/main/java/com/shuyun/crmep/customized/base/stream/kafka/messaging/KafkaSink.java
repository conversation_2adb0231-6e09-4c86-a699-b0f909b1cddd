package com.shuyun.crmep.customized.base.stream.kafka.messaging;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.stream.annotation.Input;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @Description 此类定义Kafka的接受队列
 * @date 2019/5/15
 */
@Component
@ConditionalOnExpression("${system.kafka.enabled:false}")
public interface KafkaSink {
     String KAFKA_INPUT = "kafka_input"; // 对应application.yml中的 kafka_input

     @Input(KAFKA_INPUT)
    SubscribableChannel input();


}
