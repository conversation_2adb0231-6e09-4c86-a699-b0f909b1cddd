package com.shuyun.crmep.customized.base.config;

import com.shuyun.crmep.customized.base.migration.MicServiceMigration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.Ordered;

/**
 * <AUTHOR>
 * @Description TODO
 * @date 2019/6/14
 */
@Slf4j
public class MicServiceMigrationInitializer implements ApplicationContextInitializer, Ordered {

    @Override
    public void initialize(ConfigurableApplicationContext applicationContext) {
        MicServiceMigration.executeMigration();
    }

    @Override
    public int getOrder() {
        return 12;
    }
}
