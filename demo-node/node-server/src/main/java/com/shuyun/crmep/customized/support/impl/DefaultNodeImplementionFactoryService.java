package com.shuyun.crmep.customized.support.impl;


import com.shuyun.crmep.customized.support.NodeDataHandler;
import com.shuyun.crmep.customized.support.NodeDataHandlerFactoryService;
import com.shuyun.crmep.customized.support.NodeValidator;
import com.shuyun.crmep.customized.support.NodeValidatorFactoryService;
import com.shuyun.crmep.customized.support.node.NodeStart;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by Administrator on 2015/7/3.
 */

@Component("defaultNodeImplementionFactoryService")
public class DefaultNodeImplementionFactoryService implements
        NodeDataHandlerFactoryService, NodeValidatorFactoryService {

    @Autowired
    private List<NodeDataHandler> dataHandlers;
    @Autowired
    private List<NodeValidator> validators;

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public NodeValidator getNodeValidator(String nodeType) {
        for (NodeValidator validator : validators) {
            if (validator.type() == null) {
                logger.info("NodeValidator:{},未配置nodeType。" + validator.getClass().getSimpleName());
            } else if (validator.type().equals(nodeType)) {
                return validator;
            }
        }
        return null;
    }

    @Override
    public NodeDataHandler getNodeDataHandler(String nodeType) {
        for (NodeDataHandler dataHandler : dataHandlers) {
            if (dataHandler.type() == null) {
                logger.info("getNodeDataHandler:" + dataHandler.getClass().getName());
            }
            if (!(NodeStart.TYPE.equals(dataHandler.type()))
                    && dataHandler.type().equals(nodeType)) {
                return dataHandler;
            }
        }
        return null;
    }
}
