package com.shuyun.crmep.customized.support;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by wwc on 2015/5/12.
 */
@Component
public class NodeDateHandlerFactory {
    //private Map<String, NodeDataHandler> dataHandlerMap = new ConcurrentHashMap<String, NodeDataHandler>();
    @Autowired
    private List<NodeDataHandler> allNodeDataHandler;

    public NodeDataHandler getNodeDataHandler(String nodeType) {
        for(NodeDataHandler nodeDataHandler:allNodeDataHandler){
            if(nodeDataHandler.type().equalsIgnoreCase(nodeType)){
                return nodeDataHandler;
            }
        }
        return null;
    }

}
