<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.shuyun.kylin.customized.crm</groupId>
        <artifactId>demo-node</artifactId>
        <version>1.5.0-SNAPSHOT</version>
    </parent>
    <artifactId>node-server</artifactId>
    <packaging>jar</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>

        <mainClass>com.shuyun.crmep.customized.CustomizedApplication</mainClass>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.shuyun.kylin.customized.crm</groupId>
            <artifactId>node-api</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <!--数据库操作相关-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>pip-migration-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>

        <!-- spring boot 相关jar -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-kafka</artifactId>
        </dependency>

        <!-- spring boot 相关jar -->
        <dependency>
            <groupId>com.shuyun.kylin.crm</groupId>
            <artifactId>ms-campaign-mcjaxrs</artifactId>
            <version>3.0.1-SNAPSHOT</version>
        </dependency>

        <!-- pip 相关jar -->
        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>pip-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>epassport-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>motor-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>springmvc-spring-boot-starter</artifactId>
        </dependency>
        <!-- pip 相关jar -->
        <dependency>
            <groupId>com.shuyun.kylin.crm</groupId>
            <artifactId>magic-cube-ds</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.swagger.core.v3</groupId>
                    <artifactId>swagger-annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuyun.kylin</groupId>
            <artifactId>swagger-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.kylin</groupId>
            <artifactId>error-handler-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.kylin</groupId>
            <artifactId>redis-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.kylin</groupId>
            <artifactId>signature-starter</artifactId>
        </dependency>

        <!-- csv工具 -->
        <dependency>
            <groupId>net.sf.supercsv</groupId>
            <artifactId>super-csv</artifactId>
            <version>2.3.1</version>
        </dependency>

        <dependency>
            <groupId>com.shuyun.kylin.crm.sdk</groupId>
            <artifactId>sdk-common-component</artifactId>
            <version>3.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.shuyun.ccms.ms.crmep</groupId>
            <artifactId>mini-dynamic-connection-sdk</artifactId>
            <version>1.2.1-release</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-commons</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>demo-node</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
                <executions>
                    <!-- 替换会被 maven 特别处理的 default-compile -->
                    <execution>
                        <id>default-compile</id>
                        <phase>none</phase>
                    </execution>
                    <!-- 替换会被 maven 特别处理的 default-testCompile -->
                    <execution>
                        <id>default-testCompile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>java-compile</id>
                        <phase>compile</phase>
                        <goals> <goal>compile</goal> </goals>
                    </execution>
                    <execution>
                        <id>java-test-compile</id>
                        <phase>test-compile</phase>
                        <goals> <goal>testCompile</goal> </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.2</version>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>dependency/lib/</classpathPrefix>
                            <mainClass>${mainClass}</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>2.10</version>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/dependency/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>
</project>