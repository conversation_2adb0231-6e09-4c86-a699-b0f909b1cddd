# 提示
20210422\
1. 此工程原名称为customized-node，因积分发放节点已经固定服务名，所以customized-node服务名
   让给了积分发放节点后端服务，此服务改名叫demo-node。将来fork的服务严禁使用customized-node
   以防项目上使用积分发放节点方案包导致服务冲突。
2. 项目实施人员fork此定制服务节点需要将服务名(demo-node)改为当前租户下的定制节点服务名称. 范例: qiaqia-node
3. 需要替换demo-node的文件如下
   service.yml
   .sirius.yml
   Dockerfile
   pom.xml
   register.yml


# 工程简介
 1. demo-node服务已经集成了redis, guava, 
 2. demo-node工程暂时包含base,demo两个目录
   2.1 base为基础包,此包不能删除。
   2.2 demo为演示包,定制开发时,参考此包结构来新建一个包.后续基于新建包做业务的开发
 3. 开发环境配置中心: http://************/#/home 账号:config/123456
 
 ## 开发须知:
 1. 开发环境配置中心: http://spectrum.stagecrmep.saasproj.shuyun.com  账号:dapp-plus/014E3B9B18DC4B1AA4990CBCCC9C4DA7
- 服务地址 http://{{host}}:{{port}}/demo-node/v1
- swagger开启开关: 在配置中心配置swagger2.enabled=true.
- swagger 地址 http://{{host}}:{{port}}/demo-node/v1/swagger-ui.html 
- 本地调试时，可以将注册epassport菜单功能关闭
   在启动参数配置: devMode=true    
 
 2. demo-node目录下添加如下配置:
    ribbon.com.shuyun.motor.client.sign.enable=true 
    ribbon.com.shuyun.motor.client.targetServer=${system.api.address}
    ribbon.com.shuyun.motor.client.sign.secret=${system.api.secret}
    system.datamodel.auto.discovery=false (仅用于开发环境,生产环境不需配置)
 
 3. 本地启动参数 (VM Options 或 Environment variables 二者选其一配置即可)
  - VM Options: 
    -DAPI_VERSION=v2;
    -DAPP_VERSION=1.0;
    -DENVIRONMENT=dapp-plus;
    -DSANDBOX=base;
    -DSERVICE_NAME=demo-node;
    -Dspectrum.key=dapp-plus;
    -Dspectrum.secret=014E3B9B18DC4B1AA4990CBCCC9C4DA7;
    -Dsystem.config.address=spectrum.stagecrmep.saasproj.shuyun.com;
    -DMESOS_TASK_ID=11;
    -Depassport/v1.ribbon.com.shuyun.motor.client.targetServer=api.dironman.saasproj.shuyun.com;
    -Depassport/v1.ribbon.com.shuyun.motor.client.name=demo-node;
    -Depassport/v1.ribbon.com.shuyun.motor.client.sign.secret=222sdada234g;
    -Depassport/v1.ribbon.com.shuyun.motor.client.sign.enable=true;
    -Dloyalty-manager/v1.ribbon.com.shuyun.motor.client.targetServer=api.dironman.saasproj.shuyun.com
    -Dloyalty-manager/v1.ribbon.com.shuyun.motor.client.name=demo-node
    -Dloyalty-manager/v1.ribbon.com.shuyun.motor.client.sign.secret=222sdada234g
    -Dloyalty-manager/v1.ribbon.com.shuyun.motor.client.sign.enable=true
    -Ddm.metadata.sdk.server=api.dironman.saasproj.shuyun.com;
    -Ddm.metadata.client.http.signSecret=222sdada234g
    -Dpassport.client.http.discovery.disabled=true
    -Dpassport.client.http.sign.enabled=true
    -Dpassport.client.http.serverUrl=api.dironman.saasproj.shuyun.com   
    -Dpassport.client.http.signSecret=222sdada234g
    
  - Environment variables:
     APP_VERSION=1.0.0
     MESOS_TASK_ID=123;server.connector.port=8080
     system.config.address=spectrum.stagecrmep.saasproj.shuyun.com
     system.environment=dapp-plus
     SANDBOX=base
     spectrum.key=dapp-plus
     spectrum.secret=014E3B9B18DC4B1AA4990CBCCC9C4DA7
     ENVIRONMENT=dapp-plus
     service_name=demo-node
     spectrum.disableAutoUploadProperties=true
     API_VERSION=v1
     epassport/v1.ribbon.com.shuyun.motor.client.targetServer=api.dironman.saasproj.shuyun.com
     epassport/v1.ribbon.com.shuyun.motor.client.name=demo-node
     epassport/v1.ribbon.com.shuyun.motor.client.sign.secret=222sdada234g
     epassport/v1.ribbon.com.shuyun.motor.client.sign.enable=true  
    
# Kafka使用
  默认不开启kafka.若需启用,在application.yml中将system.kafka.enabled设为true

  1. 指定接受或生产通道
     接受通道在KafkaSink中设置
     生产通道在KafkaSource中设置
  2. 开发消费者
     在KafkaConsumer中开发
  3. 开发生产者
     在KafkaProducer中开发
 