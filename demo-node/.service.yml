kind: Service
specVersion: v4
metadata:
 name: demo-node
 apiVersion: 'v1'
 accessPoint:
  container: kylin_crm/demo-node
 middleware:
    mysql:
containers:
 - name: kylin_crm/demo-node
   imagePullPolicy: PullIfNotPresent
   ports:
    - name: demo-node
      protocol: tcp
      targetPort: 0
      containerPort: 8080
profiles:
 - name: default
   cpu: 0.2
   mem: 512
   replicas: 1
   containers:
    - name: kylin_crm/demo-node
      cpu: 0.2
      mem: 512