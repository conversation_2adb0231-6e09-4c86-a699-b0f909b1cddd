<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuyun.kylin.customized.task.demo.mapper.NodeDemoMapper" >
  <resultMap id="BaseResultMap" type="com.shuyun.kylin.customized.task.demo.domain.NodeDemo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="address" property="address" jdbcType="VARCHAR" />
    <result column="mobile" property="mobile" jdbcType="VARCHAR" />
    <result column="sex" property="sex" jdbcType="VARCHAR" />
    <result column="age" property="age" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_by" property="createBy" jdbcType="BIGINT" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_by" property="updateBy" jdbcType="BIGINT" />
  </resultMap>

  <select id="getDemoByName" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select id, name, address, mobile, age, sex, create_time, create_by, update_time, update_by
    from node_demo
    where name = #{name,jdbcType=VARCHAR}
  </select>
</mapper>