server:
  servlet:
    context-path: /customized-task/v1
    port: 8080
    application-display-name: customized-task



spring:
  redis:
    host: ${redis.host}
    port: ${redis.port}
    password: ${redis.password}
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driverClassName: com.mysql.jdbc.Driver
      url: jdbc:mysql://${database.address}/${system.environment}_datamodel?useUnicode=true&characterEncoding=utf8&autoReconnect=true&allowMultiQueries=true&useSSL=false&serverTimezone=Asia/Shanghai
      username: ${database.username}
      password: ${database.password}
      initialSize: ${system.database.druid.initialSize:1}
      minIdle: ${system.database.druid.minIdle:5}
      maxActive: ${system.database.druid.maxActive:30}
      maxWait: ${system.database.druid.maxWait:60000}
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: ${system.database.druid.testWhileIdle:true}
      testOnBorrow: ${system.database.druid.testOnBorrow:false}
      testOnReturn: ${system.database.druid.testOnReturn:false}
      filter.config.enabled: ${system.database.druid.monitor:false}
      filters: stat,wall,slf4j
      connectionProperties: "druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000"
      web-stat-filter:
        enabled: ${system.database.druid.monitor:false}
        url-pattern: /*
        exclusions:
          - "*.js"
          - "*.gif"
          - "*.jpg"
          - "*.bmp"
          - "*.png"
          - "*.css"
          - "*.ico"
          - "/druid/*"
      stat-view-servlet:
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: Shuyun123
        enabled: ${system.database.druid.monitor:false}
      filter:
        stat:
          enabled: ${system.database.druid.monitor:false}
          db-type: mysql
          log-slow-sql: true
          slow-sql-millis: 2000
        wall:
          enabled: ${system.database.druid.monitor:false}
          db-type: mysql
          config.delete-allow: true
          config.drop-table-allow: false

  cloud:
    stream:
      bindings:
        kafka_input: #channelName
          destination: ${system.environment}_${coupon.node.output.topic:COUPON_NODE_OUTPUT_DATA} #CRM_COUPONS_IN  #kafka topic 消费消息
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP

        kafka_output: #channelName
          destination: ${system.environment}_${coupon.node.receive.result.topic:COUPON_NODE_RECEIVE_RESULT}  #COUPON_NODE_RECEIVE_RESULT #kafka topic
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP  #分组

        kafka_behavior: #channelName
          destination: marketing-action-custom-reach-behavior
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP
        kafka_callback: #channelName
          destination: marketing-action-task-notification-callback
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP

        kafka_youzan_coupon_sync_output: #channelName
          destination: ${system.environment}_${youzan.coupon.sync.output.topic:YOUZAN_COUPON_SYNC_DATA} #YOUZAN_COUPON_SYNC_DATA  #kafka topic 消费消息
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP

        kafka_old_bi_member_output: #channelName
          destination: KAFKA_OLD_BI_MEMBER_OUTPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP

        kafka_old_bi_return_visit_output:
          destination: KAFKA_OLD_BI_RETURN_VISIT_OUTPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP

        kafka_old_bi_member_input:
          destination: KAFKA_OLD_BI_MEMBER_INPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP

          #勋章等级进度计算_会员参与活动
        kafka_medal_progress_campaign_input:
          destination: KAFKA_MEDAL_PROGRESS_CAMPAIGN_INPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP

          #勋章等级进度计算_累计积分
        kafka_medal_progress_point_input:
          destination: KAFKA_MEDAL_PROGRESS_POINT_INPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP


          #初始化历史会员注册勋章
        KAFKA_INIT_HIS_MEMBER_REGISTER_MEDAL_INPUT:
          destination: KAFKA_INIT_HIS_MEMBER_REGISTER_MEDAL_INPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP

          #初始化历史会员注册勋章01
        KAFKA_INIT_HIS_MEMBER_REGISTER_MEDAL_INPUT_01:
          destination: KAFKA_INIT_HIS_MEMBER_REGISTER_MEDAL_INPUT_01
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP

          #初始化历史会员注册勋章02
        KAFKA_INIT_HIS_MEMBER_REGISTER_MEDAL_INPUT_02:
          destination: KAFKA_INIT_HIS_MEMBER_REGISTER_MEDAL_INPUT_02
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP


          #初始化历史会员注册勋章03
        KAFKA_INIT_HIS_MEMBER_REGISTER_MEDAL_INPUT_03:
          destination: KAFKA_INIT_HIS_MEMBER_REGISTER_MEDAL_INPUT_03
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP

          #tw历史卡券crm发放
        KAFKA_TW_HIS_INSTANCE_GRANT_INPUT:
          destination: KAFKA_TW_HIS_INSTANCE_GRANT_INPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_TW_HIS_INSTANCE_GRANT_GROUP

          #根据勋章id计算勋章获取总数
        KAFKA_MEDAL_COUNTS_INPUT:
          destination: KAFKA_MEDAL_COUNTS_INPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP

          #会员注册/更新数据
        KAFKA_COSMOS_MEMBER_INPUT:
          destination: KAFKA_COSMOS_MEMBER_INPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COSMOS_GROUP

          #微信粉丝数据
        KAFKA_COSMOS_WECHATFANS_INPUT:
          destination: KAFKA_COSMOS_WECHATFANS_INPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COSMOS_GROUP

          #微信粉丝消息，粉丝行为
        KAFKA_COSMOS_WECHATTOAEVENT_INPUT:
          destination: KAFKA_COSMOS_WECHATTOAEVENT_INPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COSMOS_GROUP

          #会员注销
        KAFKA_COSMOS_CANCEL_INPUT:
          destination: KAFKA_COSMOS_CANCEL_INPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COSMOS_GROUP

          #权益项目。小程序模版
        KAFKA_COSMOS_MASTER_INPUT:
          destination: KAFKA_COSMOS_MASTER_INPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COSMOS_GROUP

        #积分总账/等级变更
        KAFKA_COSMOS_GRADE_POINT_INPUT:
          destination: KAFKA_COSMOS_GRADE_POINT_INPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COSMOS_GROUP
          #根据勋章id计算勋章获取总数
        KAFKA_TW_HIS_PROJECT_CREATE_INPUT:
          destination: KAFKA_TW_HIS_PROJECT_CREATE_INPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_TW_HIS_PROJECT_CREATE_GROUP


          #tw历史卡券清洗到模型
        KAFKA_TW_HIS_INSTANCE_INSERT_INPUT:
          destination: KAFKA_TW_HIS_INSTANCE_INSERT_INPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_TW_HIS_INSTANCE_INSERT_GROUP



        #积分明细
        KAFKA_COSMOS_ACTIVITY_INPUT:
          destination: KAFKA_COSMOS_ACTIVITY_INPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COSMOS_GROUP

        #小程序订阅消息
        KAFKA_COSMOS_WECHATSUBSCRIPTION_INPUT:
          destination: KAFKA_COSMOS_WECHATSUBSCRIPTION_INPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COSMOS_GROUP
        #CT权益核销
        KAFKA_CT_COUPON_SYNC:
          destination: KAFKA_CT_COUPON_SYNC
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COSMOS_GROUP
        #核销计算勋章
        KAFKA_MEDAL_COUPON_INPUT:
          destination: KAFKA_MEDAL_COUPON_INPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COSMOS_GROUP

        #发放积分计算勋章进度
        KAFKA_MEDAL_PROGRESS_SEND_POINT_INPUT:
          destination: KAFKA_MEDAL_PROGRESS_SEND_POINT_INPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_MEDAL_GROUP

        #发放积分计算历史勋章进度
        KAFKA_HIS_MEDAL_PROGRESS_SEND_POINT_INPUT:
          destination: KAFKA_HIS_MEDAL_PROGRESS_SEND_POINT_INPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_HIS_MEDAL_GROUP


          #会员勋章发放
        KAFKA_INIT_MEMBER_MEDAL_OBTAIN_OUTPUT:
          destination: KAFKA_INIT_MEMBER_MEDAL_OBTAIN
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_MEDAL_OBTAIN_GROUP

        #会员勋章发放
        KAFKA_INIT_MEMBER_MEDAL_OBTAIN_INPUT:
          destination: KAFKA_INIT_MEMBER_MEDAL_OBTAIN
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_MEDAL_OBTAIN_GROUP

      kafka:
        consumer:
          #          # 手动提交必须设置为false 要不不生效
          #          enable-auto-commit: false
          group-id: CRM_COUPONS_GROUP #指定默认消费组
        #        listener:
        #            # 手动提交模式
        #          ack-mode: manual
        binder:
          brokers: ${kafka.address}
          configuration:
            auto:
              offset:
                reset: latest
          minPartitionCount: ${kafka.min.partition.count:1}
          autoAddPartitions: true
          replication-factor: 1
          consumer-properties:
            session.timeout.ms: ${kafka.session.timeout.ms:60000}
            max.poll.records: ${kafka.max.poll.records:1}
            auto.commit.interval.ms: 2000
            max.poll.interval.ms: ${kafka.max.poll.interval.ms:600000}
      default-binder: kafka
  messages:
    basename: i18n/messages
    cache-duration: 3600
    encoding: UTF-8
system:
  kafka:
    enabled: true
# 日志配置
logging:
  config: classpath:logback-spring.xml
  level:
    # The default level of all loggers. Can be OFF, ERROR, WARN, INFO, DEBUG, TRACE, or ALL.
    root: INFO
    #mybatis 访问记录
    com.shuyun.kylin.customized: INFO
    com.shuyun.motor: WARN
    com.shuyun.spectrum: WARN
    com.colin.he: ERROR
    mousio.etcd4j: WARN
    org.springframework.amqp: WARN
    com.shuyun.dm.dataapi.sdk.api: INFO
  pattern:
    console: ${system.logging.format}
    file: ${system.logging.format}

error-handler:
  module-code: "01"
  advice.enabled: true


xxl:
  job:
    admin:
      address: ${system.api.address}/xxl-job-admin/v1
    ### xxl-job executor address
    executor:
      appname: customized-task
      ip:
      port: 9999
      ### xxl-job log path
      logpath: /var/data/customized-task/log/job
      ### xxl-job log retention days
      logretentiondays: 30
    ### xxl-job, access token
    accessToken: