--// product-selector init
-- Migration SQL that makes the change goes here.


DROP TABLE IF EXISTS `node_demo`;
CREATE TABLE `node_demo` (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  name VARCHAR(500) NOT NULL COMMENT '姓名',
  address  VARCHAR(100) NOT NULL COMMENT '地址',
  mobile  VARCHAR(100) DEFAULT NULL  COMMENT '手机',
  sex  VARCHAR(10) NOT NULL COMMENT '性别',
  age  INT DEFAULT 0  COMMENT '排序',
  create_time datetime DEFAULT NULL COMMENT '创建时间',
  create_by  bigint(20) DEFAULT NULL COMMENT '创建人Id',
  update_time datetime DEFAULT NULL COMMENT '更新时间',
  update_by  bigint(20) DEFAULT NULL COMMENT '更新人id',
  PRIMARY KEY (id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;


--//@ UNDO
-- SQL to undo the change goes here.

