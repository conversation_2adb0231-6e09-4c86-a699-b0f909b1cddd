package com.shuyun.kylin.customized.task.microsoftazure;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.common.serialization.LongDeserializer;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.Properties;

/**
 * @author: Jingwei
 * @date: 2024-12-17
 */
@Slf4j
public class AzureConsumerThread {

    private final String TOPIC;

    //Each consumer needs a unique client ID per thread
    private static int id = 0;

    public AzureConsumerThread(final String TOPIC){
        this.TOPIC = TOPIC;
    }

    public void run (){
        final Consumer<Long, String> consumer = createConsumer();
        log.info("微软azure消费者-Polling");

        try {
            while (true) {
                final ConsumerRecords<Long, String> consumerRecords = consumer.poll(1000);
                for(ConsumerRecord<Long, String> cr : consumerRecords) {
                    log.info("微软azure消费者-Consumer Record:({}, {}, {}, {})\n", cr.key(), cr.value(), cr.partition(), cr.offset());
                }
                consumer.commitAsync();
            }
        } catch (CommitFailedException e) {
            log.error("微软azure消费者-CommitFailedException: ", e);
        } finally {
            consumer.close();
        }
    }

    private Consumer<Long, String> createConsumer() {
        try {
            final Properties properties = new Properties();
            synchronized (AzureConsumerThread.class) {
                properties.put(ConsumerConfig.CLIENT_ID_CONFIG, "KafkaExampleConsumer#" + id);
                id++;
            }
            properties.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, LongDeserializer.class.getName());
            properties.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());

            //Get remaining properties from config file
            InputStream resourceAsStream = this.getClass().getClassLoader().getResourceAsStream("microsoft-azure/azure-kafka.config");
            properties.load(resourceAsStream);

            // Create the consumer using properties.
            final Consumer<Long, String> consumer = new KafkaConsumer<>(properties);

            // Subscribe to the topic.
            consumer.subscribe(Collections.singletonList(TOPIC));
            return consumer;

        } catch (FileNotFoundException e){
            log.error("微软azure消费者-FileNotFoundException: ", e);
            System.exit(1);
            return null;        //unreachable
        } catch (IOException e){
            log.error("微软azure消费者-IOException: ", e);
            System.exit(1);
            return null;        //unreachable
        }
    }
}
