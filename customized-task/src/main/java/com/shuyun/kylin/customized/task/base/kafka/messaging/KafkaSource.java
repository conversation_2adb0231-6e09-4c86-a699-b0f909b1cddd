package com.shuyun.kylin.customized.task.base.kafka.messaging;

import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @Description 此类定义Kafka的发送队列
 * @date 2019/5/15
 */
@Component
@ConditionalOnExpression("${system.kafka.enabled:true}")
public interface KafkaSource {
    String KAFKA_OUTPUT = "kafka_output";
    String KAFKA_OUTPUT1 = "kafka_output1";
    String KAFKA_YOUZAN_COUPON_SYNC_OUTPUT="kafka_youzan_coupon_sync_output";

    String KAFKA_OLD_BI_MEMBER_OUTPUT = "kafka_old_bi_member_output";

    String KAFKA_OLD_BI_RETURN_VISIT_OUTPUT = "kafka_old_bi_return_visit_output";

    String KAFKA_INIT_MEMBER_MEDAL_OBTAIN = "kafka_init_member_medal_obtain_output";

    @Output(KAFKA_OUTPUT)
    MessageChannel output();
    @Output(KAFKA_OUTPUT1)
    MessageChannel output1();

//    kafka_youzan_coupon_sync_output
    @Output(KAFKA_YOUZAN_COUPON_SYNC_OUTPUT)
    MessageChannel youzanCouponSyncOutput();

    @Output(KAFKA_OLD_BI_MEMBER_OUTPUT)
    MessageChannel oldBIMemberOutput();

    @Output(KAFKA_OLD_BI_RETURN_VISIT_OUTPUT)
    MessageChannel oldBIReturnVisitOutput();


    @Output(KAFKA_INIT_MEMBER_MEDAL_OBTAIN)
    MessageChannel memberMedalObtain();
}
