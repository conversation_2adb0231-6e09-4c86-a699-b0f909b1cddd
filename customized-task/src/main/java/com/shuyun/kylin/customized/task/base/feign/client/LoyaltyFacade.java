package com.shuyun.kylin.customized.task.base.feign.client;

import com.alibaba.fastjson.JSONObject;
import com.shuyun.kylin.customized.task.medal.request.MedalObtainRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * 忠诚度内部接口
 */
@FeignClient(name = "loyaltyFacade")
@RequestMapping("loyalty-facade/v1")
public interface LoyaltyFacade {

    //计划方案信息
    @GetMapping("/plan:info")
    JSONObject getMedal(@RequestParam("planId") @NotNull Integer planId);

    //勋章发放
    @PostMapping("/medal/obtain")
    JSONObject medalObtain(@RequestBody MedalObtainRequest medalObtainRequest);
}
