package com.shuyun.kylin.customized.task.base.config;

import com.shuyun.motor.common.cons.PropsUtil;

public class ConfigurationCenterUtil {

    /**
     * 中台 Ocp-Apim-Subscription-Key
     */
    public static final String MIDDLEGROUND_KEY = PropsUtil.getSys("middleground.Subscription.key");


    /**
     * 中台 互动记录同步url
     */
    public static final String OCP_INTERACTIVE_RECORD = PropsUtil.getSys("ocp.InteractiveRecord.url");

    //忠诚度体系id
    public  final static  Integer planId = Integer.valueOf(PropsUtil.getSysOrEnv("medal.planId", "60001"));

    //勋章体系id
    public  final static  Integer medalHierarchyId = Integer.valueOf(PropsUtil.getSysOrEnv("medal.medalHierarchyId", "60001"));

    //注册勋章id
    public final static  Integer medalRegisterId = Integer.valueOf(PropsUtil.getSysOrEnv("medal.medalRegisterId", "60002"));

    //勋章等级体系id
    public final static  Integer medalGradeId = Integer.valueOf(PropsUtil.getSysOrEnv("medal.gradeId", "60002"));

    //联蔚小程序url
    public static final String wechatLwUrl = PropsUtil.getSys("lw.url");

    //联蔚小程序clientId
    public static final String wechatLwClientId = PropsUtil.getSys("lw.clientId");

    //联蔚小程序secret
    public static final String wechatLwSecret = PropsUtil.getSys("lw.secret");

    //联蔚小程序buCode
    public static final String wechatLwBuCode = PropsUtil.getSys("lw.buCode");

    //卡券实例模型
    public static final String COUPON_INSTANCE_MODEL = PropsUtil.getSysOrEnv("ko.coupon.instance.model");

}