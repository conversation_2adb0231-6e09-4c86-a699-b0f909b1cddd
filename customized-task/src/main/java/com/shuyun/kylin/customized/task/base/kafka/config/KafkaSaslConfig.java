package com.shuyun.kylin.customized.task.base.kafka.config;

import com.shuyun.kylin.customized.task.base.kafka.loader.AzureKafkaPropertiesLoader;
import com.shuyun.kylin.customized.task.base.kafka.loader.enums.AzureKafkaConfigEnum;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.Map;

/**
 * @author: Jingwei
 * @date: 2024-12-19
 */
@Configuration
public class KafkaSaslConfig {

    @Bean(name = "memberProducer")
    public ProducerFactory<String, String> memberProducerFactory() {
        AzureKafkaPropertiesLoader azureKafkaPropertiesLoader = new AzureKafkaPropertiesLoader(AzureKafkaConfigEnum.CDP_MEMBER.getCode());
        Map<String, Object> configuration = azureKafkaPropertiesLoader.getConfiguration();
        return new DefaultKafkaProducerFactory<>(configuration);
    }

    @Bean(name = "activityProducer")
    public ProducerFactory<String, String> activityProducerFactory() {
        AzureKafkaPropertiesLoader azureKafkaPropertiesLoader = new AzureKafkaPropertiesLoader(AzureKafkaConfigEnum.CDP_ACTIVITY.getCode());
        Map<String, Object> configuration = azureKafkaPropertiesLoader.getConfiguration();
        return new DefaultKafkaProducerFactory<>(configuration);
    }

    // 没有对应配置文件，暂时不加载该bean
//    @Bean(name = "orderProducer")
//    public ProducerFactory<String, String> orderProducerFactory() {
//        AzureKafkaPropertiesLoader azureKafkaPropertiesLoader = new AzureKafkaPropertiesLoader(AzureKafkaConfigEnum.CDP_ORDER.getCode());
//        Map<String, Object> configuration = azureKafkaPropertiesLoader.getConfiguration();
//        return new DefaultKafkaProducerFactory<>(configuration);
//    }

    @Bean(name = "memberDeleteProducer")
    public ProducerFactory<String, String> memberDeleteProducerFactory() {
        AzureKafkaPropertiesLoader azureKafkaPropertiesLoader = new AzureKafkaPropertiesLoader(AzureKafkaConfigEnum.CDP_MEMBER_DELETE.getCode());
        Map<String, Object> configuration = azureKafkaPropertiesLoader.getConfiguration();
        return new DefaultKafkaProducerFactory<>(configuration);
    }

    @Bean(name = "wechatoaeventProducer")
    public ProducerFactory<String, String> wechatoaeventProducerFactory() {
        AzureKafkaPropertiesLoader azureKafkaPropertiesLoader = new AzureKafkaPropertiesLoader(AzureKafkaConfigEnum.CDP_WECHATOAEVENT.getCode());
        Map<String, Object> configuration = azureKafkaPropertiesLoader.getConfiguration();
        return new DefaultKafkaProducerFactory<>(configuration);
    }

    @Bean(name = "wechatsubscriptionProducer")
    public ProducerFactory<String, String> wechatsubscriptionProducerFactory() {
        AzureKafkaPropertiesLoader azureKafkaPropertiesLoader = new AzureKafkaPropertiesLoader(AzureKafkaConfigEnum.CDP_WECHATSUBSCRIPTION.getCode());
        Map<String, Object> configuration = azureKafkaPropertiesLoader.getConfiguration();
        return new DefaultKafkaProducerFactory<>(configuration);
    }

    @Bean(name = "masterDataProducer")
    public ProducerFactory<String, String> masterDataProducerFactory() {
        AzureKafkaPropertiesLoader azureKafkaPropertiesLoader = new AzureKafkaPropertiesLoader(AzureKafkaConfigEnum.CDP_MASTERDATA.getCode());
        Map<String, Object> configuration = azureKafkaPropertiesLoader.getConfiguration();
        return new DefaultKafkaProducerFactory<>(configuration);
    }

    @Bean(name = "memberKafka")
    public KafkaTemplate<String, String> memberKafkaTemplate() {
        return new KafkaTemplate<>(memberProducerFactory());
    }

    @Bean(name = "activityKafka")
    public KafkaTemplate<String, String> activityKafkaTemplate() {
        return new KafkaTemplate<>(activityProducerFactory());
    }

//    @Bean(name = "orderKafka")
//    public KafkaTemplate<String, String> orderKafkaTemplate() {
//        return new KafkaTemplate<>(orderProducerFactory());
//    }

    @Bean(name = "memberDeleteKafka")
    public KafkaTemplate<String, String> memberDeleteKafkaTemplate() {
        return new KafkaTemplate<>(memberDeleteProducerFactory());
    }

    @Bean(name = "wechatoaeventKafka")
    public KafkaTemplate<String, String> wechatoaeventKafkaTemplate() {
        return new KafkaTemplate<>(wechatoaeventProducerFactory());
    }

    @Bean(name = "wechatsubscriptioneventKafka")
    public KafkaTemplate<String, String> wechatsubscriptioneventKafkaTemplate() {
        return new KafkaTemplate<>(wechatsubscriptionProducerFactory());
    }

    @Bean(name = "masterDataeventKafka")
    public KafkaTemplate<String, String> masterDataeventKafkaTemplate() {
        return new KafkaTemplate<>(masterDataProducerFactory());
    }
}
