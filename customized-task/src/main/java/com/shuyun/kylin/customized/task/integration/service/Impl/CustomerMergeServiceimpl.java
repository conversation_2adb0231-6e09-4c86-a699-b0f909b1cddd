package com.shuyun.kylin.customized.task.integration.service.Impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.dm.api.response.DMLResponse;
import com.shuyun.kylin.customized.task.base.feign.client.CustomerMergeServiceClient;
import com.shuyun.kylin.customized.task.base.feign.client.OpenApiFeignClient;
import com.shuyun.kylin.customized.task.base.repository.OcpMemberBindingRepository;
import com.shuyun.kylin.customized.task.base.util.DateHelper;
import com.shuyun.kylin.customized.task.integration.dto.MemberBingDto;
import com.shuyun.kylin.customized.task.integration.dto.OcpMemberBindingDto;
import com.shuyun.kylin.customized.task.integration.dto.OcpMemberDto;
import com.shuyun.kylin.customized.task.integration.service.CustomerMergeService;
import com.shuyun.kylin.customized.task.ma.util.DataApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service
public class CustomerMergeServiceimpl implements CustomerMergeService {

    @Autowired
    private CustomerMergeServiceClient customerMergeServiceClient;

    @Autowired
    private OpenApiFeignClient openApiFeignClient;

    @Autowired
    private OcpMemberBindingRepository ocpMemberBindingRepository;

    @Override
    public void updateCustomer(String fqn, String id) {
        customerMergeServiceClient.updateCustomer(fqn, id);
    }


    @Override
    public Map<String, Object> queryMemberuat(OcpMemberBindingDto ocpMemberBindingDto) {

        //指定要查询的字段(含衍生/扩展)
        List<String> optionalFields = Arrays.asList("memberSource", "memberSourceDetail");
        String memberId = ocpMemberBindingDto.getMember().get("crm_member_id").toString();

        //查询会员渠道信息
        List<MemberBingDto> memberBingDtos = openApiFeignClient.queryListChannels(memberId, "KO", optionalFields);


        OcpMemberDto memberDto = new OcpMemberDto();
        memberDto.setIs_ocp_member(ocpMemberBindingDto.getIs_ocp_member());
        memberDto.setCreated_at(getStartTimeDate(ocpMemberBindingDto.getCreated_at()));
        memberDto.setUpdated_at(getStartTimeDate(ocpMemberBindingDto.getUpdated_at()));

        log.info("查询会员渠道信息:{}",JSON.toJSONString(memberBingDtos));
        if (memberBingDtos.stream().anyMatch(m -> "SWIRE_MP".equals(m.getChannelType()))) {
            memberDto.setConsumer_source("MTYL");
            memberDto.setOrganization_name("Swire");
            memberDto.setOrganization_code("SCCL");
        }

        if (memberBingDtos.stream().anyMatch(m -> "KO_MP".equals(m.getChannelType()))) {
            memberDto.setConsumer_source(ocpMemberBindingDto.getConsumer_source());
            memberDto.setOrganization_name(ocpMemberBindingDto.getOrganization_name());
            memberDto.setOrganization_code(ocpMemberBindingDto.getOrganization_code());
        }
        if (memberBingDtos.stream().anyMatch(m -> "KO_Ali".equals(m.getChannelType()))) {
            memberDto.setConsumer_source("Ali MP");
            memberDto.setOrganization_name(ocpMemberBindingDto.getOrganization_name());
            memberDto.setOrganization_code(ocpMemberBindingDto.getOrganization_code());
        }

        HashMap<String, Object> campaignInfo = new HashMap<>();
        campaignInfo.put("info_authorized", ocpMemberBindingDto.getCampaign_info().get("info_authorized"));
        campaignInfo.put("campaign_id", ocpMemberBindingDto.getCampaign_info().get("campaign_id"));
        campaignInfo.put("lbs_authorized", ocpMemberBindingDto.getCampaign_info().get("lbs_authorized"));
        memberDto.setCampaign_info(campaignInfo);
        ArrayList<Map> arrayList = new ArrayList<>();
        ArrayList<Object> platformBind = new ArrayList<>();
        for (MemberBingDto bingDto : memberBingDtos) {
            HashMap<String, Object> bind = new HashMap<>();
            bind.put("channel", bingDto.getChannelType());
            bind.put("timestamp", getStartTimeDate(bingDto.getCreateTime()));
            bind.put("source", bingDto.getOptionalFieldData().get("memberSource"));
            if (StringUtils.isNotBlank(bingDto.getOptionalFieldData().get("memberSourceDetail"))) {
                bind.put("source_detail", bingDto.getOptionalFieldData().get("memberSourceDetail"));
            }
            bind.put("platform", "wechat");
            HashMap<String, String> map = new HashMap<>();
            map.put("app_id", bingDto.getAppId());
            if ("KO_MP".equals(bingDto.getChannelType())) {
                map.put("app_name", "可口可乐吧");
                //添加openId
                if (StringUtils.isNotBlank(bingDto.getOpenId())) {
                    HashMap<String, Object> wechatMap = new HashMap<>();
                    wechatMap.put("type", "wechat_open_id");
                    wechatMap.put("value", bingDto.getOpenId());
                    wechatMap.put("platform", "wechat");
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("is_koplus_openid", ocpMemberBindingDto.getAttribute().get("is_koplus_openid"));
                    hashMap.put("app_group_name", "可口可乐");
                    wechatMap.put("attribute", hashMap);
                    arrayList.add(wechatMap);
                }
                //添加unionId
                if (StringUtils.isNotBlank(bingDto.getUnionId())) {
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_group_name", "可口可乐");
                    HashMap<String, Object> tmallMap = new HashMap<>();
                    tmallMap.put("platform", "wechat");
                    tmallMap.put("type", "wechat_union_id");
                    tmallMap.put("value", bingDto.getUnionId());
                    tmallMap.put("attribute", hashMap);
                    arrayList.add(tmallMap);
                }
            }
            if ("SWIRE_MP".equals(bingDto.getChannelType())) {
                map.put("app_name", "每天有乐");
                //添加openId
                if (StringUtils.isNotBlank(bingDto.getOpenId())) {
                    HashMap<String, Object> wechatMap = new HashMap<>();
                    wechatMap.put("type", "wechat_open_id");
                    wechatMap.put("value", bingDto.getOpenId());
                    wechatMap.put("platform", "wechat");
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_name", "每天有乐");
                    hashMap.put("app_id", bingDto.getAppId());
                    hashMap.put("app_group_name", "太古可口可乐");
                    wechatMap.put("attribute", hashMap);
                    arrayList.add(wechatMap);
                }
                //添加unionId
                if (StringUtils.isNotBlank(bingDto.getUnionId())) {
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_group_name", "太古可口可乐");
                    HashMap<String, Object> tmallMap = new HashMap<>();
                    tmallMap.put("platform", "wechat");
                    tmallMap.put("type", "wechat_union_id");
                    tmallMap.put("value", bingDto.getUnionId());
                    tmallMap.put("attribute", hashMap);
                    arrayList.add(tmallMap);
                }
            }
            if ("KO_Ali".equals(bingDto.getChannelType())) {
                map.put("app_name", "支付宝可口可乐吧");
                bind.put("platform", "alibaba");
                if (StringUtils.isNotBlank(bingDto.getOpenId())) {
                    HashMap<String, Object> wechatMap = new HashMap<>();
                    wechatMap.put("type", "alipay");
                    wechatMap.put("value", bingDto.getOpenId());
                    wechatMap.put("platform", "alibaba");
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_name", "支付宝可口可乐吧");
                    hashMap.put("app_id", bingDto.getAppId());
                    wechatMap.put("attribute", hashMap);
                    arrayList.add(wechatMap);
                }
            }

            bind.put("attribute", map);
            platformBind.add(bind);
        }

        //添加memberId
        HashMap<String, Object> memberCrm = new HashMap<>();
        memberCrm.put("platform", ocpMemberBindingDto.getCrm_member().get("platform"));
        memberCrm.put("type", ocpMemberBindingDto.getCrm_member().get("type"));
        memberCrm.put("value", ocpMemberBindingDto.getCrm_member().get("value"));
        //memberCrm.put("attribute", Collections.EMPTY_MAP);
        arrayList.add(memberCrm);

        //添加手机号
        HashMap<String, Object> memberMobile = new HashMap<>();
        memberMobile.put("platform", "phone");
        memberMobile.put("type", "phone_number");
        memberMobile.put("value", ocpMemberBindingDto.getMember().get("mobile"));
        memberMobile.put("attribute", Collections.EMPTY_MAP);
        arrayList.add(memberMobile);
        memberDto.setPlatform_info(arrayList);
        memberDto.setDemographic(ocpMemberBindingDto.getDemographic());

        HashMap<String, Object> equity = new HashMap<>();
        if (null != ocpMemberBindingDto.getMember().get("first_bottle_time")) {
            equity.put("first_bottle_time", copStartTimeDate(JSON.toJSONString(ocpMemberBindingDto.getMember().get("first_bottle_time"))));
            equity.put("available_bottle", ocpMemberBindingDto.getMember().get("available_bottle"));
            equity.put("total_bottle", ocpMemberBindingDto.getMember().get("total_bottle"));
            equity.put("used_bottle", ocpMemberBindingDto.getMember().get("used_bottle"));
            equity.put("expired_bottle", ocpMemberBindingDto.getMember().get("expired_bottle"));
        }
        HashMap<String, Object> member = new HashMap<>();
        member.put("member_level", ocpMemberBindingDto.getMember().get("member_level"));
        member.put("register_source_detail", ocpMemberBindingDto.getMember().get("register_source_detail"));
        if (null != ocpMemberBindingDto.getMember().get("logoff_time")) {
            member.put("logoff_time", copStartTimeDate(JSON.toJSONString(ocpMemberBindingDto.getMember().get("logoff_time"))));
        }
        if (null != ocpMemberBindingDto.getMember().get("register_time")) {
            member.put("register_time", copStartTimeDate(JSON.toJSONString(ocpMemberBindingDto.getMember().get("register_time"))));
        }
        member.put("is_invite_register", ocpMemberBindingDto.getMember().get("is_invite_register"));
        member.put("is_logoff", ocpMemberBindingDto.getMember().get("is_logoff"));
        member.put("experience", ocpMemberBindingDto.getMember().get("experience"));
        member.put("member_type", ocpMemberBindingDto.getMember().get("member_type"));
        member.put("register_channel", ocpMemberBindingDto.getMember().get("register_channel"));
        member.put("crm_member_id", ocpMemberBindingDto.getMember().get("crm_member_id"));
        member.put("register_address", ocpMemberBindingDto.getMember().get("register_address"));
        member.put("register_source", ocpMemberBindingDto.getMember().get("register_source"));
        member.put("member_name", ocpMemberBindingDto.getMember().get("member_name"));
        if (null != equity) {
            member.put("equity", equity);
        }

        member.put("platform_bind", platformBind);
        memberDto.setMember(member);
         Map<String, Object> paramsMap = new HashMap<String,Object>();

        try {
            paramsMap.put("member", JSON.toJSONString(memberDto));
            //paramsMap.put("lastSync", DateHelper.getNowZone());
            log.info("封装对象:{}",JSON.toJSONString(paramsMap));
            int i = ocpMemberBindingRepository.upsert("data.prctvmkt.KO.CosmosSupplementaryMmember",memberId,paramsMap);
            return responseType(ocpMemberBindingDto.getCrm_member().get("value").toString(), true, JSON.toJSONString(memberDto));
        } catch (Exception e) {
            log.error("补推会员数据给cosmos:{}", e.getMessage());
            return responseType(ocpMemberBindingDto.getCrm_member().get("value").toString(), true, JSON.toJSONString(memberDto));
        }
    }

    public static String getStartTimeDate(String time){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeStrart = time.substring(0,19).replace("T"," ");
        String timeDate = "";
        try {
            Date dt=sdf.parse(timeStrart);
            Calendar rightNow = Calendar.getInstance();
            rightNow.setTime(dt);
            rightNow.add(Calendar.HOUR,8);
            Date nowTime = rightNow.getTime();
            timeDate = sdf.format(nowTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return timeDate;
    }

    private Map<String, Object> responseType(String memberId, Boolean state, String msg) {
        Map<String, Object> res = new HashMap<>();
        res.put("memberId", memberId);
        res.put("state", state);
        res.put("msg", msg);
        return res;
    }

    public static String copStartTimeDate(String time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeDate = time.substring(1, 20).replace("T", " ");

        return timeDate;
    }
}
