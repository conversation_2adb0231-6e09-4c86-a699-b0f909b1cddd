package com.shuyun.kylin.customized.task.base.feign.client;

import com.shuyun.kylin.customized.task.base.dto.ClientCommonResponseDto;
import com.shuyun.kylin.customized.task.base.dto.OfferInstanceUseClientRequestDto;
import com.shuyun.kylin.customized.task.base.dto.OfferInstanceUseClientResponseDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(name = "openApiFeignClientV3")
@RequestMapping("openapi/v3")
public interface OpenApiFeignClientV3 {

    /**
     * 核销
     */
    @PostMapping("/offer/instance/use")
    ClientCommonResponseDto<OfferInstanceUseClientResponseDto> instanceUse(OfferInstanceUseClientRequestDto offerInstanceUseClientRequestDto);
}