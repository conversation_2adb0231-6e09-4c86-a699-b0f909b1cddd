package com.shuyun.kylin.customized.task.base.dm;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.task.base.common.ModelConstants;
import com.shuyun.kylin.customized.task.base.util.DateHelper;
import com.shuyun.kylin.customized.task.coupon.dto.CouponExpireRemindRecordDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Component
public class CouponExpireRemindRecordRepository extends BaseDsRepository<CouponExpireRemindRecordDto> {

    /**
     * 清除昨天数据
     */
    public void deleteCouponById() {

        String queryMemberSql = "DELETE FROM " + ModelConstants.MEMBER_COUPONREMIND + " ";
        execute(queryMemberSql, Collections.EMPTY_MAP).getData();
    }

    /**
     * 查询记录
     */
    public List<CouponExpireRemindRecordDto> getCouponExpireRemind(String index, int pageSize) {

        String change = "and id > '" + index + "' order by id limit " + pageSize;

        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        calendar.add(GregorianCalendar.DATE, 5);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String format = sdf.format(calendar.getTime());
        String a = format + " 00:00:00";
        String b = format + " 23:59:59";

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("status","RECEIVED");
        queryMap.put("grantTime", DateHelper.getZone(a));
        queryMap.put("expireTime", DateHelper.getZone(b));

        String queryOrderSql = " SELECT id,couponCode,project as projectId,expireTime as expiredDate,memberIDStr as memberId " +
                "  FROM  " + ModelConstants.MEMBER_CONPON + " where status = :status and expireTime >= :grantTime and expireTime <= :expireTime " + change + "  " ;
        log.info("查询优惠券记录,开始时间:{},，结束时间:{},:{}",queryOrderSql, JSON.toJSON(DateHelper.getZone(a)), JSON.toJSON(DateHelper.getZone(b)));
        List<CouponExpireRemindRecordDto> couponList = executeSQL(queryOrderSql, queryMap);
        return couponList;
    }

    /**
     * 查询优惠券名称
     * @param projectId
     */
    public String getCouponName(String projectId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("projectId",projectId);
        String queryOrderSql = " select projectName from " + ModelConstants.MEMBER_PROJECT + " where  projectId = :projectId ";
        List<Map<String,Object>> list = execute(queryOrderSql, queryMap).getData();
        String projectName = null;
        for (Map map : list) {
            projectName = map.get("projectName").toString();
        }
        return projectName;
    }
}
