package com.shuyun.kylin.customized.task.base.kafka.config;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 *
 * @author: Jingwei
 * @date: 2024-12-19
 */
@Data
public class AzureKafkaProperties {
    @J<PERSON><PERSON>ield(name = "bootstrap.servers")
    private String bootstrapServers;
    @JSONField(name = "request.timeout.ms")
    private String timeout;
    @JSONField(name = "security.protocol")
    private String protocol;
    @JSONField(name = "sasl.mechanism")
    private String mechanism;
    @JSONField(name = "sasl.jaas.config")
    private String jaasConfig;
}
