package com.shuyun.kylin.customized.task.integration.dto;

import lombok.Data;

import java.util.Map;

@Data
public class OcpMemberBindingDto {

    private String id;
    private String consumer_source;
    private String created_at;
    private String updated_at;
    private String organization_name;
    private String organization_code;
    private Boolean is_ocp_member;
    private String topic;
    private String type;
    private String fqn;

    private Map campaign_info;
    private Map demographic;
    private Map member;
    private Map crm_member;
    private Map wechat_open;
    private Map attribute;
    private Map tmall_ouid;
    private Map dataSource;

}
