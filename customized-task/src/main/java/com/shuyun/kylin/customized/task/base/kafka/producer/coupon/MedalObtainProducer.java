package com.shuyun.kylin.customized.task.base.kafka.producer.coupon;


import com.shuyun.kylin.customized.task.base.kafka.dto.CallbackDto;
import com.shuyun.kylin.customized.task.base.kafka.messaging.KafkaCallback;
import com.shuyun.kylin.customized.task.base.kafka.messaging.KafkaSink;
import com.shuyun.kylin.customized.task.base.kafka.messaging.KafkaSource;
import com.shuyun.kylin.customized.task.medal.request.MedalObtainRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@EnableBinding({KafkaSource.class, KafkaSink.class})
@ConditionalOnExpression("${system.kafka.enabled:true}")
public class MedalObtainProducer {

    @Autowired
    private KafkaSource kafkaSource;

    public void sendMsg(MedalObtainRequest medalObtainRequest){
        kafkaSource.memberMedalObtain().send(MessageBuilder.withPayload(medalObtainRequest).build());
    }
}
