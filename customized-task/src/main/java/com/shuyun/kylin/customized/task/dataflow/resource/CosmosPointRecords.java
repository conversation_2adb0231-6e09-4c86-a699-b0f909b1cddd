package com.shuyun.kylin.customized.task.dataflow.resource;


import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.dm.api.response.DMLResponse;
import com.shuyun.kylin.customized.task.base.config.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.task.base.feign.client.OpenApiFeignClient;
import com.shuyun.kylin.customized.task.base.common.ModelConstants;
import com.shuyun.kylin.customized.task.dataflow.dto.CtSyncRetryDao;
import com.shuyun.kylin.customized.task.dataflow.service.CtConsumerService;
import com.shuyun.kylin.customized.task.integration.dto.MemberBingDto;
import com.shuyun.kylin.customized.task.integration.dto.OcpMemberBindingDto;
import com.shuyun.kylin.customized.task.integration.dto.OcpMemberDto;
import com.shuyun.kylin.customized.task.ma.util.DataApiUtil;
import com.shuyun.kylin.customized.task.ma.util.DataCofig;
import com.shuyun.kylin.customized.task.ma.util.HttpClientHelper;
import com.shuyun.kylin.customized.task.microsoftazure.AzureTestController;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.*;
import org.apache.kafka.common.serialization.LongSerializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


import java.io.FileReader;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


@Slf4j
@RestController
@RequestMapping("/dataflow/cosmos")
public class CosmosPointRecords {

    @Autowired
    @Qualifier("threadCosmosPointRecords")
    private ThreadPoolTaskExecutor cosmosPointRecordsTaskExecutor;

    @Autowired
    private OpenApiFeignClient openApiFeignClient;

    @Autowired
    @Qualifier("memberDeleteKafka")
    private KafkaTemplate<String, String> memberDeleteKafkaTemplate;

    @Autowired
    @Qualifier("wechatoaeventKafka")
    private KafkaTemplate<String, String> wechatoaeventKafkaTemplate;

    @Autowired
    @Qualifier("memberKafka")
    private KafkaTemplate<String, String> memberKafkaTemplate;

    @Autowired
    @Qualifier("masterDataeventKafka")
    private KafkaTemplate<String, String> masterDataKafkaTemplate;

    @Autowired
    private CtConsumerService ctConsumerService;

    /**
     *
     * cosmos会员注销/用户注销同步
     *
     * @param request
     * @return
     */
    @PostMapping("/kafka/Delete/member")
    public Map<String, Object> syncDeleteMember(@RequestBody Map<String, Object> request) {
        log.info(request.get("topic")+"_同步cosmos请求入参:{}", JSON.toJSONString(request.get("requestBody")));
        ConcurrentHashMap<String, Object> res = new ConcurrentHashMap<>();
        memberDeleteKafkaTemplate.send(request.get("topic").toString(),JSON.toJSONString(request.get("requestBody"))).addCallback(success -> {
            log.info("发送成功:{}", request.get("id").toString());
            res.put("msg","成功");
        }, failure -> {
            res.put("msg","失败");
            log.error("发送失败:{}", request.get("id").toString());
        });
        res.put("code","200");
        res.put("topic",request.get("topic").toString());
        res.put("id",request.get("id").toString());
        return res;
    }

    /**
     *
     * cosmos 微信行为/微信消息
     *
     * @param request
     * @return
     */
    @PostMapping("/kafka/wechatoaeventKafka")
    public Map<String, Object> syncWechatOaevent(@RequestBody Map<String, Object> request) {
        log.info(request.get("topic")+"_同步cosmos请求入参:{}", JSON.toJSONString(request.get("requestBody")));
        ConcurrentHashMap<String, Object> res = new ConcurrentHashMap<>();
        wechatoaeventKafkaTemplate.send(request.get("topic").toString(),JSON.toJSONString(request.get("requestBody"))).addCallback(success -> {
            log.info("发送成功:{}", request.get("id").toString());
            res.put("msg","成功");
        }, failure -> {
            res.put("msg","失败");
            log.error("发送失败:{}", request.get("id").toString());
        });
        res.put("code","200");
        res.put("topic",request.get("topic").toString());
        res.put("id",request.get("id").toString());
        return res;
    }


    /**
     *
     * cosmos 微信粉丝
     *
     * @param request
     * @return
     */
    @PostMapping("/kafka/wechatFans")
    public Map<String, Object> syncWechatFansOaevent(@RequestBody Map<String, Object> request) {
        log.info(request.get("topic")+"_同步cosmos请求入参:{}", JSON.toJSONString(request.get("requestBody")));
        ConcurrentHashMap<String, Object> res = new ConcurrentHashMap<>();
        memberKafkaTemplate.send(request.get("topic").toString(),JSON.toJSONString(request.get("requestBody"))).addCallback(success -> {
            log.info("发送成功:{}", request.get("id").toString());
            res.put("msg","成功");
        }, failure -> {
            res.put("msg","失败");
            log.error("发送失败:{}", request.get("id").toString());
        });
        res.put("code","200");
        res.put("topic",request.get("topic").toString());
        res.put("id",request.get("id").toString());
        return res;
    }

    /**
     *
     * cosmos 权益主数据/小程序订阅模版
     *
     * @param request
     * @return
     */
    @PostMapping("/kafka/master")
    public Map<String, Object> syncMasterOaevent(@RequestBody Map<String, Object> request) {
        log.info(request.get("topic")+"_同步cosmos请求入参:{}", JSON.toJSONString(request));
        HashMap<Object, Object> map = new HashMap<>();
        map.put("data_type",request.get("dataType"));
        map.put("data_content",JSON.toJSONString(request.get("dataContent")));
        map.put("data_source",request.get("dataSource"));
        log.info("请求权益主数据/小程序订阅模版数据封装:{}",JSON.toJSONString(map));
        ConcurrentHashMap<String, Object> res = new ConcurrentHashMap<>();
        masterDataKafkaTemplate.send(request.get("topic").toString(),JSON.toJSONString(map)).addCallback(success -> {
            log.info("发送成功:{}", request.get("id").toString());
            res.put("msg","成功");
        }, failure -> {
            res.put("msg","失败");
            log.error("发送失败:{}", request.get("id").toString());
        });
        res.put("code","200");
        res.put("topic",request.get("topic").toString());
        res.put("id",request.get("id").toString());
        return res;
    }


    @PostMapping("/point")
    public Map<String, Object> syncPointRecords(@RequestBody Map<String, Object> request) {
        ConcurrentHashMap<String, Object> res = new ConcurrentHashMap<>();
        cosmosPointRecordsTaskExecutor.execute(() ->{
            try{
                HashMap<String, String> header = new HashMap<>();
                header.put("CampaignId", "40000000");
                header.put("Content-Type", "application/json");
                header.put("Ocp-Apim-Subscription-Key", ConfigurationCenterUtil.MIDDLEGROUND_KEY);
                log.info("同步中台积分明细请求入参:{},header:{}",JSON.toJSONString(request.get("requestBody")),JSON.toJSONString(header));
                String body = HttpClientHelper.doPostJSON(ConfigurationCenterUtil.OCP_INTERACTIVE_RECORD, request.get("requestBody"), header);
                log.info("同步中台积分明细结果:{}",request.get("traceId").toString());
                //记录失败id
                if (StringUtils.isNotBlank(body)){
                    failpointRecord(request.get("traceId").toString());
                }
            }catch (Exception e) {
                failpointRecord(request.get("traceId").toString());
                log.info("同步中台积分明细失败结果:{},:{}",e.getMessage(), JSON.toJSONString(request));
            }
        });
         res.put("code","200");
         res.put("msg","成功");
        return res;
    }

    private void failpointRecord(String id){
        HashMap<String, Object> map = new HashMap<>();
        map.put("state","0");
        map.put("lastSync", ZonedDateTime.now());
        DMLResponse response = DataApiUtil.upsertIsData(DataCofig.COSMOS_POINTRECORD, id,map);
        if (!response.getIsSuccess()) {
            log.error("同步中台积分明细记录保存保存失败id:{}",id);
        }
    }

    @PostMapping("/kafka/producer")
    public Map<String, Object> syncKafkaPointRecords(@RequestBody Map<String, Object> request) {
        return doCosmosProducer(request.get("topic").toString(),request.get("id").toString(),request.get("requestBody"));
    }

    @PostMapping("/ctSync/retry")
    public Map<String, Object> ctSyncRetry(@RequestBody CtSyncRetryDao ctSyncRetryDao) {
        log.info("ct同步失败重试:{}",JSON.toJSONString(ctSyncRetryDao));
        Map<String, Object> map = ctConsumerService.doCtSyncRetry(ctSyncRetryDao);
        log.info("ct同步失败重试返回:{}",JSON.toJSONString(map));
        return map;
    }


    private static Map<String, Object> doCosmosProducer(String topic,String id,Object request){
        String configurePaths = "microsoft-azure/"+topic+".config";
        HashMap<String, Object> res = new HashMap<>();
        log.info(topic+"_同步cosmos请求入参:{}", JSON.toJSONString(request));
        final Producer<String, String> producer = createProducer(configurePaths);
        ProducerRecord<String, String> record = new ProducerRecord<>(topic, JSON.toJSONString(request));
        res.put("msg","成功");
        try {
            producer.send(record, (metadata, exception) -> {
                if (exception != null) {
                    exception.printStackTrace();
                    log.info(topic+"_azure生产者发送消息异常Message:{}",exception.getMessage());
                    res.put("msg","失败");
                }
            });
        } catch (Exception e) {
            log.info(topic+"azure生产者发送消息异常:{},:{}", e.getMessage(), JSON.toJSONString(request));
            res.put("msg","失败");
        }
        log.info(topic+"_同步cosmos请求入参:{}", JSON.toJSONString(request));
        res.put("code","200");
        res.put("topic",topic);
        res.put("id",id);
        return res;
    }


    private static Producer<String, String> createProducer(String configurePaths) {
        try{
            Properties properties = new Properties();
            InputStream resourceAsStream = AzureTestController.class.getClassLoader().getResourceAsStream(configurePaths);
            properties.load(resourceAsStream);
            //properties.put(ProducerConfig.CLIENT_ID_CONFIG, "CRM-COSMOS");
            properties.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, LongSerializer.class.getName());
            properties.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
            return new KafkaProducer<>(properties);
        } catch (Exception e){
            log.info("Failed to create producer with exception:{}",e.getMessage());
            System.exit(0);
            return null;        //unreachable
        }
    }

    public static String getStartTimeDate(String time){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeStrart = time.substring(0,19).replace("T"," ");
        String timeDate = "";
        try {
            Date dt=sdf.parse(timeStrart);
            Calendar rightNow = Calendar.getInstance();
            rightNow.setTime(dt);
            rightNow.add(Calendar.HOUR,8);
            Date nowTime = rightNow.getTime();
            timeDate = sdf.format(nowTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return timeDate;
    }
    public static String copStartTimeDate(String time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeDate = time.substring(1, 20).replace("T", " ");

        return timeDate;
    }
}
