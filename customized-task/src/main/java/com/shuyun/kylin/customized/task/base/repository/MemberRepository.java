package com.shuyun.kylin.customized.task.base.repository;


import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.kylin.crm.openapi.core.dto.common.MemberDto;
import com.shuyun.kylin.crm.openapi.core.dto.common.UnbindMemberDto;
import com.shuyun.kylin.customized.task.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.task.ma.util.DataCofig;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.ParseException;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class MemberRepository extends BaseDsRepository<MemberDto> {


    /**
     * 查询会员id
     */
    public String queryMemberId(String customerNo) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("customerNo", customerNo);
        String queryMemberSql = " select memberId from " + DataCofig.MEMBER_MEMBERBINDING + " where  customerNo = :customerNo GROUP BY memberId ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        String memberId = null;
        for (Map map : list) {
            memberId = map.get("memberId").toString();
        }
        return memberId;
    }

    /**
     * 查询会员customerNo
     */
    public String queryCustomerNo(String memberId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        String queryMemberSql = " select customerNo from " + DataCofig.MEMBER_MEMBERBINDING + " where  memberId = :memberId LIMIT 1 ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        String customerNo = null;
        for (Map map : list) {
            customerNo = map.get("customerNo").toString();
        }
        return customerNo;
    }

    public List<Map<String, String>> queryCustomerNoList(String memberId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        String queryMemberSql = " select customerNo,channelType from " + DataCofig.MEMBER_MEMBERBINDING + " where  memberId = :memberId ";
        List<Map<String, String>> list = execute(queryMemberSql, queryMap).getData();
        return list;
    }

    /**
     * 查询会员customerNo,memberId
     */
    public Map<String, String> getCustomerNo(String unionId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("unionId", unionId);
        String queryMemberSql = " select customerNo,memberId from " + DataCofig.MEMBER_MEMBERBINDING + " where unionId = :unionId and relType != '2' LIMIT 1";
        List<Map<String, String>> list = execute(queryMemberSql, queryMap).getData();
        String customerNo = null;
        String memberId = null;
        for (Map map : list) {
            customerNo = map.get("customerNo").toString();
            memberId = map.get("memberId").toString();
        }
        Map<String, String> hashMap = new HashMap<>();
        hashMap.put("customerNo", customerNo);
        hashMap.put("memberId", memberId);
        return hashMap;
    }


    /**
     * 查询会员customerNo
     */
    public String queryMember(String customerNo,String mobile) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("customerNo", customerNo);
        queryMap.put("mobile", mobile);
        String queryMemberSql = " select memberId from " + DataCofig.MEMBER_MEMBERBINDING + " where  customerNo = :customerNo and mobile = :mobile and relType !=2 ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        String memberId = null;
        for (Map map : list) {
            memberId = map.get("memberId").toString();
        }
        return memberId;
    }


    /**
     * 查询会员customerNo
     */
    public String queryCustomerNoUnioId(String unionId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("unionId", unionId);
        String queryMemberSql = " select memberId from " + DataCofig.MEMBER + " where  unionId = :unionId ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        String customerNo = null;
        for (Map map : list) {
            customerNo = map.get("memberId").toString();
        }
        return customerNo;
    }




    /**
     * 查询会员openId
     */
    public String queryOpenId(String unionId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("unionId", unionId);
        String queryMemberSql = " select openId from " + DataCofig.MEMBER_MEMBERBINDING + " where  unionId = :unionId and relType != '2'  LIMIT 1 ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        String openId = null;
        for (Map map : list) {
            openId = map.get("openId").toString();
        }
        return openId;
    }


    /**
     * 查询会员openId
     */
    public String queryMemberOpneId(String memberId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        queryMap.put("channelType", "KO_MP");
        String queryMemberSql = " select openId from " + DataCofig.MEMBER_MEMBERBINDING + " where  memberId = :memberId and channelType = :channelType ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        String openId = null;
        for (Map map : list) {
            openId = map.get("openId").toString();
        }
        return openId;
    }

    /**
     * 查询会员信息
     */
    public Map<String, String> queryMemberInfo(String memberId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        queryMap.put("channelType", "KO_MP");
        String queryMemberSql = " select openId,unionId,mobile from " + DataCofig.MEMBER_MEMBERBINDING + " where  memberId = :memberId and channelType = :channelType ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        Map<String, String> memberInfo = new HashMap<>();
        for (Map map : list) {
            memberInfo.put("openId",map.get("openId").toString()) ;
            memberInfo.put("unionId",map.get("unionId").toString()) ;
            memberInfo.put("mobile",map.get("mobile").toString()) ;
        }
        return memberInfo;
    }



    /**
     * 查询会员koid
     */
    public String queryKoidByOpenId(String openId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("openId", openId);
        String querySql = " select koId  from " + DataCofig.COSMOS_CONSUMER + " where openId = :openId ";
        List<Map<String, Object>> list = execute(querySql, queryMap).getData();
        String koid = null;
        for (Map map : list) {
            koid = map.get("koId") == null ? null : map.get("koId").toString();
        }
        return koid;
    }


    /**
     * 查询会员openId
     */
    public Map queryOpneIdByKoid(String koId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("koId", koId);
        String queryMemberSql = " select openId  from " + DataCofig.COSMOS_CONSUMER + " where koId = :koId ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        Map<String, String> returnMap = new HashMap<>();
        for (Map map : list) {
            returnMap.put("openId", map.get("openId").toString());
        }
        return returnMap;
    }

}
