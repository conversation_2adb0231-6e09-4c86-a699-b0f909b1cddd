package com.shuyun.kylin.customized.task.microsoftazure;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 微软azure测试接口
 *
 * @author: Jingwei
 * @date: 2024-12-17
 */
@Slf4j
@RestController
@RequestMapping("/azureKafkaTemplate")
public class AzureKafkaTemplateController {

    @Autowired
    @Qualifier("memberKafka")
    private KafkaTemplate<String, String> kafkaTemplate;

    @PostMapping("/testAzKafkaProduce")
    public String testAzKafkaProduce(@RequestParam("topic") String topic) {
        log.info("Azure开始发送kafka消息，topic={}", topic);
        kafkaTemplate.send(topic, "这是一条测试消息-".concat(UUID.randomUUID().toString())).addCallback(success -> {
            log.info("发送成功，success={}", JSON.toJSONString(success));
        }, failure -> {
            log.error("发送失败", failure);
        });
        return "发送结束";
    }
}
