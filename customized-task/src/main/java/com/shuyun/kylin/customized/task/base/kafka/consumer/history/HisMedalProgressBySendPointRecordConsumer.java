package com.shuyun.kylin.customized.task.base.kafka.consumer.history;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.task.base.kafka.messaging.KafkaSink;
import com.shuyun.kylin.customized.task.medal.request.MedalProgressRequest;
import com.shuyun.kylin.customized.task.medal.service.MedalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

//积分发放勋章进度计算
@Slf4j
@Component
public class HisMedalProgressBySendPointRecordConsumer {

    @Autowired
    private MedalService medalService;

    @StreamListener(KafkaSink.KAFKA_HIS_MEDAL_PROGRESS_SEND_POINT_INPUT)
    public void hisMedalProgressBySendPointRecordConsumer(MedalProgressRequest medalProgressRequest) {
        try {
            log.info("接收到历史积分发放勋章进度计算kafka数据流入参:{}", JSON.toJSONString(medalProgressRequest));
            medalService.medalProgressBySendPointRecordConsumer(medalProgressRequest);
        } catch (Exception e) {
            log.error("接收到历史积分发放勋章进度计算kafka失败:{}", JSON.toJSONString(medalProgressRequest),e);
        }
    }
}
