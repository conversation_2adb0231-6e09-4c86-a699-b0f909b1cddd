package com.shuyun.kylin.customized.task.integration.resource;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.task.integration.dto.OcpMemberBindingDto;
import com.shuyun.kylin.customized.task.integration.service.CustomerMergeService;
import com.shuyun.kylin.customized.task.ma.util.HttpClientHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/cosmos")
public class CosmosDataPushResource {

    @Autowired()
    private CustomerMergeService customerMergeService;

    @PostMapping("/data")
    public Map<String, Object> dataPush(@RequestBody Map<String, Object> request) {
        log.info("cosmos data push request: {}", JSON.toJSONString(request));
        Map<String, Object> requestLog = new HashMap<>();
        try {
            String requestUrl = (String)request.get("requestUrl");
            log.info("cosmos data push request url: {}",requestUrl);

            Map<String, String> requestHeader = (Map)request.get("requestHeader");
            log.info("cosmos data push request header: {}",JSON.toJSONString(requestHeader));

            Map<String, Object> requestBody = (Map)request.get("requestBody");
            log.info("cosmos data push request body: {}",JSON.toJSONString(requestBody));

            List<String> requestLogExtFields = (List)request.get("requestLogExtFields");
            log.info("cosmos data push requestLogExtFields: {}",JSON.toJSONString(requestLogExtFields));

            requestLog.put("requestUrl",requestUrl);
            requestLog.put("requestHeaderJsonStr",JSON.toJSONString(requestHeader));
            requestLog.put("requestBodyJsonStr",JSON.toJSONString(requestBody));
            if(requestLogExtFields!=null && requestLogExtFields.size()>0){
                for (String f : requestLogExtFields) {
                    requestLog.put(f,request.get(f));
                }
            }

            String responseBody = HttpClientHelper.doPostJSON(requestUrl, requestBody, requestHeader);
            log.info("cosmos data push response: {}",responseBody);
            requestLog.put("responseBodyJsonStr",responseBody);
        } catch (Exception e) {
            log.error("同步中台接口异常:{}", e);
        }
        return requestLog;
    }

    @PostMapping("/memberuat")
    public Map<String, Object> queryMemberuat(@Validated @RequestBody OcpMemberBindingDto ocpMemberBindingDto){
        log.info("中台会员数据同步请求入参: {}", JSON.toJSONString(ocpMemberBindingDto));
        return customerMergeService.queryMemberuat(ocpMemberBindingDto);
    }
}