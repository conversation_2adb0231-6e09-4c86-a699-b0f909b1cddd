package com.shuyun.kylin.customized.task.medal.request;

import lombok.Data;

import java.util.ArrayList;

@Data
public class MedalProgressRequest {

    //会员id
    private String memberId;

    //活动id
    private String campaignId;

    //勋章id
    private Integer medalDefinitionId;

    //设置的开始时间
    private String created;


    //体系id
    private Integer medalHierarchyId;


    //渠道
    private String  channelType;

    //行为id
    private String  activityId;

    //核销券项目
    private ArrayList<String> projectId;


    //积分触发动作
    private String  recordType;


    //积分描述
    private String  desc;

    //是否内部发放勋章
    private String  isMedalObtain;
}
