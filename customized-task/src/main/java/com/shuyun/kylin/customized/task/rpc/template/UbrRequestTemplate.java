package com.shuyun.kylin.customized.task.rpc.template;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.kylin.customized.task.rpc.dto.UbrQueryCouponResponseDto;
import com.shuyun.kylin.starter.redis.ICache;
import com.shuyun.motor.common.cons.PropsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHeaders;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * urb请求模板
 * <pre>
 * ubr.token_url=https://apiq.eubrmb.com/getTokengetToken
 * ubr.get_coupon_url=https://apiq.eubrmb.com/eco/qa/v1.0/coupons/instance/%s
 * ubr.client_id=p0028
 * ubr.client_secret=cmroDhogV6dTBhjStuuFwCMpIBldCF6U
 * ubr.grant_type=client_credentials
 * ubr.sign_method=sha256
 * ubr.sign_secret=1234567890
 * ubr.api_version=10
 * </pre>
 */
@Slf4j
@Component
public class UbrRequestTemplate {

    static final String REQUEST_TOKEN_URL;
    static final String REQUEST_SEND_COUPON_URL;
    static final String CLIENT_ID;
    static final String CLIENT_SECRET;
    static final String GRANT_TYPE;
    static final String SIGN_METHOD;
    static final String SIGN_SECRET;
    static final String API_VERSION;

    static {
        long start = System.currentTimeMillis();
        REQUEST_TOKEN_URL = PropsUtil.getSysOrEnv("ubr.token_url");
        REQUEST_SEND_COUPON_URL = PropsUtil.getSysOrEnv("ubr.get_coupon_url");
        CLIENT_ID = PropsUtil.getSysOrEnv("ubr.client_id");
        CLIENT_SECRET = PropsUtil.getSysOrEnv("ubr.client_secret");
        GRANT_TYPE = PropsUtil.getSysOrEnv("ubr.grant_type");
        SIGN_METHOD = PropsUtil.getSysOrEnv("ubr.sign_method");
        SIGN_SECRET = PropsUtil.getSysOrEnv("ubr.sign_secret");
        API_VERSION = PropsUtil.getSysOrEnv("ubr.api_version");
        log.info("urb请求模板配置初始化耗时：{}ms", System.currentTimeMillis() - start);
    }

    @Resource
    @Qualifier("redisCache")
    private ICache redisCache;

    public String getToken() {
        log.info("开始获取urb getToken method start");
        String urbToken = redisCache.get("urb_token");
        log.info("urb token缓存值，urbToken={}...", CharSequenceUtil.sub(urbToken, 0, 10));
        if (ObjectUtil.isNotEmpty(urbToken)) {
            return urbToken;
        }
        log.info("开始刷新ubr token");
        String body = HttpUtil.createPost(REQUEST_TOKEN_URL)
                .form("client_id", CLIENT_ID)
                .form("client_secret", CLIENT_SECRET)
                .form("grant_type", GRANT_TYPE)
                .execute().body();
        log.info("urb刷token返回结束");
        JSONObject jsonObject = JSON.parseObject(body, JSONObject.class);
        String accessToken = jsonObject.get("access_token").toString();
        log.info("刷新ubr缓存token，accessToken={}...", CharSequenceUtil.sub(urbToken, 0, 10));
        redisCache.put("urb_token", accessToken, 7000, TimeUnit.SECONDS);
        log.info("ubr刷token流程结束");
        return accessToken;
    }

    public UbrQueryCouponResponseDto getCoupon(String couponCode) {
        String url = String.format(REQUEST_SEND_COUPON_URL, couponCode);
//        String url = "https://apiq.eubrmb.com/eco/qa/v1.0/coupons/instance/600010Coca-67GM4S6T6RDG";

        Map<String, String> headers = new HashMap<>();
        String now = DateUtil.now();
        headers.put("timestamp", now);
        headers.put("SignSecret", SIGN_SECRET);
        String sign = getSign(JSON.toJSONString(headers));
        log.info("签名：{}", sign);

        JSONObject jsonObject = JSON.parseObject(getToken(), JSONObject.class);
        String accessToken = jsonObject.get("access_token").toString();
        log.info("getCoupon接口token={}", accessToken);
        HttpResponse execute = HttpUtil
                .createGet(url)
                .header("v", API_VERSION)
                .header(HttpHeaders.AUTHORIZATION, "Bearer ".concat(accessToken))
                .header("timestamp", now)
                .header("sign_method", SIGN_METHOD)
                .header("sign",sign ).execute();
        String body = execute.body();
        log.info("urb发券请求结果，body={}", body);
        UbrQueryCouponResponseDto ubrQueryCouponResponseDto = JSON.parseObject(body, UbrQueryCouponResponseDto.class);
        return ubrQueryCouponResponseDto;
    }

    private static String getSign(String body) {
        MessageDigest digest = null;
        try {
            digest = MessageDigest.getInstance("SHA-256");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
        byte[] hashBytes = digest.digest(JSON.toJSONString(body).getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(hashBytes);
    }
}
