package com.shuyun.kylin.customized.task.dataflow.service.impl;


import com.alibaba.fastjson.JSON;
import com.shuyun.dm.api.response.DMLResponse;
import com.shuyun.kylin.customized.task.base.common.ModelConstants;
import com.shuyun.kylin.customized.task.base.feign.client.OpenApiFeignClient;
import com.shuyun.kylin.customized.task.dataflow.enums.BehaviorEnums;
import com.shuyun.kylin.customized.task.dataflow.service.CosmosService;
import com.shuyun.kylin.customized.task.integration.dto.MemberBingDto;
import com.shuyun.kylin.customized.task.integration.dto.OcpMemberBindingDto;
import com.shuyun.kylin.customized.task.integration.dto.OcpMemberDto;
import com.shuyun.kylin.customized.task.ma.util.DataApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Service
@Slf4j
public class CosmosServiceImpl implements CosmosService {

    @Autowired
    private OpenApiFeignClient openApiFeignClient;

    @Autowired
    @Qualifier("memberKafka")
    private KafkaTemplate<String, String> memberKafkaTemplate;

    @Autowired
    @Qualifier("activityKafka")
    private KafkaTemplate<String, String> activityKafkaTemplate;

    @Autowired
    @Qualifier("wechatsubscriptioneventKafka")
    private KafkaTemplate<String, String> wechatsubscriptioneventKafkaTemplate;

    @Autowired
    @Qualifier("masterDataeventKafka")
    private KafkaTemplate<String, String> masterDataKafkaTemplate;

    @Autowired
    @Qualifier("memberDeleteKafka")
    private KafkaTemplate<String, String> memberDeleteKafkaTemplate;

    @Autowired
    @Qualifier("wechatoaeventKafka")
    private KafkaTemplate<String, String> wechatoaeventKafkaTemplate;



    /**
     * cosmos会员注册/更新,会员等级/积分更新
     *
     * @param ocpMemberBindingDto
     */
    @Override
    public void queryCosmosMember(OcpMemberBindingDto ocpMemberBindingDto) {
        //指定要查询的字段(扩展)
        List<String> optionalFields = Arrays.asList("memberSource", "memberSourceDetail");

        //查询会员渠道信息
        List<MemberBingDto> memberBingDtos = openApiFeignClient.queryListChannels(ocpMemberBindingDto.getMember().get("crm_member_id").toString(), "KO", optionalFields);
        log.info("cdp查询会员渠道信息:{}",JSON.toJSONString(memberBingDtos));
        OcpMemberDto memberDto = new OcpMemberDto();
        memberDto.setIs_ocp_member(ocpMemberBindingDto.getIs_ocp_member());
        memberDto.setCreated_at(ocpMemberBindingDto.getCreated_at());
        memberDto.setUpdated_at(ocpMemberBindingDto.getUpdated_at());

        if (memberBingDtos.stream().anyMatch(m -> "SWIRE_MP".equals(m.getChannelType()))) {
            memberDto.setConsumer_source("MTYL");
            memberDto.setOrganization_name("Swire");
            memberDto.setOrganization_code("SCCL");
        }
        if (memberBingDtos.stream().anyMatch(m -> "CBL_MP".equals(m.getChannelType()))) {
            memberDto.setConsumer_source("YXH");
            memberDto.setOrganization_name("COFCO");
            memberDto.setOrganization_code("CBL");
        }
        if (memberBingDtos.stream().anyMatch(m -> "KO_Ali".equals(m.getChannelType()))) {
            memberDto.setConsumer_source("Ali MP");
            memberDto.setOrganization_name(ocpMemberBindingDto.getOrganization_name());
            memberDto.setOrganization_code(ocpMemberBindingDto.getOrganization_code());
        }
        if (memberBingDtos.stream().anyMatch(m -> "KO_MP".equals(m.getChannelType()))) {
            memberDto.setConsumer_source(ocpMemberBindingDto.getConsumer_source());
            memberDto.setOrganization_name(ocpMemberBindingDto.getOrganization_name());
            memberDto.setOrganization_code(ocpMemberBindingDto.getOrganization_code());
        }

        HashMap<String, Object> campaignInfo = new HashMap<>();
        campaignInfo.put("info_authorized", ocpMemberBindingDto.getCampaign_info().get("info_authorized"));
        campaignInfo.put("campaign_id", ocpMemberBindingDto.getCampaign_info().get("campaign_id"));
        campaignInfo.put("lbs_authorized", ocpMemberBindingDto.getCampaign_info().get("lbs_authorized"));
        memberDto.setCampaign_info(campaignInfo);
        ArrayList<Map> arrayList = new ArrayList<>();

        ArrayList<Object> platformBind = new ArrayList<>();
        for (MemberBingDto bingDto : memberBingDtos) {
            HashMap<String, Object> bind = new HashMap<>();
            bind.put("channel", bingDto.getChannelType());
            bind.put("timestamp", getStartTimeDate(bingDto.getCreateTime()));
            bind.put("source", bingDto.getOptionalFieldData().get("memberSource"));
            if (StringUtils.isNotBlank(bingDto.getOptionalFieldData().get("memberSourceDetail"))) {
                bind.put("source_detail", bingDto.getOptionalFieldData().get("memberSourceDetail"));
            }
            bind.put("platform", "wechat");
            HashMap<String, String> map = new HashMap<>();
            map.put("app_id", bingDto.getAppId());
            if ("SWIRE_MP".equals(bingDto.getChannelType())) {
                map.put("app_name", "每天有乐");
                //添加openId
                if (StringUtils.isNotBlank(bingDto.getOpenId())) {
                    HashMap<String, Object> wechatMap = new HashMap<>();
                    wechatMap.put("type", "wechat_open_id");
                    wechatMap.put("value", bingDto.getOpenId());
                    wechatMap.put("platform", "wechat");
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_name", "每天有乐");
                    hashMap.put("app_id", bingDto.getAppId());
                    hashMap.put("app_group_name", "太古可口可乐");
                    wechatMap.put("attribute", hashMap);
                    arrayList.add(wechatMap);
                }
                //添加unionId
                if (StringUtils.isNotBlank(bingDto.getUnionId())) {
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_group_name", "太古可口可乐");
                    HashMap<String, Object> tmallMap = new HashMap<>();
                    tmallMap.put("platform", "wechat");
                    tmallMap.put("type", "wechat_union_id");
                    tmallMap.put("value", bingDto.getUnionId());
                    tmallMap.put("attribute", hashMap);
                    arrayList.add(tmallMap);
                }
            }
            if ("CBL_MP".equals(bingDto.getChannelType())) {
                map.put("app_name", "悦喜荟");
                //添加openId
                if (StringUtils.isNotBlank(bingDto.getOpenId())) {
                    HashMap<String, Object> wechatMap = new HashMap<>();
                    wechatMap.put("type", "wechat_open_id");
                    wechatMap.put("value", bingDto.getOpenId());
                    wechatMap.put("platform", "wechat");
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_name", "悦喜荟");
                    hashMap.put("app_id", bingDto.getAppId());
                    hashMap.put("app_group_name", "中粮可口可乐");
                    wechatMap.put("attribute", hashMap);
                    arrayList.add(wechatMap);
                }
                //添加unionId
                if (StringUtils.isNotBlank(bingDto.getUnionId())) {
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_group_name", "中粮可口可乐");
                    HashMap<String, Object> tmallMap = new HashMap<>();
                    tmallMap.put("platform", "wechat");
                    tmallMap.put("type", "wechat_union_id");
                    tmallMap.put("value", bingDto.getUnionId());
                    tmallMap.put("attribute", hashMap);
                    arrayList.add(tmallMap);
                }
            }
            if ("KO_Ali".equals(bingDto.getChannelType())) {
                map.put("app_name", "支付宝可口可乐吧");
                bind.put("platform", "alibaba");
                if (StringUtils.isNotBlank(bingDto.getOpenId())) {
                    HashMap<String, Object> wechatMap = new HashMap<>();
                    wechatMap.put("type", "alipay");
                    wechatMap.put("value", bingDto.getOpenId());
                    wechatMap.put("platform", "alibaba");
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_name", "支付宝可口可乐吧");
                    hashMap.put("app_id", bingDto.getAppId());
                    wechatMap.put("attribute", hashMap);
                    arrayList.add(wechatMap);
                }
            }
            if ("KO_MP".equals(bingDto.getChannelType())) {
                map.put("app_name", "可口可乐吧");
                //添加openId
                if (StringUtils.isNotBlank(bingDto.getOpenId())) {
                    HashMap<String, Object> wechatMap = new HashMap<>();
                    wechatMap.put("type", "wechat_open_id");
                    wechatMap.put("value", bingDto.getOpenId());
                    wechatMap.put("platform", "wechat");
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("is_koplus_openid", ocpMemberBindingDto.getAttribute().get("is_koplus_openid"));
                    hashMap.put("app_group_name", "可口可乐");
                    wechatMap.put("attribute", hashMap);
                    arrayList.add(wechatMap);
                }
                //添加unionId
                if (StringUtils.isNotBlank(bingDto.getUnionId())) {
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_group_name", "可口可乐");
                    HashMap<String, Object> tmallMap = new HashMap<>();
                    tmallMap.put("platform", "wechat");
                    tmallMap.put("type", "wechat_union_id");
                    tmallMap.put("value", bingDto.getUnionId());
                    tmallMap.put("attribute", hashMap);
                    arrayList.add(tmallMap);
                }
            }

            bind.put("attribute", map);
            platformBind.add(bind);
        }

        //添加memberId
        HashMap<String, Object> memberCrm = new HashMap<>();
        memberCrm.put("platform", ocpMemberBindingDto.getCrm_member().get("platform"));
        memberCrm.put("type", ocpMemberBindingDto.getCrm_member().get("type"));
        memberCrm.put("value", ocpMemberBindingDto.getCrm_member().get("value"));
        //memberCrm.put("attribute", Collections.EMPTY_MAP);
        arrayList.add(memberCrm);

        //添加手机号
        HashMap<String, Object> memberMobile = new HashMap<>();
        memberMobile.put("platform", "phone");
        memberMobile.put("type", "phone_number");
        memberMobile.put("value", ocpMemberBindingDto.getMember().get("mobile"));
        memberMobile.put("attribute", Collections.EMPTY_MAP);
        arrayList.add(memberMobile);
        memberDto.setPlatform_info(arrayList);
        memberDto.setDemographic(ocpMemberBindingDto.getDemographic());

        HashMap<String, Object> equity = new HashMap<>();
        if (null != ocpMemberBindingDto.getMember().get("first_bottle_time")) {
            equity.put("first_bottle_time", copStartTimeDate(JSON.toJSONString(ocpMemberBindingDto.getMember().get("first_bottle_time"))));
            equity.put("available_bottle", ocpMemberBindingDto.getMember().get("available_bottle"));
            equity.put("total_bottle", ocpMemberBindingDto.getMember().get("total_bottle"));
            equity.put("used_bottle", ocpMemberBindingDto.getMember().get("used_bottle"));
            equity.put("expired_bottle", ocpMemberBindingDto.getMember().get("expired_bottle"));
        }
        HashMap<String, Object> member = new HashMap<>();
        member.put("member_level", ocpMemberBindingDto.getMember().get("member_level"));
        member.put("register_source_detail", ocpMemberBindingDto.getMember().get("register_source_detail"));
        if (null != ocpMemberBindingDto.getMember().get("logoff_time")) {
            member.put("logoff_time", copStartTimeDate(JSON.toJSONString(ocpMemberBindingDto.getMember().get("logoff_time"))));
        }
        if (null != ocpMemberBindingDto.getMember().get("register_time")) {
            member.put("register_time", copStartTimeDate(JSON.toJSONString(ocpMemberBindingDto.getMember().get("register_time"))));
        }
        member.put("is_invite_register", ocpMemberBindingDto.getMember().get("is_invite_register"));
        member.put("is_logoff", ocpMemberBindingDto.getMember().get("is_logoff"));
        member.put("experience", ocpMemberBindingDto.getMember().get("experience"));
        member.put("member_type", ocpMemberBindingDto.getMember().get("member_type"));
        member.put("register_channel", ocpMemberBindingDto.getMember().get("register_channel"));
        member.put("crm_member_id", ocpMemberBindingDto.getMember().get("crm_member_id"));
        member.put("register_address", ocpMemberBindingDto.getMember().get("register_address"));
        member.put("register_source", ocpMemberBindingDto.getMember().get("register_source"));
        member.put("member_name", ocpMemberBindingDto.getMember().get("member_name"));
        if (null != equity) {
            member.put("equity", equity);
        }
        member.put("platform_bind", platformBind);
        memberDto.setMember(member);
        memberDto.setData_source(ocpMemberBindingDto.getDataSource());
        log.info(ocpMemberBindingDto.getTopic()+"_同步cosmos请求入参:{}", JSON.toJSONString(memberDto));
        memberKafkaTemplate.send(ocpMemberBindingDto.getTopic(),UUID.randomUUID().toString(),JSON.toJSONString(memberDto)).addCallback(success -> {
            dataApiSyncCosmoslog(ocpMemberBindingDto.getId(),ocpMemberBindingDto.getFqn(),ocpMemberBindingDto.getMember().get("crm_member_id").toString(),null,BehaviorEnums.SYNC_SUCCESS.getCode(), BehaviorEnums.SYNC_SUCCESS.getName(),ocpMemberBindingDto.getType());
            log.info("发送成功id:{},onlyId:{}", ocpMemberBindingDto.getId(),ocpMemberBindingDto.getMember().get("crm_member_id").toString());
        }, failure -> {
            dataApiSyncCosmoslog(ocpMemberBindingDto.getId(),ocpMemberBindingDto.getFqn(),ocpMemberBindingDto.getMember().get("crm_member_id").toString(), null,BehaviorEnums.SYNC_ERROR.getCode(), BehaviorEnums.SYNC_ERROR.getName(),ocpMemberBindingDto.getType());
            log.warn("发送失败:{},onlyId:{},msg:{}", ocpMemberBindingDto.getId(),ocpMemberBindingDto.getId(),ocpMemberBindingDto.getMember().get("crm_member_id").toString(),failure.getMessage());
        });
    }


    /**
     * cosmos activity数据
     *
     * @param request
     */
    @Override
    public void queryCosmosActivity(Map<String, Object> request) {

        log.info(request.get("topic")+"_同步cosmos请求入参:{}", JSON.toJSONString(request.get("requestBody")));

        activityKafkaTemplate.send(request.get("topic").toString(),UUID.randomUUID().toString(),JSON.toJSONString(request.get("requestBody"))).addCallback(success -> {
            dataApiSyncCosmoslog(request.get("id").toString(),request.get("fqn").toString(),request.get("onlyId").toString(),null,BehaviorEnums.SYNC_SUCCESS.getCode(), BehaviorEnums.SYNC_SUCCESS.getName(),request.get("type").toString());
            log.info("发送成功id:{},onlyId:{}", request.get("id").toString(),request.get("onlyId").toString());
        }, failure -> {

            dataApiSyncCosmoslog(request.get("id").toString(),request.get("fqn").toString(),request.get("onlyId").toString(), request.get("requestBody").toString(),BehaviorEnums.SYNC_ERROR.getCode(), BehaviorEnums.SYNC_ERROR.getName(),request.get("type").toString());
            log.warn("发送失败:{},onlyId:{},msg:{}", request.get("id").toString(),request.get("onlyId").toString(),failure.getMessage());
        });
    }

    /**
     * 小程序订阅数据
     * @param request
     */
    @Override
    public void queryCosmosWechatsubscription(Map<String, Object> request) {

        log.info(request.get("topic")+"_同步cosmos请求入参:{}", JSON.toJSONString(request.get("requestBody")));

        wechatsubscriptioneventKafkaTemplate.send(request.get("topic").toString(),UUID.randomUUID().toString(),JSON.toJSONString(request.get("requestBody"))).addCallback(success -> {
            dataApiSyncCosmoslog(request.get("id").toString(),request.get("fqn").toString(),request.get("onlyId").toString(), null,BehaviorEnums.SYNC_SUCCESS.getCode(), BehaviorEnums.SYNC_SUCCESS.getName(),request.get("type").toString());
            log.info("发送成功id:{},onlyId:{}", request.get("id").toString(),request.get("onlyId").toString());
        }, failure -> {

            dataApiSyncCosmoslog(request.get("id").toString(),request.get("fqn").toString(),request.get("onlyId").toString(), request.get("requestBody").toString(),BehaviorEnums.SYNC_ERROR.getCode(), BehaviorEnums.SYNC_ERROR.getName(),request.get("type").toString());
            log.warn("发送失败:{},onlyId:{},msg:{}", request.get("id").toString(),request.get("onlyId").toString(),failure.getMessage());
        });
    }

    /**
     * 粉丝用户
     *
     * @param request
     */
    @Override
    public void sendCosmosWechatFans(Map<String, Object> request) {
        log.info(request.get("topic")+"_同步cosmos请求入参:{}", JSON.toJSONString(request.get("requestBody")));
        memberKafkaTemplate.send(request.get("topic").toString(),UUID.randomUUID().toString(),JSON.toJSONString(request.get("requestBody"))).addCallback(success -> {
            dataApiSyncCosmoslog(request.get("id").toString(),request.get("fqn").toString(),request.get("onlyId").toString(), null,BehaviorEnums.SYNC_SUCCESS.getCode(), BehaviorEnums.SYNC_SUCCESS.getName(),request.get("type").toString());
            log.info("发送成功id:{},onlyId:{}", request.get("id").toString(),request.get("onlyId").toString());
        }, failure -> {
            dataApiSyncCosmoslog(request.get("id").toString(),request.get("fqn").toString(),request.get("onlyId").toString(), request.get("requestBody").toString(),BehaviorEnums.SYNC_ERROR.getCode(), BehaviorEnums.SYNC_ERROR.getName(),request.get("type").toString());
            log.warn("发送失败:{},onlyId:{},msg:{}", request.get("id").toString(),request.get("onlyId").toString(),failure.getMessage());
        });

    }

    /**
     * 粉丝消息，粉丝行为
     *
     * @param request
     */
    @Override
    public void sendCosmosWechatToaEvent(Map<String, Object> request) {

        log.info(request.get("topic")+"_同步cosmos请求入参:{}", JSON.toJSONString(request.get("requestBody")));

        wechatoaeventKafkaTemplate.send(request.get("topic").toString(),UUID.randomUUID().toString(),JSON.toJSONString(request.get("requestBody"))).addCallback(success -> {
            dataApiSyncCosmoslog(request.get("id").toString(),request.get("fqn").toString(),request.get("onlyId").toString(), null,BehaviorEnums.SYNC_SUCCESS.getCode(), BehaviorEnums.SYNC_SUCCESS.getName(),request.get("type").toString());
            log.info("发送成功id:{},onlyId:{}", request.get("id").toString(),request.get("onlyId").toString());

        }, failure -> {
            dataApiSyncCosmoslog(request.get("id").toString(),request.get("fqn").toString(),request.get("onlyId").toString(), request.get("requestBody").toString(),BehaviorEnums.SYNC_ERROR.getCode(), BehaviorEnums.SYNC_ERROR.getName(),request.get("type").toString());
            log.warn("发送失败:{},onlyId:{},msg:{}", request.get("id").toString(),request.get("onlyId").toString(),failure.getMessage());
        });
    }

    /**
     * 会员注销，用户注销
     *
     * @param request
     */
    @Override
    public void sendCosmosCancel(Map<String, Object> request) {
        log.info(request.get("topic")+"_同步cosmos请求入参:{}", JSON.toJSONString(request.get("requestBody")));
        memberDeleteKafkaTemplate.send(request.get("topic").toString(),UUID.randomUUID().toString(),JSON.toJSONString(request.get("requestBody"))).addCallback(success -> {
            dataApiSyncCosmoslog(request.get("id").toString(),request.get("fqn").toString(),request.get("onlyId").toString(), null,BehaviorEnums.SYNC_SUCCESS.getCode(), BehaviorEnums.SYNC_SUCCESS.getName(),request.get("type").toString());
            log.info("发送成功id:{},onlyId:{}", request.get("id").toString(),request.get("onlyId").toString());
        }, failure -> {
            dataApiSyncCosmoslog(request.get("id").toString(),request.get("fqn").toString(),request.get("onlyId").toString(), request.get("requestBody").toString(),BehaviorEnums.SYNC_ERROR.getCode(), BehaviorEnums.SYNC_ERROR.getName(),request.get("type").toString());
            log.warn("发送失败:{},onlyId:{},msg:{}", request.get("id").toString(),request.get("onlyId").toString(),failure.getMessage());
        });
    }

    /**
     * 权益项目。小程序模版
     *
     * @param request
     */
    @Override
    public void sendCosmosMaster(Map<String, Object> request) {
        log.info(request.get("topic")+"_同步cosmos请求入参:{}", JSON.toJSONString(request));
        HashMap<Object, Object> map = new HashMap<>();
        map.put("data_type",request.get("dataType"));
        map.put("data_content",JSON.toJSONString(request.get("dataContent")));
        map.put("data_source",request.get("dataSource"));
        log.info("请求权益主数据/小程序订阅模版数据封装:{}",JSON.toJSONString(map));

        masterDataKafkaTemplate.send(request.get("topic").toString(),UUID.randomUUID().toString(),JSON.toJSONString(map)).addCallback(success -> {
            dataApiSyncCosmoslog(request.get("id").toString(),request.get("fqn").toString(),request.get("onlyId").toString(), null,BehaviorEnums.SYNC_SUCCESS.getCode(), BehaviorEnums.SYNC_SUCCESS.getName(),request.get("type").toString());
            log.info("发送成功id:{},onlyId:{}", request.get("id").toString(),request.get("onlyId").toString());
        }, failure -> {
            dataApiSyncCosmoslog(request.get("id").toString(),request.get("fqn").toString(),request.get("onlyId").toString(), request.get("dataContent").toString(),BehaviorEnums.SYNC_ERROR.getCode(), BehaviorEnums.SYNC_ERROR.getName(),request.get("type").toString());
            log.warn("发送失败:{},onlyId:{},msg:{}", request.get("id").toString(),request.get("onlyId").toString(),failure.getMessage());
        });

    }


    private static void dataApiSyncCosmoslog(String id,String fqn,String onlyId,String requestBody,String code,String msg,String type){
        HashMap<String, Object> res = new HashMap<>();
        res.put("id",id);
        res.put("onlyId",onlyId);
        res.put("code",code);
        res.put("msg",msg);
        res.put("lastSync", ZonedDateTime.now());
        res.put("type",type);
        res.put("fqn",fqn);
        res.put("requestBody",requestBody);
        DMLResponse response = DataApiUtil.upsertIsData(fqn, id, res);
        if (!response.getIsSuccess()) {
            log.error("同步cosmos推送kafka记录日志失败:{},onlyId:{},fqn:{}",response.getOperation(),onlyId,fqn);
        }
    }

    public static String getStartTimeDate(String time){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeStrart = time.substring(0,19).replace("T"," ");
        String timeDate = "";
        try {
            Date dt=sdf.parse(timeStrart);
            Calendar rightNow = Calendar.getInstance();
            rightNow.setTime(dt);
            rightNow.add(Calendar.HOUR,8);
            Date nowTime = rightNow.getTime();
            timeDate = sdf.format(nowTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return timeDate;
    }
    public static String copStartTimeDate(String time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeDate = time.substring(1, 20).replace("T", " ");

        return timeDate;
    }
}
