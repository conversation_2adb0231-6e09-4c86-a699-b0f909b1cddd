package com.shuyun.kylin.customized.task.ma.service;


import com.shuyun.kylin.customized.task.ma.dto.CepAppletSubscribeDto;
import com.shuyun.kylin.customized.task.ma.dto.CepCustomizationCouponDto;
import com.shuyun.kylin.customized.task.ma.dto.CepResponseResult;

public interface CepMemberCouponService {

    CepResponseResult saveMemberCoupon(String url, CepCustomizationCouponDto cepCustomizationCouponDto);

    CepResponseResult saveAppletSubscription(String url, CepAppletSubscribeDto cepAppletSubscribeDto);

    //void saveAppletSubscriptionTask(String id, String processId, String nodeId, String taskId, String tenant);
}
