package com.shuyun.kylin.customized.task.ma.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

@Data
public class CepCustomizationCouponDto {

    @NotBlank(message = "活动Code不能为空")
    private String activityCode;
    //@NotBlank(message = "用户ID不能为空")
    private String uid;
    @NotBlank(message = "用户类型不能为空，当前支持：CRM_ID, OCP_ID")
    private String accountType;
    @NotBlank(message = "资源ID不能为空")
    private String resourceId;
    //是否加入卡包
    private Boolean bindBenefit;

    private String sendCostCenterCode;

    /**
     * cep发券幂等id
     */
    private String transactionId;

    private Map extra;
    //private String campaignId;
    //private String nodeId;

}
