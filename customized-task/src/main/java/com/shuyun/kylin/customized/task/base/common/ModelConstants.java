package com.shuyun.kylin.customized.task.base.common;


import com.shuyun.motor.common.cons.PropsUtil;

/**
 * <AUTHOR>
 * @date 2021/12/11
 */
public class ModelConstants {

    /**
     * 模型id, 应用场景: 根据id查询业务模型属性映射关系
     */
    public static final Integer MEMBER_MODEL_ID = 1;

    public static final String MEMBER_TYPE = "KO";

    /**
     * OpenAPI自动生成的模型
     */
    public static final String Member = "data.prctvmkt.KO.Member";

    /**
     * cosmos用户信息
     */
    public static final String COSMOS_CONSUMER = "data.prctvmkt.KO.Consumer";
    /**
     * 可乐消费者模型
     */
    public static final String CUSTOMER = "data.prctvmkt.KO.Customer";

    /**
     * 自定义模型
     */


    public static final String MEMBER_CONPON = "data.prctvmkt.KO.Coupon";

    public static final String SYNC_COSMOS_LOG = "data.prctvmkt.KO.SyncCosmosKafkalog";

    public static final String CT_SYNC_LOG = "data.prctvmkt.KO.CtSyncInterfacelog";

    public static final String MEMBER_COUPONREMIND = "data.prctvmkt.KO.CouponExpireRemindRecord";

    public static final String MEMBER_PROJECT = "data.prctvmkt.KO.CouponDetail";


    //勋章主数据
    public static final String MEDAL_RULE = "data.prctvmkt.KO.MedalRule";

    //会员参与活动信息
    public static final String MEMBER_CAMPAIGN = "data.prctvmkt.KO.MemberCampaign";

    //主体勋章
    public static final String MEMBER_MEDAL = PropsUtil.getSysOrEnv("loyalty.medal", "data.loyalty.member.hierarchy.Medal60001");

    //会员勋章任务进度
    public static final String MEDAL_TASK= "data.prctvmkt.KO.MedalTask";

    private static final String  pointAccountId= PropsUtil.getSysOrEnv("loyalty.pointAccountId", "60087");

    //积分明细
    public static final String POINT_RECORD= "data.loyalty.member.account.PointRecord"+pointAccountId;

    //历史券ko_user_benefit
    public static final String TW_KO_USER_BENEFIT = "data.prctvmkt.tw.ko.user.benefit";

    //历史tw_ko_user_benefit_config
    public static final String TW_KO_USER_BENEFIT_CONFIG= "data.prctvmkt.tw_ko_user_benefit_config";

    //历史tw_ko_third_party_coupon
    public static final String TW_KO_THIRD_PARTY_COUPON = "data.prctvmkt.tw_ko_third_party_coupon";

    //crm券项目模型
    public static final String OFFER_PROJECT = "data.offer.v3.Project";

    //crm会员绑定模型
    public static final String MEMBER_MEMBER_BINDING = "data.prctvmkt.KO.MemberBinding";

    //历史tw会员映射
    public static final String TW_MEMBER_ID_MAPPING = "data.prctvmkt.tw_member_id_mapping";


    //tw历史卡券临时模型
    public static final String TW_HIS_INSTANCE = "data.prctvmkt.tw_his_instance";
}
