package com.shuyun.kylin.customized.task.base.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.mapper.MapperScannerConfigurer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

/**
 * Created by yingzhou.wei on 2018/5/24 19:50.
 */
@EnableTransactionManagement
@Configuration
@Slf4j
@MapperScan("com.shuyun.kylin.customized.task.*.mapper")
public class DataBaseConfig {

    private final String driverClass = "com.mysql.jdbc.Driver";
    @Value("#{systemProperties['database.url']}")
    private String url;
    @Value("#{systemProperties['database.username']}")
    private String username;
    @Value("#{systemProperties['database.password']}")
    private String password;

    private int initialSize = 1;
    //配置获取连接等待超时的时间
    private Long maxWait = 60000L;
    //配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
    private Long timeBetweenEvictionRunsMillis = 60000L;
    //配置一个连接在池中最小生存的时间，单位是毫秒
    private Long minEvictableIdleTimeMillis = 300000L;

    private String validationQuery = "SELECT 'x' ";

    private boolean testOnReturn = false;


    @Bean(name = "myqlDataSource")
    DataSource initDataSource() {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setInitialSize(initialSize);
        dataSource.setTimeBetweenEvictionRunsMillis(timeBetweenEvictionRunsMillis);
        dataSource.setMinEvictableIdleTimeMillis(minEvictableIdleTimeMillis);
        dataSource.setTestOnReturn(testOnReturn);
        dataSource.setUrl(this.url);
        dataSource.setUsername(this.username);
        dataSource.setPassword(this.password);
        dataSource.setDriverClassName(this.driverClass);
        dataSource.setMaxActive(20);
        dataSource.setMinIdle(1);
        dataSource.setMaxWait(maxWait);
        dataSource.setValidationQuery(validationQuery);
        dataSource.setTestOnBorrow(false);
        dataSource.setTestWhileIdle(true);
        return dataSource;
    }

    @Bean(name = "sqlSessionFactory")
    SqlSessionFactory initSqlSessionFactoryBean() {
        try {
            log.info("初始化mysql开始。。");
            MybatisSqlSessionFactoryBean sqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
            sqlSessionFactoryBean.setDataSource(this.initDataSource());
            ResourcePatternResolver patternResolver = new PathMatchingResourcePatternResolver();

            sqlSessionFactoryBean.setMapperLocations(patternResolver.getResources("classpath:sqlmap/**/*.xml"));
            sqlSessionFactoryBean.setTypeAliasesPackage("com.shuyun.kylin.customized.task.*.domain");
            SqlSessionFactory sqlSessionFactory = sqlSessionFactoryBean.getObject();
            log.info("初始化mysql结束。sqlSessionFactory:{}", sqlSessionFactory);
            return sqlSessionFactory;
        } catch (Exception e) {
            log.warn("数据库初始化异常：",e);
            e.printStackTrace();
        }
        return null;
    }


    @Bean(name = "transactionManager")
    public PlatformTransactionManager transactionManager(@Qualifier("myqlDataSource") DataSource myqlDataSource) {
        return new DataSourceTransactionManager(myqlDataSource);
    }

    @Bean
    public PaginationInterceptor paginationInterceptor() {
        return new PaginationInterceptor();
    }

}
