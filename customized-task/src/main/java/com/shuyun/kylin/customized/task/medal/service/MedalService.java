package com.shuyun.kylin.customized.task.medal.service;


import com.shuyun.kylin.customized.task.medal.request.MedalObtainRequest;
import com.shuyun.kylin.customized.task.medal.request.MedalProgressRequest;

public interface MedalService {


    void  medalProgressByCampaignConsumer(MedalProgressRequest medalProgressRequest);

    void medalProgressByPointRecordConsumer(MedalProgressRequest medalProgressRequest);

    void initHisMemberRegisterMedalConsumer(MedalObtainRequest medalObtainRequest);


    void  medalCountsConsumer(MedalProgressRequest medalProgressRequest);


    void medalCouponConsumer(MedalProgressRequest medalProgressRequest);

    void medalProgressBySendPointRecordConsumer(MedalProgressRequest medalProgressRequest);

}
