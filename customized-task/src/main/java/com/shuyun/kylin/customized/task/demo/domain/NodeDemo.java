package com.shuyun.kylin.customized.task.demo.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@TableName("node_demo")
public class NodeDemo {

    public static final String TYPE = "extTfilterDemo";

    private Long id;

    private String name;

    private String address;

    private String mobile;

    private Integer age;

    private Integer sex;

    private Date createTime;

    private Long createBy;

    private Date updateTime;

    private Long updateBy;



}