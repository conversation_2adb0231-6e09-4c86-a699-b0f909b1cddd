package com.shuyun.kylin.customized.task.base.kafka.consumer.history;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.task.base.kafka.messaging.KafkaSink;
import com.shuyun.kylin.customized.task.medal.request.MedalObtainRequest;
import com.shuyun.kylin.customized.task.medal.service.MedalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

//会员勋章发放
@Slf4j
@Component
public class MemberMedalObtainConsumer {


    @Autowired
    private MedalService medalService;

    @StreamListener(KafkaSink.KAFKA_INIT_MEMBER_MEDAL_OBTAIN)
    public void memberMedalObtainConsumer(MedalObtainRequest medalObtainRequest) {
        try {
            log.info("会员勋章发放开始:{}", JSON.toJSONString(medalObtainRequest));
            medalService.initHisMemberRegisterMedalConsumer(medalObtainRequest);
        } catch (Exception e) {
            log.error("会员勋章发放开始:{}", JSON.toJSONString(medalObtainRequest),e);
        }
    }
}
