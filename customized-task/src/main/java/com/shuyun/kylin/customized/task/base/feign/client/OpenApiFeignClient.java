package com.shuyun.kylin.customized.task.base.feign.client;

import com.shuyun.kylin.customized.task.integration.dto.MemberBingDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "openApiFeignClient")
@RequestMapping("openapi/v2")
public interface OpenApiFeignClient {


    @GetMapping("/member/channels")
    List<MemberBingDto> queryListChannels(@RequestParam("memberId") String memberId,
                                          @RequestParam("memberType") String memberType,
                                          @RequestParam(value = "optionalFields",required = false) List<String> optionalFields);

}