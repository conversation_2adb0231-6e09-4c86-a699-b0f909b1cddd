package com.shuyun.kylin.customized.task.coupon.job;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.kylin.customized.task.base.util.DateHelper;
import com.shuyun.kylin.customized.task.base.feign.client.OpenApiFeignClientV3;
import com.shuyun.kylin.customized.task.base.dto.OfferInstanceUseClientRequestDto;
import com.shuyun.kylin.customized.task.coupon.dto.CouponProjectFetchDbResultDto;
import com.shuyun.kylin.customized.task.coupon.dto.CouponQueryResultDto;
import com.shuyun.kylin.customized.task.coupon.dto.DatePartsDto;
import com.shuyun.kylin.customized.task.coupon.enums.UbrCoupoonCreatedStatusEnum;
import com.shuyun.kylin.customized.task.ma.util.DataapiSdkUtil;
import com.shuyun.kylin.customized.task.rpc.dto.UbrQueryCouponResponseDto;
import com.shuyun.kylin.customized.task.rpc.template.UbrRequestTemplate;
import com.shuyun.motor.common.cons.PropsUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.enums.JobInitiationEnum;
import com.xxl.job.core.handler.annotation.XxlJob;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
public class UbrCouponUseJob {
    final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();

    @Resource
    private OpenApiFeignClientV3 openApiFeignClientV3;

    @Resource
    private UbrRequestTemplate ubrRequestTemplate;


    @XxlJob(value = "ubrCouponUseJob", cron = "0 0 1 * * ? *", jobDesc = "UBR优惠券使用情况", jobInitation = JobInitiationEnum.START)
    public ReturnT<String> scheduleUbrCouponUseJob(String params) {
        log.info("ubrCouponUseJob 任务开始，param={}，starTime={}", params, DateUtil.now());
        // 1.根据模板id查询ubr券项目
        List<CouponProjectFetchDbResultDto> ubrProjectList = getAllUbrProject();
        Stream<String> projectIdList = ubrProjectList.stream().map(CouponProjectFetchDbResultDto::getId);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("projectIdList", projectIdList);
        // 2. 以券项目id为条件，查询本地库中类型为ubr且未使用的优惠券（即状态为已发放的）
        List<DatePartsDto> dateTimeParts = getDateTimeParts();
        // 按小时划分，分批次查询券使用情况
        for (DatePartsDto dateTimePart : dateTimeParts) {
            int leftCouponSize = 0;
            String maxCreateTime = null;
            do {
                String couponInstanceSql = "select code,state,externalCode,grantPlatform,projectId,memberName,projectId,city,district,address,postalCode,recipientmobile,usedLBSProvince,usedLBSCity,usedLBSDistrict " +
                        "from data.offer.v3.Instancedovuc3q5 " +
                        "where projectId in (:projectIdList) and state='EFFECTED' " +
                        "and createAt>='" + dateTimePart.getStartTime() + "' and createAt<'" + dateTimePart.getEndTime() + "'";
                if(ObjectUtil.isNotEmpty(maxCreateTime)) {
                    couponInstanceSql += " and createAt>='" + maxCreateTime + "' order by createAt limit 1000";
                } else {
                    couponInstanceSql += " order by createAt limit 1000";
                }
                try {
                    log.info("查询券实例sql={}", couponInstanceSql);
                    BaseResponse execute = dataapiHttpSdk.execute(couponInstanceSql, paramMap);
                    List<Map<String, Object>> dataList = execute.getData();
                    leftCouponSize = dataList.size();
                    log.info("查询券实例原始结果，data={}", JSON.toJSONString(dataList));
                    List<CouponQueryResultDto> couponQueryResultDtos = BeanUtil.copyToList(dataList, CouponQueryResultDto.class);
                    maxCreateTime = couponQueryResultDtos.get(couponQueryResultDtos.size() - 1).getCreateAt();
                    // 3. 调用三方接口获取优惠券使用情况，获取到优惠券为已使用，更新本地库优惠券状态为已使用
                    List<CouponQueryResultDto> needUpdateUseStatusCouponList = new ArrayList<>();
                    // todo：这里需要考虑异常处理，细化异常
                    for (CouponQueryResultDto couponQueryResultDto : couponQueryResultDtos) {
                        String externalCode = couponQueryResultDto.getExternalCode();
                        // 三方接口调用处
                        UbrQueryCouponResponseDto ubrCouponRsp = ubrRequestTemplate.getCoupon(externalCode);
                        if (ObjectUtil.isNotEmpty(ubrCouponRsp) && ObjectUtil.notEqual(ubrCouponRsp.getStatus(), UbrCoupoonCreatedStatusEnum.REDEEMED.getCode())) {
                            continue;
                        }
                        needUpdateUseStatusCouponList.add(couponQueryResultDto);
                    }
                    // todo:如果needUpdateUseStatusCouponList不存在，则继续走下一批，否则需要调用券核销接口
                    Map<String, CouponProjectFetchDbResultDto> projectMap =
                            ubrProjectList.stream().collect(Collectors.toMap(CouponProjectFetchDbResultDto::getId, Function.identity(), (oldVal, newVal) -> newVal));
                    List<OfferInstanceUseClientRequestDto> requestDtoList = assembleCouponUseRequest(needUpdateUseStatusCouponList, projectMap);
                    // todo：这里需要考虑异常处理，细化异常
                    for (OfferInstanceUseClientRequestDto requestDto : requestDtoList) {
                        openApiFeignClientV3.instanceUse(requestDto);
                    }
                } catch (Exception e) {
                    log.error("ubr券定时任务异常，查询券实例sql={}", couponInstanceSql, e);
                    // todo: 存入异常表，重新推送
                }
            } while (leftCouponSize>0);
        }
        log.info("ubr券定时任务结束，endTime={}", DateUtil.now());
        return ReturnT.SUCCESS;
    }

    private static List<DatePartsDto> getDateTimeParts() {
        DateTime time = DateUtil.offsetDay(new Date(), -1);
        DateTime dayStart = DateUtil.beginOfDay(time);
        List<DatePartsDto> datePartsDtos = new ArrayList<>();
        for (int i = 0; i < 24; i++) {
            DatePartsDto datePartsDto = new DatePartsDto();
            datePartsDto.setStartTime(DateUtil.offsetMinute(dayStart, 60 * i).toString(DateHelper.DATE_TIME_FORMAT));
            datePartsDto.setEndTime(DateUtil.offsetMinute(dayStart, 60 * (i + 1)).toString(DateHelper.DATE_TIME_FORMAT));
            datePartsDtos.add(datePartsDto);
        }
        return datePartsDtos;
    }

    /**
     * 组装数云核销请求数据
     *
     * @param couponQueryResultDtoList
     * @param projectMap
     * @return
     */
    private List<OfferInstanceUseClientRequestDto> assembleCouponUseRequest(List<CouponQueryResultDto> couponQueryResultDtoList,
                                                                            Map<String, CouponProjectFetchDbResultDto> projectMap) {
        List<OfferInstanceUseClientRequestDto> offerInstanceUseList = new ArrayList<>();
        for (CouponQueryResultDto couponQueryResultDto : couponQueryResultDtoList) {
            OfferInstanceUseClientRequestDto offerInstanceUseClientRequestDto = new OfferInstanceUseClientRequestDto();
            // 品牌写死，默认就是可口可乐的
            offerInstanceUseClientRequestDto.setBrand("KO");
            offerInstanceUseClientRequestDto.setChannelType(couponQueryResultDtoList.get(0).getGrantPlatform());
            offerInstanceUseClientRequestDto.setTransactionId(UUID.randomUUID().toString().replace("-", ""));
            offerInstanceUseClientRequestDto.setCouponCodes(Collections.singletonList(couponQueryResultDto.getCode()));
            OfferInstanceUseClientRequestDto.Extension extension = getInstanceUseExtension(couponQueryResultDto, projectMap.get(couponQueryResultDto.getProjectId()));
            offerInstanceUseClientRequestDto.setExtension(extension);
            offerInstanceUseList.add(offerInstanceUseClientRequestDto);

        }
        return offerInstanceUseList;
    }

    private OfferInstanceUseClientRequestDto.Extension getInstanceUseExtension(CouponQueryResultDto couponQueryResultDto, CouponProjectFetchDbResultDto project) {
        OfferInstanceUseClientRequestDto.Extension extension = new OfferInstanceUseClientRequestDto.Extension();
        extension.setMemberName(couponQueryResultDto.getMemberName());
        extension.setProvince(couponQueryResultDto.getProvince());
        extension.setCity(couponQueryResultDto.getCity());
        extension.setDistrict(couponQueryResultDto.getDistrict());
        extension.setAddress(couponQueryResultDto.getAddress());
        extension.setPostalCode(couponQueryResultDto.getPostalCode());
        extension.setRecipientmobile(couponQueryResultDto.getRecipientmobile());
        matchLbsCode(extension, project, couponQueryResultDto);
        return extension;
    }

    /**
     * 如果成本中心为空，则使用券项目配置的固定成本中心
     * @param extension
     * @param couponQueryResultDto
     */
    private void matchLbsCode(OfferInstanceUseClientRequestDto.Extension extension,
                              CouponProjectFetchDbResultDto project,
                              CouponQueryResultDto couponQueryResultDto) {
        log.info("匹配成本中心值");
        if(ObjectUtil.isAllNotEmpty(couponQueryResultDto.getUsedCostCenterCode(),couponQueryResultDto.getUsedLBSProvince(), couponQueryResultDto.getUsedLBSCity(), couponQueryResultDto.getUsedLBSDistrict())) {
            log.info("成本中心不为空，默认不变");
            return;
        }
        log.info("成本中心为空，使用券项目配置的固定成本中心");
        // 取项目配置的固定成本中心
        extension.setUsedCostCenterCode(project.getUsedCostCenterCode());
        extension.setUsedLBSProvince(project.getUsedLBSProvince());
        extension.setUsedLBSCity(project.getUsedLBSCity());
        extension.setUsedLBSDistrict(project.getUsedLBSDistrict());
    }

    private List<CouponProjectFetchDbResultDto> getAllUbrProject() {
        String projectTemplateJSon = PropsUtil.getSysOrEnv("project.templateId");
        Map<String, String> projectTemplates = JSON.parseObject(projectTemplateJSon, new TypeReference<HashMap<String, String>>() {});
        String projectTemplateId = projectTemplates.get("costaSouth");
        String projectSql = String.format("select id,template,usedLBSProvince,usedLBSCity,usedLBSDistrict from data.offer.v3.Project where template='%s'", projectTemplateId);
        BaseResponse<Map> execute = dataapiHttpSdk.execute(projectSql, Collections.emptyMap());
        List<Map> data = execute.getData();
        log.info("查询券项目原始结果，data={}", JSON.toJSONString(data));
        if(CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(data, CouponProjectFetchDbResultDto.class);
    }
}
