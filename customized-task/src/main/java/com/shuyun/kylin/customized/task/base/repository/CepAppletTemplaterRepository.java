package com.shuyun.kylin.customized.task.base.repository;



import com.shuyun.kylin.customized.task.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.task.ma.dto.CepAppletTemplateDto;
import com.shuyun.kylin.customized.task.ma.util.DataCofig;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
@Component
public class CepAppletTemplaterRepository extends BaseDsRepository<CepAppletTemplateDto> {

    /**
     * 查询小程序订阅
     * @param templateId
     * @return
     */
    public CepAppletTemplateDto getSubscribers(String templateId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("id",templateId);
        //String queryOrderSql = " SELECT fieldsKey1,fieldsKey2,fieldsKey3,fieldsKey4,fieldsKey5,fieldsKey6,fieldsKey7  from " + ModelConstants.SUBSCRIPTIONS + "  where templateId = :templateId ";
        CepAppletTemplateDto TaskSubsc = queryByFilter(DataCofig.SUBSCRIPTIONS, null,queryMap);
        return TaskSubsc;
    }
}
