package com.shuyun.kylin.customized.task;


import com.shuyun.pip.BaseApplication;
import com.shuyun.pip.autoconfiguration.ReloadableMessageSourceAutoConfiguration;
import com.shuyun.pip.autoconfigure.LockerAutoConfiguration;
import com.shuyun.pip.autoconfigure.PipAutoConfiguration;
import com.shuyun.pip.autoconfigure.RedisLockerAutoConfigure;
import com.shuyun.pip.autoconfigure.ValidatorConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 * @Description
 * @date 2020/3/19
 */
@EnableFeignClients(basePackages = {"com.shuyun.kylin.customized.task.base.feign.client","com.shuyun.kylin.crm.openapi.sdk.client"})
@EnableTransactionManagement
@SpringBootApplication(
        scanBasePackages = {"com.shuyun.kylin.starter",
                "com.shuyun.kylin.customized.task"},
        exclude = {MultipartAutoConfiguration.class, DataSourceAutoConfiguration.class, LockerAutoConfiguration.class,  RedisLockerAutoConfigure.class,
                ReloadableMessageSourceAutoConfiguration.class, ValidatorConfiguration.class, PipAutoConfiguration.class})
public class CustomizedTaskApplication extends BaseApplication {
    private static Logger logger = LoggerFactory.getLogger(CustomizedTaskApplication.class);

    public CustomizedTaskApplication() {
        super(CustomizedTaskApplication.class);
    }

    public static void main(String[] args) {
        logger.info("定制定时任务启动开始...");
        new CustomizedTaskApplication().run(args);
        logger.info("定制定时任务启动成功...");
    }
}
