package com.shuyun.kylin.customized.task.dataflow.service.impl;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.dm.api.response.DMLResponse;
import com.shuyun.kylin.customized.task.base.common.ModelConstants;
import com.shuyun.kylin.customized.task.base.config.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.task.dataflow.dto.CtInterfaceDto;
import com.shuyun.kylin.customized.task.dataflow.dto.CtSyncRetryDao;
import com.shuyun.kylin.customized.task.dataflow.service.CtConsumerService;
import com.shuyun.kylin.customized.task.ma.util.DataApiUtil;
import com.shuyun.kylin.starter.redis.ICache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class CtConsumerServiceImpl implements CtConsumerService {

    @Autowired
    @Qualifier("redisCache")
    private ICache redisCache;

    private static final String wechatLwUrl = ConfigurationCenterUtil.wechatLwUrl;

    /**
     * 权益核销同步
     * @param request
     */
    @Override
    public void sendCouponVerification(Map<String, Object> request) {
        String url = String.valueOf(request.get("url"));
        String onlyId = String.valueOf(request.get("onlyId"));
        String requestBody = JSON.toJSONString(request.get("requestBody"));
        try {
            log.info("联蔚-同步...url:{},onlyId:{},requestBody:{}",requestBody, url,onlyId);
            //同步更新联蔚
            CtInterfaceDto ctBody = lwWechatWithdrawConsumer(requestBody, url);
            log.info("同步ct返回结果:{}",JSON.toJSONString(ctBody));
            if (!"200".equals(ctBody.getCode())){
                dataApiSynclog(request.get("id").toString(),ctBody.getCode(),ctBody.getMessage(),request.get("type").toString(),onlyId,requestBody,url,0);
            }else {
                dataApiSynclog(request.get("id").toString(),ctBody.getCode(),ctBody.getMessage(),request.get("type").toString(),onlyId,requestBody,url,0);
            }
        } catch (Exception e) {
            log.error("同步联蔚接口异常3秒后重试,onlyId:{},msg:{}",onlyId,e.getMessage());
        }
    }

    /**
     * 同步ct失败重试
     * @param ctSyncRetryDao
     * @return
     */
    @Override
    public Map<String, Object> doCtSyncRetry(CtSyncRetryDao ctSyncRetryDao) {
        HashMap<String, Object> map = new HashMap<>();
        Integer count= ctSyncRetryDao.getRetryCount()+1;
        try {
            //同步更新联蔚
            CtInterfaceDto ctBody = lwWechatWithdrawConsumer(ctSyncRetryDao.getRequestBody(), ctSyncRetryDao.getUrl());
            log.info("同步ct失败重试返回结果:{}",JSON.toJSONString(ctBody));
            if (!"200".equals(ctBody.getCode())){
                dataApiSynclog(ctSyncRetryDao.getId(),ctBody.getCode(),ctBody.getMessage(),ctSyncRetryDao.getType(),ctSyncRetryDao.getOnlyId(),ctSyncRetryDao.getRequestBody(),ctSyncRetryDao.getUrl(),count);
            }else {
                dataApiSynclog(ctSyncRetryDao.getId(),ctBody.getCode(),ctBody.getMessage(),ctSyncRetryDao.getType(),ctSyncRetryDao.getOnlyId(),ctSyncRetryDao.getRequestBody(),ctSyncRetryDao.getUrl(),count);
            }
            map.put("code",ctBody.getCode());
            map.put("message",ctBody.getMessage());
            map.put("onlyId",ctSyncRetryDao.getOnlyId());
        } catch (Exception e) {
            log.error("重试同步ct接口异常onlyId:{},msg:{}",ctSyncRetryDao.getOnlyId(),e.getMessage());
            map.put("code", "500");
            map.put("message", "失败");
            map.put("onlyId",ctSyncRetryDao.getOnlyId());
        }
        return map;
    }


    private static void dataApiSynclog(String id,String code,String msg,String type,String onlyId,String requestBody,String url,Integer count){
        HashMap<String, Object> res = new HashMap<>();
        res.put("id",id);
        res.put("code",code);
        res.put("msg",msg);
        res.put("lastSync", ZonedDateTime.now());
        res.put("type",type);
        res.put("onlyId",onlyId);
        res.put("requestBody",requestBody);
        res.put("url",url);
        res.put("retryCount",count);
        DMLResponse response = DataApiUtil.upsertIsData(ModelConstants.CT_SYNC_LOG,id, res);
        if (!response.getIsSuccess()) {
            log.error("同步ct推送记录日志:{},:{},:{}",response.getOperation(),id);
        }
    }

    private CtInterfaceDto lwWechatWithdrawConsumer(String boby, String url) {
        CtInterfaceDto ctInterfaceDto = new CtInterfaceDto();
        String accessToken = redisCache.get("lw.wechat.token");
        if (Objects.isNull(accessToken)) {
            accessToken = getWechatLwToken();
        }
        log.info("联蔚-同步...accessToken:{}", accessToken);
        try {
            String body = HttpUtil.createPost(url).body(boby).header("Authorization", accessToken).execute().body();
            if (ObjectUtil.isNotEmpty(body)) {
                log.info("联蔚-同步...body：{}", body);
                return JSON.parseObject(body, CtInterfaceDto.class);
            }
        } catch (Exception e) {
            log.error("联蔚-同步失败:{}", e);
            ctInterfaceDto.setCode("500");
            ctInterfaceDto.setMessage("失败");
        }
        return ctInterfaceDto;
    }

    public String getWechatLwToken() {
        try {
            String url = String.format("%s/api/platform-tenant/authentication/client?clientId=%s&secret=%s", wechatLwUrl, ConfigurationCenterUtil.wechatLwClientId, ConfigurationCenterUtil.wechatLwSecret);
            log.info("getWechatLwToken...url:{}",url);
            String body = HttpUtil.createPost(url).execute().body();
            if (ObjectUtil.isNotEmpty(body)) {
                log.info("联蔚小程序token返回结果：{}", body);
                JSONObject jsonObject = JSON.parseObject(body, JSONObject.class);
                if (jsonObject.get("accessToken") != null) {
                    String accessToken = jsonObject.getString("accessToken");
                    redisCache.put("lw.wechat.token", accessToken, 3600, TimeUnit.SECONDS);
                    log.info("联蔚小程序token：{}", accessToken);
                    return  accessToken;
                }
            }
        } catch (Exception e) {
            log.error("获取联蔚小程序token异常，", e);
        }
        return null;
    }
}
