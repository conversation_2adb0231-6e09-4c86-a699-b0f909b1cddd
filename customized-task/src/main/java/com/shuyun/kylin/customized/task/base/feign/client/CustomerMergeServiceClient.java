package com.shuyun.kylin.customized.task.base.feign.client;



import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;


@FeignClient(name = "customerMergeServiceClient")
@RequestMapping(path ="customer-merge/v1")
public interface CustomerMergeServiceClient{

    @PutMapping("/merge/{fqn}/{id}")
    void updateCustomer(@PathVariable("fqn") String fqn, @PathVariable("id") String id);

}
