package com.shuyun.kylin.customized.task.base.kafka.loader;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shuyun.kylin.customized.task.base.kafka.config.AzureKafkaProperties;
import com.shuyun.kylin.customized.task.base.kafka.loader.enums.AzureKafkaConfigEnum;
import com.shuyun.motor.common.cons.PropsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerConfig;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * @author: Jingwei
 * @date: 2024-12-19
 */
@Slf4j
public class AzureKafkaPropertiesLoader {

    private static final String KAFKA_CONFIG_ENV = PropsUtil.getSysOrEnv("ko.kafka.config.env", "dev");

    private final String resourceKey;
    private String resourcePath;

    public AzureKafkaPropertiesLoader(String resourceKey) {
        this.resourceKey = resourceKey;
        this.initResourcePath();
    }

    public Map<String, Object> getConfiguration() {
        AzureKafkaProperties azureKafkaProperties = loadAzureKafkaProperties();
        Map<String, Object> configProps = new HashMap<>();
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, azureKafkaProperties.getBootstrapServers());
        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        configProps.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, azureKafkaProperties.getTimeout()); // 连接超时时间
        configProps.put(ProducerConfig.RETRIES_CONFIG, 3); // 重试次数
        configProps.put(ProducerConfig.LINGER_MS_CONFIG, 1000); // 等待时间
        configProps.put("metadata.max.age.ms", 180000); // 元数据的最大年龄
        configProps.put("connections.max.idle.ms", 180000); // 连接的最大空闲时间
        configProps.put("max.request.size", 1000000); // 最大请求大小
        //configProps.put("compression.type", "gzip"); // 压缩类型
        configProps.put("socket.keepalive.enable", true); // 启用 TCP KeepAlive

        configProps.put("security.protocol", azureKafkaProperties.getProtocol()); // 安全协议
        configProps.put("sasl.mechanism", azureKafkaProperties.getMechanism()); // 使用的 SASL 机制
        configProps.put("sasl.jaas.config", azureKafkaProperties.getJaasConfig()); // SASL 配置

        return configProps;
    }

    public synchronized AzureKafkaProperties loadAzureKafkaProperties() {

        log.info("loadAzureKafkaProperties, resourceKey={}, resourcePath={}", this.resourceKey, this.resourcePath);
        InputStream resource = getClass().getClassLoader().getResourceAsStream(this.resourcePath);
        Properties properties = new Properties();
        Map<String, String> configMap = new HashMap<>();
        try {
            properties.load(resource);
            for (String key : properties.stringPropertyNames()) {
                configMap.put(key, properties.getProperty(key));
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            if (resource != null) {
                try {
                    resource.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        log.info("loadAzureKafkaProperties, configMap={}", JSON.toJSONString(configMap).substring(0,20));
        return JSON.parseObject(JSON.toJSONString(configMap), AzureKafkaProperties.class);
    }
    private void initResourcePath() {
        AzureKafkaConfigEnum azureKafkaConfigEnum = AzureKafkaConfigEnum.findByCode(this.resourceKey);
        if (ObjectUtil.isNull(azureKafkaConfigEnum)) {
            throw new IllegalArgumentException("kafka sourceKey is not valid");
        }
        if(ObjectUtil.isEmpty(KAFKA_CONFIG_ENV)) {
            throw new IllegalArgumentException("kafka_config_env is not valid");
        }
        switch (KAFKA_CONFIG_ENV) {
            case "dev":
                this.resourcePath = azureKafkaConfigEnum.getDevEnv();
                log.info("initResourcePath, dev-resourcePath={}", this.resourcePath);
                break;
            case "prod":
                this.resourcePath = azureKafkaConfigEnum.getProdEnv();
                log.info("initResourcePath, prod-resourcePath={}", this.resourcePath);
                break;
            default:
                throw new IllegalArgumentException("kafka_config_env is not valid");
        }
    }
}
