package com.shuyun.kylin.customized.task.medal.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.kylin.customized.task.base.common.ModelConstants;
import com.shuyun.kylin.customized.task.base.config.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.task.base.feign.client.LoyaltyFacade;
import com.shuyun.kylin.customized.task.base.kafka.producer.coupon.MedalObtainProducer;
import com.shuyun.kylin.customized.task.base.util.MD5Util;
import com.shuyun.kylin.customized.task.ma.util.DataApiUtil;
import com.shuyun.kylin.customized.task.ma.util.DataapiSdkUtil;
import com.shuyun.kylin.customized.task.medal.request.MedalObtainRequest;
import com.shuyun.kylin.customized.task.medal.request.MedalProgressRequest;
import com.shuyun.kylin.customized.task.medal.response.CampaignRecordResponse;
import com.shuyun.kylin.customized.task.medal.response.PointRecordResponse;
import com.shuyun.kylin.customized.task.medal.service.MedalService;
import com.shuyun.lite.context.GlobalContext;
import com.shuyun.motor.common.cons.PropsUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class MedalServiceImpl implements MedalService {
    @Autowired
    private static MedalObtainProducer medalObtainProducer;
    final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();

    private final JdbcTemplate jdbcTemplate;
    private static final String tenantId = GlobalContext.defTenantId();
    private static final String dataModel = tenantId+"_datamodel";

    /**
     * 忠诚度接口
     */
    @Autowired
    private  LoyaltyFacade loyaltyFacade;

    public MedalServiceImpl(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }


    /**
     * 根据参与活动计算勋章进度
     * 1.累计两次（2周）
     * 2.连续4周
     * 3.累计3个月（13周）
     * 4.累计12个月（50周）
     * 会员日的周期：周日00:00到下周六23:59:59
     * 1.累计两次（2周）： 累加起来参与2周会员日（不要求连续）
     * 2.连续4周：持续不间断的连着参与4周会员日
     * 3.累计3个月（13周）：累加起来参与13周会员日（不要求连续）
     * 4.累计12个月（50周）：累加起来参与50周会员日（不要求连续）
     * @param medalProgressRequest
     */
    public void medalProgressByCampaignConsumer(MedalProgressRequest medalProgressRequest) {
        log.info("medalProgressByCampaignConsumer...medalProgressRequest:{}", JSONObject.toJSONString(medalProgressRequest));
        String memberId = medalProgressRequest.getMemberId();
        Integer getMedalDefinitionId = medalProgressRequest.getMedalDefinitionId();
        String campaignId = medalProgressRequest.getCampaignId();
        BaseResponse medalRuleResponse = getBaseResponse(getMedalDefinitionId, memberId);
        if (medalRuleResponse == null) return;
        List<Map<String, Object>> medalRuleData = medalRuleResponse.getData();
        //勋章id对应的门槛
        int countStart = Integer.parseInt(medalRuleData.get(0).get("countStart").toString());
        Integer medalDefinitionId = Integer.valueOf(medalRuleData.get(0).get("medalDefinitionId").toString());
        String medalDefinitionName = medalRuleData.get(0).get("medalDefinitionName").toString();
        //查询勋章规则类型 counts=累计参与次数  weeks=连续签到周 months=累计参与月数
        String ruleType = medalRuleData.get(0).get("ruleType").toString();

        //当前进度
        int taskProgress = 0;
        //根据勋章规则进行不同sql查询
        if ("counts".equals(ruleType)) {
            log.info("开始计算会员参与活动次数数据");
            //查询会员参与活动次数
            String getMemberCampaignByCountSql = String.format("select campaignId , count(1)  as count  from " + ModelConstants.MEMBER_CAMPAIGN + " where    memberId = '%s' and campaignId = '%s' ", memberId, campaignId);
            log.info("medalProgressByCampaignConsumer...getMemberCampaignByCountSql:{}", getMemberCampaignByCountSql);
            BaseResponse getMemberCampaignByCountResponse = dataapiHttpSdk.execute(getMemberCampaignByCountSql, Collections.emptyMap());
            if (CollectionUtils.isNotEmpty(getMemberCampaignByCountResponse.getData())) {
                List<Map<String, Object>> getMemberCampaignByCountData = getMemberCampaignByCountResponse.getData();
                taskProgress = Integer.parseInt(getMemberCampaignByCountData.get(0).get("count").toString());
            }
        }
//        //发放积分计算勋章
//        if ("pointCounts".equals(ruleType)) {
//            //01 累计成功扫盖20次UTC活动产品即可获得徽章
//            //02 累计成功扫盖50次UTC活动产品即可获得徽章
//            log.info("开始计算会员发放积分勋章");
//            //查询会员参与活动次数
//            String getMemberPointRecordsByCountSql = String.format("select count(1)  as count  from " + ModelConstants.POINT_RECORD + "" +
//                    " where  created >='%s' and  memberId = '%s'  and recordType='%s' and `desc` in (%s) ",
//                    medalProgressRequest.getCreated(), memberId,medalProgressRequest.getRecordType(),medalProgressRequest.getDesc());
//            log.info("medalProgressByCampaignConsumer...getMemberPointRecordsByCountSql:{}", getMemberPointRecordsByCountSql);
//            BaseResponse execute = dataapiHttpSdk.execute(getMemberPointRecordsByCountSql, Collections.emptyMap());
//            if (CollectionUtils.isNotEmpty(execute.getData())) {
//                List<Map<String, Object>> getMemberPointRecordByCountData = execute.getData();
//                taskProgress = Integer.parseInt(getMemberPointRecordByCountData.get(0).get("count").toString());
//            }
//        }
        /**
         * 2.连续4周
         */
        if ("weeks".equals(ruleType)) {

            try{
            // 获取本周往前推 3 周的日期范围
            List<LocalDateTime> lastThreeWeeksRange = getLastThreeWeeksRange();

            LocalDateTime  startDate = lastThreeWeeksRange.get(0);
            LocalDateTime  endDate = lastThreeWeeksRange.get(1);

            log.info("memberid:{},startDate:{},endDate:{}",memberId,startDate,endDate);

            // 过滤出时间范围内的活动日期
            String getMemberCampaignByWeekSql = "select DATE_FORMAT(add_hours(createTime, 8),'yyyy-MM-dd') as createTime from " + ModelConstants.MEMBER_CAMPAIGN + " where    memberId = '" + memberId + "' and campaignId = '" + campaignId + "'  and createTime  BETWEEN  '" + startDate + "' and '" + endDate + "' ";
            log.info("计算连续参与周数sql...getMemberCampaignByWeekSql:{}", getMemberCampaignByWeekSql);
            BaseResponse getMemberCampaign = dataapiHttpSdk.execute(getMemberCampaignByWeekSql, Collections.emptyMap());
            if (CollectionUtils.isNotEmpty(getMemberCampaign.getData())) {
                List<CampaignRecordResponse> getMemberCampaignByWeek = getMemberCampaign.getData();
                List<CampaignRecordResponse> memberCampaignByWeekJson = JSON.parseArray(JSON.toJSONString(getMemberCampaignByWeek), CampaignRecordResponse.class);
                log.info("计算连续参与周数：getMemberCampaignByWeek:{}", JSONObject.toJSONString(getMemberCampaignByWeek));
                // 计算连续参与周数
                List<LocalDate> campaignByWeek = new ArrayList<>();
                for (CampaignRecordResponse recordResponse : memberCampaignByWeekJson) {
                    campaignByWeek.add(LocalDate.parse(recordResponse.getCreateTime()));
                }
                log.info("计算连续参与周数memberId:{},campaignByWeek:{}", JSONObject.toJSONString(campaignByWeek));
                int consecutiveWeeks = calculateConsecutiveWeeks(campaignByWeek);
                taskProgress = consecutiveWeeks;
                log.info("计算连续参与周数memberId:{},结果:{}",memberId,consecutiveWeeks);

            }
            }catch (Exception e) {
                log.error("计算连续参与周数结果...异常:{}", e.getMessage());
            }
        }

        /**
         * 1.累计两次（2周）
         * 3.累计3个月（13周）
         * 4.累计12个月（50周）
         */
        if ("months".equals(ruleType)) {
            String memberCampaignTableName = dataModel+"."+  PropsUtil.getSys("datamodel.member.campaign");
            String getMemberCampaignByWeekSql =
                    "SELECT DATE_FORMAT(DATE_SUB(CONVERT_TZ(createTime, '+00:00', '+08:00'), INTERVAL (WEEKDAY(CONVERT_TZ(createTime, '+00:00', '+08:00')) + 1) % 7 + 1 DAY), '%Y-%m-%d') AS week_start, " +
                            "DATE_FORMAT(DATE_SUB(CONVERT_TZ(createTime, '+00:00', '+08:00'), INTERVAL (WEEKDAY(CONVERT_TZ(createTime, '+00:00', '+08:00')) + 1) % 7 + 1 DAY) + INTERVAL 6 DAY, '%Y-%m-%d') AS week_end, " +
                            "COUNT(id) AS count FROM " + memberCampaignTableName +
                            " WHERE memberId = ? AND campaignId = ? GROUP BY week_start, week_end ORDER BY week_start";
            log.info("medalProgressByCampaignConsumer...getMemberCampaignByWeekSql: {}", getMemberCampaignByWeekSql);
            // 查询会员累计签到周数
            List<Map<String, Object>> resultList = jdbcTemplate.queryForList(getMemberCampaignByWeekSql, memberId, campaignId);
            if (CollectionUtils.isNotEmpty(resultList)) {
                log.info("medalProgressByCampaignConsumer...resultList:{}", JSONObject.toJSONString(resultList));
                taskProgress = resultList.size();
            }
        }

        /**
         * 1. by活动类型累计参与3次
         */
        if ("orderby".equals(ruleType)){
            List<String> list = Stream.of(medalProgressRequest.getActivityId().split("_"))
                    .collect(Collectors.toList());
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("memberId", memberId);
            queryMap.put("campaignId", campaignId);
            queryMap.put("activityId", list);
            log.info("by活动类型累计参与getActivityId:{}",JSON.toJSONString(list));
            String queryMemberSql = " select activityId from " + ModelConstants.MEMBER_CAMPAIGN + " where memberId =:memberId and campaignId =:campaignId and activityId in (:activityId) group by activityId  ";
            BaseResponse getMemberCampaignByCountResponse = dataapiHttpSdk.execute(queryMemberSql, queryMap);
            log.info("by活动类型累计参与sql...getMemberCampaignByCountResponse:{}", JSONObject.toJSONString(getMemberCampaignByCountResponse));
            if (CollectionUtils.isNotEmpty(getMemberCampaignByCountResponse.getData())) {
                List<Map<String, Object>> getMemberCampaignByCountData = getMemberCampaignByCountResponse.getData();
                log.info("by活动类型累计...resultList:{}", JSONObject.toJSONString(getMemberCampaignByCountData));
                taskProgress = getMemberCampaignByCountData.size();
            }
        }

        upsertMedalTask(memberId, medalDefinitionId, medalDefinitionName, taskProgress, countStart,medalProgressRequest.getIsMedalObtain());
    }




    /**
     * 根据消耗积分计算勋章进度
     * @param medalProgressRequest
     */
    @Override
    public void medalProgressByPointRecordConsumer(MedalProgressRequest medalProgressRequest) {
        log.info("medalProgressByPointRecordConsumer...medalProgressRequest:{}", JSONObject.toJSONString(medalProgressRequest));
        String memberId = medalProgressRequest.getMemberId();
        Integer medalDefinitionId = medalProgressRequest.getMedalDefinitionId();
        BaseResponse medalRuleResponse = getBaseResponse(medalDefinitionId, memberId);
        if (medalRuleResponse == null) return;
        List<Map<String, Object>> medalRuleData = medalRuleResponse.getData();
        //勋章id对应的门槛
        int countStart = Integer.parseInt(medalRuleData.get(0).get("countStart").toString());
        String medalDefinitionName = medalRuleData.get(0).get("medalDefinitionName").toString();

        //查询积分
        int  pointRecordChangePoint =0;
        String getPointRecordSql = String.format("select sum(changePoint) as changePoint from "+ModelConstants.POINT_RECORD+" where changePoint < 0  and created >= '%s' and memberId = '%S' and status not in ('FROZEN','EXPIRE')  and recordType  not in ('REVERSE_DEDUCT','RECALCULATE')  " ,medalProgressRequest.getCreated(),medalProgressRequest.getMemberId());
        log.info("medalProgressByPointRecordConsumer...getPointRecordSql:{}",getPointRecordSql);
        BaseResponse pointRecordExe = dataapiHttpSdk.execute(getPointRecordSql, Collections.emptyMap());
        if (CollectionUtils.isEmpty(pointRecordExe.getData())) {
            log.error("medalProgress...memberId:{},medalDefinitionId:{}", memberId,medalDefinitionId);
        }else {
            List<Map> data = pointRecordExe.getData();
            if (ObjectUtil.isNotEmpty(data.get(0).get("changePoint"))){
                String points = data.get(0).get("changePoint").toString();
                pointRecordChangePoint = (int) Double.parseDouble(points);
            }
        }
        log.info("medalProgressByPointRecordConsumer...pointRecordChangePoint:{}",pointRecordChangePoint);
        int  pointRecordReverseSendPoint =0;
        String  getPointRecordReverseSendSql =  String.format("select sum(changePoint) as changePoint from "+ModelConstants.POINT_RECORD+" where changePoint >  0  and created >= '%s' and memberId = '%S' and   recordType ='REVERSE_SEND'  " ,medalProgressRequest.getCreated(),medalProgressRequest.getMemberId());
        log.info("medalProgressByPointRecordConsumer...getPointRecordReverseSendSql:{}",getPointRecordReverseSendSql);
        BaseResponse pointRecordReverseSendExe = dataapiHttpSdk.execute(getPointRecordReverseSendSql, Collections.emptyMap());
        if (CollectionUtils.isEmpty(pointRecordReverseSendExe.getData())) {
            log.error("medalProgress...memberId:{},medalDefinitionId:{}", memberId,medalDefinitionId);
        }else {
            List<Map> data = pointRecordReverseSendExe.getData();
            if(ObjectUtil.isNotEmpty(data.get(0).get("changePoint"))){
                String points = data.get(0).get("changePoint").toString();
                pointRecordReverseSendPoint = (int) Double.parseDouble(points);
            }
        }
        log.info("medalProgressByPointRecordConsumer...pointRecordReverseSendPoint:{}",pointRecordReverseSendPoint);
        //当前进度
        int taskProgress = Math.abs(pointRecordChangePoint+pointRecordReverseSendPoint);
        upsertMedalTask(memberId,medalDefinitionId,medalDefinitionName,taskProgress,countStart,medalProgressRequest.getIsMedalObtain());
    }

    @Override
    public void initHisMemberRegisterMedalConsumer(MedalObtainRequest medalObtainRequest) {
        //调用内部发放勋章接口
        try {
            loyaltyFacade.medalObtain(medalObtainRequest);
            log.info("initHisMemberRegisterMedalConsumer...会员勋章完成:{}", JSONObject.toJSONString(medalObtainRequest));
        } catch (Exception e) {
            log.error("initHisMemberRegisterMedalConsumer...会员勋章完成异常:{}", e);
        }
    }

    /**
     * 根据勋章id计算勋章获取总数
     * @param medalProgressRequest
     */
    @Override
    public void medalCountsConsumer(MedalProgressRequest medalProgressRequest) {
        Integer medalDefinitionId = medalProgressRequest.getMedalDefinitionId();
        String getMedalCounts = "select  count(id) as counts  from  "+ ModelConstants.MEMBER_MEDAL+"   where medalDefinitionId=:medalDefinitionId  and disabled=:disabled ";
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("medalDefinitionId",medalDefinitionId);
        queryMap.put("disabled","false");
        log.info("medalCountsConsumer...getMedalCounts:{}", getMedalCounts);
        List<Map<String, String>> lists = DataApiUtil.queryAnalysis(getMedalCounts, queryMap);
        log.info("medalCountsConsumer...lists:{}", lists);
//        BaseResponse baseResponse = dataapiHttpSdk.execute(getMedalCounts, Collections.emptyMap());
//        List<Map<String, Object>> data = baseResponse.getData();
        int counts =0;
        for (Map<String, String> list : lists) {
            counts = Integer.parseInt(String.valueOf(list.get("counts")));
        }
        log.info("medalCountsConsumer...counts:{}", counts);
        Map<String, Object> map = new HashMap<>();
        String id = String.valueOf(medalDefinitionId);
        map.put("medalCounts", counts);
        map.put("id", id);
        dataapiHttpSdk.update(ModelConstants.MEDAL_RULE, id, map, false);
    }

    @Override
    public void medalCouponConsumer(MedalProgressRequest medalProgressRequest) {
        log.info("权益核销计算勋章进度...:{}", JSONObject.toJSONString(medalProgressRequest));
        String memberId = medalProgressRequest.getMemberId();
        Integer getMedalDefinitionId = medalProgressRequest.getMedalDefinitionId();
        BaseResponse medalRuleResponse = getBaseResponse(getMedalDefinitionId, memberId);
        if (medalRuleResponse == null) return;
        List<Map<String, Object>> medalRuleData = medalRuleResponse.getData();
        //勋章id对应的门槛
        int countStart = Integer.parseInt(medalRuleData.get(0).get("countStart").toString());
        Integer medalDefinitionId = Integer.valueOf(medalRuleData.get(0).get("medalDefinitionId").toString());
        String medalDefinitionName = medalRuleData.get(0).get("medalDefinitionName").toString();
        //查询勋章规则类型 counts=累计参与次数
        String ruleType = medalRuleData.get(0).get("ruleType").toString();

        //当前进度
        int taskProgress = 0;
        //根据勋章规则进行不同sql查询
        if ("counts".equals(ruleType)) {

            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("holder", memberId);
            queryMap.put("state", "USED");
            queryMap.put("projectId", medalProgressRequest.getProjectId());
            String querySql = " select count(1) AS count from " + ConfigurationCenterUtil.COUPON_INSTANCE_MODEL + " where holder = :holder and state=:state and projectId in (:projectId) ";
            //查询会员参与活动次数
            BaseResponse getMemberCampaignByCountResponse = dataapiHttpSdk.execute(querySql, queryMap);
            log.info("勋章权益核销计算的结果:{}",JSON.toJSONString(getMemberCampaignByCountResponse.getData()));
            if (CollectionUtils.isNotEmpty(getMemberCampaignByCountResponse.getData())) {
                List<Map<String, Object>> getMemberCampaignByCountData = getMemberCampaignByCountResponse.getData();
                taskProgress = Integer.parseInt(getMemberCampaignByCountData.get(0).get("count").toString());
            }
        }
        //写入当前进去
        upsertMedalTask(memberId,medalDefinitionId,medalDefinitionName,taskProgress,countStart,medalProgressRequest.getIsMedalObtain());
    }


    /**
     * 根据发放积分获取勋章进度逻辑
     *
     * @param medalProgressRequest
     */
    @Override
    public void medalProgressBySendPointRecordConsumer(MedalProgressRequest medalProgressRequest) {
        log.info("medalProgressBySendPointRecordConsumer...medalProgressRequest:{}", JSONObject.toJSONString(medalProgressRequest));
        String memberId = medalProgressRequest.getMemberId();
        Integer getMedalDefinitionId = medalProgressRequest.getMedalDefinitionId();
        BaseResponse medalRuleResponse = getBaseResponse(getMedalDefinitionId, memberId);
        if (medalRuleResponse == null) return;
        List<Map<String, Object>> medalRuleData = medalRuleResponse.getData();
        //勋章id对应的门槛
        int countStart = Integer.parseInt(medalRuleData.get(0).get("countStart").toString());
        Integer medalDefinitionId = Integer.valueOf(medalRuleData.get(0).get("medalDefinitionId").toString());
        String medalDefinitionName = medalRuleData.get(0).get("medalDefinitionName").toString();
        //查询勋章规则类型 pointSendCounts=累计参与次数  pointSendWeeks=连续签到周 months=累计参与月数
        String ruleType = medalRuleData.get(0).get("ruleType").toString();

        //当前进度
        int taskProgress = 0;
        //发放积分次数计算勋章
        if ("pointSendCounts".equals(ruleType)) {
            //01 累计成功扫盖20次UTC活动产品即可获得徽章
            //02 累计成功扫盖50次UTC活动产品即可获得徽章
            log.info("medalProgressBySendPointRecordConsumer...开始计算会员勋章:{}", getMedalDefinitionId);
            //查询会员参与活动次数
            String getMemberPointRecordsByCountSql = String.format("select count(1)  as count  from " + ModelConstants.POINT_RECORD + "" +
                            " where  created >='%s' and  memberId = '%s'  and recordType='%s' and `desc` in (%s) ",
                    medalProgressRequest.getCreated(), memberId, medalProgressRequest.getRecordType(), medalProgressRequest.getDesc());
            log.info("medalProgressBySendPointRecordConsumer...getMemberPointRecordsByCountSql:{}", getMemberPointRecordsByCountSql);
            BaseResponse execute = dataapiHttpSdk.execute(getMemberPointRecordsByCountSql, Collections.emptyMap());
            if (CollectionUtils.isNotEmpty(execute.getData())) {
                List<Map<String, Object>> getMemberPointRecordByCountData = execute.getData();
                taskProgress = Integer.parseInt(getMemberPointRecordByCountData.get(0).get("count").toString());
            }
        }
        //发放积分总数计算勋章
        if ("pointSendSum".equals(ruleType)) {
            //每天有乐消费累计获得5个快乐瓶(积分)
            //每天有乐消费累计获得30个快乐瓶(积分)
            log.info("medalProgressBySendPointRecordConsumer...开始计算会员勋章:{}", getMedalDefinitionId);
            //查询会员参与活动次数
            String getMemberPointRecordsByCountSql = String.format("select sum(changePoint)  as sumPoint  from " + ModelConstants.POINT_RECORD + "" +
                            " where  created >='%s' and  memberId = '%s'  and recordType='%s' and `desc` in (%s) ",
                    medalProgressRequest.getCreated(), memberId, medalProgressRequest.getRecordType(), medalProgressRequest.getDesc());
            log.info("medalProgressBySendPointRecordConsumer...getMemberPointRecordsByCountSql:{}", getMemberPointRecordsByCountSql);
            BaseResponse execute = dataapiHttpSdk.execute(getMemberPointRecordsByCountSql, Collections.emptyMap());
            if (CollectionUtils.isNotEmpty(execute.getData())) {
                List<Map<String, Object>> getMemberPointRecordByCountData = execute.getData();
                BigDecimal sumPoint = new BigDecimal(getMemberPointRecordByCountData.get(0).get("sumPoint").toString());
                taskProgress = sumPoint.intValue();
            }
        }
        /**
         * 2.连续4周
         */
        if ("pointSendWeeks".equals(ruleType)) {
            try {
                // 获取本周往前推 3 周的日期范围
                HashMap<String, LocalDateTime> lastThreeWeeksRangeByDays = getLastThreeWeeksRangeByDays(7, medalProgressRequest.getCreated());
                String pattern = "yyyy-MM-dd'T'HH:mm:ss";
                // 定义一个日期时间格式器以指定输出格式
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                String  startDate = lastThreeWeeksRangeByDays.get("startDate").format(formatter);
                LocalDateTime endDate = lastThreeWeeksRangeByDays.get("endDate");
                log.info("memberId:{},startDate:{},endDate:{}", memberId, startDate, endDate);
                // 过滤出时间范围内的活动日期
                String getMemberSendPointByWeekSql = "select DATE_FORMAT(add_hours(created, 8),'yyyy-MM-dd') as created from " + ModelConstants.POINT_RECORD + " where    memberId = '" + memberId + "' and  `desc` = '" + medalProgressRequest.getDesc() + "'  and created  BETWEEN  '" + startDate + "' and '" + endDate + "' ";
                log.info("发放积分计算连续参与周数sql...getMemberSendPointByWeekSql:{}", getMemberSendPointByWeekSql);
                BaseResponse getMemberSendPointRecordByWeek = dataapiHttpSdk.execute(getMemberSendPointByWeekSql, Collections.emptyMap());
                if (CollectionUtils.isNotEmpty(getMemberSendPointRecordByWeek.getData())) {
                    List<PointRecordResponse> getMemberSendPointByWeek = getMemberSendPointRecordByWeek.getData();
                    List<PointRecordResponse> memberCampaignByWeekJson = JSON.parseArray(JSON.toJSONString(getMemberSendPointByWeek), PointRecordResponse.class);
                    log.info("计算连续参与周数：getMemberSendPointByWeek:{}", JSONObject.toJSONString(memberCampaignByWeekJson));
                    // 计算连续参与周数
                    List<LocalDate> sendPointRecordByWeek = new ArrayList<>();
                    for (PointRecordResponse recordResponse : memberCampaignByWeekJson) {
                        sendPointRecordByWeek.add(LocalDate.parse(recordResponse.getCreated()));
                    }
                    log.info("发放积分计算连续参与周数memberId:{},sendPointRecordByWeek:{}", memberId, JSONObject.toJSONString(sendPointRecordByWeek));
                    int consecutiveWeeks = calculatePointSendConsecutiveWeeks(sendPointRecordByWeek);
                    taskProgress = consecutiveWeeks;
                    log.info("发放积分计算连续参与周数memberId:{},结果:{}", memberId, consecutiveWeeks);
                }
            } catch (Exception e) {
                log.error("发放积分计算连续参与周数结果...异常:{}，会员id:{}", e.getMessage(), memberId);
            }
        }

        upsertMedalTask(memberId, medalDefinitionId, medalDefinitionName, taskProgress, countStart,medalProgressRequest.getIsMedalObtain());
    }


    private static BaseResponse getBaseResponse(Integer getMedalDefinitionId, String memberId) {
        //根据入参medalDefinitionId查询对应规则主数据
        String getMedalRuleSql = String.format("select medalDefinitionId, medalDefinitionName, countStart,ruleType from " + ModelConstants.MEDAL_RULE + " where medalDefinitionId =%s and typeData ='medalRule' ", getMedalDefinitionId);
        log.info("medalProgress...getMedalRuleSql:{}", getMedalRuleSql);
        BaseResponse medalRuleResponse = dataapiHttpSdk.execute(getMedalRuleSql, Collections.emptyMap());
        if (CollectionUtils.isEmpty(medalRuleResponse.getData())) {
            log.error("medalProgress...memberId:{},medalDefinitionId:{}", memberId, getMedalDefinitionId);
            return null;
        }
        log.info("getBaseResponse...medalRuleResponse:{}", JSONObject.toJSONString(medalRuleResponse.getData()));
        return medalRuleResponse;
    }


    private void upsertMedalTask(String memberId, Integer medalDefinitionId, String medalDefinitionName, int taskProgress, int countStart,String  isMedalObtain) {
        log.info("开始写入勋章进度模型...memberId:{},medalDefinitionId:{},medalDefinitionName:{},taskProgress:{},countStart:{}",memberId,medalDefinitionId,medalDefinitionName,taskProgress,countStart);
        //写入任务进度模型
        JSONObject jsonObject = new JSONObject();
        String id = MD5Util.encrypt(memberId + medalDefinitionId);
        jsonObject.put("id", id);
        jsonObject.put("memberId", memberId);
        jsonObject.put("medalDefinitionId", medalDefinitionId);
        jsonObject.put("medalDefinitionName", medalDefinitionName);
        jsonObject.put("taskThresholdValue", countStart);
        //判断进度是否满足
        String isFinished = "N";
        if (taskProgress >= countStart) {
            taskProgress=countStart;
            isFinished = "Y";
        }
        jsonObject.put("taskProgress", taskProgress);
        jsonObject.put("isFinished", isFinished);
        jsonObject.put("lastSync", ZonedDateTime.now());
        log.info("medalProgress...upsert:{}", jsonObject);
        //如果勋章进度门槛为1的 不需要存储进度只需要发勋章
        dataapiHttpSdk.upsert(ModelConstants.MEDAL_TASK, id, jsonObject, false);
        if ("Y".equals(isFinished) && "Y".equals(isMedalObtain)){
            //发放对应的勋章
            MedalObtainRequest medalObtainRequest = new MedalObtainRequest();
            medalObtainRequest.setMemberId(memberId);
            medalObtainRequest.setMedalDefinitionId(medalDefinitionId);
            medalObtainRequest.setMedalHierarchyId(ConfigurationCenterUtil.medalHierarchyId);
            medalObtainRequest.setChannelType("KO_MP");
            try {
                log.info("请求忠诚度接口发放勋章入参medalObtainRequest:{}", JSONObject.toJSONString(medalObtainRequest));
                loyaltyFacade.medalObtain(medalObtainRequest);
            }catch (Exception  e){
                log.error("请求忠诚度接口发放勋章异常:medalObtainRequest:{},e:{}",JSONObject.toJSONString(medalObtainRequest),e);
            }
        }
    }

    // 计算给定时间戳所在周的起始日期（星期天）
    public static LocalDate getWeekStart(LocalDateTime timestamp) {
        return timestamp.toLocalDate().with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY));
    }

    // 获取本周往前推 3 周的日期范围（包含本周和前 3 周的完整天数）
    public static List<LocalDateTime> getLastThreeWeeksRange() {
        LocalDateTime now = LocalDateTime.now(); // 当前时间（UTC）
        LocalDate startOfCurrentWeek = getWeekStart(now);
        LocalDate startOfThreeWeeksAgo = startOfCurrentWeek.minusWeeks(4);

        List<LocalDateTime> range = new ArrayList<>();
        range.add(startOfThreeWeeksAgo.plusDays(6).atTime(15, 59, 59)); // 起始时间（3 周前的星期六 23:59:59）
        range.add(startOfCurrentWeek.plusDays(6).atTime(15, 59, 59)); // 结束时间（本周的星期六 23:59:59.999）
        log.info("getWeekStart...range:{}",JSONObject.toJSONString(range));
        return range;
    }


    // 获取本周往前推 3 周的日期范围（包含本周和前 3 周的完整天数）
    public static HashMap<String, LocalDateTime> getLastThreeWeeksRangeByDays(long  days,String created) {
        LocalDateTime now = LocalDateTime.now(); // 当前时间（UTC）
        log.info("getLastThreeWeeksRangeByDays...now:{}",now);
        // 获取本周日的结束时间
        LocalDate endOfCurrentWeek = getWeekEnd(now);
        LocalDateTime endTime = endOfCurrentWeek.atTime(15, 59, 59);
        LocalDateTime startTime = endTime.minusDays(28);
        HashMap<String, LocalDateTime> map = new HashMap<>();
        if (startTime.isBefore(LocalDateTime.parse(created))){
            map.put("startDate",LocalDateTime.parse(created));
        }else {
            map.put("startDate",startTime);
        }
        map.put("endDate",endTime);
        log.info("getLastThreeWeeksRangeByDays...map:{}",JSONObject.toJSONString(map));
        return map;
    }

    public static LocalDate getWeekEnd(LocalDateTime dateTime) {
        return dateTime.toLocalDate().with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
    }

    // 计算连续积分发放的周数
    public static int calculatePointSendConsecutiveWeeks(List<LocalDate> activityDates) {
        if (activityDates == null || activityDates.isEmpty()) {
            return 0;
        }
       return   getCountsByWeek(activityDates,DayOfWeek.SUNDAY);
    }
//        // 去重并排序
//        Set<LocalDate> uniqueDates = new HashSet<>(activityDates);
//        List<LocalDate> sortedDates = new ArrayList<>(uniqueDates);
//        sortedDates.sort(LocalDate::compareTo);
//
//        // 转换为周的起始日期
//        Set<LocalDate> weekStarts = new HashSet<>();
//        for (LocalDate date : sortedDates) {
//            weekStarts.add(date.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)));
//        }
//        log.info("转换为周的起始日期-计算开始时间:{}",JSON.toJSONString(weekStarts));
//        // 将周的起始日期排序
//        List<LocalDate> sortedWeekStarts = new ArrayList<>(weekStarts);
//        sortedWeekStarts.sort(LocalDate::compareTo);
//
//        // 计算连续周数
//        int maxConsecutive = 1;
//        int currentConsecutive = 1;
//
//        for (int i = 1; i < sortedWeekStarts.size(); i++) {
//            LocalDate previousWeekStart = sortedWeekStarts.get(i - 1);
//            LocalDate currentWeekStart = sortedWeekStarts.get(i);
//
//            if (previousWeekStart.plusWeeks(1).equals(currentWeekStart)) {
//                currentConsecutive++;
//                if (currentConsecutive > maxConsecutive) {
//                    maxConsecutive = currentConsecutive;
//                }
//            } else {
//                currentConsecutive = 1;
//            }
//        }
//        log.info("计算连续周数-结果:{}",maxConsecutive);
//        return maxConsecutive;
//    }

    public static int calculateConsecutiveWeeks(List<LocalDate> activityDates) {
        if (activityDates == null || activityDates.isEmpty()) {
            return 0;
        }
         return   getCountsByWeek(activityDates,DayOfWeek.SATURDAY);
//        // 1. 去重并转换为周数（ISO标准周）
//        Set<Integer> weekNumbers = activityDates.stream()
//                .map(date -> date.get(WeekFields.ISO.weekOfWeekBasedYear()))
//                .collect(Collectors.toSet());
//
//        // 2. 排序周数
//        List<Integer> sortedWeeks = new ArrayList<>(weekNumbers);
//        Collections.sort(sortedWeeks);
//
//        // 3. 计算最大连续周数
//        int maxConsecutive = 1;
//        int currentConsecutive = 1;
//
//        for (int i = 1; i < sortedWeeks.size(); i++) {
//            if (sortedWeeks.get(i) == sortedWeeks.get(i-1) + 1) {
//                currentConsecutive++;
//                maxConsecutive = Math.max(maxConsecutive, currentConsecutive);
//            } else {
//                currentConsecutive = 1;
//            }
//        }
//
//        log.info("计算连续周数-周数列表:{}, 结果:{}", sortedWeeks, maxConsecutive);
//        return maxConsecutive;
    }


    private static   int getCountsByWeek(List<LocalDate> activityDates,DayOfWeek week) {
        int i = 0;
        boolean change;
        LocalDate endDate = LocalDate.now().with(TemporalAdjusters.nextOrSame(week));
        LocalDate startDate = endDate.minusDays(27);
        for (LocalDate periodEnd = endDate; periodEnd.isAfter(startDate); periodEnd = periodEnd.minusDays(7)) {
            LocalDate periodStart = periodEnd.minusDays(6);
            change = false;
            for (LocalDate activityDate : activityDates) {
                if (!activityDate.isBefore(periodStart) && !activityDate.isAfter(periodEnd)) {
                    i++;
                    change = true;
                    break;
                }
            }
            if (!change) {
                break;
            }
        }
        return i;
    }

}
