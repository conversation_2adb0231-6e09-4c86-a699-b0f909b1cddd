package com.shuyun.kylin.customized.task.ma.util;

import com.shuyun.motor.common.cons.PropsUtil;

/**
 * <AUTHOR>
 * @Date 2022/6/20
 */

public class ConfigurationCenterUtil {



    /**
     * 批量两卡券推送pms，单页数量
     */
    public static final Integer THREAD_SYNC_PMS_COUPON_PAGESIZE = Integer.parseInt(PropsUtil.getSysOrEnv("send.pms.coupon.pageSize", "20"));

    /**
     * 跳转小程序类型：developer为开发
     * 版；trial为体验版；formal为正式版；
     * 默认为正式版
     */
    public static final String CEP_MINIPROGRAM_STATE = PropsUtil.getSys("cep.miniProgram.state");


    /**
     * cep密钥
     */
    public static final String CEP_SECRED = PropsUtil.getSys("cep.secrec.key");

    /**
     * cep域名
     */
    public static final String CEP_URL = PropsUtil.getSys("cep.domain.url");

    /**
     * cepTOKEN
     */
    public static final String CEP_TOCKEN = PropsUtil.getSys("cep.domain.token");

    /**
     * cep用户名
     */
    public static final String CEP_USERNAME = PropsUtil.getSys("cep.userName");

    /**
     * cep密码
     */
    public static final String CEP_PASSWOED = PropsUtil.getSys("cep.password");

    /**
     * httpClient最大连接数
     */
    public static final int HTTPCLIENT_MAX_TOTAL = PropsUtil.getIntSys("httpclient.max.total", 200);

    /**
     * httpClient单链接最大连接数
     */
    public static final int HTTPCLIENT_MAX_TOTAL_ROUTE = PropsUtil.getIntSys("httpclient.max.total.route", 50);

    /**
     * httpClient ConnectTimeout连接超时
     */
    public static final int HTTPCLIENT_CONNECT_TIME_OUT = PropsUtil.getIntSys("httpclient.connect.time.out", 10000);




    /**
     * 自定义触达-发放权益券-活动记录
     */
    public static final String MEMBER_COUPON = PropsUtil.getSys("custom.made.coupon.model");
    /**
     * 自定义触达-发送小程序订阅消息-活动记录
     */
    public static final String MEMBER_SUBSCRIBE = PropsUtil.getSys("custom.made.applet.model");



    /**
     * cep 小程序请求地址
     */
    public static final String MEMBER_CEP_APPLET = PropsUtil.getSys("cep.applet.url");

    /**
     * 小程序订阅 核心线程数
     */
    public static final int THREAD__SUBSCRIBED_CORES = Integer.parseInt(PropsUtil.getSys("thread.subscribed.cores"));

    /**
     * 小程序订阅 队列数
     */
    public static final int THREAD_SUBSCRIBED_QUEUE = Integer.parseInt(PropsUtil.getSys("thread.subscribed.queue"));

    /**
     * 小程序订阅 分页数
     */
    public static final int THREAD_SUBSCRIBED_PAGESIZE = Integer.parseInt(PropsUtil.getSys("thread.subscribed.pageSize"));

    /**
     * 请求cep发订阅消息重试次数(-1:无限重试)
     */
    public static final int SUBSCRIPTION_RETRY_NUM = PropsUtil.getIntSys("subscription.retry.num", 3);

    /**
     * 请求cep发订阅消息重试时间间隔
     */
    public static final String SEND_SUBSCRIPTION_SLEEP = PropsUtil.getSysOrEnv("send.subscription.sleep", "1000");



    /**
     * cep 优惠券请求地址
     */
    public static final String MEMBER_CEP_COUPON = PropsUtil.getSys("cep.coupon.url");
    /**
     * 优惠券 核心线程数
     */
    public static final int THREAD__COUPONS_CORES = Integer.parseInt(PropsUtil.getSys("thread.coupon.cores"));

    /**
     * 优惠券 队列数
     */
    public static final int THREAD_COUPONS_QUEUE = Integer.parseInt(PropsUtil.getSys("thread.coupon.queue"));

    /**
     * 优惠券 分页数
     */
    public static final int THREAD_COUPONS_PAGESIZE = Integer.parseInt(PropsUtil.getSys("thread.coupon.pageSize"));





    /**
     * 批量下发回访活动核心线程数
     */
    public static final int THREAD_RETURNVISIT_CORES = Integer.parseInt(PropsUtil.getSysOrEnv("thread.returnvisit.cores", "2"));

    /**
     * 批量下发回访活动队列数
     */
    public static final int THREAD_RETURNVISIT_QUEUE = Integer.parseInt(PropsUtil.getSysOrEnv("thread.returnvisit.queue", "10"));

    /**
     * 批量下发回访活动单页数量数
     */
    public static final Integer THREAD_RETURNVISIT_PAGESIZE = Integer.parseInt(PropsUtil.getSysOrEnv("send.returnvisit.pageSize", "2000"));





}
