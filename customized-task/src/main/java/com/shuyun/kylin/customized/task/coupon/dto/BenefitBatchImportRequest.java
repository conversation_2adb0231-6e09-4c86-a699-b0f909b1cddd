package com.shuyun.kylin.customized.task.coupon.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class BenefitBatchImportRequest {

    private String fqn;

    private List<BenefitImports> benefitImports;


    @Data
    public static class BenefitImports {

        //卡券实例
        private Benefit benefit;

        //事务Id
        private String transactionId;

        //核销事务Id,反核销场景必传
        private String useTransactionId;

        // 锁定事务Id,解锁场景必传
        private String lockTransactionId;
    }

    @Data
    public static class Benefit {

        //券码
        private String code;

        //code
        private String id;

        //code
        private String externalCode;

        //人工设置失效时间
        private Boolean expiredManual;

        //人工设置生效时间
        private  Boolean   effectiveManual;

        //失效时间
        private String expiredAt;

        //生效时间
        private String effectiveAt;

        //启用时间
        private String activateAt;

        //方案ID
        private String program;

        //项目ID
        private String projectId;

        //项目名称
        private String projectName;

        //模板ID
        private String templateId;

        //状态
        private String state;


        //发放原因
        private String grantReason;

        //发放平台
        private String grantPlatform;

        //会员id
        private String holder;

        //创建人
        private String createBy;

        //创建时间
        private String createAt;

        //修改人
        private String updateBy;

        //修改时间
        private String updateAt;

        //核销时间
        private String useAt;

        //核销平台
        private String usePlatform;

        //发放时间
        private String grantAt;

        //使用成本中心
        private String usedCostCenterCode;

        //发放成本中心
        private String sendCostCenterCode;

        //活动id
        private String campaignId;

        //活动名称
        private String campaignName;

        //programId
        private String programId;

        //dataVersion
        private long dataVersion;

        //projectSnapshotId
        private String projectSnapshotId;

        //使用方式
        private String redeemType;


        //跳转参数
        private String jumpParameter;

    }
}
