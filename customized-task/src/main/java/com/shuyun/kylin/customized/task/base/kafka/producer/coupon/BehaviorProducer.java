package com.shuyun.kylin.customized.task.base.kafka.producer.coupon;


import com.shuyun.kylin.customized.task.base.kafka.dto.ReachBehaviorDto;
import com.shuyun.kylin.customized.task.base.kafka.messaging.KafkaBehavior;
import com.shuyun.kylin.customized.task.base.kafka.messaging.KafkaSink;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@EnableBinding({KafkaBehavior.class, KafkaSink.class})
@ConditionalOnExpression("${system.kafka.enabled:true}")
public class BehaviorProducer {

    @Autowired
    private KafkaBehavior kafkaBehavior;

    public void sendMsg(ReachBehaviorDto reachBehaviorDto){
//        log.info("【 发送到主动营销 Topic：marketing-action-custom-reach-behavior  开始=======> 送出参数: {} 】",reachBehaviorDto);
        kafkaBehavior.output().send(MessageBuilder.withPayload(reachBehaviorDto).build());
//        log.info("【 发送到主动营销 Topic：marketing-action-custom-reach-behavior 结束=======> 送出参数: {} 】", reachBehaviorDto);
    }
}
