package com.shuyun.kylin.customized.task.microsoftazure;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

/**
 * @author: Jingwei
 * @date: 2024-12-17
 */
@Slf4j
public class AzureDataReporter implements Runnable {

    private static final int NUM_MESSAGES = 100;
    private final String TOPIC;

    private Producer<Long, String> producer;

    public AzureDataReporter(final Producer<Long, String> producer, String TOPIC) {
        this.producer = producer;
        this.TOPIC = TOPIC;
    }

    @Override
    public void run() {
        for (int i = 0; i < NUM_MESSAGES; i++) {
            long time = System.currentTimeMillis();
            log.info("微软azure生产者-Test Data #{} from thread #{}", i, Thread.currentThread().getId());

            final ProducerRecord<Long, String> record = new ProducerRecord<>(TOPIC, time, "这是一条测试数据 ==> Test Data #" + i);
            Future<RecordMetadata> recordMetadataFuture = producer.send(record, (metadata, exception) -> {
                if (exception != null) {
                    log.info("微软azure生产者发送消息异常", exception);
                    System.exit(1);
                }
            });
            if(recordMetadataFuture.isDone()) {
                try {
                    log.info("微软azure生产者-Record written to offset {}, partition {}", recordMetadataFuture.get().offset(), recordMetadataFuture.get().partition());
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                } catch (ExecutionException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        log.info("微软azure生产者-Finished sending " + NUM_MESSAGES + " messages from thread #{}!", Thread.currentThread().getId());
    }
}
