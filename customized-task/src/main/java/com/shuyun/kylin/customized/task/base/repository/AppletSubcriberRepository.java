package com.shuyun.kylin.customized.task.base.repository;


import com.shuyun.kylin.customized.task.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.task.ma.dto.AppletSubscriberDto;
import com.shuyun.kylin.customized.task.ma.util.ConfigurationCenterUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class AppletSubcriberRepository extends BaseDsRepository<AppletSubscriberDto> {


    /**
     * 查询是否测试执行
     *
     * @param taskId
     * @return
     */
    public Boolean getTestask(String taskId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId", taskId);
        String queryMemberSql = " SELECT testRun FROM  " + ConfigurationCenterUtil.MEMBER_SUBSCRIBE + " where taskId = :taskId LIMIT 1 ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        Boolean count = true;
        if (!CollectionUtils.isEmpty(list)) {
            for (Map map : list) {
                count = (Boolean) map.get("testRun");
            }
        }
        return count;
    }

    /**
     * 查询小程序订阅
     *
     * @param taskId
     * @return
     */
    public List<AppletSubscriberDto> getAppletSubscribersBatch(String taskId, String index, int pageSize) {

        String change = " id > '" + index + "' order by id limit " + pageSize;

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId", taskId);
        String queryOrderSql = " SELECT id,templateId,title,fields1,fields2,fields3,fields4,fields5,fields6,fields7,campaignId,taskId,objectId," +
                "occId,nodeId,nodeName,campaignName,testRun,type,sceneCommen,sceneCampaign,landingPage,customerTheme  FROM  " + ConfigurationCenterUtil.MEMBER_SUBSCRIBE + "  where taskId = :taskId and " + change + "";
        log.info("查询小程序订阅taskid:{},index:{},sql:{}", taskId, index, queryOrderSql);
        List<AppletSubscriberDto> couponList = executeSQL(queryOrderSql, queryMap);
        return couponList;
    }

    /**
     * 查询小程序订阅
     *
     * @param taskId
     * @return
     */
    public List<AppletSubscriberDto> getAppletSubscribers(String taskId) {

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId", taskId);
        String queryOrderSql = " SELECT id,templateId,title,fields1,fields2,fields3,fields4,fields5,fields6,fields7,campaignId,taskId,objectId," +
                "occId,nodeId,nodeName,campaignName,testRun,type,sceneCommen,sceneCampaign,landingPage,customerTheme  FROM  " + ConfigurationCenterUtil.MEMBER_SUBSCRIBE + "  where taskId = :taskId ";
        log.info("查询小程序订阅taskid:{},sql:{}", taskId, queryOrderSql);
        List<AppletSubscriberDto> couponList = executeSQL(queryOrderSql, queryMap);
        return couponList;
    }


    /**
     * 查询小程序
     * @param taskId
     */
   /* public String getSubscriber(String taskId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId", taskId);
        String queryMemberSql = " select id from "+ ConfigurationCenterUtil.MEMBER_SUBSCRIBE + "  where taskId = :taskId LIMIT 1 ";
        List<Map<String,Object>> list = execute(queryMemberSql, queryMap).getData();
        String subscriberId = "0";
        for (Map map : list) {
            subscriberId = map.get("id").toString();
        }
        return subscriberId;
    }
*/

    /**
     * 查询小程序
     *
     * @param taskId
     */
    public Map<String, String> getSubscriber(String taskId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId", taskId);
        String queryMemberSql = " select id,count(1) as countSum from " + ConfigurationCenterUtil.MEMBER_SUBSCRIBE + "  where taskId = :taskId LIMIT 1";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        String id = "0";
        String countSum = "0";
        for (Map map : list) {
            id = String.valueOf(map.get("id"));
            countSum = String.valueOf(map.get("countSum"));
        }
        Map<String, String> hashMap = new HashMap<>();
        hashMap.put("id", id);
        hashMap.put("countSum", countSum);
        return hashMap;
    }
}
