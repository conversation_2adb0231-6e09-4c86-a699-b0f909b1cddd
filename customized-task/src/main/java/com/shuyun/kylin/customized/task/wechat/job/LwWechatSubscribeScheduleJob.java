package com.shuyun.kylin.customized.task.wechat.job;

import com.shuyun.kylin.customized.task.wechat.job.service.WechatService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.enums.JobInitiationEnum;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 获取联蔚小程序token
 */
@Slf4j
@Component
public class LwWechatSubscribeScheduleJob {

    @Autowired
    private WechatService wechatService;



    @XxlJob(value = "getWechatTokenScheduleJob", cron = "0 0 * * * ?", jobDesc = "获取联蔚小程序token", jobInitation = JobInitiationEnum.START)
    public ReturnT<String> getWechatTokenScheduleJob(String param) {
        wechatService.getWechatLwToken();
        return ReturnT.SUCCESS;
    }
}
