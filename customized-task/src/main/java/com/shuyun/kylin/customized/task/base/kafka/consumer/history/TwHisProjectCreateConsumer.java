package com.shuyun.kylin.customized.task.base.kafka.consumer.history;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.task.base.kafka.messaging.KafkaSink;
import com.shuyun.kylin.customized.task.coupon.service.InstanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

//tw历史卡券项目创建
@Slf4j
@Component
public class TwHisProjectCreateConsumer {
    @Autowired
    private InstanceService instanceService;

    @StreamListener(KafkaSink.KAFKA_TW_HIS_PROJECT_CREATE_INPUT)
    public void initTwHisProjectCreateConsumer(String values) {
        try {
            log.info("tw历史卡券项目创建开始:{}", values);
            instanceService.twHisTwHisProjectCreate(values);
        } catch (Exception e) {
            log.error("tw历史卡券项目创建失败:{}", JSON.toJSONString(values),e);
        }
    }
}
