package com.shuyun.kylin.customized.task.coupon.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.kylin.customized.task.base.common.ModelConstants;
import com.shuyun.kylin.customized.task.base.feign.client.BenefitMgmtClient;
import com.shuyun.kylin.customized.task.base.feign.client.BenefitServiceClient;
import com.shuyun.kylin.customized.task.base.util.DateHelper;
import com.shuyun.kylin.customized.task.coupon.dto.BenefitBatchImportRequest;
import com.shuyun.kylin.customized.task.coupon.response.BenefitBatchImportResponse;
import com.shuyun.kylin.customized.task.ma.util.DataapiSdkUtil;
import lombok.extern.slf4j.Slf4j;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import org.springframework.stereotype.Service;


@Service
@Slf4j
public class InstanceService {
    final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();

    // 数据服务DateTime格式
    public static final String DS_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

    @Resource
    private BenefitServiceClient benefitServiceClient;


    @Resource
    private BenefitMgmtClient benefitMgmtClient;

    public void twHisInstanceGrant(String values) {
        Map convert = JSONUtil.toBean(values, Map.class);
        BenefitBatchImportRequest request = new BenefitBatchImportRequest();
        request.setFqn(convert.get("fqn").toString());
        List<BenefitBatchImportRequest.BenefitImports> importRequests = new ArrayList<>();
        BenefitBatchImportRequest.BenefitImports benefitImportRequest = new BenefitBatchImportRequest.BenefitImports();
        BenefitBatchImportRequest.Benefit benefit = new BenefitBatchImportRequest.Benefit();
        benefit.setCode(convert.get("code").toString());
        String id = convert.get("id").toString();
        benefit.setId(id);
        benefit.setExternalCode(convert.get("externalCode") == null ? null : convert.get("externalCode").toString());
        benefit.setExpiredManual(true);
        benefit.setEffectiveManual(true);
        String state = convert.get("state").toString();
        if("USED".equals(state)){
            benefit.setUseAt(convert.get("useAt")== null ? null : convert.get("useAt").toString());
            benefit.setUsePlatform(convert.get("usePlatform")== null ? null : convert.get("usePlatform").toString());
        }
        //生效时间
        benefit.setEffectiveAt(convert.get("effectiveAt") == null ? null : convert.get("effectiveAt").toString());
        //启用时间
        benefit.setActivateAt(convert.get("activateAt")== null ? null : convert.get("activateAt").toString());
        //发放时间
        benefit.setGrantAt(convert.get("grantAt")== null ? null : convert.get("grantAt").toString());
        benefit.setCreateAt(convert.get("createAt").toString());
        benefit.setUpdateAt(convert.get("updateAt").toString());
        benefit.setProgramId(convert.get("programId").toString());
        benefit.setProjectId(convert.get("projectId").toString());
        benefit.setTemplateId(convert.get("templateId").toString());
        benefit.setState(state);
        benefit.setGrantReason(convert.get("grantReason").toString());
        benefit.setGrantPlatform(convert.get("grantPlatform").toString());
        benefit.setHolder(convert.get("holder")== null ? null : convert.get("holder").toString());
        benefit.setCreateBy(convert.get("createBy").toString());
        benefit.setUpdateBy(convert.get("updateBy").toString());
        benefit.setSendCostCenterCode(convert.get("sendCostCenterCode").toString());
        benefit.setUsedCostCenterCode(convert.get("usedCostCenterCode").toString());
        benefit.setCampaignId(convert.get("campaignId").toString());
        benefit.setCampaignName(convert.get("campaignName").toString());
        if (ObjectUtil.isNotEmpty(convert.get("jumpParameter"))){
            benefit.setJumpParameter(convert.get("jumpParameter").toString());
        }
        benefit.setRedeemType(convert.get("redeemType")== null ? null : convert.get("redeemType").toString());
        benefit.setDataVersion(Long.parseLong(convert.get("dataVersion").toString()));
        benefit.setProjectSnapshotId(convert.get("projectSnapshotId").toString());
        benefit.setProjectName(convert.get("projectName").toString());
        benefit.setExpiredAt(convert.get("expiredAt")== null ? null : convert.get("expiredAt").toString());
        benefitImportRequest.setBenefit(benefit);
        benefitImportRequest.setTransactionId(benefit.getCode());
        importRequests.add(benefitImportRequest);
        request.setBenefitImports(importRequests);
        try {
            log.info("tw历史卡券调用卡券服务开始:{}", JSONUtil.toJsonStr(request));
            BenefitBatchImportResponse benefitBatchImportResponse = benefitServiceClient.importBatch(request);
            log.info("tw历史卡券调用卡券服务返回:{}", JSONUtil.toJsonStr(benefitBatchImportResponse));
            if (ObjectUtil.isNotEmpty(benefitBatchImportResponse.getSuccess())){
                HashMap<String, Object> map = new HashMap<>();
                map.put("id",id);
                map.put("isFinished","Y");
                map.put("lastSync", DateHelper.getZone(DateHelper.getDateTimeFormat()));
                dataapiHttpSdk.update(ModelConstants.TW_HIS_INSTANCE,id,map,false);
            }
        } catch (Exception e) {
            log.error("tw历史卡券调用卡券服务失败:", e);
            throw new RuntimeException(e);
        }
    }


    public void twHisInstanceInsert(String request) {
        Map map = JSONUtil.toBean(request, Map.class);
        //根据configId查询出对应的券项目信息
        String configId  = map.get("configId").toString();
        String id = map.get("id").toString();
        String getConfigSql = String.format("select id,code as twProjectId ,name,activity_code as  campaignId ,activity_name as campaignName   from " + ModelConstants.TW_KO_USER_BENEFIT_CONFIG + "  where id = '%s' " ,configId);
        log.info("twHisInstanceInsert...getConfigSql:{}",getConfigSql);
        BaseResponse configResponse = dataapiHttpSdk.execute(getConfigSql, Collections.emptyMap());
        String twProjectId =null;
        if (CollectionUtils.isNotEmpty(configResponse.getData())) {
            List<Map<String, Object>> configResponseData = configResponse.getData();
            twProjectId = configResponseData.get(0).get("twProjectId").toString();
            map.put("campaignId",configResponseData.get(0).get("campaignId")) ;
            map.put("campaignName",configResponseData.get(0).get("campaignName")) ;
            map.put("projectId",configResponseData.get(0).get("twProjectId")) ;
        }

        /**
         * 2. 对应关系如下
         * ko_third_party_coupon.extra_info.benefitAcquireId （若有）对应 ko_user_benefit.acquire_id
         * ko_user_benefit.extra.thirdPartyCouponOrderId （若有）对应 ko_third_party_coupon.order_id
         */
        String extra = map.get("extra").toString();
        JSONObject extraJson = JSONObject.parseObject(extra);
        log.info("twHisInstanceInsert...extraJson:{}",extraJson);
        if (extraJson.containsKey("thirdPartyCouponOrderId")){
            //为了获取到三方券码
            // ko_user_benefit.extra.thirdPartyCouponOrderId （若有）对应 ko_third_party_coupon.order_id
            //关联查询 根据ko_user_benefit.extra.thirdPartyCouponOrderId字段 和tw_ko_third_party_coupon表中order_id
            String thirdPartyCouponOrderId = extraJson.getString("thirdPartyCouponOrderId");
            String getThirdPartyCouponSql = String.format("select extra_info as extraInfo,coupon_code as externalCode from " + ModelConstants.TW_KO_THIRD_PARTY_COUPON + "  where order_id = '%s' " ,thirdPartyCouponOrderId);
            log.info("twHisInstanceInsert...getThirdPartyCouponSql:{}",getThirdPartyCouponSql);
            BaseResponse thirdPartyCouponResponse = dataapiHttpSdk.execute(getThirdPartyCouponSql, Collections.emptyMap());
            if (CollectionUtils.isNotEmpty(thirdPartyCouponResponse.getData())) {
                List<Map<String, Object>> thirdPartyCouponResponseData = thirdPartyCouponResponse.getData();
                map.put("externalCode",thirdPartyCouponResponseData.get(0).get("externalCode"));
            }
        }
        //成本中心可以通过crm券项目获取
        //查询crm券项目数据
        String expiredPeriod =null;
        if (ObjectUtil.isNotEmpty(twProjectId)){
            String getCrmProjectSql = String.format("select  id,usedCostCenterCode, sendCostCenterCode ,template,title,expiredPeriod,program,jumpParameter,redeemType  from " + ModelConstants.OFFER_PROJECT + "  where twProjectId = '%s' " ,twProjectId);
            log.info("twHisInstanceInsert...getCrmProjectSql:{}",getCrmProjectSql);
            BaseResponse crmProjectResponse = dataapiHttpSdk.execute(getCrmProjectSql, Collections.emptyMap());
            if (CollectionUtils.isNotEmpty(crmProjectResponse.getData())) {
                List<Map<String, Object>> crmProjectResponseData = crmProjectResponse.getData();
                map.put("usedCostCenterCode",replaceCoup(crmProjectResponseData.get(0).get("usedCostCenterCode").toString()) );
                map.put("sendCostCenterCode",replaceCoup(crmProjectResponseData.get(0).get("sendCostCenterCode").toString()));
                map.put("projectId",crmProjectResponseData.get(0).get("id"));
                map.put("templateId",crmProjectResponseData.get(0).get("template"));
                map.put("projectName",crmProjectResponseData.get(0).get("title"));
                map.put("programId",crmProjectResponseData.get(0).get("program"));
                map.put("redeemType",crmProjectResponseData.get(0).get("redeemType"));
                map.put("jumpParameter",crmProjectResponseData.get(0).get("jumpParameter"));
                expiredPeriod = crmProjectResponseData.get(0).get("expiredPeriod").toString();
            }
        }


        String format = "yyyy-MM-dd HH:mm:ss";
        String effectiveAt = null;
        if (ObjectUtil.isNotEmpty(map.get("effectiveAt")) ) {
//            String effectiveAt1 = map.get("effectiveAt").toString();
//            String[] parts = effectiveAt1.split("\\.");
//            String dateTimeWithoutMillis = parts[0];
//            java.util.Date date = DateUtil.parse(dateTimeWithoutMillis, format);
//            effectiveAt = DateUtil.format(date, DS_DATE_FORMAT);
            try {
                String effectiveAt1 = map.get("effectiveAt").toString();
                if (ObjectUtil.isNotEmpty(effectiveAt1)) {
                    String[] parts = effectiveAt1.split("\\.");
                    String dateTimeWithoutMillis = parts[0];
                    java.util.Date date = DateUtil.parse(dateTimeWithoutMillis, format);
                    String formattedDateTime = DateUtil.format(date, DS_DATE_FORMAT);
                    map.put("effectiveAt", formattedDateTime);
                    effectiveAt= formattedDateTime;
                }
            } catch (Exception e) {
                log.error("twHisInstanceInsert...effectiveAt:{}", e);
            }
        }
        if (ObjectUtil.isNotEmpty(map.get("activateAt")) ){
            String activateAt = map.get("activateAt").toString();
            if (ObjectUtil.isNotEmpty(activateAt)) {
                String[] parts = activateAt.split("\\.");
                String dateTimeWithoutMillis = parts[0];
                java.util.Date date = DateUtil.parse(dateTimeWithoutMillis, format);
                String formattedDateTime = DateUtil.format(date, DS_DATE_FORMAT);
                map.put("activateAt",formattedDateTime);
            }
        }
        if (ObjectUtil.isNotEmpty(map.get("useAt")) ) {
            try {
                String useAt = map.get("useAt").toString();
                if (ObjectUtil.isNotEmpty(useAt)) {
                    String[] parts = useAt.split("\\.");
                    String dateTimeWithoutMillis = parts[0];
                    java.util.Date date = DateUtil.parse(dateTimeWithoutMillis, format);
                    String formattedDateTime = DateUtil.format(date, DS_DATE_FORMAT);
                    map.put("useAt", formattedDateTime);
                }
            } catch (Exception e) {
                log.info("twHisInstanceInsert...useAt:{}", e);
            }
        }

        if (ObjectUtil.isNotEmpty(map.get("grantAt"))){
            String grantAt = map.get("grantAt").toString();
            if (ObjectUtil.isNotEmpty(grantAt)) {
                String[] parts = grantAt.split("\\.");
                String dateTimeWithoutMillis = parts[0];
                java.util.Date date = DateUtil.parse(dateTimeWithoutMillis, format);
                String formattedDateTime = DateUtil.format(date, DS_DATE_FORMAT);
                map.put("grantAt",formattedDateTime);
            }
//            String grantAt = map.get("grantAt").toString();
//            String[] parts = grantAt.split("\\.");
//            String dateTimeWithoutMillis = parts[0];
//            java.util.Date date = DateUtil.parse(dateTimeWithoutMillis, format);
//            String formattedDateTime = DateUtil.format(date, DS_DATE_FORMAT);
//            map.put("grantAt",formattedDateTime);
        }


        //查询crm映射的会员信息
        if (ObjectUtil.isNotEmpty(map.get("memberId"))){
            //查询tw会员和crm会员映射关系表
            String getMemberSql = String.format("select  a.memberId,a.openId,b.memberId as crmMemberId  from " + ModelConstants.TW_MEMBER_ID_MAPPING + " a inner join " + ModelConstants.MEMBER_MEMBER_BINDING + " b  on a.openId =b.openId  where a.memberId = '%s' " ,map.get("memberId"));
            log.info("twHisInstanceInsert...getMemberSql:{}",getMemberSql);
            BaseResponse memberResponse = dataapiHttpSdk.execute(getMemberSql, Collections.emptyMap());
            if (CollectionUtils.isNotEmpty(memberResponse.getData())) {
                List<Map<String, Object>> memberResponseData = memberResponse.getData();
                map.put("holder",memberResponseData.get(0).get("crmMemberId"));
            }
        }

        //过期时间
        JSONObject expiredPeriodJson = JSONObject.parseObject(expiredPeriod);
        if (ObjectUtil.isNotEmpty(expiredPeriodJson)){
            String expiredType = expiredPeriodJson.getString("type");
            if ("FIXED_TIME".equals(expiredType)){
                map.put("expiredAt",expiredPeriodJson.getString("fixed"));
            }
            if ("NEVER".equals(expiredType)){
                map.put("expiredAt","2099-12-31 23:59:59.000Z");
            }
            if ("RELATIVE_TIME".equals(expiredType)){
                com.alibaba.fastjson.JSONArray relatives = expiredPeriodJson.getJSONArray("relatives");
                if (ObjectUtil.isNotEmpty(relatives)){
                    JSONObject durationJson = relatives.getJSONObject(0).getJSONObject("duration");
                    if (ObjectUtil.isNotEmpty(durationJson)){
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DS_DATE_FORMAT);
                        // 解析字符串为 LocalDateTime 对象
                        LocalDateTime originalDateTime = LocalDateTime.parse(effectiveAt, formatter);
                        if(ObjectUtil.isNotEmpty(durationJson.getString("month"))){
                            Integer month = durationJson.getInteger("month");
                            if (ObjectUtil.isNotEmpty(effectiveAt)){
                                LocalDateTime newDateTime = originalDateTime.plusMonths(month);
                                // 格式化为字符串
                                String expiredAt = newDateTime.format(formatter);
                                map.put("expiredAt",expiredAt);
                            }
                        }
                        if(ObjectUtil.isNotEmpty(durationJson.getString("day"))){
                            Integer day = durationJson.getInteger("day");
                            if (ObjectUtil.isNotEmpty(effectiveAt)){
                                LocalDateTime newDateTime = originalDateTime.plusDays(day);
                                String expiredAt = newDateTime.format(formatter);
                                map.put("expiredAt",expiredAt);
                            }
                        }
                    }
                }
            }
        }
        String state = map.get("state").toString();
        if (!"USED".equals(state)) {
            if (ObjectUtil.isNotEmpty(map.get("effectiveAt"))) {
                //校验过期时间是否超过当前时间 如果超过当前时间设置券实例状态为已失效
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSX").withZone(ZoneId.of("UTC"));
                ZonedDateTime givenTime = ZonedDateTime.parse(effectiveAt, formatter);
                ZonedDateTime currentTime = ZonedDateTime.now(ZoneId.of("UTC"));
                if (givenTime.isBefore(currentTime)) {
                    map.put("state", "EXPIRED");
                } else if (givenTime.isAfter(currentTime)) {
                    map.put("state", "EFFECTED");
                }
            }
        }


        if (map.get("code").toString().contains("_")){
            //THIRTY_PARTY_COUPON_485380299528552448
            //截取code最后一个_后面的数据
            String code = map.get("code").toString();
            int lastUnderscoreIndex = code.lastIndexOf("_");
            String result = code.substring(lastUnderscoreIndex + 1);
            map.put("code",result);
        }
        map.put("projectSnapshotId",id);
        map.put("lastSync", DateHelper.getZone(DateHelper.getDateTimeFormat()));
        map.put("createAt", DateHelper.getZone(DateHelper.getDateTimeFormat()));
        map.put("updateAt", DateHelper.getZone(DateHelper.getDateTimeFormat()));
        map.remove("extra");
        map.remove("configId");
        map.remove("memberId");
        map.remove("acquireId");
        log.info("twHisInstanceInsert...map:{}",JSONObject.toJSONString(map));
        //插入到临时模型
        dataapiHttpSdk.upsert(ModelConstants.TW_HIS_INSTANCE,id,map,false);
    }

    public String replaceCoup(String res) {
        String s = res.replace("[", "");
        String rep = s.replace("]", "");
        String replace = rep.replace("\"", "");
        return replace;
    }

    public  void twHisTwHisProjectCreate(String values){
        JSONObject request = JSONObject.parseObject(values);
        log.info("tw历史卡券项目创建请求产品接口开始:{}", JSONUtil.toJsonStr(request));
        try {

            /**
             *    "_i18nPayload": {
             *         "title": {
             *             "zh-CN": "这是dev测试券项目名称222"
             *         },
             *         "campaignTitle": {
             *             "zh-CN": "这是dev测试券活动名称222"
             *         },
             *         "description": {
             *             "zh-CN": "这是dev测试券项目描述222"
             *         }
             *     },
             */

            JSONObject i18nPayloadJSon = new JSONObject();

            JSONObject titleJson = new JSONObject();
            titleJson.put("zh-CN", request.get("title"));
            i18nPayloadJSon.put("title",titleJson);

            JSONObject campaignTitleJson = new JSONObject();
            campaignTitleJson.put("zh-CN", request.get("title"));
            i18nPayloadJSon.put("campaignTitle",campaignTitleJson);

            JSONObject descriptionJson = new JSONObject();
            descriptionJson.put("zh-CN", request.get("title"));
            i18nPayloadJSon.put("description",descriptionJson);
            request.put("_i18nPayload",i18nPayloadJSon);

            /**
             *  "restrict": {
             *         "platformsRef": "KO_Ali"
             *     },
             */
            JSONObject restRictJson = new JSONObject();
            restRictJson.put("platformsRef",request.get("restrictPlatFormsRef"));
            request.put("restrict",restRictJson);


            /**
             *   "grantRestrict": {
             *         "platformsRef": "KO_Ali"
             *     },
             */
            JSONObject grantRestrictJson = new JSONObject();
            grantRestrictJson.put("platformsRef",request.get("grantRestrictFormsRef"));
            request.put("grantRestrict",grantRestrictJson);


            /**
             *  "effectPeriod": {
             *         "type": "RELATIVE_TIME",
             *         "relatives": [
             *             {
             *                 "scene": "activate",
             *                 "duration": {
             *                     "second": 0
             *                 },
             *                 "toDayBegin": false
             *             }
             *         ]
             *     },
             */
            JSONObject effectPeriodJson = new JSONObject();
            effectPeriodJson.put("type","RELATIVE_TIME");
            JSONArray relativesJsonArray = new JSONArray();
            JSONObject relativesJsonObject = new JSONObject();
            relativesJsonObject.put("scene","activate");
            relativesJsonObject.put("toDayBegin",false);

            JSONObject durationJsonObject = new JSONObject();
            durationJsonObject.put("second",0);
            relativesJsonObject.put("duration",durationJsonObject);
            relativesJsonArray.add(relativesJsonObject);
            effectPeriodJson.put("relatives",relativesJsonArray);
            request.put("effectPeriod",effectPeriodJson);

            /**
             *  "expiredPeriod": {
             *         "type": "NEVER"
             *     }
             */
            JSONObject expiredPeriodJsonObject = new JSONObject();
            expiredPeriodJsonObject.put("type","NEVER");
            request.put("expiredPeriod",expiredPeriodJsonObject);

            /**
             * 跳转方式处理
             * campaignCode：ko_user_benefit_config.action.campaignCode
             * appId：ko_user_benefit_config.action.appId
             * path：ko_user_benefit_config.action.path
             * content：ko_user_benefit_config.action.content
             */
            request.remove("restrictPlatFormsRef");
            request.remove("grantRestrictFormsRef");

            // 处理 usedCostCenterCode
            JSONArray usedCostCenterCodeArray = new JSONArray();
            usedCostCenterCodeArray.add(request.getString("usedCostCenterCode"));
            request.put("usedCostCenterCode", usedCostCenterCodeArray.toString());



            // 处理 sendCostCenterCode
            JSONArray sendCostCenterCodeCodeArray = new JSONArray();
            sendCostCenterCodeCodeArray.add(request.getString("sendCostCenterCode"));
            request.put("sendCostCenterCode", sendCostCenterCodeCodeArray.toString());

            log.info("tw历史卡券项目创建请求产品接口入参request:{}", request);
            JSONObject project = benefitMgmtClient.createProject(request);
            log.info("tw历史卡券项目创建请求产品接口返回project:{}", JSONUtil.toJsonStr(project));
        }catch (Exception e){
            log.error("tw历史卡券项目创建请求产品接口失败:",e);
        }
    }
}
