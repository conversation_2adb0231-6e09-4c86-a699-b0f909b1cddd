package com.shuyun.kylin.customized.task.base.kafka.messaging;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @Description 此类定义Kafka的接受队列
 * @date 2019/5/15
 */
@Component
@ConditionalOnExpression("${system.kafka.enabled:true}")
public interface KafkaSink {
     String KAFKA_INPUT = "kafka_input"; // 对应application.yml中的 kafka_input

     String KAFKA_OLD_BI_MEMBER_INPUT = "kafka_old_bi_member_input";
     @Input(KAFKA_INPUT)
    SubscribableChannel input();

    @Output(KAFKA_OLD_BI_MEMBER_INPUT)
    SubscribableChannel oldBIMemberOutput();



    String KAFKA_MEDAL_PROGRESS_CAMPAIGN_INPUT = "kafka_medal_progress_campaign_input"; // 勋章等级进度计算_会员参与活动
    @Input(KAFKA_MEDAL_PROGRESS_CAMPAIGN_INPUT)
    SubscribableChannel medalProgressCampaignInput();



    String KAFKA_MEDAL_PROGRESS_POINT_INPUT = "kafka_medal_progress_point_input"; // 勋章等级进度计算_累计积分
    @Input(KAFKA_MEDAL_PROGRESS_POINT_INPUT)
    SubscribableChannel medalProgressPointInput();

    String KAFKA_INIT_HIS_MEMBER_REGISTER_MEDAL_INPUT = "kafka_init_his_member_register_medal_input"; // 初始化历史会员注册勋章
    @Input(KAFKA_INIT_HIS_MEMBER_REGISTER_MEDAL_INPUT)
    SubscribableChannel initHisMemberRegisterMedalInput();


    String KAFKA_TW_HIS_INSTANCE_GRANT_INPUT = "kafka_tw_his_instance_grant_input"; // tw历史卡券crm发放
    @Input(KAFKA_TW_HIS_INSTANCE_GRANT_INPUT)
    SubscribableChannel twHisInstanceGrantInput();

    String KAFKA_TW_HIS_PROJECT_CREATE_INPUT = "kafka_tw_his_project_create_input"; // tw历史卡券项目创建
    @Input(KAFKA_TW_HIS_PROJECT_CREATE_INPUT)
    SubscribableChannel twHisProjectCreateInput();

    String KAFKA_TW_HIS_INSTANCE_INSERT_INPUT = "kafka_tw_his_instance_insert_input"; // tw历史卡券插入crm临时表
    @Input(KAFKA_TW_HIS_INSTANCE_INSERT_INPUT)
    SubscribableChannel twHisInstanceInsertInput();


    String KAFKA_MEDAL_COUNTS_INPUT = "kafka_medal_counts_input"; // 根据勋章id计算勋章获取总数
    @Input(KAFKA_MEDAL_COUNTS_INPUT)
    SubscribableChannel medalCountsInput();


    String KAFKA_INIT_HIS_MEMBER_REGISTER_MEDAL_INPUT_01 = "kafka_init_his_member_register_medal_input_01"; // 初始化历史会员注册勋章1
    @Input(KAFKA_INIT_HIS_MEMBER_REGISTER_MEDAL_INPUT_01)
    SubscribableChannel initHisMemberRegisterMedalInput01();

    String KAFKA_INIT_HIS_MEMBER_REGISTER_MEDAL_INPUT_02 = "kafka_init_his_member_register_medal_input_02"; // 初始化历史会员注册勋章2
    @Input(KAFKA_INIT_HIS_MEMBER_REGISTER_MEDAL_INPUT_02)
    SubscribableChannel initHisMemberRegisterMedalInput02();

    String KAFKA_INIT_HIS_MEMBER_REGISTER_MEDAL_INPUT_03 = "kafka_init_his_member_register_medal_input_03"; // 初始化历史会员注册勋章3
    @Input(KAFKA_INIT_HIS_MEMBER_REGISTER_MEDAL_INPUT_03)
    SubscribableChannel initHisMemberRegisterMedalInput03();


    String KAFKA_COSMOS_MEMBER_INPUT = "kafka_cosmos_member_input"; // 会员注册/更新数据
    @Input(KAFKA_COSMOS_MEMBER_INPUT)
    SubscribableChannel cosmosMemberInput();

    String KAFKA_COSMOS_GRADE_POINT_INPUT = "kafka_cosmos_grade_point_input"; // 积分总账/等级变更
    @Input(KAFKA_COSMOS_GRADE_POINT_INPUT)
    SubscribableChannel cosmosGradePointInput();

    String KAFKA_COSMOS_ACTIVITY_INPUT = "kafka_cosmos_activity_input"; // 积分明细
    @Input(KAFKA_COSMOS_ACTIVITY_INPUT)
    SubscribableChannel cosmosActivityInput();

    String KAFKA_COSMOS_WECHATSUBSCRIPTION_INPUT = "kafka_cosmos_wechatsubscription_input"; // 积分明细
    @Input(KAFKA_COSMOS_WECHATSUBSCRIPTION_INPUT)
    SubscribableChannel cosmosWechatsubscriptionInput();

    String KAFKA_CT_COUPON_SYNC = "kafka_ct_coupon_sync"; // ct权益核销同步
    @Input(KAFKA_CT_COUPON_SYNC)
    SubscribableChannel ctCouponVerificationInput();

    String KAFKA_MEDAL_COUPON_INPUT = "kafka_medal_coupon_input"; // 权益核销计算勋章进度
    @Input(KAFKA_MEDAL_COUPON_INPUT)
    SubscribableChannel medalCouponInput();

    String KAFKA_COSMOS_WECHATFANS_INPUT = "kafka_cosmos_wechatfans_input"; // 粉丝用户
    @Input(KAFKA_COSMOS_WECHATFANS_INPUT)
    SubscribableChannel cosmosWechatFansInput();

    String KAFKA_COSMOS_WECHATTOAEVENT_INPUT = "kafka_cosmos_wechattoaevent_input"; // 粉丝消息，粉丝行为
    @Input(KAFKA_COSMOS_WECHATTOAEVENT_INPUT)
    SubscribableChannel cosmosWechatToaEventInput();

    String KAFKA_COSMOS_CANCEL_INPUT = "kafka_cosmos_cancel_input"; // 会员注销，用户注销
    @Input(KAFKA_COSMOS_CANCEL_INPUT)
    SubscribableChannel cosmosCancelInput();

    String KAFKA_COSMOS_MASTER_INPUT = "kafka_cosmos_master_input"; // 权益项目。小程序模版
    @Input(KAFKA_COSMOS_MASTER_INPUT)
    SubscribableChannel cosmosMasterInput();


    String KAFKA_MEDAL_PROGRESS_SEND_POINT_INPUT = "kafka_medal_progress_send_point_input"; // 勋章等级进度计算_累计发放积分
    @Input(KAFKA_MEDAL_PROGRESS_SEND_POINT_INPUT)
    SubscribableChannel medalProgressSendPointInput();

    String KAFKA_HIS_MEDAL_PROGRESS_SEND_POINT_INPUT = "kafka_his_medal_progress_send_point_input"; // 历史勋章等级进度计算_累计发放积分
    @Input(KAFKA_HIS_MEDAL_PROGRESS_SEND_POINT_INPUT)
    SubscribableChannel hisMedalProgressSendPointInput();

    String KAFKA_INIT_MEMBER_MEDAL_OBTAIN = "kafka_init_member_medal_obtain_input"; // 会员勋章发放
    @Input(KAFKA_INIT_MEMBER_MEDAL_OBTAIN)
    SubscribableChannel memberMedalObtain();
}
