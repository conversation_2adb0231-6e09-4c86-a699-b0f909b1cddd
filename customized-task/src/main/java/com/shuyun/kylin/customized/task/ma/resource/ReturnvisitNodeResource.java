package com.shuyun.kylin.customized.task.ma.resource;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.task.base.repository.CustomizedTaskRepository;
import com.shuyun.kylin.customized.task.ma.dto.PmsCouponResponseDto;
import com.shuyun.kylin.customized.task.ma.service.ReturnvisitNodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;


@Slf4j
@RestController
@RequestMapping("/customizedNode/process")
public class ReturnvisitNodeResource {

    public static final String EXECUTION_STATUS_INIT = "INIT";
    public static final String EXECUTION_STATUS_PROCESSING = "PROCESSING";
    public static final String EXECUTION_STATUS_FINISH = "FINISH";

    @Autowired
    private ReturnvisitNodeService customizedNodeService;
    @Autowired
    CustomizedTaskRepository customizedTaskRepository;

    /**
     * 事件发送小程序订阅
     * @param request
     * @return
     */
    @PostMapping("/subscribe")
    public Map<String, String> sendSubscribe(@RequestBody Map request) {
        log.info("事件发送小程序订阅入参:{}", JSON.toJSONString(request));
        Map<String, String> map = new HashMap<>();
        if (customizedTaskRepository.existCustomizedTaskExecuteStatus(request.get("taskId").toString(), EXECUTION_STATUS_PROCESSING, EXECUTION_STATUS_FINISH)) {
            map.put("taskId", request.get("taskId").toString());
            map.put("executeStatus", request.get("executeStatus").toString());
            return map;
        }
        return this.customizedNodeService.sendSubscribe(request, map);
    }

    /**
     * 营销发送小程序订阅
     * @param request
     * @return
     */
    @PostMapping("/batch/subscribe")
    public Map<String, String> sendBatecSubscribe(@RequestBody Map request) {
        log.info("营销发送小程序订阅入参:{}", JSON.toJSONString(request));
        Map<String, String> map = new HashMap<>();
        if (customizedTaskRepository.existCustomizedTaskExecuteStatus(request.get("taskId").toString(), EXECUTION_STATUS_PROCESSING, EXECUTION_STATUS_FINISH)) {
            map.put("taskId", request.get("taskId").toString());
            map.put("executeStatus", request.get("executeStatus").toString());
            return map;
        }
        return this.customizedNodeService.sendBatecSubscribe(request, map);
    }

    /**
     * 营销发券
     * @param pmsCouponRequest
     * @return
     */
    @PostMapping("/batch/coupon")
    public PmsCouponResponseDto pmsBatchCoupons(@RequestBody Map pmsCouponRequest) {
        log.info("批量推送优惠券，入参:{}", JSON.toJSONString(pmsCouponRequest));
        Map<String, String> map = new HashMap<>();
        //先去数据库查询状态，如果不是INIT，就返回
        if (customizedTaskRepository.existCustomizedTaskExecuteStatus(pmsCouponRequest.get("taskId").toString(), EXECUTION_STATUS_PROCESSING, EXECUTION_STATUS_FINISH)) {
            return new PmsCouponResponseDto(pmsCouponRequest.get("taskId").toString(), pmsCouponRequest.get("executeStatus").toString());
        }
        return customizedNodeService.pmsBatchCoupons(pmsCouponRequest);
    }

    /**
     * 事件发券
     * @param pmsCouponRequest
     * @return
     */
    @PostMapping("/coupon")
    public PmsCouponResponseDto pmsCoupon(@RequestBody Map pmsCouponRequest) {
        log.info("事件发券优惠券，入参:{}", JSON.toJSONString(pmsCouponRequest));
        Map<String, String> map = new HashMap<>();
        //先去数据库查询状态，如果不是INIT，就返回
        if (customizedTaskRepository.existCustomizedTaskExecuteStatus(pmsCouponRequest.get("taskId").toString(), EXECUTION_STATUS_PROCESSING, EXECUTION_STATUS_FINISH)) {
            return new PmsCouponResponseDto(pmsCouponRequest.get("taskId").toString(), pmsCouponRequest.get("executeStatus").toString());
        }
        return customizedNodeService.pmsCoupon(pmsCouponRequest);
    }
}
