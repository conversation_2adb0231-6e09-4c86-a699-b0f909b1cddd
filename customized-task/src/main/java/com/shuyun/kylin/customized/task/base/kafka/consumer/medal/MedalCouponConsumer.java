package com.shuyun.kylin.customized.task.base.kafka.consumer.medal;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.task.base.kafka.messaging.KafkaSink;
import com.shuyun.kylin.customized.task.medal.request.MedalProgressRequest;
import com.shuyun.kylin.customized.task.medal.service.MedalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MedalCouponConsumer {

    @Autowired
    private MedalService medalService;

    @StreamListener(KafkaSink.KAFKA_MEDAL_COUPON_INPUT)
    public void medalProgressByCampaignConsumer(MedalProgressRequest medalProgressRequest) {
        try {
            log.info("接收到会员权益核销勋章计算kafka数据流入参:{}", JSON.toJSONString(medalProgressRequest));
            medalService.medalCouponConsumer(medalProgressRequest);
        } catch (Exception e) {
            log.error("接收到会员权益核销勋章计算kafka失败:{}", JSON.toJSONString(medalProgressRequest),e);
        }
    }

}
