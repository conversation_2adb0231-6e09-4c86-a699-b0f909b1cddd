package com.shuyun.kylin.customized.task.base.kafka.consumer.medal;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.task.base.kafka.messaging.KafkaSink;
import com.shuyun.kylin.customized.task.medal.request.MedalProgressRequest;
import com.shuyun.kylin.customized.task.medal.service.MedalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

/**
 * 根据勋章id计算勋章获取总数
 */
@Slf4j
@Component
public class MedalCountsConsumer {

    @Autowired
    private MedalService medalService;

    @StreamListener(KafkaSink.KAFKA_MEDAL_COUNTS_INPUT)
    public void medalProgressByCampaignConsumer(MedalProgressRequest medalProgressRequest) {
        try {
            log.info("接收到勋章数据入参:{}", JSON.toJSONString(medalProgressRequest));
            medalService.medalCountsConsumer(medalProgressRequest);
        } catch (Exception e) {
            log.error("接收到勋章数据计算kafka失败:{}", JSON.toJSONString(medalProgressRequest),e);
        }
    }
}
