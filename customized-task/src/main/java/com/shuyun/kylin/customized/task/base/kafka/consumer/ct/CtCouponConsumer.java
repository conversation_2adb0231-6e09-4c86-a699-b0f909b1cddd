package com.shuyun.kylin.customized.task.base.kafka.consumer.ct;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.task.base.kafka.messaging.KafkaSink;
import com.shuyun.kylin.customized.task.dataflow.service.CosmosService;

import com.shuyun.kylin.customized.task.dataflow.service.CtConsumerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class CtCouponConsumer {


    @Autowired
    private CtConsumerService ctConsumerService;

    /**
     * ct权益核销
     *
     * @param request
     */
    @StreamListener(KafkaSink.KAFKA_CT_COUPON_SYNC)
    public void medalProgressByCampaignConsumer(Map<String, Object> request) {
        try {
            log.info("接收到ct权益核销入参:{}", JSON.toJSONString(request));
            ctConsumerService.sendCouponVerification(request);
        } catch (Exception e) {
            log.error("接收到ct权益核销入参:{}", JSON.toJSONString(request),e);
        }
    }
}
