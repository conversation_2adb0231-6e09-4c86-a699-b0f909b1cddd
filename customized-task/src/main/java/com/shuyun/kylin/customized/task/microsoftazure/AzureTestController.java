package com.shuyun.kylin.customized.task.microsoftazure;

import cn.hutool.core.thread.ThreadUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.LongSerializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.web.bind.annotation.*;

import java.io.InputStream;
import java.util.Properties;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 微软azure测试接口
 *
 * @author: Jingwei
 * @date: 2024-12-17
 */
@Slf4j
@RestController
@RequestMapping("/azure")
public class AzureTestController {

    private static final int NUM_THREADS = 1;

    /**
     * 测试Azure Kafka发送消息
     * @return
     */
    @GetMapping("/testAzKafkaConsume")
    public String testAzKafkaConsume(@RequestParam("topic") String topic) {
        log.info("Azure开始消费kafka消息，topic={}", topic);
        new AzureConsumerThread(topic).run();
        return "消费结束";
    }

    @PostMapping("/testAzKafkaProduce")
    public String testAzKafkaProduce(@RequestParam("topic") String topic) {
        log.info("Azure开始发送kafka消息，topic={}", topic);
        //Create Kafka Producer
        final Producer<Long, String> producer = createProducer();

        ThreadUtil.sleep(5000);

        final ExecutorService executorService = Executors.newFixedThreadPool(NUM_THREADS);

        //Run NUM_THREADS TestDataReporters
        for (int i = 0; i < NUM_THREADS; i++)
            executorService.execute(new AzureDataReporter(producer, topic));
        return "发送结束";
    }

    private static Producer<Long, String> createProducer() {
        try{
            Properties properties = new Properties();
            InputStream resourceAsStream = AzureTestController.class.getClassLoader().getResourceAsStream("microsoft-azure/azure-kafka.config");
            properties.load(resourceAsStream);
            properties.put(ProducerConfig.CLIENT_ID_CONFIG, "KafkaExampleProducer");
            properties.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, LongSerializer.class.getName());
            properties.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
            return new KafkaProducer<>(properties);
        } catch (Exception e){
            log.error("微软azure生产者-Failed to create producer with exception: ", e);
            System.exit(0);
            return null;        //unreachable
        }
    }
}
