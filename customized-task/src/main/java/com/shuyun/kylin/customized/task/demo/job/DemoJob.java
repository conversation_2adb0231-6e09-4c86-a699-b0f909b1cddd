/*
package com.shuyun.kylin.customized.task.demo.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.enums.JobInitiationEnum;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

*/
/**
 * <AUTHOR>
 * @Description  用于显示定时任务
 *               自定义定时任务执行参考此Demo写
 * @date 2020/3/19
 *//*


@Component
public class DemoJob {

    Logger logger = LoggerFactory.getLogger(DemoJob.class);

    // cron 命令参考: https://www.iteye.com/blog/liuzidong-1119773
   @XxlJob(value = "demoJob",cron = "0 0 * * * ?",jobDesc="demo job",jobInitation= JobInitiationEnum.START)
    public ReturnT<String> demoJob(String param) throws Exception {
         logger.info("XXL-JOB, start ");

        for (int i = 0; i < 5; i++) {
            logger.info("demo job is run count:{}",i);
            TimeUnit.SECONDS.sleep(2);
        }
        logger.info("XXL-JOB, end ");
        return ReturnT.SUCCESS;
    }



}
*/
