package com.shuyun.kylin.customized.task.base.kafka.consumer.cosmos;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.task.base.kafka.messaging.KafkaSink;
import com.shuyun.kylin.customized.task.dataflow.service.CosmosService;
import com.shuyun.kylin.customized.task.integration.dto.OcpMemberBindingDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class CosmosParketConsumer {

    @Autowired
    private CosmosService cosmosService;

    /**
     * cosmos会员注册/更新
     *
     * @param ocpMemberBindingDto
     */
    @StreamListener(KafkaSink.KAFKA_COSMOS_MEMBER_INPUT)
    public void medalProgressByCampaignConsumer(OcpMemberBindingDto ocpMemberBindingDto) {
        try {
            log.info("接收到cosmos会员注册/更新入参:{}", JSON.toJSONString(ocpMemberBindingDto));
            cosmosService.queryCosmosMember(ocpMemberBindingDto);
        } catch (Exception e) {
            log.error("接收到cosmos会员注册/更新失败:{}", JSON.toJSONString(ocpMemberBindingDto),e);
        }
    }

    /**
     * cosmos会员等级/积分更新
     *
     * @param ocpMemberBindingDto
     */
    @StreamListener(KafkaSink.KAFKA_COSMOS_GRADE_POINT_INPUT)
    public void memberGrade(OcpMemberBindingDto ocpMemberBindingDto) {
        try {
            log.info("接收到cosmos会员等级/积分更新入参:{}", JSON.toJSONString(ocpMemberBindingDto));
            cosmosService.queryCosmosMember(ocpMemberBindingDto);
        } catch (Exception e) {
            log.error("接收到cosmos会员等级/积分更新失败:{}", JSON.toJSONString(ocpMemberBindingDto),e);
        }
    }

    /**
     * cosmos activity数据
     *
     * @param request
     */
    @StreamListener(KafkaSink.KAFKA_COSMOS_ACTIVITY_INPUT)
    public void memberActivity(Map<String, Object> request) {
        try {
            log.info("接收到cosmos_activity数据入参:{}", JSON.toJSONString(request));
            cosmosService.queryCosmosActivity(request);
        } catch (Exception e) {
            log.error("接收到cosmos_activity数据失败:{}", JSON.toJSONString(request),e);
        }
    }

    /**
     * cosmos 小程序订阅数据
     *
     * @param request
     */
    @StreamListener(KafkaSink.KAFKA_COSMOS_WECHATSUBSCRIPTION_INPUT)
    public void memberWechatsubscription(Map<String, Object> request) {
        try {
            log.info("接收到cosmos_小程序订阅数据数据入参:{}", JSON.toJSONString(request));
            cosmosService.queryCosmosWechatsubscription(request);
        } catch (Exception e) {
            log.error("接收到cosmos_小程序订阅数据数据失败:{}", JSON.toJSONString(request),e);
        }
    }

    /**
     * cosmos 粉丝用户
     *
     * @param request
     */
    @StreamListener(KafkaSink.KAFKA_COSMOS_WECHATFANS_INPUT)
    public void cosmosWechatFans(Map<String, Object> request) {
        try {
            log.info("接收到cosmos_粉丝用户数据数据入参:{}", JSON.toJSONString(request));
            cosmosService.sendCosmosWechatFans(request);
        } catch (Exception e) {
            log.error("接收到cosmos_粉丝用户数据数据失败:{}", JSON.toJSONString(request),e);
        }
    }

    /**
     * cosmos 粉丝消息，粉丝行为
     *
     * @param request
     */
    @StreamListener(KafkaSink.KAFKA_COSMOS_WECHATTOAEVENT_INPUT)
    public void cosmosWechatToaEvent(Map<String, Object> request) {
        try {
            log.info("接收到cosmos_粉丝消息，粉丝行为数据数据入参:{}", JSON.toJSONString(request));
            cosmosService.sendCosmosWechatToaEvent(request);
        } catch (Exception e) {
            log.error("接收到cosmos_粉丝消息，粉丝行为数据数据失败:{}", JSON.toJSONString(request),e);
        }
    }

    /**
     * cosmos 会员注销，用户注销
     *
     * @param request
     */
    @StreamListener(KafkaSink.KAFKA_COSMOS_CANCEL_INPUT)
    public void cosmosCancel(Map<String, Object> request) {
        try {
            log.info("接收到cosmos_会员注销，用户注销数据数据入参:{}", JSON.toJSONString(request));
            cosmosService.sendCosmosCancel(request);
        } catch (Exception e) {
            log.error("接收到cosmos_会员注销，用户注销数据数据失败:{}", JSON.toJSONString(request),e);
        }
    }

    /**
     * cosmos 权益项目。小程序模版
     *
     * @param request
     */
    @StreamListener(KafkaSink.KAFKA_COSMOS_MASTER_INPUT)
    public void cosmosMaster(Map<String, Object> request) {
        try {
            log.info("接收到cosmos_权益项目。小程序模版数据数据入参:{}", JSON.toJSONString(request));
            cosmosService.sendCosmosMaster(request);
        } catch (Exception e) {
            log.error("接收到cosmos_权益项目。小程序模版数据数据失败:{}", JSON.toJSONString(request),e);
        }
    }

}
