package com.shuyun.kylin.customized.task.wechat.job.service.impl;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.kylin.customized.task.base.config.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.task.wechat.job.service.WechatService;
import com.shuyun.kylin.starter.redis.ICache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class WechatServiceImpl implements WechatService {

    @Autowired
    @Qualifier("redisCache")
    private ICache redisCache;


    private static final String wechatLwUrl = ConfigurationCenterUtil.wechatLwUrl;


    @Override
    public String getWechatLwToken() {
        try {
            String url = String.format("%s/api/platform-tenant/authentication/client?clientId=%s&secret=%s", wechatLwUrl, ConfigurationCenterUtil.wechatLwClientId, ConfigurationCenterUtil.wechatLwSecret);
            log.info("getWechatLwToken...url:{}",url);
            String body = HttpUtil.createPost(url).execute().body();
            if (ObjectUtil.isNotEmpty(body)) {
                log.info("联蔚小程序token返回结果：{}", body);
                JSONObject jsonObject = JSON.parseObject(body, JSONObject.class);
                if (jsonObject.get("accessToken") != null) {
                    String accessToken = jsonObject.getString("accessToken");
                    redisCache.put("lw.wechat.token", accessToken, 3600, TimeUnit.SECONDS);
                    log.info("联蔚小程序token：{}", accessToken);
                    return  accessToken;
                }
            }
        } catch (Exception e) {
            log.error("获取联蔚小程序token异常，", e);
        }
        return null;
    }


//    /**
//     * 联蔚小程序订阅消息
//     */
//    @Override
//    public void lwWechatSubscribeConsumer(LwWechatSubscribeRequest lwWechatRequest) {
//        String accessToken = redisCache.get("lw.wechat.token");
//        if (Objects.isNull(accessToken)){
//            accessToken = getWechatLwToken();
//        }
//        log.info("lwWechatSubscribeConsumer...accessToken:{}",accessToken);
//        String url = String.format("'%s'/api/icoke-subscribe/'%s'/subscribe/subscribeTemplate", wechatLwUrl, ConfigurationCenterUtil.wechatLwBuCode);
//        log.info("lwWechatSubscribeConsumer...url:{}",url);
//        String jsonString = JSON.toJSONString(lwWechatRequest);
//        String body = HttpUtil.createPost(url).body(jsonString).header("Authorization",accessToken).execute().body();
//        if (ObjectUtil.isNotEmpty(body)) {
//            log.info("lwWechatSubscribeConsumer...body：{}", body);
//            JSONObject jsonObject = JSON.parseObject(body, JSONObject.class);
//            if (jsonObject.get("code") != null) {
//                String code = jsonObject.getString("code");
//                if (0==Integer.parseInt(code)) {
//                    log.info("联蔚小程序订阅消息发送成功");
//                } else {
//                    log.info("联蔚小程序订阅消息发送失败");
//                }
//            }
//        }
//    }
}
