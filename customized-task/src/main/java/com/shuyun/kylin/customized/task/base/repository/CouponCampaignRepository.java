package com.shuyun.kylin.customized.task.base.repository;


import com.shuyun.kylin.customized.task.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.task.ma.dto.CampaignActionOutputDto;
import com.shuyun.kylin.customized.task.ma.util.ConfigurationCenterUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class CouponCampaignRepository extends BaseDsRepository<CampaignActionOutputDto> {

    /**
     * 查询活动会员 (分页)
     * @param pages
     * @param limit
     * @return
     */
    public List<CampaignActionOutputDto> getMemberCouponslist(int pages, int limit) {
        Map<String, Object> queryMap = new HashMap<>();
        //queryMap.put("lastSync", ZonedDateTime.now().minusMinutes(10));
        //queryMap.put("lastSync", );
        String queryOrderSql = " SELECT FROM  " + limit + " OFFSET " + pages + " ";
        List<CampaignActionOutputDto> orderList = executeSQL(queryOrderSql, queryMap);
        return orderList;
    }

    /**
     * 查询是否测试执行
     * @return
     */
    public Boolean getCountCoupons(String taskId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId",taskId);
        String queryMemberSql = " SELECT testRun FROM  " + ConfigurationCenterUtil.MEMBER_COUPON + " where taskId = :taskId LIMIT 1 ";
        List<Map<String,Object>> list = execute(queryMemberSql, queryMap).getData();
        Boolean count = true;
        if (!CollectionUtils.isEmpty(list)){
            for (Map map : list) {
                count = (Boolean) map.get("testRun");
            }
        }
        return count;
    }


    public List<CampaignActionOutputDto> getMemberCoupons(String taskId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId",taskId);
        String queryOrderSql = " SELECT id,isSendCardsOffers,costCenter,coupon,objectId,campaignId,taskId,occId,nodeId,nodeName,campaignName,testRun,scene,type  FROM  " + ConfigurationCenterUtil.MEMBER_COUPON + "  where taskId = :taskId ";
        List<CampaignActionOutputDto> couponList = executeSQL(queryOrderSql, queryMap);
        return couponList;
    }




    /**
     * 查询活动会员
     * @return
     */
    public List<CampaignActionOutputDto> getMemberCouponsList(String taskId,String index, int pageSize) {

        String change = " id > '" + index + "' order by id limit " + pageSize;

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId",taskId);
        String queryOrderSql = " SELECT id,isSendCardsOffers,costCenter,coupon,objectId,campaignId,taskId,occId,nodeId,nodeName,campaignName,testRun,scene,type  FROM  " + ConfigurationCenterUtil.MEMBER_COUPON + "  where taskId = :taskId and " + change + " ";
        List<CampaignActionOutputDto> couponList = executeSQL(queryOrderSql, queryMap);
        return couponList;
    }


    /**
     * 更新会员优惠券发状态
     * @return
     */
    public void updateMemberCoupons(String memberId,String taskId,String unionId,String state,String koResultDesc) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId",taskId);
        queryMap.put("objectId",unionId);
        queryMap.put("koResult",state);
        queryMap.put("koResultDesc",koResultDesc);
        queryMap.put("memberId",memberId);
        queryMap.put("executeTime", ZonedDateTime.now());
        String queryOrderSql = " UPDATE " + ConfigurationCenterUtil.MEMBER_COUPON + " SET member=:memberId,koResult = :koResult,koResultDesc =:koResultDesc,executeTime =:executeTime WHERE taskId = :taskId and objectId = :objectId ";
        execute(queryOrderSql, queryMap);

    }

    /**
     * 更新会员小程序订阅状态
     * @return
     */
    public void updateMemberSubscribe(String taskId,String unionId,String state,String koResultDesc) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId",taskId);
        queryMap.put("objectId",unionId);
        queryMap.put("koResult",state);
        queryMap.put("koResultDesc",koResultDesc);
        queryMap.put("executeTime", ZonedDateTime.now());
        String queryOrderSql = " UPDATE " + ConfigurationCenterUtil.MEMBER_SUBSCRIBE + " SET koResult = :koResult,koResultDesc =:koResultDesc,executeTime =:executeTime WHERE taskId = :taskId and objectId = :objectId and koResult is null";
        execute(queryOrderSql, queryMap);

    }

    /**
     * 查询优惠券
     * @param taskId
     */
    public Map<String, String> getCoupons(String taskId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId", taskId);
        String queryMemberSql = " select id,count(1) as countSum from "+ ConfigurationCenterUtil.MEMBER_COUPON + "  where taskId = :taskId LIMIT 1";
        List<Map<String,Object>> list = execute(queryMemberSql, queryMap).getData();
        String couponId = "0";
        String countSum = "0";
        for (Map map : list) {
            couponId = String.valueOf(map.get("id"));
            countSum = String.valueOf(map.get("countSum"));

        }
        Map<String, String> hashMap = new HashMap<>();
        hashMap.put("couponId",couponId);
        hashMap.put("countSum",countSum);
        return hashMap;
        //return count;

    }

    public Map<String, String> getCouponSum(String taskId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId", taskId);
        String queryMemberSql = " select id,count(1) as countSum from " + ConfigurationCenterUtil.MEMBER_COUPON + "  where taskId = :taskId LIMIT 1";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        String id = "0";
        String countSum = "0";
        for (Map map : list) {
            id = String.valueOf(map.get("id"));
            countSum = String.valueOf(map.get("countSum"));
        }
        Map<String, String> hashMap = new HashMap<>();
        hashMap.put("id", id);
        hashMap.put("countSum", countSum);
        return hashMap;
    }
}
