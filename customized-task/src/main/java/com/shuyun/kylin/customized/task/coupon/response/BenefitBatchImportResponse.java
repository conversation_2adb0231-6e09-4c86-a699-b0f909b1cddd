package com.shuyun.kylin.customized.task.coupon.response;

import lombok.Data;
import java.util.List;

@Data
public class BenefitBatchImportResponse {

    //总数
    private Integer total;
    //成功数量
    private Integer success;

   //失败详情
    private List<BenefitBatchErrorDetail> errorDetails;

    @Data
    public static class BenefitBatchErrorDetail {
        //异常信息
        private String errMsg;

        //方案逻辑id
        private String programId;

        //模板Id
        private String templateId;

        //项目Id
        private String projectId;

        //券码
        private String code;

        //事务Id
        private String transactionId;

    }
}
