package com.shuyun.kylin.customized.task.base.dm;

import com.google.common.collect.Lists;
import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.dm.api.response.DMLResponse;
import com.shuyun.mc.ds.suql.ApiExecutor;
import com.shuyun.mc.ds.suql.model.DataResult;
import com.shuyun.mc.ds.suql.model.QueryOptional;
import com.shuyun.mc.ds.suql.utils.MCDataFactoryUtil;
import com.shuyun.pip.component.json.JsonUtils;

import javax.validation.constraints.NotNull;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 数据服务基类
 *
 * @param <T>
 */
public abstract class BaseDsRepository<T> {
    private Class<T> entityClass;

    protected BaseDsRepository() {
        Type type = getClass().getGenericSuperclass();
        Type trueType = ((ParameterizedType) type).getActualTypeArguments()[0];
        this.entityClass = (Class<T>) trueType;
    }

    /**
     * 根据filter获取数据模型数据
     *
     * @param fqn        fqn
     * @param fieldNames 查询的属性列表，不传默认会去查询所有
     * @param filter     筛选条件
     * @return
     */
    public T queryByFilter(String fqn, List<String> fieldNames, Map<String, Object> filter) {
        //获取返回的数据模型字段
        Class<T> clazz = this.entityClass;
        if (null == fieldNames || fieldNames.isEmpty()) {
            fieldNames = this.getDeclaredField(clazz);
        }
        Map<String, Object> resultMap = ApiExecutor.INSTANCE.queryMap(fqn, fieldNames, filter);
        //将map转换成对象
        return !Objects.isNull(resultMap) && !resultMap.isEmpty() ? JsonUtils.convert(resultMap, clazz) : null;
    }

    public List<T> queryList(String fqn, List<String> fieldNames, QueryOptional queryOptiona) {
        //获取返回的数据模型字段
        Class<T> clazz = this.entityClass;
        if (null == fieldNames || fieldNames.isEmpty()) {
            fieldNames = this.getDeclaredField(clazz);
        }
        DataResult<Map<String, Object>> resultMap = ApiExecutor.INSTANCE.queryMap(fqn, fieldNames, queryOptiona);
        List<Map<String, Object>> data = resultMap.getData();
        return !Objects.isNull(data) && !data.isEmpty() ? data.stream().map(map -> JsonUtils.convert(map, clazz)).collect(Collectors.toList()) : null;
    }

    /**
     * ex: select name from $table where name = :name
     **/
    public List<T> executeSQL(String sql, Map<String, Object> paramsMap) {
        BaseResponse response = MCDataFactoryUtil.INSTANCE.getDataapiSdk().execute(sql, paramsMap);
        return response.getData();
    }

    public BaseResponse execute(String sql, Map<String, Object> paramsMap) {
        return MCDataFactoryUtil.INSTANCE.getDataapiSdk().execute(sql, paramsMap);
    }

    /**
     * 向数据模型插入多条数据
     *
     * @param fqn
     * @param list
     * @return
     */
    public int batchInsert(@NotNull String fqn, @NotNull List<T> list) {
        List<Map<String, Object>> params = Lists.newArrayList();
        list.forEach(item -> {
            params.add(JsonUtils.objectToMap(item));
        });
        DMLResponse response = MCDataFactoryUtil.INSTANCE.getDataapiSdk().batchInsert(fqn, params, true, false);
        return response.getAffectedRows();
    }

    /**
     * 向数据模型插入多条数据
     *
     * @param fqn     全限定名
     * @param params  参数
     * @param cascade 是否级联操作，仅针对引用关系
     * @return
     */
    public int originalBatchInsert(@NotNull String fqn, @NotNull List<Map<String, Object>> params,boolean cascade) {
        DMLResponse response = MCDataFactoryUtil.INSTANCE.getDataapiSdk().batchInsert(fqn, params, false, cascade);
        return response.getAffectedRows();
    }

    /**
     * 根据id删除数据模型
     *
     * @param fqn
     * @param objectId
     * @return
     */
    public int deleteById(String fqn, String objectId) {
        DMLResponse response = MCDataFactoryUtil.INSTANCE.getDataapiSdk().delete(fqn, objectId, false);
        return response.getAffectedRows();
    }

    /**
     * 根据id删除数据模型
     *
     * @param fqn
     * @return
     */
    public int deleteByFilter(String fqn, Map<String, Object> params) {
        DMLResponse response = MCDataFactoryUtil.INSTANCE.getDataapiSdk().deleteByFilter(fqn, JsonUtils.toJson(params));
        return response.getAffectedRows();
    }


    /**
     * 插入或更新一条数据
     *
     * @param fqn
     * @param objectId
     * @param params
     * @return
     */
    public int upsert(String fqn, String objectId, Map<String, Object> params) {
        DMLResponse response = MCDataFactoryUtil.INSTANCE.getDataapiSdk().upsert(fqn, objectId, params, false);
        return response.getAffectedRows();
    }

    /**
     * 更新数据模型
     *
     * @param fqn
     * @param objectId
     * @param params
     * @return
     */
    public int update(String fqn, String objectId, Map<String, Object> params) {
        DMLResponse response = MCDataFactoryUtil.INSTANCE.getDataapiSdk().update(fqn, objectId, params, false);
        return response.getAffectedRows();
    }

    /**
     * 新增
     *
     * @param fqn
     * @param o
     * @return
     */
    public String insert(String fqn, T o) {
        return ApiExecutor.INSTANCE.insert(fqn, JsonUtils.objectToMap(o), false);
    }

    /**
     * 更新
     *
     * @param fqn
     * @param filter
     * @param params
     * @return
     */
    public int update(String fqn, Map<String, Object> filter, Map<String, Object> params) {
        return ApiExecutor.INSTANCE.update(fqn, filter, params);
    }

    /**
     * 获取类定义的属性列表
     *
     * @param clazz
     * @return
     */
    private List<String> getDeclaredField(Class clazz) {
        Field[] fields = clazz.getDeclaredFields();
        // 获取当前类的属性
        List<String> fieldNames = Arrays.stream(fields).map(field -> field.getName()).collect(Collectors.toList());
        // 拼接上父类的属性
        Class superClazz = clazz.getSuperclass();
        Field[] superFields = superClazz.getDeclaredFields();
        fieldNames.addAll(Arrays.stream(superFields).map(field -> field.getName()).collect(Collectors.toList()));
        return fieldNames;
    }

}
