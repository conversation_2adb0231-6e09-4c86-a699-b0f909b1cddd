package com.shuyun.kylin.customized.task.base.feign.client;

import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import retrofit2.http.Body;

@FeignClient(name = "benefitMgmt")
@RequestMapping("benefit-mgmt/v1")
public interface BenefitMgmtClient {

    @PostMapping("/api/project")
    JSONObject createProject(@Body JSONObject request);
}
