package com.shuyun.kylin.customized.task.base.feign.client;

import com.shuyun.kylin.customized.task.coupon.dto.BenefitBatchImportRequest;
import com.shuyun.kylin.customized.task.coupon.response.BenefitBatchImportResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import retrofit2.http.Body;

/**
 * 卡券内部接口
 */
@FeignClient(name = "benefitService")
@RequestMapping("benefit-service/v1")
public interface BenefitServiceClient {


    @PostMapping("/api/domain/benefit/import/batch")
    BenefitBatchImportResponse importBatch(@Body BenefitBatchImportRequest request);
}
