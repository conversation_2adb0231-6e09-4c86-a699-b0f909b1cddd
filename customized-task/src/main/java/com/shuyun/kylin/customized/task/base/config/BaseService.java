package com.shuyun.kylin.customized.task.base.config;



import com.shuyun.api.hub.client.ApiMgmtHttpClientFactory;
import com.shuyun.api.hub.mgmt.request.HttpApiRegisterRequest;
import com.shuyun.motor.common.cons.App;
import com.shuyun.motor.common.cons.PropsUtil;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

@Service
public class BaseService {




    @PostConstruct
    @Order(1000)
    public void init(){
        String json ="{\n" +
                "\t\"openapi\": \"3.0.1\",\n" +
                "\t\"info\": {\n" +
                "\t\t\"title\": \"OpenAPI definition\",\n" +
                "\t\t\"version\": \"v1\"\n" +
                "\t},\n" +
                "\t\"servers\": [{\n" +
                "\t\t\"url\": \"http://localhost:8080\",\n" +
                "\t\t\"description\": \"Generated server url\"\n" +
                "\t}],\n" +
                "\t\"tags\": [{\n" +
                "\t\t\"name\": \"会员修改2567服务\",\n" +
                "\t\t\"description\": \"会员修改2567相关的接口\"\n" +
                "\t}],\n" +
                "\t\"paths\": {\n" +
                "\t\t\"/work/member/modify\": {\n" +
                "\t\t\t\"post\": {\n" +
                "\t\t\t\t\"tags\": [\"会员修改2567服务\"],\n" +
                "\t\t\t\t\"summary\": \"会员修改2567\",\n" +
                "\t\t\t\t\"operationId\": \"memberModify\",\n" +
                "\t\t\t\t\"requestBody\": {\n" +
                "\t\t\t\t\t\"content\": {\n" +
                "\t\t\t\t\t\t\"application/json\": {\n" +
                "\t\t\t\t\t\t\t\"schema\": {\n" +
                "\t\t\t\t\t\t\t\t\"$ref\": \"#/components/schemas/WorkMemberRequest\"\n" +
                "\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"required\": true\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"responses\": {\n" +
                "\t\t\t\t\t\"default\": {\n" +
                "\t\t\t\t\t\t\"description\": \"OK\",\n" +
                "\t\t\t\t\t\t\"content\": {\n" +
                "\t\t\t\t\t\t\t\"application/json\": {\n" +
                "\t\t\t\t\t\t\t\t\"schema\": {\n" +
                "\t\t\t\t\t\t\t\t\t\"$ref\": \"#/components/schemas/ResponsesVo\"\n" +
                "\t\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t}\n" +
                "\t\t\t}\n" +
                "\t\t}\n" +
                "\t},\n" +
                "\t\"components\": {\n" +
                "\t\t\"schemas\": {\n" +
                "\t\t\t\"WorkMemberRequest\": {\n" +
                "\t\t\t\t\"required\": [\"memberId\", \"memberName\", \"gender\", \"mobile\", \"dateOfBirth\"],\n" +
                "\t\t\t\t\"type\": \"object\",\n" +
                "\t\t\t\t\"properties\": {\n" +
                "\t\t\t\t\t\"memberId\": {\n" +
                "\t\t\t\t\t\t\"type\": \"string\",\n" +
                "\t\t\t\t\t\t\"description\": \"会员Id\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"memberName\": {\n" +
                "\t\t\t\t\t\t\"type\": \"string\",\n" +
                "\t\t\t\t\t\t\"description\": \"会员姓名\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"gender\": {\n" +
                "\t\t\t\t\t\t\"type\": \"string\",\n" +
                "\t\t\t\t\t\t\"description\": \"性别\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"mobile\": {\n" +
                "\t\t\t\t\t\t\"type\": \"string\",\n" +
                "\t\t\t\t\t\t\"description\": \"手机号\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"dateOfBirth\": {\n" +
                "\t\t\t\t\t\t\"type\": \"string\",\n" +
                "\t\t\t\t\t\t\"format\": \"date-time\",\n" +
                "\t\t\t\t\t\t\"description\": \"生日\"\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"description\": \"会员修改2567相关的请求参数\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"ResponsesVo\": {\n" +
                "\t\t\t\t\"type\": \"object\",\n" +
                "\t\t\t\t\"properties\": {\n" +
                "\t\t\t\t\t\"code\": {\n" +
                "\t\t\t\t\t\t\"type\": \"integer\",\n" +
                "\t\t\t\t\t\t\"format\": \"int32\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"message\": {\n" +
                "\t\t\t\t\t\t\"type\": \"string\"\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t}\n" +
                "\t\t\t}\n" +
                "\t\t}\n" +
                "\t}\n" +
                "}";
        HttpApiRegisterRequest request=new HttpApiRegisterRequest();
        request.setApiVersion(App.getApiVersion());
        request.setServiceName(App.getServiceName());
        request.setDescription(App.getServiceName());
        request.setRootPath(App.getContextPath());
        String var10001 = PropsUtil.getSysOrEnv("open.api.mgmt.register.service.title");
        if (var10001 == null) {
            var10001 = App.getServiceName();
        }
        request.setTitle(var10001);
        request.setOpenApiDoc(json);
        ApiMgmtHttpClientFactory.apiMgmt().register(request);
    }

}
