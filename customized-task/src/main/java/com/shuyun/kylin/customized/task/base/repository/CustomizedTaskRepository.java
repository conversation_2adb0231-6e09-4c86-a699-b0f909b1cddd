package com.shuyun.kylin.customized.task.base.repository;


import com.shuyun.kylin.customized.task.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.task.ma.dto.CustomizedTaskDto;
import com.shuyun.kylin.customized.task.ma.util.DataCofig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class CustomizedTaskRepository extends BaseDsRepository<CustomizedTaskDto> {


    public Boolean existCustomizedTaskExecuteStatus(String taskId, String... executeStatus) {
        String executeStatusStr = StringUtils.join(executeStatus, ",");
        log.info("existCustomizedTaskExecuteStatus taskId:{},executeStatus:{}", taskId, executeStatusStr);
        Map<String, Object> queryMap = new HashMap<>();
//        queryMap.put("taskId", taskId);
        String queryMemberSql = " select id from " + DataCofig.JAHUA_CUSTOMIZEDTASK + "  where taskId = '" + taskId + "' and executeStatus in ('" + StringUtils.join(executeStatus, "','") + "') limit 1 ";
        log.info("existCustomizedTaskExecuteStatus sql:{}", queryMemberSql);
        List<CustomizedTaskDto> customizedTaskList = executeSQL(queryMemberSql, queryMap);
        if (!CollectionUtils.isEmpty(customizedTaskList)) {
            log.info("existCustomizedTaskExecuteStatus__存在状态为{}的记录，taskId:{}", executeStatusStr, taskId);
            return true;
        }
        log.info("existCustomizedTaskExecuteStatus__不存在状态为{}的记录，taskId:{}", executeStatusStr, taskId);
        return false;
    }

    public static void main(String[] args) {
        String[] arr=new String[]{"a","b"};
        System.out.println(StringUtils.join(arr,"','"));
    }
}
