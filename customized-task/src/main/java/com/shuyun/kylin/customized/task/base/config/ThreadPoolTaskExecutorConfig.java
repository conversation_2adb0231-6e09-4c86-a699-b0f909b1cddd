package com.shuyun.kylin.customized.task.base.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

@EnableAsync
@Configuration
public class ThreadPoolTaskExecutorConfig {

    // 核心线程数=cpu核数
    private  int CORE_POOL_SIZE = Runtime.getRuntime().availableProcessors()+1;
    // 最大线程数
    private  int MAX_POOL_SIZE = 20;
    // 队列容量
    private  int QUEUE_CAPACITY = 100;
    // 线程活跃时间（秒）,超过时间就会被线程池回收
    private  int KEEP_ALIVE_SECONDS = 1;

    @Bean(name = "threadCosmosPointRecords")
    public ThreadPoolTaskExecutor threadCosmosPointRecords() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setMaxPoolSize(8);
        executor.setCorePoolSize(3);
        executor.setQueueCapacity(QUEUE_CAPACITY);
        executor.setKeepAliveSeconds(KEEP_ALIVE_SECONDS);
        // 线程池对拒绝任务(无线程可用)的处理策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }
}
