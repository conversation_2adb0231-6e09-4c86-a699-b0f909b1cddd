package com.shuyun.kylin.customized.task.integration.resource;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.task.base.feign.client.CustomerMergeServiceClient;
import com.shuyun.kylin.customized.task.integration.service.CustomerMergeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/customer")
public class CustomerMergeResource {

    @Autowired()
    private CustomerMergeService customerMergeService;

    @PostMapping("/fqn")
    public Map<String, String> updateCustomer(@RequestBody Map request) {
        log.info("单条整合请求入参: {}", JSON.toJSONString(request));

        String fqn = request.get("fqn").toString();
        String id = request.get("id").toString();
        customerMergeService.updateCustomer(fqn, id);
        Map<String, String> map = new HashMap<>();
        map.put("id", id);
        return map;
    }
}