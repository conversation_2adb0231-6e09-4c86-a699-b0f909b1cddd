package com.shuyun.kylin.customized.task.medal.job;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.kylin.customized.task.base.common.ModelConstants;
import com.shuyun.kylin.customized.task.base.config.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.task.base.feign.client.LoyaltyFacade;
import com.shuyun.kylin.customized.task.ma.util.DataapiSdkUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.enums.JobInitiationEnum;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.HashMap;

@Slf4j
@Component
public class MedalRuleScheduleJob {

    final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();

    //勋章体系id
//    final static  Integer planId = Integer.valueOf(PropsUtil.getSysOrEnv("medal.planId", "60001"));


    //勋章等级体系id
//    final static  Integer medalGradeId = Integer.valueOf(PropsUtil.getSysOrEnv("medal.gradeId", "60807"));

    /**
     * 忠诚度接口
     */
    @Autowired
    private LoyaltyFacade loyaltyFacade;

    @XxlJob(value = "medalRuleJob", cron = "0 0 * * * ? *", jobDesc = "勋章规则主数据", jobInitation = JobInitiationEnum.START)
    public ReturnT<String> medalRuleScheduleJob(String param) {
        try {
            log.info("开始拉取产品忠诚度勋章主数据");
            JSONObject gradeHierarchy = loyaltyFacade.getMedal(ConfigurationCenterUtil.planId);
            log.info("medal...gradeHierarchy:{}", gradeHierarchy);
            if (ObjectUtil.isEmpty(gradeHierarchy)) {
                throw new RuntimeException("调用内部勋章查询失败");
            }
            if (ObjectUtil.isNotEmpty(gradeHierarchy.getJSONArray("subjects"))) {
                JSONArray subjectRes = gradeHierarchy.getJSONArray("subjects");
                if (ObjectUtil.isNotEmpty(subjectRes) && subjectRes.size() > 0) {
                    JSONObject firstSubject = subjectRes.getJSONObject(0);
                    //勋章主数据
                    if (ObjectUtil.isNotEmpty(firstSubject.getJSONArray("medalHierarchies")) && firstSubject.getJSONArray("medalHierarchies").size() > 0) {
                        JSONArray medalHierarchiesJsonArray = firstSubject.getJSONArray("medalHierarchies");
                        log.info("medal...medalHierarchiesJsonArray:{}", medalHierarchiesJsonArray);
                        JSONArray medalDefinitionsJsonArray = medalHierarchiesJsonArray.getJSONObject(0).getJSONArray("medalDefinitions");
                        log.info("medal...medalDefinitionsJsonArray:{}", medalDefinitionsJsonArray);
                        for (int j = 0; j < medalDefinitionsJsonArray.size(); j++) {
                            HashMap<String, Object> jsonObject = new HashMap<>();
                            Integer id = medalDefinitionsJsonArray.getJSONObject(j).getInteger("id");
                            jsonObject.put("id", id);
                            jsonObject.put("medalDefinitionId", id);
                            jsonObject.put("lastSync", ZonedDateTime.now());
                            jsonObject.put("medalDefinitionName", medalDefinitionsJsonArray.getJSONObject(j).getString("name"));
                            jsonObject.put("typeData", "medalRule");
                            jsonObject.put("medalSort", medalDefinitionsJsonArray.getJSONObject(j).getString("sort"));
                            dataapiHttpSdk.upsert(ModelConstants.MEDAL_RULE, String.valueOf(id), jsonObject, false);
                        }
                    }
                    //勋章等级主数据
                    if (ObjectUtil.isNotEmpty(firstSubject.getJSONArray("gradeHierarchies")) && firstSubject.getJSONArray("gradeHierarchies").size() > 0) {
                        JSONArray gradeHierarchies = firstSubject.getJSONArray("gradeHierarchies");
                        log.info("medal...gradeHierarchies:{}", gradeHierarchies);
                        for (int i = 0; i < gradeHierarchies.size(); i++) {
                            if (ObjectUtil.isNotEmpty(gradeHierarchies.getJSONObject(i)) && gradeHierarchies.getJSONObject(i).size() > 0) {
                                JSONObject gradeHierarchiesJSONObject = gradeHierarchies.getJSONObject(i);
                                log.info("gradeHierarchies...等级相关体系：{}", gradeHierarchiesJSONObject);
                                if (ObjectUtil.equals(ConfigurationCenterUtil.medalGradeId, gradeHierarchiesJSONObject.getInteger("id"))) {
                                    if (ObjectUtil.isNotEmpty(gradeHierarchiesJSONObject.getJSONArray("gradeDefinitions")) && gradeHierarchiesJSONObject.getJSONArray("gradeDefinitions").size() > 0) {
                                        JSONArray gradeDefinitions = gradeHierarchiesJSONObject.getJSONArray("gradeDefinitions");
                                        for (int k = 0; k < gradeDefinitions.size(); k++) {
                                            HashMap<String, Object> jsonObject = new HashMap<>();
                                            Integer id = gradeDefinitions.getJSONObject(k).getInteger("id");
                                            jsonObject.put("id", id);
                                            jsonObject.put("medalDefinitionId", id);
                                            jsonObject.put("lastSync", ZonedDateTime.now());
                                            jsonObject.put("medalDefinitionName", gradeDefinitions.getJSONObject(k).getString("name"));
                                            jsonObject.put("typeData", "medalGradeRule");
                                            jsonObject.put("medalSort", gradeDefinitions.getJSONObject(k).getString("sort"));
                                            log.info("medal...勋章等级主数据：{}", jsonObject);
                                            dataapiHttpSdk.upsert(ModelConstants.MEDAL_RULE, String.valueOf(id), jsonObject, false);
                                        }
                                    }
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("获取勋章列表失败:{}", e);
        }
        return ReturnT.SUCCESS;
    }
}
