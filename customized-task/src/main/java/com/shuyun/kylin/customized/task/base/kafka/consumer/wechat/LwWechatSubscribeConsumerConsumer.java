package com.shuyun.kylin.customized.task.base.kafka.consumer.wechat;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.task.base.kafka.messaging.KafkaSink;
import com.shuyun.kylin.customized.task.wechat.job.service.WechatService;
import com.shuyun.kylin.customized.task.wechat.request.LwWechatSubscribeRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

/**
 * 联蔚小程序消息订阅自定义事件
 *
 */
@Slf4j
@Component
public class LwWechatSubscribeConsumerConsumer {
//
//    @Autowired
//    private WechatService wechatService;
//
//    @StreamListener(KafkaSink.KAFKA_LW_WECHAT_SUBSCRIBE_INPUT)
//    public void lwWechatSubscribeConsumer(LwWechatSubscribeRequest lwWechatRequest) {
//        try {
//            log.info("接收到数据流联蔚订阅消息入参:{}", JSON.toJSONString(lwWechatRequest));
//            wechatService.lwWechatSubscribeConsumer(lwWechatRequest);
//        } catch (Exception e) {
//            log.error("接收到数据流联蔚订阅消息失败:{}", JSON.toJSONString(lwWechatRequest),e);
//        }
//    }
}
