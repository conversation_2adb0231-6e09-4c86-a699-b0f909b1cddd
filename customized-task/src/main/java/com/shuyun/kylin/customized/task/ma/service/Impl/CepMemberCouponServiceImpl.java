package com.shuyun.kylin.customized.task.ma.service.Impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.kylin.customized.task.ma.dto.CepAppletSubscribeDto;
import com.shuyun.kylin.customized.task.ma.dto.CepCustomizationCouponDto;
import com.shuyun.kylin.customized.task.ma.dto.CepResponseResult;
import com.shuyun.kylin.customized.task.ma.service.CepMemberCouponService;
import com.shuyun.kylin.customized.task.ma.util.AuthenticationUtil;
import com.shuyun.kylin.customized.task.ma.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.task.ma.util.HttpClientHelper;
import com.shuyun.kylin.starter.redis.ICache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.stereotype.Service;

import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;


import java.util.HashMap;

import java.util.Map;

@Slf4j
@Service
public class CepMemberCouponServiceImpl implements CepMemberCouponService {

    @Autowired
    @Qualifier("redisCache")
    private ICache redisCache;

    @Override
    public CepResponseResult saveMemberCoupon(String url, CepCustomizationCouponDto body) {

        //获取token
        Object token = redisCache.get("cep_token");
        //log.info("token值:{}", token);
        if (null == token) {
            token = getToken();
        }
        Map headers = AuthenticationUtil.methodPostJson(url, token.toString(), body);
        try {
            String s1 = HttpClientHelper.doPostJSON(ConfigurationCenterUtil.CEP_URL + url, body, headers);
            CepResponseResult responseResult = JSONObject.parseObject(s1, CepResponseResult.class);
            log.info("优惠券发放结果封装customerNo:{},json:{}",body.getUid(),JSON.toJSONString(responseResult));
            return responseResult;
        } catch (RestClientException e) {
            log.info("调用cep注销接口异常customerNo:{},msg:{}", body.getUid(),e.getMessage());
        }
        return null;
    }

    @Override
    public CepResponseResult saveAppletSubscription(String url, CepAppletSubscribeDto body) {
        //获取token
        Object token = redisCache.get("cep_token");
        //log.info("token值:{}", token);
        if (null == token) {
            token = getToken();
        }
        Map headers = AuthenticationUtil.methodPostJson(url, token.toString(), body);
        String s1 = HttpClientHelper.doPostJSON(ConfigurationCenterUtil.CEP_URL + url, body, headers);
        CepResponseResult responseResult = JSONObject.parseObject(s1, CepResponseResult.class);
        log.info("小程序订阅返回结果 openid:{},json:{}", body.getToUser(),JSON.toJSONString(s1));
        return responseResult;
    }




    public String getToken() {
        log.info("url:{},username:{},password:{}", ConfigurationCenterUtil.CEP_URL, ConfigurationCenterUtil.CEP_USERNAME, ConfigurationCenterUtil.CEP_PASSWOED);
        HashMap<String, String> map = new HashMap<>();
        map.put("username", ConfigurationCenterUtil.CEP_USERNAME);
        map.put("password", ConfigurationCenterUtil.CEP_PASSWOED);

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(map), headers);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(ConfigurationCenterUtil.CEP_URL + "/backend/open-api/v1/tokens/bearer", requestEntity, String.class);
        String body = responseEntity.getBody();
        log.info("cep-token接口响应body:{}",body);
        JSONObject object = JSONObject.parseObject(body);
        Object data = object.get("data");
        Map<String, String> maps = JSONObject.parseObject(JSONObject.toJSONString(data), Map.class);
        String token = maps.get("token");
        log.info("cep最新token值: {}", token);
        redisCache.put("cep_token", token);
        return token;
    }


    public String replaceCoup(String res) {
        String s = res.replace("[", "");
        String rep = s.replace("]", "");
        String replace = rep.replace("\"", "");
        return replace;
    }

}
