package com.shuyun.kylin.customized.task.base.kafka.consumer.history;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.task.base.kafka.messaging.KafkaSink;
import com.shuyun.kylin.customized.task.medal.request.MedalObtainRequest;
import com.shuyun.kylin.customized.task.medal.service.MedalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

//初始化历史会员注册勋章
@Slf4j
@Component
public class InitHisMemberRegisterMedalConsumer03 {


    @Autowired
    private MedalService medalService;

    @StreamListener(KafkaSink.KAFKA_INIT_HIS_MEMBER_REGISTER_MEDAL_INPUT_03)
    public void initHisMemberRegisterMedalConsumer(MedalObtainRequest medalObtainRequest) {
        try {
            log.info("历史会员初始化注册勋章开始03:{}", JSON.toJSONString(medalObtainRequest));
            medalService.initHisMemberRegisterMedalConsumer(medalObtainRequest);
        } catch (Exception e) {
            log.error("历史会员初始化注册勋章失败03:{}", JSON.toJSONString(medalObtainRequest),e);
        }
    }
}
