package com.shuyun.kylin.customized.task.base.repository;


import com.shuyun.kylin.customized.task.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.task.ma.util.DataCofig;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;


import java.util.*;


@Slf4j
@Component
public class ReturnvisitNodeRepository extends BaseDsRepository<Object> {

    public List<Map<String, String>> queryListbyTaskId(String index, String taskId, String fqn, int pageSize) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId", taskId);
        queryMap.put("index", index);
        String queryMemberSql = " select id,activity,objectId,occId,brand from " + fqn + "  where taskId = :taskId and id > :index order by id limit " + pageSize;
        List<Map<String, String>> returnvisitDataDtos = execute(queryMemberSql, queryMap).getData();
//        log.info("回访批次查询结果:{} ,taskId:{} index :{}", JSON.toJSONString(returnvisitDataDtos),taskId,index);
        return returnvisitDataDtos;
    }


    public void updateCustomizedTask(String taskId, String beforeState, String afterState) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId", taskId);
        queryMap.put("beforeState", beforeState);
        queryMap.put("afterState", afterState);
        String queryMemberSql = " UPDATE  " + DataCofig.JAHUA_CUSTOMIZEDTASK + " set executeStatus = :afterState where taskId = :taskId and executeStatus = :beforeState ";
        executeSQL(queryMemberSql, queryMap);
    }

}
