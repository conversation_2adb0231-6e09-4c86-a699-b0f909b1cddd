package com.shuyun.kylin.customized.task.dataflow.service;

import com.shuyun.kylin.customized.task.integration.dto.OcpMemberBindingDto;

import java.util.Map;

public interface CosmosService {

    void queryCosmosMember(OcpMemberBindingDto ocpMemberBindingDto);

    void queryCosmosActivity(Map<String, Object> request);

    void queryCosmosWechatsubscription(Map<String, Object> request);

    void sendCosmosWechatFans(Map<String, Object> request);

    void sendCosmosWechatToaEvent(Map<String, Object> request);

    void sendCosmosCancel(Map<String, Object> request);

    void sendCosmosMaster(Map<String, Object> request);
}
