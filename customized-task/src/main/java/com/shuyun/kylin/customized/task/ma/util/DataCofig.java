package com.shuyun.kylin.customized.task.ma.util;

import com.shuyun.motor.common.cons.PropsUtil;

/**
 * <AUTHOR>
 * @Date 2022/6/20
 */

public class DataCofig {

    public static final String MEMBERTYPE = "jahwa";
    /**
     * qc-节点数据完成事件模型
     */
    public static final String JAHUA_CUSTOMIZEDTASK = "data.prctvmkt.jahwa.CustomizedTask"; //TODO 改成可乐模型

    /**
     * 小程序模板
     */
    public static final String SUBSCRIPTIONS = "data.prctvmkt.KO.SubscriptionMessage";

    public static final String MEMBER_MEMBERBINDING = "data.prctvmkt.KO.MemberBinding";

    public static final String MEMBER = "data.prctvmkt.KO.Member";

    /**
     * cosmos用户信息
     */
    public static final String COSMOS_CONSUMER = "data.prctvmkt.KO.Consumer";

    public static final String COSMOS_POINTRECORD = "data.prctvmkt.KO.CosmospointRecordlog";

    public static final String JAHUA_RETURNVISITACTIVITIES = "data.prctvmkt.jahwa.returnvisitActivities";

    public static final String JAHUA_RETURNVISITACTIVITIES_DETAIL = "data.prctvmkt.jahwa.returnvisitActivitiesdetailed";
    public static final String JAHUA_MEMBER = "data.prctvmkt.jahwa.Member";
    public static final String JAHUA_MEMBER_IDENTIFY = "data.prctvmkt.jahwa.MemberIdentify";
    public static final String JAHUA_GRADE_RECORD = "data.loyalty.member.hierarchy.GradeRecord60005";

    public static final String JAHUA_MEMBEBINDING = "data.prctvmkt.jahwa.MemberBinding";

    public static final String JAHUA_ORDER = "data.prctvmkt.jahwa.Order";
    public static final String JAHUA_MEMBERSHIP = "data.prctvmkt.jahwa.Membership";

    public static final String OLD_BI_MEMBER_LIST = "data.prctvmkt.jahwa.oldBImember";
    public static final String JAHUA_LABEL = "data.prctvmkt.jahwa.label";

    public static final String JAHUA_RETURN_VISIT_ACTIVITIER_RESULT = "data.prctvmkt.jahwa.returnvisitActivitieresult";

    public static final String JAHUA_SENFRETURNVISITACTIVITIES = "data.prctvmkt.jahwa.SenfReturnvisitActivities";

    public static final String JAHUA_RETURN_VISIT_ACTIVITIER_OUT_LOG = "data.prctvmkt.jahwa.returnvisitActivitieOutLog";

    public static final String JAHUA_MEMBERBINDLOG = "data.prctvmkt.jahwa.MemberBindingLog";

    public static final String JAHUA_POINTLOG = "data.loyalty.member.account.point.Log60004";

    public static final String JAHUA_REFUNDORDER = "data.prctvmkt.jahwa.RefundOrder";

    //优惠券项目
    public static final String COUPONPROJECT = "data.prctvmkt.jahwa.couponProject";

    /**
     * 门店主数据模型
     */
    public static final String JAHUA_SHOP = "data.prctvmkt.jahwa.Shop";


    public final static String SYSTEM_ENVIRONMENT = PropsUtil.getSysOrEnv("system.environment");

    public static class YOUZAN {
        public static final String JAHUA_COUPON_PROJECT = "data.prctvmkt.jahwa.couponProject";
        public static final String apiKey = PropsUtil.getSysOrEnv("youzan.api.key", "");
        public static final String apiSecret = PropsUtil.getSysOrEnv("youzan.api.secret", "");
        public static final String subscribeUrl = PropsUtil.getSysOrEnv("youzan.events.subscribe.url", "");
        public static final String apiUrl = PropsUtil.getSysOrEnv("youzan.api.url", "");
        public static final String clientId = PropsUtil.getSysOrEnv("youzan.clientId", "");
        public static final String eventsGroup = PropsUtil.getSysOrEnv("youzan.events.subscribe.group", "EBRAND_YOUZAN_" + SYSTEM_ENVIRONMENT);
        public static final String subscribeCouponTopics = PropsUtil.getSysOrEnv("youzan.events.subscribe.coupon.topics", "COUPON_CUSTOMER_PROMOTION");
//        public static final String shopId = PropsUtil.getSysOrEnv("youzan.shopId", "");
        public static final String couponTypeSynPageSize = PropsUtil.getSysOrEnv("youzan.couponType.syn.pageSize", "50");
    }


}
