package com.shuyun.kylin.customized.task.coupon.job;


import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.task.base.common.ModelConstants;
import com.shuyun.kylin.customized.task.base.dm.CouponExpireRemindRecordRepository;
import com.shuyun.kylin.customized.task.base.util.DateHelper;
import com.shuyun.kylin.customized.task.coupon.dto.CouponExpireRemindRecordDto;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.enums.JobInitiationEnum;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class CouponSchedulng {

    @Autowired
    private CouponExpireRemindRecordRepository couponExpireRemindRecordRepository;

    //@Scheduled(cron = "0 */5 * * * ?")
    //@Scheduled(cron = "0 0 1 * * ?")
    @XxlJob(value = "couponJob", cron = "0 0 1 * * ?", jobDesc = "优惠券即将过期", jobInitation = JobInitiationEnum.START)
    public ReturnT<String> couponJob(String param){

        final int pageSize = 1000;
        String index = "0";

        //清除昨天记录
        couponExpireRemindRecordRepository.deleteCouponById();

        while (true) {
            List<CouponExpireRemindRecordDto> couponExpireRemind;
            try {
                //查询符合规则的优惠券  （已领取  +  到期时间+5天）
                couponExpireRemind  = couponExpireRemindRecordRepository.getCouponExpireRemind(index,pageSize);
            } catch (Exception e) {
                log.error("优惠券即将查询超时index:{},pageSize:{}; 继续查询...", index, pageSize);
                continue;
            }
            if (CollectionUtils.isEmpty(couponExpireRemind)){
                break;
            }
            //遍历插入
            List<CouponExpireRemindRecordDto> couponDtoList = JSON.parseArray(JSON.toJSONString(couponExpireRemind), CouponExpireRemindRecordDto.class);
            //记录该批次中最大id值
            index = couponDtoList.get(couponDtoList.size() - 1).getId();
            try {
                for (CouponExpireRemindRecordDto coupon : couponDtoList) {
                    String projectName = couponExpireRemindRecordRepository.getCouponName(coupon.getProjectId());
                    CouponExpireRemindRecordDto recordDto = new CouponExpireRemindRecordDto();
                    recordDto.setProjectId(coupon.getProjectId());
                    recordDto.setMemberId(coupon.getMemberId());
                    recordDto.setExpiredDate(DateHelper.getZone(getStartTimeDate(coupon.getExpiredDate())));
                    recordDto.setCouponCode(coupon.getCouponCode());
                    recordDto.setCouponName(projectName);
                    couponExpireRemindRecordRepository.insert(ModelConstants.MEMBER_COUPONREMIND,recordDto);
                }
            } catch (Exception e) {
                log.error("重新优惠券项目或插入数据失败，index:{},pageSize:{},msg:{}; 继续查询...", index, pageSize,e.getMessage());
            }
            if (couponDtoList.size() < pageSize) {
                break;
            }

        }

        return ReturnT.SUCCESS;
    }

    public static String getStartTimeDate(String time){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeStrart = time.substring(0,19).replace("T"," ");
        String timeDate = "";
        try {
            Date dt=sdf.parse(timeStrart);
            Calendar rightNow = Calendar.getInstance();
            rightNow.setTime(dt);
            rightNow.add(Calendar.HOUR,8);
            Date nowTime = rightNow.getTime();
            timeDate = sdf.format(nowTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return timeDate;
    }
}
