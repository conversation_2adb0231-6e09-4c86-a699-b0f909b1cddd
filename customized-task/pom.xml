<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.shuyun.kylin.customized.crm</groupId>
        <artifactId>kylin-customized-packages</artifactId>
        <version>1.5.0-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>customized-task</artifactId>
    <name>customized-task</name>
    <packaging>jar</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <mainClass>com.shuyun.kylin.customized.task.CustomizedTaskApplication</mainClass>
        <calc.version>1.18.0.RC1</calc.version>
        <cdp.sdk.version>1.21.0.RC1</cdp.sdk.version>
    </properties>

<!--    <dependencies>
        &lt;!&ndash; api的jar&ndash;&gt;
        <dependency>
            <groupId>com.shuyun.kylin.crm</groupId>
            <artifactId>magic-cube-ds</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.api.mgmt</groupId>
            <artifactId>api-mgmt-sdk</artifactId>
            <version>1.1.1.RELEASE</version>
        </dependency>
        &lt;!&ndash; pip相关jar 脚本执行器&ndash;&gt;
        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>pip-migration-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>epassport-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>motor-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>pip-spring-boot-starter</artifactId>
        </dependency>
        &lt;!&ndash; pip相关jar 脚本执行器&ndash;&gt;
        &lt;!&ndash; cloud stream 集成 Kafka &ndash;&gt;
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-kafka</artifactId>
        </dependency>
        &lt;!&ndash; spring boot 相关 jar &ndash;&gt;
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        &lt;!&ndash; spring boot 相关 jar &ndash;&gt;

        &lt;!&ndash;数据库操作相关&ndash;&gt;
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis-spring</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-migrations</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
            <version>2.0.5</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>
        &lt;!&ndash;数据库操作相关&ndash;&gt;

        <dependency>
            <groupId>com.shuyun.kylin</groupId>
            <artifactId>xxl-job-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.kylin</groupId>
            <artifactId>signature-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.kylin</groupId>
            <artifactId>redis-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.11</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
            <version>0.9.10</version>
        </dependency>

        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
        </dependency>
        <dependency>
            <groupId>com.ning</groupId>
            <artifactId>async-http-client</artifactId>
            <version>1.8.13</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-openfeign-core</artifactId>
            <version>2.1.1.RELEASE</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>-->

    <dependencies>

        <dependency>
            <groupId>com.shuyun.calc.service</groupId>
            <artifactId>calc-online</artifactId>
            <version>${calc.version}</version>
        </dependency>
        <!-- spring boot 相关jar -->


        <!-- pip相关jar 脚本执行器-->
        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>pip-migration-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>epassport-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>motor-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>pip-spring-boot-starter</artifactId>
        </dependency>
        <!-- pip相关jar 脚本执行器-->
        <!--dataapi sdk-->
        <dependency>
            <groupId>com.shuyun.kylin.crm</groupId>
            <artifactId>magic-cube-ds</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--fastjson官方漏洞修复(https://mp.weixin.qq.com/s/vUA-rLaaeSQejQjxvb6blw) 替换上面fastjson1.2.73版本-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <!--数据库操作相关-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>
        <!-- cloud stream 集成 Kafka -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-kafka</artifactId>
        </dependency>
        <!-- cloud stream 集成 Kafka end -->
        <dependency>
            <groupId>net.sf.json-lib</groupId>
            <artifactId>json-lib</artifactId>
            <version>2.4</version>
            <classifier>jdk15</classifier>
        </dependency>
        <!--封装的starter-->
        <dependency>
            <groupId>com.shuyun.kylin</groupId>
            <artifactId>redis-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.kylin</groupId>
            <artifactId>swagger3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.kylin</groupId>
            <artifactId>signature-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.kylin.crm</groupId>
            <artifactId>openapi-spi</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuyun.kylin.crm</groupId>
            <artifactId>openapi-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuyun.kylin.crm</groupId>
            <artifactId>openapi-message-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuyun.obg</groupId>
            <artifactId>sdk</artifactId>
            <version>3.8.6</version>
        </dependency>

        <dependency>
            <groupId>com.shuyun.kylin</groupId>
            <artifactId>ebrand-member-spi</artifactId>
            <version>1.23.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.shuyun.kylin</groupId>
            <artifactId>xxl-job-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.kylin.data</groupId>
            <artifactId>data-spi</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.shuyun.dm</groupId>
            <artifactId>dm-dataapi-sdk</artifactId>
            <version>1.30.0.RC1</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.13</version>
        </dependency>
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
        </dependency>
        <dependency>
            <groupId>com.ning</groupId>
            <artifactId>async-http-client</artifactId>
            <version>1.8.13</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.kylin</groupId>
            <artifactId>cdp-starter</artifactId>
            <version>1.28.0</version>
        </dependency>
        <dependency>
            <groupId>com.shuyun.cdp</groupId>
            <artifactId>cdp-sdk</artifactId>
            <version>${cdp.sdk.version}</version>
        </dependency>

        <!--hutool 依赖-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.11</version> <!-- 请根据需要选择最新版本 -->
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.34</version>
        </dependency>

    </dependencies>

    <build>
        <defaultGoal>compile</defaultGoal>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
