kind: Service
specVersion: v4
metadata:
  name: customized-task
  apiVersion: 'v1'
  accessPoint:
    container: kylin_crm/customized-task
  middleware:
    mysql:
containers:
  - name: kylin_crm/customized-task
    imagePullPolicy: PullIfNotPresent
    ports:
      - name: customized-task
        protocol: tcp
        targetPort: 0
        containerPort: 8080
profiles:
  - name: default
    cpu: 0.5
    mem: 1024
    replicas: 1
    containers:
      - name: kylin_crm/customized-task
        cpu: 0.5
        mem: 1024