kylin customized packages
=========================
Kylin Customized Packages 用于实施定制节点、定制服务、定制节点执行、定制调度任务的整体方案包

## 模块介绍:
1. customized-service: 定制服务,用于项目提供对外定制接口的微服务
2. customized-task: 定制调度任务,用于项目定制定时任务
3. demo-node: 定制节点, 用于项目定制节点


## 开发须知:
1. kylin-customized-packages发布构建时会产生三个镜像.若只需其中一个镜像可以分别注释.sirius.yml中package和compose的dockerfile和service.yml
     ![image](./image/sirius.PNG) 

2. 定制调度任务(customized-task)开发注意事项:
  - 先发xxl-job服务端服务: xxl-job-admin, 在KYLIN-CRM里发布xxl-job-admin即可 
  - xxl-job-admin发布成功后, 登录管理界面: http://${crm域名}/xxl-job-admin/v1   注: crm域名是不需要签名校验的域名,不是ual域名
     ex: http://crm.dironman.saasproj.shuyun.com/xxl-job-admin/v1/
  - 管理界面账号: admin/Shuyun#123
  - 本地启动参数
     > vm options: -DAPI_VERSION=v1 -DAPP_VERSION=1.0.0 -DENVIRONMENT=weiyingzhou -DdevMode=true -DSANDBOX=base -Dnode_NAME=customized-task -Dsystem.config.address=************ -Dspectrum.key=config -Dspectrum.secret=123456
     > program arguments: server
  - 定制调度任务开发细节
     下面是一个定时任务的例子
```java
       @XxlJob(value = "demoJob",cron = "0 * */2 * * ? *",jobDesc="demo job",jobInitation= JobInitiationEnum.START)
       public ReturnT<String> demoJob(String param) throws Exception {
            logger.info("XXL-JOB, start ");
   
           for (int i = 0; i < 5; i++) {
               logger.info("demo job is run count:{}",i);
               TimeUnit.SECONDS.sleep(2);
           }
           logger.info("XXL-JOB, end ");
           // TODO -- 此处需要实施定制实现
           return ReturnT.SUCCESS;
       }
```
    a. 出参、入参必须与demo保持一致.
    b. @XxlJob 只需配置value,cron, jobDesc, jobInitation
       value: job 名称,必须是唯一, 用英文
       cron: job执行时间
       jobDesc: job描述
       jobInitation: job 注册时状态:  启用(START)  -- 表示定时任务注册时就启用job
                                    关闭(STOP)   -- 表示定时任务注册时不启用,若启用,需要到xxl-job-admin界面手动启动
 
3. 定制节点(demo-node)开发注意事项:
  - 服务地址 http://{{host}}:{{port}}/demo-node/v1
  - swagger开启开关: 在配置中心配置swagger2.enabled=true.
  - swagger 地址 http://{{host}}:{{port}}/demo-node/v1/swagger-ui.html 
  - 本地启动参数
     > vm options: -DAPI_VERSION=v1 -DAPP_VERSION=1.0.0 -DENVIRONMENT=weiyingzhou -DdevMode=true -DSANDBOX=base -Dnode_NAME=demo-node -Dsystem.config.address=************ -Dspectrum.key=config -Dspectrum.secret=123456 -DdevMode=true
     > program arguments: server
  - 本地调试时，可以将注册epassport菜单功能关闭
     在启动参数配置: devMode=true  
  - 开发环境配置中心: http://************/#/home 账号:config/123456
  
 
  