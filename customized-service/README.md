## 开发须知:
1. 开发环境配置中心: http://spectrum.stagecrmep.saasproj.shuyun.com  账号:dapp-plus/014E3B9B18DC4B1AA4990CBCCC9C4DA7
 
2. 需要在配置中心customized-servcie目录下添加如下配置:
ribbon.com.shuyun.motor.client.sign.enable=true
ribbon.com.shuyun.motor.client.sign.enable=${system.api.address}
system.datamodel.auto.discovery=false (仅用于开发环境,生产环境不需配置)

3. 本地启动参数 (VM Options 或 Environment variables 二者选其一配置即可)
 - VM Options: 
   -DAPI_VERSION=v2
   -DAPP_VERSION=1.0
   -DENVIRONMENT=dapp-plus
   -DSANDBOX=base
   -DSERVICE_NAME=customized-service
   -Dspectrum.key=dapp-plus
   -Dspectrum.secret=014E3B9B18DC4B1AA4990CBCCC9C4DA7
   -Dsystem.config.address=spectrum.stagecrmep.saasproj.shuyun.com
   -DMESOS_TASK_ID=11
   -Depassport/v1.ribbon.com.shuyun.motor.client.targetServer=api.dironman.saasproj.shuyun.com
   -Depassport/v1.ribbon.com.shuyun.motor.client.name=customized-service
   -Depassport/v1.ribbon.com.shuyun.motor.client.sign.secret=222sdada234g
   -Depassport/v1.ribbon.com.shuyun.motor.client.sign.enable=true
   -Dloyalty-manager/v1.ribbon.com.shuyun.motor.client.targetServer=api.dironman.saasproj.shuyun.com
   -Dloyalty-manager/v1.ribbon.com.shuyun.motor.client.name=customized-service
   -Dloyalty-manager/v1.ribbon.com.shuyun.motor.client.sign.secret=222sdada234g
   -Dloyalty-manager/v1.ribbon.com.shuyun.motor.client.sign.enable=true
   -Ddm.metadata.sdk.server=api.dironman.saasproj.shuyun.com
   -Ddm.metadata.client.http.signSecret=222sdada234g
   -Dpassport.client.http.discovery.disabled=true
   -Dpassport.client.http.sign.enabled=true
   -Dpassport.client.http.serverUrl=api.dironman.saasproj.shuyun.com   
   -Dpassport.client.http.signSecret=222sdada234g
   
 - Environment variables:
   APP_VERSION=1.0.0
   MESOS_TASK_ID=123;server.connector.port=8080
   system.config.address=spectrum.stagecrmep.saasproj.shuyun.com
   system.environment=dapp-plus
   SANDBOX=base
   spectrum.key=dapp-plus
   spectrum.secret=014E3B9B18DC4B1AA4990CBCCC9C4DA7
   ENVIRONMENT=dapp-plus
   service_name=customized-service
   spectrum.disableAutoUploadProperties=true
   API_VERSION=v1
   epassport/v1.ribbon.com.shuyun.motor.client.targetServer=api.dironman.saasproj.shuyun.com
   epassport/v1.ribbon.com.shuyun.motor.client.name=customized-service
   epassport/v1.ribbon.com.shuyun.motor.client.sign.secret=222sdada234g
   epassport/v1.ribbon.com.shuyun.motor.client.sign.enable=true