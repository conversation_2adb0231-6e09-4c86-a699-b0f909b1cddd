<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>kylin-customized-packages</artifactId>
        <groupId>com.shuyun.kylin.customized.crm</groupId>
        <version>1.5.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>customized-service</artifactId>
    <properties>
        <mainClass>com.shuyun.kylin.customized.CustomizedServiceApplication</mainClass>
        <calc.version>1.18.0.RC1</calc.version>
        <cdp.sdk.version>1.21.0.RC1</cdp.sdk.version>
    </properties>
    <dependencies>

        <dependency>
            <groupId>com.shuyun.calc.service</groupId>
            <artifactId>calc-online</artifactId>
            <version>${calc.version}</version>
        </dependency>
        <!-- spring boot 相关jar -->


        <!-- pip相关jar 脚本执行器-->
        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>pip-migration-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>epassport-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>motor-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>pip-spring-boot-starter</artifactId>
        </dependency>
        <!-- pip相关jar 脚本执行器-->
        <!--dataapi sdk-->
        <dependency>
            <groupId>com.shuyun.kylin.crm</groupId>
            <artifactId>magic-cube-ds</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--fastjson官方漏洞修复(https://mp.weixin.qq.com/s/vUA-rLaaeSQejQjxvb6blw) 替换上面fastjson1.2.73版本-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <!--数据库操作相关-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>
        <!-- cloud stream 集成 Kafka -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-kafka</artifactId>
        </dependency>
        <!-- cloud stream 集成 Kafka end -->
        <dependency>
            <groupId>net.sf.json-lib</groupId>
            <artifactId>json-lib</artifactId>
            <version>2.4</version>
            <classifier>jdk15</classifier>
        </dependency>
        <!--封装的starter-->
        <dependency>
            <groupId>com.shuyun.kylin</groupId>
            <artifactId>redis-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.kylin</groupId>
            <artifactId>swagger3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.kylin</groupId>
            <artifactId>signature-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.kylin.crm</groupId>
            <artifactId>openapi-spi</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuyun.kylin.crm</groupId>
            <artifactId>openapi-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuyun.kylin.crm</groupId>
            <artifactId>openapi-message-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuyun.obg</groupId>
            <artifactId>sdk</artifactId>
            <version>3.8.6</version>
        </dependency>

        <dependency>
            <groupId>com.shuyun.kylin</groupId>
            <artifactId>ebrand-member-spi</artifactId>
            <version>1.23.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.shuyun.kylin.data</groupId>
            <artifactId>data-spi</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.shuyun.dm</groupId>
            <artifactId>dm-dataapi-sdk</artifactId>
            <version>1.30.0.RC1</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.13</version>
        </dependency>
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
        </dependency>
        <dependency>
            <groupId>com.ning</groupId>
            <artifactId>async-http-client</artifactId>
            <version>1.8.13</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuyun.kylin</groupId>
            <artifactId>cdp-starter</artifactId>
            <version>1.28.0</version>
        </dependency>
        <dependency>
            <groupId>com.shuyun.cdp</groupId>
            <artifactId>cdp-sdk</artifactId>
            <version>${cdp.sdk.version}</version>
        </dependency>
        <!--hutool 依赖-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.11</version> <!-- 请根据需要选择最新版本 -->
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>2.3.3</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.junit.jupiter/junit-jupiter -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.8.2</version>
            <scope>test</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.mockito/mockito-core -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>5.2.0</version>
            <scope>test</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.mockito/mockito-inline -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>5.2.0</version>
            <scope>test</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/net.bytebuddy/byte-buddy -->
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <version>1.14.1</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/net.bytebuddy/byte-buddy-agent -->
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy-agent</artifactId>
            <version>1.14.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.9.2</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.9.2</version>
        </dependency>
        <dependency>
            <groupId>com.shuyun.kylin</groupId>
            <artifactId>swagger-starter</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>customized-service</finalName>
        <plugins>
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
                <version>${kotlin.version}</version>
                <executions>
                    <execution>
                        <id>compile</id>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>${project.basedir}/src/main/kotlin</sourceDir>
                                <sourceDir>${project.basedir}/src/main/java</sourceDir>
                            </sourceDirs>
                        </configuration>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>${project.basedir}/src/test/kotlin</sourceDir>
                                <sourceDir>${project.basedir}/src/test/java</sourceDir>
                            </sourceDirs>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
                <executions>
                    <!-- 替换会被 maven 特别处理的 default-compile -->
                    <execution>
                        <id>default-compile</id>
                        <phase>none</phase>
                    </execution>
                    <!-- 替换会被 maven 特别处理的 default-testCompile -->
                    <execution>
                        <id>default-testCompile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>java-compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>java-test-compile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.2</version>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>dependency/lib/</classpathPrefix>
                            <mainClass>${mainClass}</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>2.10</version>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/dependency/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>
</project>