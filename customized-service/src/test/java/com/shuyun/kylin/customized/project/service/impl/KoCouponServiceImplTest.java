package com.shuyun.kylin.customized.project.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.enums.ResponseCodeEnum;
import com.shuyun.kylin.customized.base.feign.client.OpenApiFeignClientV3;
import com.shuyun.kylin.customized.base.feign.dao.OfferProjectDetailClientResponse;
import com.shuyun.kylin.customized.project.enums.GrantStatusEnum;
import com.shuyun.kylin.customized.project.request.GrantCouponRequest;
import com.shuyun.kylin.customized.project.response.GrantCouponResponse;
import com.shuyun.kylin.customized.project.service.SendCouponService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

class KoCouponServiceImplTest {

    @Spy
    @InjectMocks
    private KoCouponServiceImpl koCouponService;

    @Mock
    private SendCouponService sendCouponService;

    @Mock
    private OpenApiFeignClientV3 openApiFeignClientV3;

    @Mock
    private DataapiHttpSdk dataapiHttpSdk;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testInstanceGrant_Success() {
//        try(MockedStatic<DataapiSdkUtil> dataapiHttpSdk = Mockito.mockStatic(DataapiSdkUtil.class)) {
            // Arrange
            GrantCouponRequest request = new GrantCouponRequest();
            request.setProjectId("project123");
            request.setChannelType("KO_MP");
            request.setPoint(100);

            OfferProjectDetailClientResponse projectDetail = new OfferProjectDetailClientResponse();
            OfferProjectDetailClientResponse.RspData rspData = new OfferProjectDetailClientResponse.RspData();
            OfferProjectDetailClientResponse.RspData.GrantRestrict grantRestrict = new OfferProjectDetailClientResponse.RspData.GrantRestrict();
            grantRestrict.setPlatformsRef("KO_MP");
            rspData.setGrantRestrict(grantRestrict);
            OfferProjectDetailClientResponse.RspData.ExtData extData = new OfferProjectDetailClientResponse.RspData.ExtData();

            extData.setPoint(100);
            extData.setIsPointExchange(true);
            rspData.setExtData(extData);
            projectDetail.setData(rspData);

            //when(koCouponService.getCouponInfoByProjectId("project123")).thenReturn(projectDetail);
            doReturn(projectDetail).when(koCouponService).getCouponInfoByProjectId("project123");
            //when(koCouponService.checkPoint(request, projectDetail)).thenReturn(true);
            doReturn(true).when(koCouponService).checkPoint(request, projectDetail);
            doReturn("template123").when(koCouponService).getTemplateIdByDb("project123");
            doNothing().when(koCouponService).innerCallIQiYi("template123", "coupon123");

            GrantCouponResponse sendCouponResult = new GrantCouponResponse();
            sendCouponResult.setCode(ResponseCodeEnum.SUCCESS.getCode());
            sendCouponResult.setCouponCode("coupon123");
            when(sendCouponService.doSendCoupon(anyString(), eq(request), eq(projectDetail)))
                    .thenReturn(new ResponseResult<>(ResponseCodeEnum.SUCCESS, sendCouponResult));

            // Act
            GrantCouponResponse response = koCouponService.instanceGrant(request);

            // Assert
            assertEquals(ResponseCodeEnum.SUCCESS.getCode(), response.getCode());
            assertEquals("coupon123", response.getCouponCode());
            assertEquals(GrantStatusEnum.SUCCESS.getCode(), response.getStatus());
//        }
    }

    @Test
    void testInstanceGrant_PointNotEnough() {
        // Arrange
        GrantCouponRequest request = new GrantCouponRequest();
        request.setProjectId("project123");
        request.setChannelType("KO_MP");
        request.setPoint(100);

        OfferProjectDetailClientResponse projectDetail = buildValidProjectDetail();
        projectDetail.getData().getExtData().setPoint(100); // 项目配置100积分


        doReturn(projectDetail).when(koCouponService).getCouponInfoByProjectId("project123");
        doReturn(false).when(koCouponService).checkPoint(request, projectDetail);

        // Act
        GrantCouponResponse response = koCouponService.instanceGrant(request);

        // Assert
        assertAll("积分不足场景",
                () -> assertEquals(ResponseCodeEnum.POINT_NOT_ENOUGH.getCode(), response.getCode()),
                () -> assertEquals(GrantStatusEnum.FAIL.getCode(), response.getStatus()),
                () -> assertNull(response.getCouponCode())
                );
    }

    @Test
    void testInstanceGrant_ChannelNotMatch() {
        // Arrange
        GrantCouponRequest request = new GrantCouponRequest();
        request.setProjectId("project123");
        request.setChannelType("WEB");
        request.setPoint(100);

        OfferProjectDetailClientResponse projectDetail = buildValidProjectDetail();
        projectDetail.getData().getExtData().setPoint(100);
        projectDetail.getData().getGrantRestrict().setPlatformsRef("MOBILE"); // 项目限制MOBILE渠道

        doReturn(projectDetail).when(koCouponService).getCouponInfoByProjectId("project123");
        doReturn(true).when(koCouponService).checkPoint(request, projectDetail);

        // Act
        GrantCouponResponse response = koCouponService.instanceGrant(request);

        // Assert
        assertAll("渠道不匹配场景",
                () -> assertEquals(ResponseCodeEnum.GRANT_CHANNEL_NOT_MATCH.getCode(), response.getCode()),
                () -> assertEquals(GrantStatusEnum.FAIL.getCode(), response.getStatus()),
                () -> assertNull(response.getCouponCode())
                );
    }

    @Test
    void testInstanceGrant_Exception() {
        // Arrange
        GrantCouponRequest request = new GrantCouponRequest();
        request.setProjectId("project123");

        doThrow(new RuntimeException("模拟异常"))
                .when(koCouponService).getCouponInfoByProjectId("project123");

        // Act
        GrantCouponResponse response = koCouponService.instanceGrant(request);

        // Assert
        assertAll("异常处理场景",
                () -> assertEquals(ResponseCodeEnum.OPENAPI_FAILED.getCode(), response.getCode()),
                () -> assertEquals(GrantStatusEnum.FAIL.getCode(), response.getStatus()),
                () -> assertNotNull(response.getMsg())
        );
    }

    @Test
    void testInstanceGrant_RequestGetPointNull() {
        GrantCouponRequest request = new GrantCouponRequest();
        request.setProjectId("project123");
        request.setChannelType("KO_MP");

        OfferProjectDetailClientResponse projectDetail = buildValidProjectDetail();
        projectDetail.getData().getExtData().setPoint(100);
        doReturn(projectDetail).when(koCouponService).getCouponInfoByProjectId("project123");
        doReturn(true).when(koCouponService).checkPoint(request, projectDetail);

        // Act
        GrantCouponResponse response = koCouponService.instanceGrant(request);

        assertAll("Request.getPoint为Null值",
                () -> assertEquals(ResponseCodeEnum.OPENAPI_FAILED.getCode(), response.getCode()),
                () -> assertEquals(GrantStatusEnum.FAIL.getCode(), response.getStatus())
                );
    }

    @Test
    void testInstanceGrant_ProjectDetailGetDataGetExtDataGetPointNull() {
        GrantCouponRequest request = new GrantCouponRequest();
        request.setProjectId("project123");
        request.setChannelType("KO_MP");
        request.setPoint(100);

        OfferProjectDetailClientResponse projectDetail = buildValidProjectDetail();
        doReturn(projectDetail).when(koCouponService).getCouponInfoByProjectId("project123");
        doReturn(true).when(koCouponService).checkPoint(request, projectDetail);

        // Act
        GrantCouponResponse response = koCouponService.instanceGrant(request);

        assertAll("projectDetail.getData.getExtData.getPoint为Null值",
                () -> assertEquals(ResponseCodeEnum.OPENAPI_FAILED.getCode(), response.getCode()),
                () -> assertEquals(GrantStatusEnum.FAIL.getCode(), response.getStatus())
        );
    }

    @Test
    void testInstanceGrant_RequestGetPointProjectDetailGetPointNull() {
        GrantCouponRequest request = new GrantCouponRequest();
        request.setProjectId("project123");
        request.setChannelType("KO_MP");

        OfferProjectDetailClientResponse projectDetail = buildValidProjectDetail();
        doReturn(projectDetail).when(koCouponService).getCouponInfoByProjectId("project123");
        doReturn(true).when(koCouponService).checkPoint(request, projectDetail);

        // Act
        GrantCouponResponse response = koCouponService.instanceGrant(request);

        assertAll("两个getPoint都为Null值",
                () -> assertEquals(ResponseCodeEnum.OPENAPI_FAILED.getCode(), response.getCode()),
                () -> assertEquals(GrantStatusEnum.FAIL.getCode(), response.getStatus())
        );
    }

    @Test
    void testInstanceGrant_PointIsNotCorrect() {
        GrantCouponRequest request = new GrantCouponRequest();
        request.setProjectId("project123");
        request.setChannelType("KO_MP");
        request.setPoint(100);

        OfferProjectDetailClientResponse projectDetail = buildValidProjectDetail();
        projectDetail.getData().getExtData().setPoint(200);
        doReturn(projectDetail).when(koCouponService).getCouponInfoByProjectId("project123");
        doReturn(true).when(koCouponService).checkPoint(request, projectDetail);

        // Act
        GrantCouponResponse response = koCouponService.instanceGrant(request);

        assertAll("兑换积分不对",
                () -> assertEquals(ResponseCodeEnum.GRANT_COUPON_FAILED.getCode(), response.getCode()),
                () -> assertEquals(GrantStatusEnum.FAIL.getCode(), response.getStatus())
        );
    }

    @Test
    void testInstanceGrant_NoGrantRestrict() {
        try(MockedStatic<ObjectUtil> mockedObjectUtil = mockStatic(ObjectUtil.class)) {
            GrantCouponRequest request = new GrantCouponRequest();
            request.setProjectId("project123");
            request.setChannelType("KO_MP");
            request.setPoint(100);

            OfferProjectDetailClientResponse projectDetail = buildValidProjectDetail();
            projectDetail.getData().getExtData().setPoint(100);
            mockedObjectUtil.when(() -> ObjectUtil.isEmpty(projectDetail.getData().getGrantRestrict()))
                    .thenReturn(true);
            doReturn("template123").when(koCouponService).getTemplateIdByDb("project123");
            doReturn(projectDetail).when(koCouponService).getCouponInfoByProjectId("project123");
            doReturn(true).when(koCouponService).checkPoint(request, projectDetail);

            // Act
            GrantCouponResponse response = koCouponService.instanceGrant(request);

            assertAll("没有设置发券渠道限制",
                    () -> assertEquals(ResponseCodeEnum.GRANT_CHANNEL_NOT_MATCH.getCode(), response.getCode())
            );
        }
    }

    @Test
    void testInstanceGrant_ExternalInstanceGrantFailed() {
        try(MockedStatic<ObjectUtil> mockedObjectUtil = mockStatic(ObjectUtil.class)) {
            GrantCouponRequest request = new GrantCouponRequest();
            request.setProjectId("project123");
            request.setChannelType("KO_MP");
            request.setPoint(100);

            OfferProjectDetailClientResponse projectDetail = buildValidProjectDetail();
            projectDetail.getData().getExtData().setPoint(100);

            GrantCouponResponse sendCouponResult = new GrantCouponResponse();
            sendCouponResult.setCouponCode("coupon123");
            sendCouponResult.setCode("500");
            when(sendCouponService.doSendCoupon(anyString(), eq(request), eq(projectDetail)))
                    .thenReturn(new ResponseResult<>(ResponseCodeEnum.SUCCESS, sendCouponResult));

            mockedObjectUtil.when(() -> ObjectUtil.notEqual(anyString(),eq(ResponseCodeEnum.SUCCESS.getCode())))
                    .thenReturn(true);

            doReturn("template123").when(koCouponService).getTemplateIdByDb("project123");
            doReturn(projectDetail).when(koCouponService).getCouponInfoByProjectId("project123");
            doReturn(true).when(koCouponService).checkPoint(request, projectDetail);

            // Act
            GrantCouponResponse response = koCouponService.instanceGrant(request);
            assertAll("外部发券失败",
                    () -> assertEquals(GrantStatusEnum.FAIL.getCode(), response.getStatus())
            );
        }
    }
    // 公共方法抽取
    private OfferProjectDetailClientResponse buildValidProjectDetail() {
        OfferProjectDetailClientResponse projectDetail = new OfferProjectDetailClientResponse();
        OfferProjectDetailClientResponse.RspData rspData = new OfferProjectDetailClientResponse.RspData();

        OfferProjectDetailClientResponse.RspData.GrantRestrict grantRestrict =
                new OfferProjectDetailClientResponse.RspData.GrantRestrict();
        grantRestrict.setPlatformsRef("KO_MP");

        OfferProjectDetailClientResponse.RspData.ExtData extData =
                new OfferProjectDetailClientResponse.RspData.ExtData();
        //extData.setPoint(100);
        extData.setIsPointExchange(true);

        rspData.setGrantRestrict(grantRestrict);
        rspData.setExtData(extData);
        projectDetail.setData(rspData);
        return projectDetail;
    }
}
