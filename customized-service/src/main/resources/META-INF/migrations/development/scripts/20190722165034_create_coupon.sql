DROP TABLE IF EXISTS `tb_coupon`;
CREATE TABLE `tb_coupon` (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  project_id  varchar(150) not null comment '项目id',
  `type` varchar(50) not null comment '类型',
  batch_id varchar(150) not null comment '批次id',
  sub_job_id  varchar(150) not null comment '子任务id',
  node_id  varchar(150) not null comment '节点id',
  customer_no varchar(150) not null comment '客户编号',
  coupon varchar(150) not null comment '优惠券码',
  status varchar(50)  comment '状态',
  create_time datetime DEFAULT NULL COMMENT '创建时间',
  create_by  bigint(20) DEFAULT NULL COMMENT '创建人Id',
  update_time datetime DEFAULT NULL COMMENT '更新时间',
  update_by  bigint(20) DEFAULT NULL COMMENT '更新人id',
  PRIMARY KEY (id)

) ENGINE = InnoDB DEFAULT CHARSET = utf8;