CREATE TABLE `t_model_mapping` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `member_type` varchar(64) NOT NULL COMMENT '会员类型',
  `model_name` varchar(250) NOT NULL COMMENT '模型类型 CUSTOMER:客户信息,MEMBER:会员信息:',
  `fqn_name` varchar(255) NOT NULL COMMENT '模型全限定名称(namespace+fqn)',
  `create_at` datetime DEFAULT NULL,
  `update_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='业务模型与数据服务模型对应关系表';