<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>

    <context id="MysqlContext" targetRuntime="MyBatis3Simple" defaultModelType="flat">

        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <commentGenerator>
            <property name="suppressAllComments" value="true"/>
            <property name="suppressDate" value="true"/>
        </commentGenerator>

        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="******************************************************************************************************************************"
                        userId="qa1ccms"
                        password="qa1ccms">
        </jdbcConnection>

        <javaModelGenerator targetPackage="com.shuyun.crmep.customized.common.domain"
                            targetProject="./src/main/java">
            <property name="enableSubPackages" value="false"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="sqlmap/common" targetProject="./src/main/resources"/>

        <javaClientGenerator type="XMLMAPPER" targetPackage="com.shuyun.crmep.customized.common.mapper"
                             targetProject="./src/main/java">
            <property name="enableSubPackages" value="false"/>
            <property name="methodNameCalculator" value="extended"/>
        </javaClientGenerator>

<!--
        <table tableName="t_plat" domainObjectName="Plat"></table>
        <table tableName="t_shop" domainObjectName="Shop"></table>-->
        <table tableName="t_member_grade" domainObjectName="MemberGrade"></table>
        <table tableName="t_member_point" domainObjectName="MemberPoint"></table>

    </context>
</generatorConfiguration>