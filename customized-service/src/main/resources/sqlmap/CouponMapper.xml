<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuyun.kylin.customized.coupon.mapper.CouponMapper">
  <resultMap id="BaseResultMap" type="com.shuyun.kylin.customized.coupon.domain.Coupon">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="batch_id" jdbcType="VARCHAR" property="batchId" />
    <result column="sub_job_id" jdbcType="VARCHAR" property="subJobId" />
    <result column="node_id" jdbcType="VARCHAR" property="nodeId" />
    <result column="customer_no" jdbcType="VARCHAR" property="customerNo" />
    <result column="coupon" jdbcType="VARCHAR" property="coupon" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
  </resultMap>
  <!--<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tb_coupon
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.shuyun.crmep.customized.coupon.domain.Coupon">
    insert into tb_coupon (id, project_id, type, 
      batch_id, sub_job_id, node_id, 
      customer_no, coupon, status, 
      create_time, create_by, update_time, 
      update_by)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, 
      #{batchId,jdbcType=VARCHAR}, #{subJobId,jdbcType=VARCHAR}, #{nodeId,jdbcType=VARCHAR}, 
      #{customerNo,jdbcType=VARCHAR}, #{coupon,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=BIGINT})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.shuyun.crmep.customized.coupon.domain.Coupon">
    update tb_coupon
    set project_id = #{projectId,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      batch_id = #{batchId,jdbcType=VARCHAR},
      sub_job_id = #{subJobId,jdbcType=VARCHAR},
      node_id = #{nodeId,jdbcType=VARCHAR},
      customer_no = #{customerNo,jdbcType=VARCHAR},
      coupon = #{coupon,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select id, project_id, type, batch_id, sub_job_id, node_id, customer_no, coupon, 
    status, create_time, create_by, update_time, update_by
    from tb_coupon
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, project_id, type, batch_id, sub_job_id, node_id, customer_no, coupon, 
    status, create_time, create_by, update_time, update_by
    from tb_coupon
  </select>-->
  <select id="selectParam" parameterType="com.shuyun.kylin.customized.coupon.domain.Coupon" resultType="com.shuyun.kylin.customized.coupon.domain.Coupon">
    select
        t.id,
        t.project_id projectId,
        t.`type` ,
        t.batch_id batchId,
        t.sub_job_id subJobId,
        t.node_id nodeId,
        t.customer_no customerNo,
        t.coupon coupon,
        t.status,
        t.create_time createTime,
        t.create_by createBy,
        t.update_time updateTime,
        t.update_by updateBy
    from tb_coupon t

    where

    <if test="batchId != null"  >
      t.batch_id = #{batchId,jdbcType=VARCHAR}
    </if>

    <if test="type != null"  >
      and t.`type` = #{type,jdbcType=VARCHAR}
    </if>

  </select>



  <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
    insert into tb_coupon (id, project_id, type,
      batch_id, sub_job_id, node_id,
      customer_no, coupon, status,
      create_time, create_by, update_time,
      update_by)
    values
    <foreach collection="list" item="item" index="index" separator=",">
     (#{item.id,jdbcType=BIGINT}, #{item.projectId,jdbcType=VARCHAR}, #{item.type,jdbcType=VARCHAR},
      #{item.batchId,jdbcType=VARCHAR}, #{item.subJobId,jdbcType=VARCHAR}, #{item.nodeId,jdbcType=VARCHAR},
      #{item.customerNo,jdbcType=VARCHAR}, #{item.coupon,jdbcType=VARCHAR}, #{item.status,jdbcType=VARCHAR},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP},
      #{item.updateBy,jdbcType=BIGINT})
    </foreach>
  </insert>

</mapper>