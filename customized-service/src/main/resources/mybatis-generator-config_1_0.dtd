<?xml version="1.0" encoding="UTF-8"?>
        <!--

               Copyright 2006-2016 the original author or authors.

               Licensed under the Apache License, Version 2.0 (the "License");
               you may not use this file except in compliance with the License.
               You may obtain a copy of the License at

                  http://www.apache.org/licenses/LICENSE-2.0

               Unless required by applicable law or agreed to in writing, software
               distributed under the License is distributed on an "AS IS" BASIS,
               WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
               See the License for the specific language governing permissions and
               limitations under the License.

        -->
        <!--
          This DTD defines the structure of the MyBatis generator configuration file.
          Configuration files should declare the DOCTYPE as follows:

          <!DOCTYPE generatorConfiguration PUBLIC
            "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
            "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

          Please see the documentation included with MyBatis generator for details on each option
          in the DTD.  You may also view documentation on-line here:

          http://www.mybatis.org/generator/

        -->

        <!--
          The generatorConfiguration element is the root element for configurations.
        -->
        <!ELEMENT generatorConfiguration (properties?, classPathEntry*, context+)>

        <!--
          The properties element is used to define a standard Java properties file
          that contains placeholders for use in the remainder of the configuration
          file.
        -->
        <!ELEMENT properties EMPTY>
        <!ATTLIST properties
                resource CDATA #IMPLIED
                url CDATA #IMPLIED>

        <!--
          The context element is used to describer a context for generating files, and the source
          tables.
        -->
        <!ELEMENT context (property*, plugin*, commentGenerator?, (connectionFactory | jdbcConnection), javaTypeResolver?,
                javaModelGenerator, sqlMapGenerator?, javaClientGenerator?, table+)>
        <!ATTLIST context id ID #REQUIRED
                defaultModelType CDATA #IMPLIED
                targetRuntime CDATA #IMPLIED
                introspectedColumnImpl CDATA #IMPLIED>

        <!--
          The connectionFactory element is used to describer the connection factory used
          for connecting to the database for introspection.  Either connectionFacoty
          or jdbcConnection must be specified, but not both.
        -->
        <!ELEMENT connectionFactory (property*)>
        <!ATTLIST connectionFactory
                type CDATA #IMPLIED>

        <!--
          The jdbcConnection element is used to describer the JDBC connection that the generator
          will use to introspect the database.
        -->
        <!ELEMENT jdbcConnection (property*)>
        <!ATTLIST jdbcConnection
                driverClass CDATA #REQUIRED
                connectionURL CDATA #REQUIRED
                userId CDATA #IMPLIED
                password CDATA #IMPLIED>

        <!--
          The classPathEntry element is used to add the JDBC driver to the run-time classpath.
          Repeat this element as often as needed to add elements to the classpath.
        -->
        <!ELEMENT classPathEntry EMPTY>
        <!ATTLIST classPathEntry
                location CDATA #REQUIRED>

        <!--
          The property element is used to add custom properties to many of the generator's
          configuration elements.  See each element for example properties.
          Repeat this element as often as needed to add as many properties as necessary
          to the configuration element.
        -->
        <!ELEMENT property EMPTY>
        <!ATTLIST property
                name CDATA #REQUIRED
                value CDATA #REQUIRED>

        <!--
          The plugin element is used to define a plugin.
        -->
        <!ELEMENT plugin (property*)>
        <!ATTLIST plugin
                type CDATA #REQUIRED>

        <!--
          The javaModelGenerator element is used to define properties of the Java Model Generator.
          The Java Model Generator builds primary key classes, record classes, and Query by Example
          indicator classes.
        -->
        <!ELEMENT javaModelGenerator (property*)>
        <!ATTLIST javaModelGenerator
                targetPackage CDATA #REQUIRED
                targetProject CDATA #REQUIRED>

        <!--
          The javaTypeResolver element is used to define properties of the Java Type Resolver.
          The Java Type Resolver is used to calculate Java types from database column information.
          The default Java Type Resolver attempts to make JDBC DECIMAL and NUMERIC types easier
          to use by substituting Integral types if possible (Long, Integer, Short, etc.)
        -->
        <!ELEMENT javaTypeResolver (property*)>
        <!ATTLIST javaTypeResolver
                type CDATA #IMPLIED>

        <!--
          The sqlMapGenerator element is used to define properties of the SQL Map Generator.
          The SQL Map Generator builds an XML file for each table that conforms to iBATIS'
          SqlMap DTD.
        -->
        <!ELEMENT sqlMapGenerator (property*)>
        <!ATTLIST sqlMapGenerator
                targetPackage CDATA #REQUIRED
                targetProject CDATA #REQUIRED>

        <!--
          The javaClientGenerator element is used to define properties of the Java client Generator.
          The Java Client Generator builds Java interface and implementation classes
          (as required) for each table.
          If this element is missing, then the generator will not build Java Client classes.
        -->
        <!ELEMENT javaClientGenerator (property*)>
        <!ATTLIST javaClientGenerator
                type CDATA #REQUIRED
                targetPackage CDATA #REQUIRED
                targetProject CDATA #REQUIRED
                implementationPackage CDATA #IMPLIED>

        <!--
          The table element is used to specify a database table that will be the source information
          for a set of generated objects.
        -->
        <!ELEMENT table (property*, generatedKey?, columnRenamingRule?, (columnOverride | ignoreColumn | ignoreColumnsByRegex)*) >
        <!ATTLIST table
                catalog CDATA #IMPLIED
                schema CDATA #IMPLIED
                tableName CDATA #REQUIRED
                alias CDATA #IMPLIED
                domainObjectName CDATA #IMPLIED
                mapperName CDATA #IMPLIED
                sqlProviderName CDATA #IMPLIED
                enableInsert CDATA #IMPLIED
                enableSelectByPrimaryKey CDATA #IMPLIED
                enableSelectByExample CDATA #IMPLIED
                enableUpdateByPrimaryKey CDATA #IMPLIED
                enableDeleteByPrimaryKey CDATA #IMPLIED
                enableDeleteByExample CDATA #IMPLIED
                enableCountByExample CDATA #IMPLIED
                enableUpdateByExample CDATA #IMPLIED
                selectByPrimaryKeyQueryId CDATA #IMPLIED
                selectByExampleQueryId CDATA #IMPLIED
                modelType CDATA #IMPLIED
                escapeWildcards CDATA #IMPLIED
                delimitIdentifiers CDATA #IMPLIED
                delimitAllColumns CDATA #IMPLIED>

        <!--
          The columnOverride element is used to change certain attributes of the column
          from their default values.
        -->
        <!ELEMENT columnOverride (property*)>
        <!ATTLIST columnOverride
                column CDATA #REQUIRED
                property CDATA #IMPLIED
                javaType CDATA #IMPLIED
                jdbcType CDATA #IMPLIED
                typeHandler CDATA #IMPLIED
                isGeneratedAlways CDATA #IMPLIED
                delimitedColumnName CDATA #IMPLIED>

        <!--
          The ignoreColumn element is used to identify a column that should be ignored.
          No generated SQL will refer to the column, and no property will be generated
          for the column in the model objects.
        -->
        <!ELEMENT ignoreColumn EMPTY>
        <!ATTLIST ignoreColumn
                column CDATA #REQUIRED
                delimitedColumnName CDATA #IMPLIED>

        <!--
          The ignoreColumnsByRegex element is used to identify a column pattern that should be ignored.
          No generated SQL will refer to the column, and no property will be generated
          for the column in the model objects.
        -->
        <!ELEMENT ignoreColumnsByRegex (except*)>
        <!ATTLIST ignoreColumnsByRegex
                pattern CDATA #REQUIRED>

        <!--
          The except element is used to identify an exception to the ignoreColumnsByRegex rule.
          If a column matches the regex rule, but also matches the exception, then the
          column will be included in the generated objects.
        -->
        <!ELEMENT except EMPTY>
        <!ATTLIST except
                column CDATA #REQUIRED
                delimitedColumnName CDATA #IMPLIED>

        <!--
          The generatedKey element is used to identify a column in the table whose value
          is calculated - either from a sequence (or some other query), or as an identity column.
        -->
        <!ELEMENT generatedKey EMPTY>
        <!ATTLIST generatedKey
                column CDATA #REQUIRED
                sqlStatement CDATA #REQUIRED
                identity CDATA #IMPLIED
                type CDATA #IMPLIED>

        <!--
          The columnRenamingRule element is used to specify a rule for renaming
          columns before the corresponding property name is calculated
        -->
        <!ELEMENT columnRenamingRule EMPTY>
        <!ATTLIST columnRenamingRule
                searchString CDATA #REQUIRED
                replaceString CDATA #IMPLIED>

        <!--
          The commentGenerator element is used to define properties of the Comment Generator.
          The Comment Generator adds comments to generated elements.
        -->
        <!ELEMENT commentGenerator (property*)>
        <!ATTLIST commentGenerator
                type CDATA #IMPLIED>
  