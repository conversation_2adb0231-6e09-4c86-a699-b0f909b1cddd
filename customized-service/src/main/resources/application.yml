
application:
  server:
    name: customized-service

server:
  servlet:
    context-path: /customized-service/v1
  undertow:
      io-threads: 16
      worker-threads: 256
      buffer-size: 1024
      direct-buffers: true

logging:
  config: classpath:logback-spring.xml
  level:
    com:
      shuyun:
        crmep:
          customized: INFO
spring:
  application:
    name: customized-service
  jackson:
      default-property-inclusion: non_null
      date-format: yyyy-MM-dd'T'HH:mm:ss.SSSXXX
      time-zone: GMT+8 # Asia/Shanghai
  cloud:
    stream:
      bindings:
        kafka_input:  #channelName
          destination: ${system.environment}_${coupon.node.output.topic:COUPON_NODE_OUTPUT_DATA} #CRM_COUPONS_IN  #kafka topic 消费消息
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP

        kafka_output:   #channelName
          destination: ${system.environment}_${coupon.node.receive.result.topic:COUPON_NODE_RECEIVE_RESULT}  #COUPON_NODE_RECEIVE_RESULT #kafka topic
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP  #分组

        kafka_notification: #channelName
          destination: marketing-action-task-notification
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP
        kafka_behavior: #channelName
          destination: marketing-action-custom-reach-behavior
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP
        kafka_callback: #channelName
          destination: marketing-action-task-notification-callback
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP
        kafka_appletsuBscribeOutput: #channelName
          destination: marketing-action-appletsuBscribeOutput
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP
        kafka_appletsuBscribeInput: #channelName
          destination: marketing-action-appletsuBscribeInput
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP

        kafka_input_add_checklist:
          destination: KAFKA_INPUT_ADD_CHECKLIST
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROU

        kafka_input_beyond_point_add_checklist:
          destination: KAFKA_INPUT_BEYOND_POINT_ADD_CHECKLIST
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROU

        kafka_input_beyond_point_add_appletsuBscribe:
          destination: KAFKA_INPUT_BEYOND_POINT_ADD_APPLETSUBSCRIBE
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROU

        #同步消费KAFKA太古注册
        kafka_send_swire_member_input:
          destination: KAFKA_SEND_SWIRE_MEMBER_INPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP
        #同步消费KAFKA太古更新
        kafka_update_swire_member_input:
          destination: KAFKA_UPDATE_SWIRE_MEMBER_INPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_COUPONS_GROUP

        #会员参与活动异步处理
        kafka_member_campaign_output:
          destination:  KAFKA_MEMBER_CAMPAIGN_OUTPUT
          #设置消息的类型
          contentType: application/json
          nativeDecoding: true
          group: CRM_MEMBER_CAMPAIGN_GROUP  #分组
#          #勋章等级进度计算_会员参与活动
#        kafka_medal_progress_campaign_input:
#          destination: KAFKA_MEDAL_PROGRESS_CAMPAIGN_INPUT
#          #设置消息的类型
#          contentType: application/json
#          nativeDecoding: true
#          group: CRM_COUPONS_GROUP
#
#          #勋章等级进度计算_累计积分
#        kafka_medal_progress_point_input:
#          destination: KAFKA_MEDAL_PROGRESS_POINT_INPUT
#          #设置消息的类型
#          contentType: application/json
#          nativeDecoding: true
#          group: CRM_COUPONS_GROUP

      kafka:
        consumer:
#          # 手动提交必须设置为false 要不不生效
#          enable-auto-commit: false
           group-id: CRM_COUPONS_GROUP #指定默认消费组
#        listener:
#            # 手动提交模式
#          ack-mode: manual
        binder:
          brokers: ${kafka.address}
          configuration:
            auto:
              offset:
                reset: latest
          minPartitionCount: ${kafka.min.partition.count:1}
          autoAddPartitions: true
          consumer-properties:
            session.timeout.ms: ${kafka.session.timeout.ms:60000}
            max.poll.records: ${kafka.max.poll.records:1}
            auto.commit.interval.ms: 2000
            max.poll.interval.ms: ${kafka.max.poll.interval.ms:600000}
      default-binder: kafka
  messages:
    basename: i18n/messages
    cache-duration: 3600
    encoding: UTF-8
system:
  kafka:
    enabled: true
customized-service:
  openapi:
    spi:
      enabled : false
error-handler:
  module-code: 03

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
