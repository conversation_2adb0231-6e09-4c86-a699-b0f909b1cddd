

-- 目标表
-- 互动记录表：ir_dev_point_record

-- 0、创建目标表-互动活动记录数据迁移表
DROP TABLE IF EXISTS `ir_history_interactive_records_patch`;
CREATE TABLE `ir_history_interactive_records_patch`
(
    `id`                  varchar(32) NOT NULL COMMENT 'id',
    `customerNo`          varchar(255)   DEFAULT NULL COMMENT '会员渠道编号',
    `joinTime`            datetime(3) NOT NULL COMMENT '参与时间',
    `signTime`            bigint(8) DEFAULT NULL COMMENT '连续签到次数',
    `channelType`         varchar(32)    DEFAULT NULL COMMENT '渠道',
    `interactiveRecordId` varchar(255)   DEFAULT NULL COMMENT '参与互动记录id',
    `mobile`              varchar(255)   DEFAULT NULL COMMENT '参与人手机号',
    `interactive`         varchar(32)    DEFAULT NULL COMMENT '互动编号',
    `interactiveType`     varchar(32)    DEFAULT NULL COMMENT '参与行为类型',
    `invitee`             varchar(64)    DEFAULT NULL COMMENT '被邀请者的渠道编号',
    `inviteeMember`       varchar(32)    DEFAULT NULL COMMENT '被邀请者',
    `member`              varchar(32)    DEFAULT NULL COMMENT '	会员ID',
    `memberIDStr`         varchar(64)    DEFAULT NULL COMMENT '会员ID String',
    `isGetRewards`        tinyint(4) DEFAULT NULL COMMENT '是否获奖',
    `rewardsType`         varchar(64)    DEFAULT NULL COMMENT '获奖类型',
    `memberIntegral`      decimal(21, 3) DEFAULT NULL COMMENT '本次互动发放的积分数',
    `grantStatus`         varchar(32)    DEFAULT NULL COMMENT '发放状态',
    `created`             datetime(3) DEFAULT NULL COMMENT '创建时间',
    `lastSync`            datetime(3) DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='互动活动记录数据迁移表';

-- 源表：
-- ir_dev_point_record 互动获得积分记录
-- ir_dev_member 会员表

-- 2、插入历史表：ir_history_interactive_records_5
INSERT INTO `ir_history_interactive_records_patch`
SELECT UUID_SHORT() AS `id`,                  -- id,逻辑：uuid
       point.`customerNo`                                 AS `customerNo`,          -- 会员渠道编号
      adddate(point.`changeTime`,interval -8 hour)                                AS `joinTime`,            -- 参与时间
       CASE
           WHEN ir_splitStr(point.`desc`, '#', 2) = '签到(连续签到3天)' THEN 3
           ELSE NULL
           END                                            AS `signTime`,            -- 连续签到次数，逻辑：只有连续签到才置为 3
       'KO_MP'                                            AS `channelType`,         -- 渠道，逻辑：写死 KO_MP
       point.`businessId`                                 AS `interactiveRecordId`, -- 参与互动记录id，逻辑：取积分流水.businessId
       m1.`mobile`                                        AS `mobile`,              -- 参与人手机号，逻辑：取会员表中的手机号
       CASE
           WHEN ir_splitStr(point.`desc`, '#', 2) = '签到(连续签到3天)' THEN 'CONSECUTIVE_CHECK_IN'
           ELSE ir_splitStr(point.`desc`, '#', 1)
           END                                            AS `interactive`,         -- 互动编号，逻辑：积分流水中的 desc 字段，crm 已经洗好了
       CASE
           WHEN ir_splitStr(point.`desc`, '#', 2) = '签到(连续签到3天)' THEN 'CONSECUTIVE_CHECK_IN'
           ELSE ir_splitStr(point.`desc`, '#', 1)
           END                                            AS `interactiveType`,     -- 参与行为类型，逻辑：根据积分流水的 `desc` 字段进行case
       point.`invitee`                                    AS `invitee`,             -- 被邀请者的渠道编号
       m2.`memberId`                                      AS `inviteeMember`,       -- 被邀请者
       m1.`memberId`                                      AS `member`,              -- 会员ID，逻辑：从发放记录表customerNo，关联关系表后，取memberId
       m1.`memberId`                                      AS `memberIDStr`,         -- 会员ID String，逻辑同：member
       1                                                  AS `isGetRewards`,        -- 是否获奖，逻辑：能走到这里的，都是获奖的，写死1
       '发积分'                                              AS `rewardsType`,         -- 获奖类型，逻辑：写死 "发积分"
       point.`changePoint`                                AS `memberIntegral`,      -- 本次互动发放的积分数，逻辑：积分变更记录.changePoint
       '0'                                                AS `grantStatus`,         -- 发放状态，逻辑：写死0，0-发放权益成功
      adddate(point.`changeTime`,interval -8 hour)                                AS `created`,             -- 创建时间，逻辑：积分变更记录.changeTime
       adddate(now(),interval -8 hour)                                              AS `lastSync`             -- 更新时间
FROM `ir_patch` point
         INNER JOIN `ir_member_all` m1 on point.`customerNo` = m1.`customerNo`
         LEFT JOIN `ir_member_all` m2 on point.`invitee` = m2.`customerNo`
;

-- 受影响的行: 71172
-- 时间: 100.299s