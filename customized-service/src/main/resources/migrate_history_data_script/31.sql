drop table if exists tmp_ir_0531_check_data;
create table tmp_ir_0531_check_data as
select id, interactiveRecordId, customerNo, signTime, joinTime, interactiveType, memberIntegral, grantStatus, isGetRewards, refuseReason
from ecoke_datamodel.d_p_k_interactiverecords_355
where  joinTime >= SUBTIME('2022-05-31 00:00:00.000', '08:00:00')
  and joinTime <= SUBTIME('2022-05-31 23:59:59.999', '08:00:00')
  and interactiveType in ('CHECK_IN', 'CONSECUTIVE_CHECK_IN');
