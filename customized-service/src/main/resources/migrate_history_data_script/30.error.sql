drop table if exists tmp_ir_last_6_all;
create table tmp_ir_last_6_all as
select id, interactiveRecordId, customerNo, signTime, joinTime, interactiveType, memberIntegral
from ecoke_datamodel.d_p_k_interactiverecords_355
where joinTime >= SUBTIME('2022-05-25 00:00:00.000', '08:00:00')
  and memberIntegral > 0
  and refuseReason is null
  and (interactiveType like 'CHECK_IN%' or interactiveType = 'CONSECUTIVE_CHECK_IN');


ALTER TABLE `tmp_ir_last_6_all`
    ADD INDEX `inx_cn` (`customerNo`) ,
ADD INDEX `inx_ii` (`interactiveRecordId`) ,
ADD INDEX `inx_id` (`id`);


drop table if exists tmp_error_c_ids;
create table tmp_error_c_ids as
select id, interactiveRecordId, customerNo, signTime, joinTime, interactiveType, memberIntegral
from ecoke_datamodel.d_p_k_interactiverecords_355
where memberIntegral = 2
  and joinTime >= '2022-05-25 16:00:00.000'
  and (interactiveType like 'CHECK_IN%' or interactiveType = 'CONSECUTIVE_CHECK_IN');

//------------------------------------------------27-------------------------------------
-- 51
drop table if exists tmp_ir_27;
create table tmp_ir_27 as
select *
from tmp_error_c_ids
where joinTime >= SUBTIME('2022-05-27 00:00:00.000', '08:00:00')
  and joinTime <= SUBTIME('2022-05-27 23:59:59.999', '08:00:00');

-- 27：26
drop table if exists tmp_ir_27_error;
create table tmp_ir_27_error as
select *
from tmp_ir_27
where customerNo not in (select customerNo
                         from (select * from tmp_ir_last_6_all
                               where customerNo in (select customerNo from tmp_ir_27)
                                 and joinTime >= SUBTIME('2022-05-26 00:00:00.000', '08:00:00')
                                 and joinTime <= SUBTIME('2022-05-26 23:59:59.999', '08:00:00')) t);

select *
from tmp_ir_27_error;

-- 因为26日未签到，更新 27 号为第1次
update tmp_ir_27_error set signTime = 1 ,  interactiveType = 'CHECK_IN';

-- 跟踪更新 这些人 28、29、30 的数据
-- 28--26
drop table if exists tmp_ir_27_error_28;
create table tmp_ir_27_error_28 as
select * from tmp_ir_last_6_all
where customerNo in (select customerNo from tmp_ir_27_error)
  and joinTime >= SUBTIME('2022-05-28 00:00:00.000', '08:00:00')
  and joinTime <= SUBTIME('2022-05-28 23:59:59.999', '08:00:00') ;
-- 更新签到次数为 2
update tmp_ir_27_error_28 set signTime = 2;

-- 29--2
drop table if exists tmp_ir_27_error_29;
create table tmp_ir_27_error_29 as
select * from tmp_ir_last_6_all
where customerNo in (select customerNo from tmp_ir_27_error_28)
  and joinTime >= SUBTIME('2022-05-29 00:00:00.000', '08:00:00')
  and joinTime <= SUBTIME('2022-05-29 23:59:59.999', '08:00:00') ;
-- signTime = 0,interactiveType = 'CONSECUTIVE_CHECK_IN'
update tmp_ir_27_error_29 set signTime = 0,interactiveType = 'CONSECUTIVE_CHECK_IN';

-- 30--2
drop table if exists tmp_ir_27_error_30;
create table tmp_ir_27_error_30 as
select * from tmp_ir_last_6_all
where customerNo in (select customerNo from tmp_ir_27_error_29)
  and joinTime >= SUBTIME('2022-05-30 00:00:00.000', '08:00:00')
  and joinTime <= SUBTIME('2022-05-30 23:59:59.999', '08:00:00') ;
-- signTime = 0,interactiveType = 'CONSECUTIVE_CHECK_IN'
update tmp_ir_27_error_30 set signTime = 1,interactiveType = 'CHECK_IN';

drop table if exists tmp_ir_27_error_fixed_total;
create table tmp_ir_27_error_fixed_total as
select * from (select *
               from tmp_ir_27_error
               union all
               select *
               from tmp_ir_27_error_28
               union all
               select *
               from tmp_ir_27_error_29
               union all
               select *
               from tmp_ir_27_error_30) t order by customerNo, joinTime;


------------------------------------------------ 28 <USER>


<GROUP> 1876
drop table if exists tmp_ir_28;
create table tmp_ir_28 as
select *
from tmp_error_c_ids
where joinTime >= SUBTIME('2022-05-28 00:00:00.000', '08:00:00')
  and joinTime <= SUBTIME('2022-05-28 23:59:59.999', '08:00:00');

-- 28：336
drop table if exists tmp_ir_28_error;
create table tmp_ir_28_error as
select *
from tmp_ir_28
where customerNo not in (select customerNo
                         from tmp_ir_last_6_all
                         where customerNo in (select customerNo from tmp_ir_29)
                           and joinTime >= SUBTIME('2022-05-27 00:00:00.000', '08:00:00')
                           and joinTime <= SUBTIME('2022-05-27 23:59:59.999', '08:00:00'));

-- 删除27日已更新的数据
delete from tmp_ir_28_error where customerNo in (select customerNo from tmp_ir_27_error);
-- 0

-- 因为27日未签到，更新 28 号为第1次
update tmp_ir_28_error set signTime = 1 ,  interactiveType = 'CHECK_IN';
-- 336

-- 跟踪更新 这些人 28、29、30 的数据
-- 29
drop table if exists tmp_ir_28_error_29;
create table tmp_ir_28_error_29 as
select * from tmp_ir_last_6_all
where customerNo in (select customerNo from tmp_ir_28_error)
  and joinTime >= SUBTIME('2022-05-29 00:00:00.000', '08:00:00')
  and joinTime <= SUBTIME('2022-05-29 23:59:59.999', '08:00:00') ;
-- signTime = 0,interactiveType = 'CONSECUTIVE_CHECK_IN'
update tmp_ir_28_error_29 set signTime = 2,interactiveType = 'CHECK_IN';
-- 281


-- 30--2
create table tmp_ir_28_error_30 as
select * from tmp_ir_last_6_all
where customerNo in (select customerNo from tmp_ir_28_error_29)
  and joinTime >= SUBTIME('2022-05-30 00:00:00.000', '08:00:00')
  and joinTime <= SUBTIME('2022-05-30 23:59:59.999', '08:00:00') ;
-- signTime = 0,interactiveType = 'CONSECUTIVE_CHECK_IN'
update tmp_ir_28_error_30 set signTime = 0,interactiveType = 'CONSECUTIVE_CHECK_IN';


drop table if exists tmp_ir_28_error_fixed_total;
create table tmp_ir_28_error_fixed_total as
select * from (select *
               from tmp_ir_28_error
               union all
               select *
               from tmp_ir_28_error_29
               union all
               select *
               from tmp_ir_28_error_30) t order by customerNo, joinTime


//---------------------------------29-------------------------------------------

-- 1605
drop table if exists tmp_ir_29;
create table tmp_ir_29 as
select *
from tmp_error_c_ids
where joinTime >= SUBTIME('2022-05-29 00:00:00.000', '08:00:00')
  and joinTime <= SUBTIME('2022-05-29 23:59:59.999', '08:00:00');

-- 29：136
-- 29：136
drop table if exists tmp_ir_29_error;
create table tmp_ir_29_error as
select *
from tmp_ir_29
where customerNo not in (select customerNo
                         from tmp_ir_last_6_all
                         where customerNo in (select customerNo from tmp_ir_29)
                           and joinTime >= SUBTIME('2022-05-28 00:00:00.000', '08:00:00')
                           and joinTime <= SUBTIME('2022-05-28 23:59:59.999', '08:00:00'));

-- 删除27日已更新的数据
delete from tmp_ir_28_error where customerNo in (select customerNo from tmp_ir_27_error);

update tmp_ir_29_error set signTime = 1 ,  interactiveType = 'CHECK_IN';

-- 30--2
create table tmp_ir_29_error_30 as
select * from tmp_ir_last_6_all
where customerNo in (select customerNo from tmp_ir_29_error)
  and joinTime >= SUBTIME('2022-05-30 00:00:00.000', '08:00:00')
  and joinTime <= SUBTIME('2022-05-30 23:59:59.999', '08:00:00') ;
-- signTime = 0,interactiveType = 'CONSECUTIVE_CHECK_IN'
update tmp_ir_29_error_30 set signTime = 2,interactiveType = 'CHECK_IN';


drop table if exists tmp_ir_29_error_fixed_total;
create table tmp_ir_29_error_fixed_total as
select * from (select *
               from tmp_ir_29_error
               union all
               select *
               from tmp_ir_29_error_30) t order by customerNo, joinTime;


//---------------------------------------30--------------------------------------------

-- 672
drop table if exists tmp_ir_30;
create table tmp_ir_30 as
select *
from tmp_error_c_ids
where joinTime >= SUBTIME('2022-05-30 00:00:00.000', '08:00:00')
  and joinTime <= SUBTIME('2022-05-30 23:59:59.999', '08:00:00');


-- 30：147
drop table if exists tmp_ir_30_error;
create table tmp_ir_30_error as
select *
from tmp_ir_30
where customerNo not in (select customerNo
                         from tmp_ir_last_6_all
                         where customerNo in (select customerNo from tmp_ir_30)
                           and joinTime >= SUBTIME('2022-05-29 00:00:00.000', '08:00:00')
                           and joinTime <= SUBTIME('2022-05-29 23:59:59.999', '08:00:00'));

update tmp_ir_30_error set signTime = 1 ,  interactiveType = 'CHECK_IN';

drop table if exists tmp_ir_30_error_fixed_total;
create table tmp_ir_30_error_fixed_total as
select * from tmp_ir_30_error order by customerNo, joinTime;


drop table if exists tmp_ir_0531_fixed_total;
create table tmp_ir_0531_fixed_total as
select * from tmp_ir_27_error_fixed_total union all
select * from tmp_ir_28_error_fixed_total union all
select * from tmp_ir_29_error_fixed_total union all
select * from tmp_ir_30_error_fixed_total;


//-----------------------------------------31--------------------------------------
drop table if exists tmp_ir_31;
create table tmp_ir_31 as
select *
from tmp_error_c_ids
where joinTime >= SUBTIME('2022-05-31 00:00:00.000', '08:00:00')
  and joinTime <= SUBTIME('2022-05-31 23:59:59.999', '08:00:00');

--

create table tmp_ir_ser_1 as
select id from tmp_ir_0531_fixed_total where memberIntegral=2 and interactiveType='CHECK_IN' union
select id from tmp_ir_0531_fixed_total where memberIntegral=1 and interactiveType='CONSECUTIVE_CHECK_IN'


-- 删除重复数据
create table tmp_ir_0531_fixed_error_mutli as
select *
from tmp_ir_0531_fixed_total
where customerNo in (
                     'wxa5811e0426a94686_oa-6a5XlRiq_8O4aFk2BT6lQIk1M',
                     'wxa5811e0426a94686_oa-6a5eDu2DvQvGQFxtsQLucvWQI',
                     'wxa5811e0426a94686_oa-6a5eMeLwoDCbGJKd0CK8k3sJw',
                     'wxa5811e0426a94686_oa-6a5Q-Apwg6lzFRZ6LcSZuc0tk',
                     'wxa5811e0426a94686_oa-6a5Q-Apwg6lzFRZ6LcSZuc0tk',
                     'wxa5811e0426a94686_oa-6a5S9YLZNcmaCELVC09QbR84Y',
                     'wxa5811e0426a94686_oa-6a5U6ie7UsVv5Y528XtX2_0n8',
                     'wxa5811e0426a94686_oa-6a5WHhDS-txGLpUn50sg0oows',
                     'wxa5811e0426a94686_oa-6a5X3xGCQ5v97P3UIl5fRyzOI',
                     'wxa5811e0426a94686_oa-6a5ZC9yTHH_7QcNCWg9ZrWtRI',
                     'wxa5811e0426a94686_oa-6a5ZDjJ5Tizma3lCX6FLNphnc',
                     'wxa5811e0426a94686_oa-6a5ZDjJ5Tizma3lCX6FLNphnc',
                     'wxa5811e0426a94686_oa-6a5ZLCiL_E9m2zrCpHJZToziI',
                     'wxa5811e0426a94686_oa-6a5ZLCiL_E9m2zrCpHJZToziI',
                     'wxa5811e0426a94686_oa-6a5SIXxjKU8GKPTYgAi3DGSqk',
                     'wxa5811e0426a94686_oa-6a5SIXxjKU8GKPTYgAi3DGSqk',
                     'wxa5811e0426a94686_oa-6a5Snnxu2k4t7DtSQFYdJB-tk',
                     'wxa5811e0426a94686_oa-6a5ZE1_09jYmeWI8ZJCcySB-c',
                     'wxa5811e0426a94686_oa-6a5QRsDXCk_DCqfusvnsipD54',
                     'wxa5811e0426a94686_oa-6a5TkzKF0EJ6f7MGmJUzH5X00'
    );


delete
from tmp_ir_0531_fixed_total
where customerNo in (
                     'wxa5811e0426a94686_oa-6a5XlRiq_8O4aFk2BT6lQIk1M',
                     'wxa5811e0426a94686_oa-6a5eDu2DvQvGQFxtsQLucvWQI',
                     'wxa5811e0426a94686_oa-6a5eMeLwoDCbGJKd0CK8k3sJw',
                     'wxa5811e0426a94686_oa-6a5Q-Apwg6lzFRZ6LcSZuc0tk',
                     'wxa5811e0426a94686_oa-6a5Q-Apwg6lzFRZ6LcSZuc0tk',
                     'wxa5811e0426a94686_oa-6a5S9YLZNcmaCELVC09QbR84Y',
                     'wxa5811e0426a94686_oa-6a5U6ie7UsVv5Y528XtX2_0n8',
                     'wxa5811e0426a94686_oa-6a5WHhDS-txGLpUn50sg0oows',
                     'wxa5811e0426a94686_oa-6a5X3xGCQ5v97P3UIl5fRyzOI',
                     'wxa5811e0426a94686_oa-6a5ZC9yTHH_7QcNCWg9ZrWtRI',
                     'wxa5811e0426a94686_oa-6a5ZDjJ5Tizma3lCX6FLNphnc',
                     'wxa5811e0426a94686_oa-6a5ZDjJ5Tizma3lCX6FLNphnc',
                     'wxa5811e0426a94686_oa-6a5ZLCiL_E9m2zrCpHJZToziI',
                     'wxa5811e0426a94686_oa-6a5ZLCiL_E9m2zrCpHJZToziI',
                     'wxa5811e0426a94686_oa-6a5SIXxjKU8GKPTYgAi3DGSqk',
                     'wxa5811e0426a94686_oa-6a5SIXxjKU8GKPTYgAi3DGSqk',
                     'wxa5811e0426a94686_oa-6a5Snnxu2k4t7DtSQFYdJB-tk',
                     'wxa5811e0426a94686_oa-6a5ZE1_09jYmeWI8ZJCcySB-c',
                     'wxa5811e0426a94686_oa-6a5QRsDXCk_DCqfusvnsipD54',
                     'wxa5811e0426a94686_oa-6a5TkzKF0EJ6f7MGmJUzH5X00'
    );

-- 从 27 开始，4天都有签到的人
create table tmp_ir_0531_fixed_total_full_check as
select *
from tmp_ir_0531_fixed_total
where customerNo in
      ('wxa5811e0426a94686_oa-6a5aaWNT83GsT80MWaPaT7_n0', 'wxa5811e0426a94686_oa-6a5e3J4qMRnmHO1UFDha3IQhY');

delete
from tmp_ir_0531_fixed_total
where customerNo in
      ('wxa5811e0426a94686_oa-6a5aaWNT83GsT80MWaPaT7_n0', 'wxa5811e0426a94686_oa-6a5e3J4qMRnmHO1UFDha3IQhY');


-- 第一批次 修复数据
-- 1. 第一批取出有三条记录的客户，把0,1变更时间对调，形成临时表： 记录ID，变更时间


drop table if exists tmp_ir_ser_1_data;
create table tmp_ir_ser_1_data as
select DATE_ADD(joinTime, INTERVAL 8 HOUR) as realJoinTime, t.*
from tmp_ir_0531_fixed_total t where
        customerno in (select customerNo from tmp_ir_ser_1)
                                 and ((memberIntegral=2 and interactiveType='CHECK_IN' ) or (memberIntegral=1 and interactiveType='CONSECUTIVE_CHECK_IN'))
order  by customerNo, joinTime desc ;


drop table if exists tmp_ir_0531_update_1;
create table tmp_ir_0531_update_1 as
select t.id, t.customerNo, t.interactiveRecordId, t.signTime, t.joinTime, t2.signTime as signTime2, t2.joinTime as joinTime2
from tmp_ir_ser_1_data t
         left join tmp_ir_ser_1_data t2 on t.customerno = t2.customerno
    and t.signTime != t2.signTime;



-- 给祖辉的第一张表
drop table if exists tmp_ir_0531_for_batch_1;

create table tmp_ir_0531_for_batch_1 as
select interactiveRecordId,joinTime2 from tmp_ir_0531_update_1;



-- 第二批数据
-- 2. 排除第一批客户NO，剩余的数据里memberIntegral=2 and interactiveType='CHECK_IN'的数据形成临时表：记录ID，CHECK_IN#签到
drop table if exists tmp_ir_0531_base_batch_2;
create table tmp_ir_0531_base_batch_2 as
select *
from tmp_ir_0531_fixed_total
where customerNo not in (select customerNo from tmp_ir_0531_update_1)
  and customerNo not in (
    -- 有重复数据的数据
                         'wxa5811e0426a94686_oa-6a5XlRiq_8O4aFk2BT6lQIk1M',
                         'wxa5811e0426a94686_oa-6a5eDu2DvQvGQFxtsQLucvWQI',
                         'wxa5811e0426a94686_oa-6a5eMeLwoDCbGJKd0CK8k3sJw',
                         'wxa5811e0426a94686_oa-6a5Q-Apwg6lzFRZ6LcSZuc0tk',
                         'wxa5811e0426a94686_oa-6a5Q-Apwg6lzFRZ6LcSZuc0tk',
                         'wxa5811e0426a94686_oa-6a5S9YLZNcmaCELVC09QbR84Y',
                         'wxa5811e0426a94686_oa-6a5U6ie7UsVv5Y528XtX2_0n8',
                         'wxa5811e0426a94686_oa-6a5WHhDS-txGLpUn50sg0oows',
                         'wxa5811e0426a94686_oa-6a5X3xGCQ5v97P3UIl5fRyzOI',
                         'wxa5811e0426a94686_oa-6a5ZC9yTHH_7QcNCWg9ZrWtRI',
                         'wxa5811e0426a94686_oa-6a5ZDjJ5Tizma3lCX6FLNphnc',
                         'wxa5811e0426a94686_oa-6a5ZDjJ5Tizma3lCX6FLNphnc',
                         'wxa5811e0426a94686_oa-6a5ZLCiL_E9m2zrCpHJZToziI',
                         'wxa5811e0426a94686_oa-6a5ZLCiL_E9m2zrCpHJZToziI',
                         'wxa5811e0426a94686_oa-6a5SIXxjKU8GKPTYgAi3DGSqk',
                         'wxa5811e0426a94686_oa-6a5SIXxjKU8GKPTYgAi3DGSqk',
                         'wxa5811e0426a94686_oa-6a5Snnxu2k4t7DtSQFYdJB-tk',
                         'wxa5811e0426a94686_oa-6a5ZE1_09jYmeWI8ZJCcySB-c',
                         'wxa5811e0426a94686_oa-6a5QRsDXCk_DCqfusvnsipD54',
                         'wxa5811e0426a94686_oa-6a5TkzKF0EJ6f7MGmJUzH5X00',
    -- 4天全签到的数据
                         'wxa5811e0426a94686_oa-6a5aaWNT83GsT80MWaPaT7_n0',
                         'wxa5811e0426a94686_oa-6a5e3J4qMRnmHO1UFDha3IQhY'
    )
  and memberIntegral = 2
  and interactiveType = 'CHECK_IN'
;

drop table if exists tmp_ir_0531_for_batch_2;
create table tmp_ir_0531_for_batch_2 as
select interactiveRecordId,'CHECK_IN#签到' as `description` from tmp_ir_0531_base_batch_2


-- 更新模型数据
update ecoke_datamodel.d_p_k_interactiverecords_355 a
    join tmp_ir_0531_fixed_total b on a.id = b.id
set a.signTime = b.signTime,
    a.interactiveType = b.interactiveType;
-- 受影响的行: 1181
-- 时间: 0.095s

create table tmp_ir_0531_check_data as
select * from ecoke_datamodel.d_p_k_interactiverecords_355
where  joinTime >= SUBTIME('2022-05-31 00:00:00.000', '08:00:00')
  and joinTime <= SUBTIME('2022-05-31 23:59:59.999', '08:00:00')


Select id,
       customerNo,
       invitee,
       inviteeMember,
       inviteeMobile,
       mobile,
       member,
       memberIDStr,
       memberIntegral,
       interactive,
       interactiveScene,
       interactiveId,
       interactiveRecordId,
       interactiveType,
       channelType,
       coupon,
       couponList,
       couponDetailList,
       grantStatus,
       isGetRewards,
       joinTime,
       refuseReason,
       signTime,
       created,
       lastSync
From data.prctvmkt.KO.interactiveRecords
Where interactiveScene = 'CHECK_IN'
  And customerNo = 'wxa5811e0426a94686_oa-6a5YfyTUsuqQovzAB6tSvfG_0'
  And grantStatus = 0
Order By joinTime Desc Limit 1;

