-- 表说明
-- 有以下源表：
-- - 积分流水记录表：xd7bq5a4i1_customized_service.history_PointRecord_20220513
-- - 积分流水记录表：xd7bq5a4i1_customized_service.history_PointRecord_20220513

-- 1、创建临时表
-- 临时表：ir_dev_point_record 互动获得积分记录
-- 逻辑从积水流水中提取由于 “参与互动” 而获取积分的记录
DROP TABLE IF EXISTS `ir_dev_point_record_5`;
CREATE TABLE `ir_dev_point_record_5`
(
    `customerNo`         varchar(64)    DEFAULT NULL,
    `businessId`         varchar(64) NOT NULL,
    `changeTime`         datetime(3) DEFAULT NULL,
    `changeType`         varchar(32)    DEFAULT NULL,
    `changePoint`        decimal(11, 1) DEFAULT NULL,
    `channelType`        varchar(32)    DEFAULT NULL,
    `changeSource`       varchar(64)    DEFAULT NULL,
    `changeSourceDetail` varchar(128)   DEFAULT NULL,
    `costTracing`        varchar(32)    DEFAULT NULL,
    `desc`               varchar(128)   DEFAULT NULL,
    `effectiveDate`      datetime(3) DEFAULT NULL,
    `overdueDate`        datetime(3) DEFAULT NULL,
    `shopId`             varchar(32)    DEFAULT NULL,
    `invitee`            varchar(64)    DEFAULT NULL,
    PRIMARY KEY (`businessId`),
    KEY                  `index_customerNo` (`customerNo`),
    KEY                  `index_invitee` (`invitee`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

INSERT INTO `ir_dev_point_record_5`
SELECT *
FROM `history_pointrecord_20220527`
WHERE 1 = 1
  AND `changeSource` = '参与互动'
  AND `changeType` = 'SEND' -- 只处理获取积分的记录，扣除积分不迁移
  AND `desc` IN
      ('REGISTER#完成注册成为会员', 'CHECK_IN#签到', 'COMPLETE_INFO#完善个人信息', 'INVITE_REGISTER#邀请好友注册', 'CHECK_IN#签到(连续签到3天)')
;

-- 受影响的行: 118168
-- 时间: 19.040s


-- 临时表：ir_dev_member 会员表
DROP TABLE IF EXISTS `ir_member_all`;
CREATE TABLE `ir_member_all`
(
    `customerNo` varchar(64) NOT NULL,
    `memberId`   varchar(64) DEFAULT NULL,
    `mobile`     varchar(32) DEFAULT NULL,
    PRIMARY KEY (`customerNo`),
    KEY          `index_customerNo` (`memberId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

INSERT INTO `ir_member_all`
SELECT consumers.`customerNo`,
       consumers.`memberId`,
       members.`mobile`
FROM `history_member_20220527` members
         INNER JOIN `temp_history_consumer_all` consumers
                    ON consumers.`customerNo` = concat(members.appid, "_", members.openid)
;

-- 受影响的行: 92618
-- 时间: 381.319s


-- 目标表
-- 互动记录表：ir_dev_point_record

-- 0、创建目标表-互动活动记录数据迁移表
DROP TABLE IF EXISTS `ir_history_interactive_records_5`;
CREATE TABLE `ir_history_interactive_records_5`
(
    `id`                  varchar(32) NOT NULL COMMENT 'id',
    `customerNo`          varchar(255)   DEFAULT NULL COMMENT '会员渠道编号',
    `joinTime`            datetime(3) NOT NULL COMMENT '参与时间',
    `signTime`            bigint(8) DEFAULT NULL COMMENT '连续签到次数',
    `channelType`         varchar(32)    DEFAULT NULL COMMENT '渠道',
    `interactiveRecordId` varchar(255)   DEFAULT NULL COMMENT '参与互动记录id',
    `mobile`              varchar(255)   DEFAULT NULL COMMENT '参与人手机号',
    `interactive`         varchar(32)    DEFAULT NULL COMMENT '互动编号',
    `interactiveType`     varchar(32)    DEFAULT NULL COMMENT '参与行为类型',
    `invitee`             varchar(64)    DEFAULT NULL COMMENT '被邀请者的渠道编号',
    `inviteeMember`       varchar(32)    DEFAULT NULL COMMENT '被邀请者',
    `member`              varchar(32)    DEFAULT NULL COMMENT '	会员ID',
    `memberIDStr`         varchar(64)    DEFAULT NULL COMMENT '会员ID String',
    `isGetRewards`        tinyint(4) DEFAULT NULL COMMENT '是否获奖',
    `rewardsType`         varchar(64)    DEFAULT NULL COMMENT '获奖类型',
    `memberIntegral`      decimal(21, 3) DEFAULT NULL COMMENT '本次互动发放的积分数',
    `grantStatus`         varchar(32)    DEFAULT NULL COMMENT '发放状态',
    `created`             datetime(3) DEFAULT NULL COMMENT '创建时间',
    `lastSync`            datetime(3) DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='互动活动记录数据迁移表';

-- 源表：
-- ir_dev_point_record 互动获得积分记录
-- ir_dev_member 会员表

-- 2、插入历史表：ir_history_interactive_records_5
INSERT INTO `ir_history_interactive_records_5`
SELECT UUID_SHORT() AS `id`,                  -- id,逻辑：uuid
       point.`customerNo`                                 AS `customerNo`,          -- 会员渠道编号
      adddate(point.`changeTime`,interval -8 hour)                                AS `joinTime`,            -- 参与时间
       CASE
           WHEN ir_splitStr(point.`desc`, '#', 2) = '签到(连续签到3天)' THEN 3
           ELSE NULL
           END                                            AS `signTime`,            -- 连续签到次数，逻辑：只有连续签到才置为 3
       'KO_MP'                                            AS `channelType`,         -- 渠道，逻辑：写死 KO_MP
       point.`businessId`                                 AS `interactiveRecordId`, -- 参与互动记录id，逻辑：取积分流水.businessId
       m1.`mobile`                                        AS `mobile`,              -- 参与人手机号，逻辑：取会员表中的手机号
       CASE
           WHEN ir_splitStr(point.`desc`, '#', 2) = '签到(连续签到3天)' THEN 'CONSECUTIVE_CHECK_IN'
           ELSE ir_splitStr(point.`desc`, '#', 1)
           END                                            AS `interactive`,         -- 互动编号，逻辑：积分流水中的 desc 字段，crm 已经洗好了
       CASE
           WHEN ir_splitStr(point.`desc`, '#', 2) = '签到(连续签到3天)' THEN 'CONSECUTIVE_CHECK_IN'
           ELSE ir_splitStr(point.`desc`, '#', 1)
           END                                            AS `interactiveType`,     -- 参与行为类型，逻辑：根据积分流水的 `desc` 字段进行case
       point.`invitee`                                    AS `invitee`,             -- 被邀请者的渠道编号
       m2.`memberId`                                      AS `inviteeMember`,       -- 被邀请者
       m1.`memberId`                                      AS `member`,              -- 会员ID，逻辑：从发放记录表customerNo，关联关系表后，取memberId
       m1.`memberId`                                      AS `memberIDStr`,         -- 会员ID String，逻辑同：member
       1                                                  AS `isGetRewards`,        -- 是否获奖，逻辑：能走到这里的，都是获奖的，写死1
       '发积分'                                              AS `rewardsType`,         -- 获奖类型，逻辑：写死 "发积分"
       point.`changePoint`                                AS `memberIntegral`,      -- 本次互动发放的积分数，逻辑：积分变更记录.changePoint
       '0'                                                AS `grantStatus`,         -- 发放状态，逻辑：写死0，0-发放权益成功
      adddate(point.`changeTime`,interval -8 hour)                                AS `created`,             -- 创建时间，逻辑：积分变更记录.changeTime
       adddate(now().`changeTime`,interval -8 hour)                                              AS `lastSync`             -- 更新时间
FROM `ir_dev_point_record_5` point
         INNER JOIN `ir_member_all` m1 on point.`customerNo` = m1.`customerNo`
         LEFT JOIN `ir_member_all` m2 on point.`invitee` = m2.`customerNo`
;

-- 受影响的行: 71172
-- 时间: 100.299s