-- 删除第4、5批次数据，以及昨晚的补数
-- 736683
delete from ecoke_datamodel.d_p_k_interactiverecords_355
where
    (id >= '13430454370588439998' and id <= '13430454370588558165') or (id >= '13430454370588558166' and id <= '13430454370588639241') or -- batch_4_5
    (id >= '13430452680341726154' and id <= '13430452680341825861') or -- batch_4
    (id >= '13430454370588164096' and id <= '13430454370588235267') or -- batch_5
    (id >= '13430454370588639242' and id <= '13430454370589174676') -- batch_patch
;

-- 重新插入第 4、5 批数据与昨晚的补数
-- 昨晚的补数已经删除与第4、5批次的重复
-- 706315
insert into ecoke_datamodel.d_p_k_interactiverecords_355
(`id`,`customerNo`,`joinTime`,`signTime`,`channelType`,`interactiveRecordId`,`mobile`,`interactive`,`interactiveType`,`invitee`,
 `inviteeMember`,`member`,`memberIDStr`,`isGetRewards`,`rewardsType`,`memberIntegral`,`grantStatus`,`created`,`lastSync`)
select * from batch_4_5 union all
select * from ir_history_interactive_records_patch;

update ecoke_datamodel.d_p_k_interactiverecords_355 set interactive = '8b3174ceab774f2f93245f0fe4906a3c' where interactive = 'CONSECUTIVE_CHECK_IN';
update ecoke_datamodel.d_p_k_interactiverecords_355 set interactive = '8b3174ceab774f2f93245f0fe4906a3c' where interactive = 'CHECK_IN';
update ecoke_datamodel.d_p_k_interactiverecords_355 set interactive = 'e6f1836346ee4b91956bb90a249b40ba' where interactive = 'REGISTER';
update ecoke_datamodel.d_p_k_interactiverecords_355 set interactive = '9c8070cbf9ee497db298f3da942f028e' where interactive = 'INVITE_REGISTER';
update ecoke_datamodel.d_p_k_interactiverecords_355 set interactive = 'b89bdc2dc92947bd895cb29b4d45d7bb' where interactive = 'COMPLETE_INFO';

-- 验证如下：
-- 一、补数与 4、5 批次无重复数据，不会插入重复数据到模型
create table tmp_all_interactiveRecordId as
select interactiveRecordId from
    (
        select interactiveRecordId from batch_4_5 union all
        select interactiveRecordId from ir_history_interactive_records_patch
    ) t;
select count(interactiveRecordId),count(distinct interactiveRecordId) from tmp_all_interactiveRecordId;
-- 706315	706315


-- 二、整体数据中， YM 数据正常：
select id,joinTime,interactiveRecordId,interactiveType,created,lastSync from batch_4_5 where customerNo = 'wxa5811e0426a94686_oa-6a5SAklQs7vxRyoDK5AKr_kS0' union ALL
select id,joinTime,interactiveRecordId,interactiveType,created,lastSync from ir_history_interactive_records_patch where customerNo = 'wxa5811e0426a94686_oa-6a5SAklQs7vxRyoDK5AKr_kS0'
;

13430454370588444637	2022-05-23 08:28:25.000	403334826330607616	CHECK_IN	2022-05-23 08:28:25.000	2022-05-26 21:50:08.000
13430454370588444638	2022-05-24 01:59:23.000	403599314373459968	CHECK_IN	2022-05-24 01:59:23.000	2022-05-26 21:50:08.000
13430454370588444639	2022-05-25 02:25:04.000	403968162904330240	CONSECUTIVE_CHECK_IN	2022-05-25 02:25:04.000	2022-05-26 21:50:08.000
13430454370588560667	2022-05-26 15:55:53.000	404534599764660224	CHECK_IN	2022-05-26 15:55:53.000	2022-05-26 21:52:28.000
13430454370588673896	2022-05-11 07:45:01.000	398975249737904128	CHECK_IN	2022-05-11 07:45:01.000	2022-05-26 22:53:30.000

-- 数据无重复，日期时间正常

