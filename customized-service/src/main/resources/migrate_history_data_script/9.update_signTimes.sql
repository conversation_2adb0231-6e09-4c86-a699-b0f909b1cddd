




-- 从 5 批次取昨天的签到会员，这是要被更新签到次数的基数
drop table if exists ir_last_interactive_records_0526;
create table ir_last_interactive_records_0526 as
select * from ir_history_interactive_records_5_2
         where joinTime >= '2022-05-26 00:00:00'
           and interactiveType like 'CHECK_IN%'
           and signTime is null ;
-- 2198

-- 将前两天的数据放到临时表中，以备按人汇总连续签到次数
drop table if exists ir_last_interactive_records_0524_25;
create table ir_last_interactive_records_0524_25 as
select * from ir_history_interactive_records_5_2 where (joinTime like '2022-05-25%' or joinTime like '2022-05-24%') and interactiveType like 'CHECK_IN%' union all
select * from ir_history_interactive_records_4_2 where (joinTime like '2022-05-25%' or joinTime like '2022-05-24%') and interactiveType like 'CHECK_IN%'
-- 4877

-- 签到次数按人汇总
drop table if exists ir_last_sign_times;
create table ir_last_sign_times as
select customerNo,count(1) times from ir_last_interactive_records_0524_25 group by customerNo ;

-- 4842
-- 大部分这两天，都只签到1次


-- 更新签到次数
update ir_last_interactive_records_0526 t set signTime = (select times from ir_last_sign_times x where x.customerNo = t.customerNo);
-- 25

-- 如果当天也签到了，次数 + 1
update ir_last_interactive_records_0526 set signTime = signTime+1 where  signTime is not null;
--

-- 如果没有关联上，则昨天就是第 1 次签到
update ir_last_interactive_records_0526 set signTime = 1 where  signTime is  null;
--

