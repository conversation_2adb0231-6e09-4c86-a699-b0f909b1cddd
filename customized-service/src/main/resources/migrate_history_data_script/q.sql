-- create table tmp_ir_0531_update_1 as
select t.id,t.customerNo, t.interactiveRecordId, t.signTime,  t2.joinTime as joinTime2,t.memberIntegral
from tmp_ll_part t
         left join tmp_ll_part t2 on t.customerno = t2.customerno and t.id != t2.id
where t.id in (
    '8216a0316a29460db8fbfac26254b925',
    '321dce467d074040aca3abd30a78e697',
    '7c57d0824e90489bb90e957785ad8131',
    '2ed13eea2efa4a9db4849c49e660c8de',
    '866737ffde584a198a9b317487318dad',
    '56c71f2a9a574b6081defa2730f3cf53',
    '8d6fc86416be4361814bf6a067969085',
    'b459d1b16e114a419063e216b8ff12a4',
    '954f7fd0f0664a439b8e9b5c2aecd5c6',
    '5bd28eb11d8d421cb8dc2bc316f59eb1',
    '96bcdb1cd75b4f6dbf738f02f4772b21',
    '438d52bdf93540b9a3077b772de0c97b',
    '9c8f0f3787fb4807b0fc55d09179b590',
    '73d097c1840d4e79bfcaef0894d752cb',
    'ab3e24a48db34b6983bea06b2bae47f6',
    '78dcd709280a4710b3bb203b03ecdb38',
    'afbf3775bd2241f7bc305f88a939f391',
    'c7131a2b027a4638b5fcdcd5f3c8b84f',
    'b3a398c50da747c089a5b5c10d08bd98',
    'fa6adea0381047348aa07f7d2e9305ce',
    'b591d1b7e3a44fb99c37ec143b768f7e',
    '0db2d6087202435c9c4d9d7b90659345',
    'b83cb1c634c147cf9db16568aead4238',
    '61545d686f8a438e81d99aa0dba05bf5',
    'b87a0ddd69ee406fa6bcc2e45cbed419',
    'a23090fcfb824f868f189f4825f6418f',
    'b9332e3a0c964e54be71833156e8038b',
    'd1474c5653e942a5ab842ca4c68f3d30',
    'b9896bd07a2f4aabb158f3b641d83b6a',
    'f1999866b96b423ea340cebfa098ae3b',
    'bb7e8566e4654afaabe09d2fdc0e7938',
    'c15f0c3667c44575b08b441a766da42a',
    'bddb181c3d944548ac7b572210eb30b7',
    '88c02ac20d304fbc88183116ffc0bb95',
    'bf05cd98a0ca4bbaaf803ead4fe244d6',
    '69ec2b07b483441f841e400ab6c54973',
    'bfa764506d384a0abaf2b3731d5434a7',
    '3aaf304b93cf4eca819dc8f639e59b2e',
    'bff2180b24a5427ea51b48668d99ee4f',
    'c015aff5aba449808b94839807f9f67d',
    'c4e285dc5f694ea0bbb49e54c75cee35',
    'fe81ccb7fe374386bda1db3fdb887e3b',
    'c9eb5d66babb4bd7a9bdf297f2262ec3',
    '17f2e64b243244178f758f5f6c21dd11',
    'cc515f6491c548e6a0d1a019fe99a15d',
    '491e693c8a6f44f5ad94c938b66d32e2',
    'ccbf87283eb04f0486e3afaac7b687d8',
    '026d6d78f52f44f5a5bcc5783bcdf348',
    'd4fdf0b0d8044061914cf19c2ea86a63',
    '445ae2004c904e30a294ee008000b658',
    'd89bd0ca912b41449db92ab7219de3b0',
    '14424ed64b3d42d6bf3e7ca5f49f0bf4',
    'de23954b9cd14fae9a67086acdb91174',
    '438de99e06304313b3802bbd6fc15309',
    '88259a41a71a4bddaed8430ad7fff884',
    '405c9885c0274d8583b09b377a19b7c1',
    'dfc47db24f6c48cb9e517e3632f6d516',
    '7b2a2f995bf846b383f343fb8b756c62',
    'e18a7c6eca7e47ce86640953f15c794d',
    'b69dc72fef2947c2ab37eb8a3c1d3f34',
    'e21659d6a8544968924cc4ad08c7eda7',
    '659448d559e04a6193dc9ef1aaa9980a',
    'ce3e8694acfc472fbfec15312ff269f6',
    'ed9d8d958493480499d0e6d61ca6b66f',
    'f31ecf7ac7a94fa8967e75d8077a9dde',
    'd059cbdb7c314578b43df2c2e4b9aa4b',
    'f372245bfd1f4a93954635a9cdcb38e0',
    '647fdd1302434fe88fca0721ce38bf2b',
    '28d1c5639c834e1cae6185c245e72fbf',
    'fa2d3c097ebb45fd811cf949c5dae45c',
    'fb44abe1814e46ccbd088d2b05c497a9',
    '436b714835bb4d3986c3e6232294a3f5',
    'fce704e16bd845ae8ead260df3e9be38',
    '665874520b254d58a789548d459027dd',
    'fd0e6571a89546df8950b1a3606673c5',
    'a3babece416548388f599f7e0859a35e',
    'ffd7d8c6d2954731b81e2f4d4da0c565',
    '639fe6b1d84f4cce8ab6f50aaca7f1eb'

    )
  and t2.id in (

    '8216a0316a29460db8fbfac26254b925',
    '321dce467d074040aca3abd30a78e697',
    '7c57d0824e90489bb90e957785ad8131',
    '2ed13eea2efa4a9db4849c49e660c8de',
    '866737ffde584a198a9b317487318dad',
    '56c71f2a9a574b6081defa2730f3cf53',
    '8d6fc86416be4361814bf6a067969085',
    'b459d1b16e114a419063e216b8ff12a4',
    '954f7fd0f0664a439b8e9b5c2aecd5c6',
    '5bd28eb11d8d421cb8dc2bc316f59eb1',
    '96bcdb1cd75b4f6dbf738f02f4772b21',
    '438d52bdf93540b9a3077b772de0c97b',
    '9c8f0f3787fb4807b0fc55d09179b590',
    '73d097c1840d4e79bfcaef0894d752cb',
    'ab3e24a48db34b6983bea06b2bae47f6',
    '78dcd709280a4710b3bb203b03ecdb38',
    'afbf3775bd2241f7bc305f88a939f391',
    'c7131a2b027a4638b5fcdcd5f3c8b84f',
    'b3a398c50da747c089a5b5c10d08bd98',
    'fa6adea0381047348aa07f7d2e9305ce',
    'b591d1b7e3a44fb99c37ec143b768f7e',
    '0db2d6087202435c9c4d9d7b90659345',
    'b83cb1c634c147cf9db16568aead4238',
    '61545d686f8a438e81d99aa0dba05bf5',
    'b87a0ddd69ee406fa6bcc2e45cbed419',
    'a23090fcfb824f868f189f4825f6418f',
    'b9332e3a0c964e54be71833156e8038b',
    'd1474c5653e942a5ab842ca4c68f3d30',
    'b9896bd07a2f4aabb158f3b641d83b6a',
    'f1999866b96b423ea340cebfa098ae3b',
    'bb7e8566e4654afaabe09d2fdc0e7938',
    'c15f0c3667c44575b08b441a766da42a',
    'bddb181c3d944548ac7b572210eb30b7',
    '88c02ac20d304fbc88183116ffc0bb95',
    'bf05cd98a0ca4bbaaf803ead4fe244d6',
    '69ec2b07b483441f841e400ab6c54973',
    'bfa764506d384a0abaf2b3731d5434a7',
    '3aaf304b93cf4eca819dc8f639e59b2e',
    'bff2180b24a5427ea51b48668d99ee4f',
    'c015aff5aba449808b94839807f9f67d',
    'c4e285dc5f694ea0bbb49e54c75cee35',
    'fe81ccb7fe374386bda1db3fdb887e3b',
    'c9eb5d66babb4bd7a9bdf297f2262ec3',
    '17f2e64b243244178f758f5f6c21dd11',
    'cc515f6491c548e6a0d1a019fe99a15d',
    '491e693c8a6f44f5ad94c938b66d32e2',
    'ccbf87283eb04f0486e3afaac7b687d8',
    '026d6d78f52f44f5a5bcc5783bcdf348',
    'd4fdf0b0d8044061914cf19c2ea86a63',
    '445ae2004c904e30a294ee008000b658',
    'd89bd0ca912b41449db92ab7219de3b0',
    '14424ed64b3d42d6bf3e7ca5f49f0bf4',
    'de23954b9cd14fae9a67086acdb91174',
    '438de99e06304313b3802bbd6fc15309',
    '88259a41a71a4bddaed8430ad7fff884',
    '405c9885c0274d8583b09b377a19b7c1',
    'dfc47db24f6c48cb9e517e3632f6d516',
    '7b2a2f995bf846b383f343fb8b756c62',
    'e18a7c6eca7e47ce86640953f15c794d',
    'b69dc72fef2947c2ab37eb8a3c1d3f34',
    'e21659d6a8544968924cc4ad08c7eda7',
    '659448d559e04a6193dc9ef1aaa9980a',
    'ce3e8694acfc472fbfec15312ff269f6',
    'ed9d8d958493480499d0e6d61ca6b66f',
    'f31ecf7ac7a94fa8967e75d8077a9dde',
    'd059cbdb7c314578b43df2c2e4b9aa4b',
    'f372245bfd1f4a93954635a9cdcb38e0',
    '647fdd1302434fe88fca0721ce38bf2b',
    '28d1c5639c834e1cae6185c245e72fbf',
    'fa2d3c097ebb45fd811cf949c5dae45c',
    'fb44abe1814e46ccbd088d2b05c497a9',
    '436b714835bb4d3986c3e6232294a3f5',
    'fce704e16bd845ae8ead260df3e9be38',
    '665874520b254d58a789548d459027dd',
    'fd0e6571a89546df8950b1a3606673c5',
    'a3babece416548388f599f7e0859a35e',
    'ffd7d8c6d2954731b81e2f4d4da0c565',
    '639fe6b1d84f4cce8ab6f50aaca7f1eb'

    )
order by t.customerNo,t2.joinTime
;



select t.id, t.customerNo, t.interactiveRecordId, t.signTime, t2.joinTime as joinTime2, t.memberIntegral
from tmp_jx_part t
         left join tmp_jx_part t2 on t.customerno = t2.customerno and t.id != t2.id
where t.id in (
    '26cf656aa4dc4a848a6aee8ecbf343a5'
    , '19ba01246ba640dda1fc4a8a275b55fa'
    , '741b92339ffb4d438678ec265777e1ef'
    , 'fea2b047a94d4f3c9c37e93227d1c273'
    , '728341dbfe7a4106866aa62f69402e36'
    , '210094e7a0e541c8852cb8537de1d72b'
    , '70763d442a2644c58d74767de1c701fb'
    , '9ceadf743b9c4f7280d3eb8b90192b51'
    , '33e8f4c482174f98ad82106e62d14ffa'
    , '6f9bed4f32364d6abfbd9ba84292f9f6'
    , '6e53425654804108ba83cef2ee070a51'
    , '20bf3b7038d240498f36f3cf2e82fa26'
    , '6ca57fdee49f4d89b43c3e99dab8cf09'
    , '69df641b6f4742ecae66f9ec998cf623'
    , '6c13b92cc00d410db46db90ac5bcf8c8'
    , '70b00062e0bf4b61a893cf2ffd0e7d7f'
    , '6965b826f07f450c8e8379bf874b0b32'
    , '18adadf1686844c18cc90d487d92181d'
    , '68f73f0134fc43f1ab67f31f8359dd2b'
    , '5f416eb804744bab99f59f2ce2fc4fb2'
    , '6cd8e4afa67d417392c82c21fd511003'
    , '633f687813f34bdc929e746c04a914b7'
    , '5f92a5c6eac544a2af9e0f210a39cb00'
    , '8e5ebaec362544e080746d7cf691b4d6'
    , '5f07aecd3da64042b21625660bc90e8b'
    , '29bf0b8938d34fec97bfc1200f11fba0'
    , '0ae24155016f463aa5bbdc478262c4b9'
    , '8908e3b1569d4728852e35d92fa660be')
  and t2.id in ('26cf656aa4dc4a848a6aee8ecbf343a5'
    , '19ba01246ba640dda1fc4a8a275b55fa'
    , '741b92339ffb4d438678ec265777e1ef'
    , 'fea2b047a94d4f3c9c37e93227d1c273'
    , '728341dbfe7a4106866aa62f69402e36'
    , '210094e7a0e541c8852cb8537de1d72b'
    , '70763d442a2644c58d74767de1c701fb'
    , '9ceadf743b9c4f7280d3eb8b90192b51'
    , '33e8f4c482174f98ad82106e62d14ffa'
    , '6f9bed4f32364d6abfbd9ba84292f9f6'
    , '6e53425654804108ba83cef2ee070a51'
    , '20bf3b7038d240498f36f3cf2e82fa26'
    , '6ca57fdee49f4d89b43c3e99dab8cf09'
    , '69df641b6f4742ecae66f9ec998cf623'
    , '6c13b92cc00d410db46db90ac5bcf8c8'
    , '70b00062e0bf4b61a893cf2ffd0e7d7f'
    , '6965b826f07f450c8e8379bf874b0b32'
    , '18adadf1686844c18cc90d487d92181d'
    , '68f73f0134fc43f1ab67f31f8359dd2b'
    , '5f416eb804744bab99f59f2ce2fc4fb2'
    , '6cd8e4afa67d417392c82c21fd511003'
    , '633f687813f34bdc929e746c04a914b7'
    , '5f92a5c6eac544a2af9e0f210a39cb00'
    , '8e5ebaec362544e080746d7cf691b4d6'
    , '5f07aecd3da64042b21625660bc90e8b'
    , '29bf0b8938d34fec97bfc1200f11fba0'
    , '0ae24155016f463aa5bbdc478262c4b9'
    , '8908e3b1569d4728852e35d92fa660be'

    )
order by t.customerNo, t2.joinTime
;

