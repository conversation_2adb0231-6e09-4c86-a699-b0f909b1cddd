drop table if exists tmp_ir_last_6_all;
create table tmp_ir_last_6_all as
select id, interactiveRecordId, customerNo, signTime, joinTime, interactiveType, memberIntegral,grantStatus,mobile,memberIDStr
from ecoke_datamodel.d_p_k_interactiverecords_355
where joinTime >= SUBTIME('2022-05-25 00:00:00.000', '08:00:00')
  and memberIntegral > 0
  and refuseReason is null
  and interactiveType in ('CHECK_IN','CONSECUTIVE_CHECK_IN');

drop table if exists tmp_ir_31_lost_base;
create table tmp_ir_31_lost_base as
select *
from tmp_ir_last_6_all
where joinTime >= SUBTIME('2022-05-27 00:00:00.000', '08:00:00')
  and joinTime <= SUBTIME('2022-05-27 23:59:59.000', '08:00:00')
  -- and signTime = 2
  and grantStatus = 0
  and interactiveType = 'CHECK_IN'
  and customerNo in (select customerNo
                     from tmp_ir_last_6_all
                     where joinTime >= SUBTIME('2022-05-25 00:00:00.000', '08:00:00')
                       and joinTime <= SUBTIME('2022-05-25 23:59:59.000', '08:00:00')
                       and signTime = 1
                       and grantStatus = 0
                       and interactiveType = 'CHECK_IN')
  and customerNo in (select customerNo
                     from tmp_ir_last_6_all
                     where joinTime >= SUBTIME('2022-05-26 00:00:00.000', '08:00:00')
                       and joinTime <= SUBTIME('2022-05-26 23:59:59.000', '08:00:00')
                       and signTime = 1
                       and grantStatus = 0
                       and interactiveType = 'CHECK_IN');

drop table if exists tmp_ir_31_lost_need_fix;
create table tmp_ir_31_lost_need_fix as
select * from tmp_ir_31_lost_base where customerNo in (select customerNo from tmp_ir_0531_fixed_total);

select * from  tmp_ir_31_lost_need_fix;


select * from ecoke_datamodel.d_p_k_interactiverecords_355 where customerNo = 'wxa5811e0426a94686_oa-6a5dITLhvZOql3eSBGuXfcsXU' order by joinTime
select * from tmp_ir_last_6_all where customerNo = 'wxa5811e0426a94686_oa-6a5dITLhvZOql3eSBGuXfcsXU' order by joinTime

-- 查询会员所有
select t.id,
       DATE_ADD(joinTime, INTERVAL 8 HOUR) as realJoinTime,
       t.signTime,
       t.joinTime,
       t.interactiveRecordId,
       t.interactiveType,
       t.memberIntegral,
       t.memberIdStr
from tmp_ir_last_6_all t
where customerNo = 'wxa5811e0426a94686_oa-6a5QxQ7l8LpvRp74CMxMUFiSY'
order by 2;

-- 查积分流水
select `memberId`,
       `point`,
       `changePoint`,
       `recordType`,
       `desc`,
       `modified`,
       `created`,
       `changeMode`,
       `KZZD1`
from ecoke_datamodel.`d_l_m_a_PointRecord60003_429`
where memberId = '03a7fe9286194e2183de85ee49c851e7'
  and modified >= SUBTIME('2022-05-25 00:00:00.000', '08:00:00')
order by modified;


-- 插入结果表
-- CHECK_IN#签到
-- CHECK_IN#签到(连续签到3天)
insert into tmp_ir_32_fixed_result
select '' as interactiveRecordId, '' as joinTime, '' as `description`;

update ecoke_datamodel.d_p_k_interactiverecords_355 t
    join tmp_ir_0601_fixed x
ON t.id = x.id
    set
        t.signTime = x.signTime,
        t.interactiveType = x.interactiveType;