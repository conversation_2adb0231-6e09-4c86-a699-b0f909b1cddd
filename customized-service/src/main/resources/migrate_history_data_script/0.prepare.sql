-- 创建辅助函数
DROP FUNCTION IF EXISTS `ir_splitStr`;
CREATE FUNCTION `ir_splitStr`(
    x VARCHAR (255),
    delim VARCHAR (12),
    pos INT
) RETURNS varchar(255) CHARSET utf8
RETURN REPLACE(SUBSTRING(SUBSTRING_INDEX(x, delim, pos),
       LENGTH(SUBSTRING_INDEX(x, delim, pos -1)) + 1),
       delim, '');




select count(1) from ir_history_interactive_records_1; -- 1845761
select count(1) from ir_history_interactive_records_2; -- 7392022
select count(1) from ir_history_interactive_records_3; -- 350381
select count(1) from ir_history_interactive_records_4; -- 99708



select 7392022+ 1845761+350381+99708 -- 9237783

select count(1) from ecoke_datamodel.d_p_k_interactiverecords_355; -- 9237783



select * from information_schema.`PROCESSLIST` where `DB` = 'ecoke_customized_service';




update ir_history_interactive_records_4 set interactive = '8b3174ceab774f2f93245f0fe4906a3c' where interactive = 'CONSECUTIVE_CHECK_IN';
update ir_history_interactive_records_4 set interactive = '8b3174ceab774f2f93245f0fe4906a3c' where interactive = 'CHECK_IN';
update ir_history_interactive_records_4 set interactive = 'e6f1836346ee4b91956bb90a249b40ba' where interactive = 'REGISTER';
update ir_history_interactive_records_4 set interactive = '9c8070cbf9ee497db298f3da942f028e' where interactive = 'INVITE_REGISTER';


insert into ecoke_datamodel.d_p_k_interactiverecords_355
(`id`,`customerNo`,`joinTime`,`signTime`,`channelType`,`interactiveRecordId`,`mobile`,`interactive`,`interactiveType`,`invitee`,
 `inviteeMember`,`member`,`memberIDStr`,`isGetRewards`,`rewardsType`,`memberIntegral`,`grantStatus`,`created`,`lastSync`)
select * from ir_history_interactive_records_4;




update ir_history_interactive_records_4_2 set interactive = '8b3174ceab774f2f93245f0fe4906a3c' where interactive = 'CONSECUTIVE_CHECK_IN';
update ir_history_interactive_records_4_2 set interactive = '8b3174ceab774f2f93245f0fe4906a3c' where interactive = 'CHECK_IN';
update ir_history_interactive_records_4_2 set interactive = 'e6f1836346ee4b91956bb90a249b40ba' where interactive = 'REGISTER';
update ir_history_interactive_records_4_2 set interactive = '9c8070cbf9ee497db298f3da942f028e' where interactive = 'INVITE_REGISTER';
update ir_history_interactive_records_4_2 set interactive = 'b89bdc2dc92947bd895cb29b4d45d7bb' where interactive = 'COMPLETE_INFO';



update ecoke_datamodel.d_p_k_interactiverecords_355 set interactive = '8b3174ceab774f2f93245f0fe4906a3c' where interactive = 'CONSECUTIVE_CHECK_IN';
update ecoke_datamodel.d_p_k_interactiverecords_355 set interactive = '8b3174ceab774f2f93245f0fe4906a3c' where interactive = 'CHECK_IN';
update ecoke_datamodel.d_p_k_interactiverecords_355 set interactive = 'e6f1836346ee4b91956bb90a249b40ba' where interactive = 'REGISTER';
update ecoke_datamodel.d_p_k_interactiverecords_355 set interactive = '9c8070cbf9ee497db298f3da942f028e' where interactive = 'INVITE_REGISTER';
update ecoke_datamodel.d_p_k_interactiverecords_355 set interactive = 'b89bdc2dc92947bd895cb29b4d45d7bb' where interactive = 'COMPLETE_INFO';



insert into ecoke_datamodel.d_p_k_interactiverecords_355
(`id`,`customerNo`,`joinTime`,`signTime`,`channelType`,`interactiveRecordId`,`mobile`,`interactive`,`interactiveType`,`invitee`,
 `inviteeMember`,`member`,`memberIDStr`,`isGetRewards`,`rewardsType`,`memberIntegral`,`grantStatus`,`created`,`lastSync`)
select * from ir_history_interactive_records_5;




insert into ecoke_datamodel.d_p_k_interactiverecords_355
(`id`,`customerNo`,`joinTime`,`signTime`,`channelType`,`interactiveRecordId`,`mobile`,`interactive`,`interactiveType`,`invitee`,
 `inviteeMember`,`member`,`memberIDStr`,`isGetRewards`,`rewardsType`,`memberIntegral`,`grantStatus`,`created`,`lastSync`)
select * from ir_history_interactive_records_patch

-- YM：wxa5811e0426a94686_oa-6a5SAklQs7vxRyoDK5AKr_kS0