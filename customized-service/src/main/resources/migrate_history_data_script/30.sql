-- 261270
select count(1) from ecoke_datamodel.d_p_k_interactiverecords_355 where id like '13430454370588%' and signTime is null;

-- 190
select count(1) from ecoke_datamodel.d_p_k_interactiverecords_355 where id like '13430454370588%' and signTime is null and memberIntegral = 2;
update ecoke_datamodel.d_p_k_interactiverecords_355 set signTime = 0
where id like '13430454370588%' and signTime is null and memberIntegral = 2;
-- 受影响的行: 190
-- 时间: 1.998s

-- 261080
select count(1),max(memberIntegral),max(joinTime) from ecoke_datamodel.d_p_k_interactiverecords_355 where id like '13430454370588%' and signTime is null and memberIntegral != 2;
update ecoke_datamodel.d_p_k_interactiverecords_355 set signTime = memberIntegral
where id like '13430454370588%' and signTime is null and memberIntegral != 2;
-- 受影响的行: 261080
-- 时间: 7.998s

-- 再次检查是否有 signTime 为空数据
-- 122936
select count(1) from ecoke_datamodel.d_p_k_interactiverecords_355 where signTime is null and (interactiveType like 'CHECK_IN%' or interactiveType like 'CONSECUTIVE_CHECK_IN%');
-- 122936
select count(1) from ecoke_datamodel.d_p_k_interactiverecords_355 where signTime is null and (interactiveType like 'CHECK_IN%' or interactiveType like 'CONSECUTIVE_CHECK_IN%') and id like '134304543705%';

-- 更新
update ecoke_datamodel.d_p_k_interactiverecords_355
set signTime = memberIntegral
where signTime is null and (interactiveType like 'CHECK_IN%' or interactiveType like 'CONSECUTIVE_CHECK_IN%') and id like '134304543705%';
-- 受影响的行: 122936
-- 时间: 2.450s


-- 再次检查 签到次数是否有空
select count(1) from ecoke_datamodel.d_p_k_interactiverecords_355 where signTime is null and (interactiveType like 'CHECK_IN%' or interactiveType like 'CONSECUTIVE_CHECK_IN%');
-- 0