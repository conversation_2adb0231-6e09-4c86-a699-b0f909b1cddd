package com.shuyun.kylin.customized.member.service.impl;

import com.shuyun.kylin.customized.base.feign.client.MemberCdpClient;
import com.shuyun.kylin.customized.base.feign.dao.ChannelBindOriginIdDto;
import com.shuyun.kylin.customized.base.feign.dao.CommonTagListResponse;
import com.shuyun.kylin.customized.base.feign.dao.QueryOriginIdRequest;
import com.shuyun.kylin.customized.member.service.MessageReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;


@Slf4j
@Service
public class MessageReportServiceImpl implements MessageReportService {

    @Autowired
    MemberCdpClient memberCdpClient;

    @Override
    public Map messageReport(Map request) {
        return getChannelInfoByOccId(request);
    }

    public Map getChannelInfoByOccId(Map dataMap) {
        QueryOriginIdRequest queryOriginIdRequest = new QueryOriginIdRequest();
        queryOriginIdRequest.setOccId(dataMap.get("occId").toString());
        CommonTagListResponse<ChannelBindOriginIdDto> channelInfoList = memberCdpClient.getOccIdByOriginIds(queryOriginIdRequest);
        dataMap.put("channelInfoList",channelInfoList.getData());
        return dataMap;
    }
}
