package com.shuyun.kylin.customized.base.util;

import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.Instant;
import java.util.Base64;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/25 1:37 下午
 * 通过https://github.com/wechatpay-apiv3/wechatpay-java 拿到类研究出来获得
 */
@Slf4j
public class WechatPayUtils {
    private static String schema = "WECHATPAY2-SHA256-RSA2048";

    public static String getToken(String merchantId,String certificateSerialNo,String privateKey,String method, HttpUrl url, String body) throws UnsupportedEncodingException {
        String nonceStr = System.currentTimeMillis()+"";
        long timestamp = System.currentTimeMillis()/1000;
        String message = buildMessage(method, url, timestamp, nonceStr, body);
        String signature = sign(privateKey,message.getBytes("utf-8"));

        return schema+" mchid=\"" + merchantId + "\","
                + "nonce_str=\"" + nonceStr + "\","
                + "timestamp=\"" + timestamp + "\","
                + "serial_no=\"" + certificateSerialNo + "\","
                + "signature=\"" + signature + "\"";
    }

    public static String sign(String privateKey,byte[] message) {
        try {
            Signature sign = Signature.getInstance("SHA256withRSA");
            sign.initSign(getPrivateKey(privateKey));
            sign.update(message);
            return Base64.getEncoder().encodeToString(sign.sign());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String buildMessage(String method, HttpUrl url, long timestamp, String nonceStr, String body) {
        String canonicalUrl = url.encodedPath();
        if (url.encodedQuery() != null) {
            canonicalUrl += "?" + url.encodedQuery();
        }

        return method + "\n"
                + canonicalUrl + "\n"
                + timestamp + "\n"
                + nonceStr + "\n"
                + body + "\n";
    }

    /**
     * 获取私钥。
     *
     * @return 私钥对象
     */
    public static PrivateKey getPrivateKey(String content) {
        try {
            String privateKey = content.replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replaceAll("\\s+", "");

            KeyFactory kf = KeyFactory.getInstance("RSA");
            return kf.generatePrivate(
                    new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKey)));
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("当前Java环境不支持RSA", e);
        } catch (InvalidKeySpecException e) {
            throw new RuntimeException("无效的密钥格式");
        }
    }

    public static boolean checkTimestamp(String timestamp, int minutes) {
        try {
            if (timestamp != null) {
                long remote = Long.parseLong(timestamp);
                long local = Instant.now().toEpochMilli();
                return local - remote <= (long) (minutes * 60) * 1000L;
            } else {
                return false;
            }
        } catch (NumberFormatException e) {
            log.error("parse timestamp error. exception: {}, timestamp: {}", e.getMessage(), timestamp);
            return false;
        }
    }

    public static String buildUrl(String baseUrl, Map<String, String> params) throws IOException {
        String query = ApiSignUtils.buildQuery(params, "UTF-8");
        return ApiSignUtils.buildRequestUrl(baseUrl, query);
    }


    public static void main(String[] args) throws UnsupportedEncodingException {
        String merchantId="1465263802";
        String certificateSerialNo="35D528EC13DCF4D6AE86382C8933098044359BA3";
        String privateKey="-----BEGIN PRIVATE KEY-----" +
                "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDxDJ53c1vHbC+K" +
                "VGqVdJXOSgalgy9KRlQL8moXCdpOTk5G/inDm5/dO4tgpWPoqq8MCmHC7UcnZFRw" +
                "AAMUif8eul2So/B+4ecfKq03Ycs8vyVe7hckEZ78w4/44+gNItfQ2xpEIyejEFE6" +
                "bLsNzMgrGrukvTGddiquqRy4I8QN2uOm+OHp9NtiujuWL/sCabOFVsEfbaw/1WdF" +
                "HVryvx6U0/V4vxBc6rIgnp47UKOwg6Y/WYYbalYfUo3H2UgeUkAN6I8clM+HjOF2" +
                "O5ETPinmz7ZBBObtBT+ZrFoauFEjDRSvm5LY+nZURKmbTZ2wCr3a5DDcgfqusBLK" +
                "BjOBPymhAgMBAAECggEAJFQHie8b1OlMHOfuDEo+zZFAgS9NMD97KTXGoWQ9v3Fz" +
                "wKVLfAx3QmtX7R/f98/Jg+IIq80A3UH0q9CEplLJ3UKvGYuVkjnPkFXpNyCI9cXF" +
                "BxKQ8EGrmWKLzzHh5iDrL7HluoEl1yB9bXW3lzPWsxBbWU+eK6IEesBH8MSLJp8c" +
                "sRBkCCPC4EdR4nYJp9mLUCnxJFvy7/V0hc8gk6T/Wc/qoDKszGfVIFPXXTR8hy/9" +
                "vMYZQ3I4vS1IIvwX7nNVuVoL5p+9B7JPaLqmQDp6xKm9rZwrRStBGaGz18kXS6b9" +
                "acuFleGIUrUCxIs2VxlNpz/PSqtl2+Mp8o10n/stwQKBgQD6aSA9DIy2+3VxBpPc" +
                "JgN5E7mx2oy3oPisiRolOOF5v8CrAD/yh28bmUD9hBwCAHcxjX9Bwcr/aWAvEr3D" +
                "+RkAIBnfobO7SutPmrc2ynTHc3LWRnp7Zb9dUpAyU8nP5EgKCT3A2YXzr6DNNcj8" +
                "BqsMz8Aiy8GBJDt2JAXWC+pr2QKBgQD2bgBSvYg0DqxVJ4xBVfqv3RD16aRmy5aA" +
                "v0mRxQWcXJ+ug6jl0kCihmhXGuSnMOCnruBOG3uDniB4q9KVNjguuhr1PGyb4nNn" +
                "15NpedT/DF/LfmTDW8SEJMXOu7yIBEN4yNBzBVyOWiqtn+nmNXK/kdQvYnS58RFE" +
                "+PUCDqv3CQKBgQDN47PjHjdwmC6G4Q6fuw2t9p7+8iAzN4Jfclbx9ePGE/iZYRoz" +
                "ElIzkf2StJXGu5VFQ/83FN0QxhaUfcdTo7N/m+VKL4002LPDfwVXYn1EJNNuBQLe" +
                "BNmkJKd1CdlsOllVDyYcM33k0CnZsHhxaBYp8YMb2QZgZWcSiynVRXTYeQKBgCBh" +
                "7CDzTwaoNvUpsUCUr9CUWDo4baYkcq6QdHZ3UXluon0oR/WLX3r79pIbsphVz++5" +
                "Bzi8W8q76MBys8Vvzoo1Yn1/ZzZE1615oqFlpRuu4Je5EDyrvnMu8d9sxCWLieJB" +
                "YUL8HWGHb7xJG09pDp8l5qAja/tTNlW98VRH4UkhAoGBAKgkcBtitBuBL6YNqagn" +
                "CoBnPGfZFDtRdxV33BcBf5xBIR8e966wOfOwVd03ym4UNqBAOyIVZW276iFt0VTR" +
                "QzdkgHq+6zxNwyLRNJ8Wjg8/7CGF/tzrXf8LXkv0dPu4sW5OdDSBdc2FvTG/uwwr" +
                "c+MPsxRYSIy22O673lEpftUm" +
                "-----END PRIVATE KEY-----";
        String method="POST";
        String url="https://api.mch.weixin.qq.com/v3/marketing/favor/coupon-stocks";
        HttpUrl httpurl = HttpUrl.parse(url);
        String body="{\n" +
                "    \"stock_name\":\"8月1号活动\",\n" +
                "    \"belong_merchant\":\"1465263802\",\n" +
                "    \"available_begin_time\":\"2023-08-10T13:29:35+08:00\",\n" +
                "    \"available_end_time\":\"2023-08-25T13:29:35+08:00\",\n" +
                "    \"stock_use_rule\":{\n" +
                "        \"max_coupons\":1,\n" +
                "        \"max_amount\":5,\n" +
                "        \"max_coupons_per_user\":1,\n" +
                "        \"natural_person_limit\":true,\n" +
                "        \"prevent_api_abuse\":true\n" +
                "    },\n" +
                "    \"stock_type\":\"NORMAL\",\n" +
                "    \"coupon_use_rule\":{\n" +
                "        \"coupon_available_time\":{\n" +
                "            \"fix_available_time\":{\n" +
                "                \"available_week_day\":[1],\n" +
                "                \"begin_time\":0,\n" +
                "                \"end_time\":3600\n" +
                "            }\n" +
                "        },\n" +
                "        \"fixed_normal_coupon\":{\n" +
                "            \"coupon_amount\":5,\n" +
                "            \"transaction_minimum\":100\n" +
                "        },\n" +
                "        \"available_merchants\":[\"1465263802\"]\n" +
                "    },\n" +
                "    \"no_cash\":true,\n" +
                "    \"out_request_no\":\"100002322023080754234sfdf\"\n" +
                "}";

        System.out.println(WechatPayUtils.getToken(merchantId,certificateSerialNo,privateKey,method,httpurl,body));
    }
}
