package com.shuyun.kylin.customized.rpc.dto;

import lombok.Data;

import java.util.List;

/**
 * @author: Jingwei
 * @date: 2025-01-08
 */
@Data
public class McdSendCouponResponseDto {
    /**
     * 状态代码
     */
    private String code;

    /**
     * 状态描述
     */
    private String msg;

    private RspData data;

    @Data
    public static class RspData {
        /**
         * 交易号
         */
        private String tradeNo;
        /**
         * 用户 ID
         */
        private String customerId;
        /**
         * 领券结果列表
         */
        private List<Coupon> coupons;

        @Data
        public static class Coupon {
            /**
             * 优惠券名称
             */
            private String campaignName;
            /**
             * 卡券 ID
             */
            private String couponId;
            /**
             * 券码列表
             */
            private List<CouponCodeItem> couponCodes;

            @Data
            public static class CouponCodeItem {
                /**
                 * 券码
                 */
                private String couponCode;
                /**
                 * 可核销开始时间
                 */
                private String couponCodeStartTime;
                /**
                 * 可核销结束时间
                 */
                private String couponCodeEndTime;
            }
        }
    }
}
