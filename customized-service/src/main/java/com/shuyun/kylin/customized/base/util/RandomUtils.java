package com.shuyun.kylin.customized.base.util;

import com.shuyun.kylin.customized.base.common.Constants;

import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
/**
 * <AUTHOR>
 * @Created by on 2019/04/24 17:49.
 * @Description:
 */
public class RandomUtils {
    private RandomUtils() {
    }
    private static final Random RANDOM = new Random();
    private static char[] CODES = {
            '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
            'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'
    };
    /**
     * 返回随机字符串，同时包含数字、大小写字母
     *
     * @param len 字符串长度，不能小于3
     * @return String 随机字符串
     */
    public static String randomAlphanumeric(int len) {
        if (len < 3) {
            throw new IllegalArgumentException("字符串长度不能小于3");
        }
        char[] chArr = new char[len];
        chArr[0] = (char) ('0' + uniform(0, 10));
        chArr[1] = (char) ('A' + uniform(0, 26));
        chArr[2] = (char) ('a' + uniform(0, 26));
        for (int i = 3; i < len; i++) {
            chArr[i] = CODES[uniform(0, CODES.length)];
        }
        for (int i = 0; i < len; i++) {
            int r = i + uniform(len - i);
            char temp = chArr[i];
            chArr[i] = chArr[r];
            chArr[r] = temp;
        }
        return new String(chArr);
    }
    /**
     * 随机返回0到N-1之间的整数 [0,N)
     *
     * @param n 上限
     * @return int 随机数
     */
    public static int uniform(int n) {
        return RANDOM.nextInt(n);
    }
    /**
     * 随机返回a到b-1之间的整数 [a,b)
     *
     * @param a 下限
     * @param b 上限
     * @return int 随机数
     */
    public static int uniform(int a, int b) {
        return a + uniform(b - a);
    }

    public static boolean checkNum(String str) {
        Matcher m =  Pattern.compile("[0-9]+").matcher(str);
        return m.find();
    }

    public static String generateCode() {
    /*    for (int i = 0; i < 100000; i++) {
              String s = randomAlphanumeric(Constants.len);
          if (!checkNum(s)) {
                return s;
            }
            return s;
        }*/
        return randomAlphanumeric(Constants.len);
    }

    public static String generateCodePlus(Integer len) {
    /*    for (int i = 0; i < 100000; i++) {
            String s = randomAlphanumeric(len);
            if (!checkNum(s)) {
                return s;
            }
            return s;
        }
        return "";*/

        return randomAlphanumeric(len);
    }
}
