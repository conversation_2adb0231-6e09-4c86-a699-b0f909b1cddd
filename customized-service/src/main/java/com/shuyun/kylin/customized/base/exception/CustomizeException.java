package com.shuyun.kylin.customized.base.exception;

import com.shuyun.kylin.customized.base.enums.ExceptionEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/12/11
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public class CustomizeException extends RuntimeException {

    private static final long serialVersionUID = 7661219337594528300L;

    private String error_code;
    private String msg;

    public CustomizeException(ExceptionEnum errorCodeEnum, String extraMsg) {
        this.error_code = errorCodeEnum.getCode();
        this.msg = StringUtils.isEmpty(extraMsg) ? errorCodeEnum.getMsg() : extraMsg;
    }

    public CustomizeException(ExceptionEnum errorCodeEnum ) {
        this.error_code = errorCodeEnum.getCode();
        this.msg = errorCodeEnum.getMsg();
    }


    @Override
    public String toString() {
        return "Error Code"+this.error_code+"Error Msg"+this.msg;
    }
}