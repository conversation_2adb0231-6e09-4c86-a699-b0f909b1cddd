package com.shuyun.kylin.customized.member.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CepRulePointLogDto {

    private String customerNo;
    @NotBlank(message = "businessId不为空")
    private String businessId;
    @JsonProperty("KZZD3")
    private String KZZD3;
    @NotBlank(message = "costTracing")
    private String costTracing;

}
