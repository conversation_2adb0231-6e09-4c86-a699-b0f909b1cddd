package com.shuyun.kylin.customized.member.dm;

import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.base.util.DateHelper;
import com.shuyun.kylin.customized.member.dto.CampaignPointsRuleDto;
import com.shuyun.kylin.customized.member.dto.CostCenterDto;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class CampaignPointsRuleRepository extends BaseDsRepository<CampaignPointsRuleDto> {
    /**
     * 查询成本中心
     */
    public List<Map<String,Object>> selectCostCenter() {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("1", 1);
        String queryMemberSql = " select scene,type,ruleType,task,startTime,endTime,sendOrDeductValuePerTime,sendPointTimeliness," +
                "sendTimeLimitPerDay,sendTimeLimitPerMonth,sendTimeTotalLimit,isTransactionRelated from " + ModelConstants.CAMPAIGN_CAMPAIGNPOINTSRULE +"  where 1 = :1 ";
        List<Map<String,Object>> list =  execute(queryMemberSql, queryMap).getData();
        return list;
    }
}
