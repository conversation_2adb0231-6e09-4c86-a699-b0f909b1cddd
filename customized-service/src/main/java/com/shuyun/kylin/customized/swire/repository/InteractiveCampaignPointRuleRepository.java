package com.shuyun.kylin.customized.swire.repository;

import com.alibaba.fastjson.JSON;
import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.member.dto.ConsumerPlatInfoDto;
import com.shuyun.kylin.customized.swire.dto.InteractiveAndCampaignPointsRuleDto;
import com.shuyun.kylin.customized.swire.dto.RulePointRecordDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class InteractiveCampaignPointRuleRepository extends BaseDsRepository<InteractiveAndCampaignPointsRuleDto> {


    /**
     * 查询活动
     * @param id
     * @return
     */
    public InteractiveAndCampaignPointsRuleDto getCampaign(String id) {
        Map<String, Object> map = new HashMap<>();
        map.put("id",id);
        List<String> list = new ArrayList<>();
        list.add("name");
        list.add("scene");
        list.add("channelType");
        list.add("startTime");
        list.add("endTime");
        list.add("type");
        InteractiveAndCampaignPointsRuleDto interactiveAndCampaignPointsRuleDto = queryByFilter(ModelConstants.INTERACTIVE_CAMPAIGN_POINT_RULE,list,map);
        return interactiveAndCampaignPointsRuleDto;
    }

    public void deleteByScene(Integer ruleId, List<String> id) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("ruleCode",ruleId);
        queryMap.put("id", id);
        String queryOrderSql = " delete from " + ModelConstants.KAILY_LIMITED_POINTSRULE + " where ruleCode=:ruleCode and id not in (:id)";
        execute(queryOrderSql, queryMap);
    }

}
