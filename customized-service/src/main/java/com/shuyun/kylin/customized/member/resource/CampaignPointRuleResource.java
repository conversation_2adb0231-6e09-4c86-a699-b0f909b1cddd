package com.shuyun.kylin.customized.member.resource;

import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.member.dto.*;
import com.shuyun.kylin.customized.member.service.ICampaignPointRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description 活动积分规则增删改
 * @date 2021/12/18
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/campaign/point/rule")
public class CampaignPointRuleResource {

    @Autowired
    ICampaignPointRuleService iCampaignPointRuleService;

    /**
     * @param scene 活动积分规则查询
     * <AUTHOR>
     * @Date 2021/12/17 15:48
     */
    @GetMapping("/get")
    public ResponseResult getCampaignPointsRule(@RequestParam(required = false) String scene) {
        log.info("活动积分规则查询: {}", scene);
        return iCampaignPointRuleService.getCampaignPointsRule(scene);
    }

    /**
     * @param scene 活动积分规则删除
     * <AUTHOR>
     * @Date 2021/12/17 15:48
     */
    @DeleteMapping("/delete")
    public ResponseResult deletCampaignPointsRule(@RequestParam String scene) {
        log.info("活动积分规则删除: {}", scene);
        return iCampaignPointRuleService.deletCampaignPointsRule(scene);
    }

    /**
     * 活动积分规则保存/更新
     *
     * @param cepRegulationPointDto
     * @return
     */
    @PostMapping("/save")
    public ResponseResult saveCampaignPointsRule(@Validated @RequestBody CepRegulationPointDto cepRegulationPointDto) {
        log.info("活动积分规则保存/更新: {}", cepRegulationPointDto);
        return iCampaignPointRuleService.saveCampaignPointsRule(cepRegulationPointDto);
    }


    /**
     * 规则场景变更会员积分(瓶子)
     *
     * @param cepMemberDeductionDto
     * @return
     */
    @PostMapping("/point")
    public ResponseResult updateMemberPoint(@Validated @RequestBody CepMemberDeductionDto cepMemberDeductionDto) {
        log.info("规则场景变更会员积分(瓶子)请求入参: {}", cepMemberDeductionDto);
        return iCampaignPointRuleService.updateMemberPoint(cepMemberDeductionDto);
    }

    /**
     * 规则场景可变积分查询
     * @param cepMemberGetableDto
     * @return
     */
    @PostMapping("/point/getable")
    public ResponseResult queryGetablePoint(@Validated @RequestBody CepMemberGetableDto cepMemberGetableDto) {
        log.info("规则场景可变积分查询请求入参: {}", cepMemberGetableDto);
        return iCampaignPointRuleService.queryGetablePoint(cepMemberGetableDto);
    }

    /**
     * 修改规则场景变更会员积分(KZZD3)
     *
     * @param cepRulePointLogDto
     * @return
     */
    @PostMapping("/point/update")
    public ResponseResult updateMemberPointLog(@Validated @RequestBody CepRulePointLogDto cepRulePointLogDto) {
        log.info("修改规则场景变更会员积分(KZZD3)请求入参: {}", cepRulePointLogDto);
        return iCampaignPointRuleService.updateMemberPointLog(cepRulePointLogDto);
    }

}
