package com.shuyun.kylin.customized.base.stream.kafka.producer.coupon;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.shuyun.kylin.customized.base.stream.kafka.dao.ReachBehaviorDto;
import com.shuyun.kylin.customized.base.stream.kafka.messaging.KafkaBehavior;
import com.shuyun.kylin.customized.base.stream.kafka.messaging.KafkaNotification;
import com.shuyun.kylin.customized.swire.dto.SwireMemberDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@EnableBinding({KafkaBehavior.class, KafkaNotification.class})
@ConditionalOnExpression("${system.kafka.enabled:true}")
public class BehaviorProducer {

    @Autowired
    private KafkaBehavior kafkaBehavior;

    public void sendMsg(ReachBehaviorDto reachBehaviorDto){
        log.info("【 发送到主动营销 Topic：marketing-action-custom-reach-behavior  开始=======> 送出参数: {} 】",reachBehaviorDto);
        kafkaBehavior.output().send(MessageBuilder.withPayload(reachBehaviorDto).build());
        log.info("【 发送到主动营销 Topic：marketing-action-custom-reach-behavior 结束=======> 送出参数: {} 】", reachBehaviorDto);
    }

}
