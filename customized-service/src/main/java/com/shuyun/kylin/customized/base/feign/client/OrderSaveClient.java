package com.shuyun.kylin.customized.base.feign.client;

import com.shuyun.kylin.customized.base.feign.dao.MemberOrderDao;
import com.shuyun.kylin.customized.member.dto.ec.EcNormalOrderDto;
import com.shuyun.kylin.customized.member.dto.ec.EcRefundOrderDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = "orderSaveClient")
@RequestMapping("openapi/v2")
public interface OrderSaveClient {


    @PostMapping("/order/save/normalOrder")
    Boolean saveNormalOrder(@RequestBody EcNormalOrderDto ecNormalOrderDto);

    @PostMapping("/order/save/refundOrder")
    Boolean saveRefundOrder(@RequestBody EcRefundOrderDto ecRefundOrderDto);

    @GetMapping("/order/property/KO/page")
    MemberOrderDao queryMemberOrder(@RequestParam String memberType,
                                    @RequestParam String memberId,
                                    @RequestParam(required = false) String mobile,
                                    @RequestParam(required = false) String status,
                                    @RequestParam(required = false) String keyType,
                                    @RequestParam(required = false) String keyWord,
                                    @RequestParam(required = false) String channelType,
                                    @RequestParam(required = false) String orderBeginTime,
                                    @RequestParam(required = false) String orderEndTime,
                                    @RequestParam(required = false) Integer currentPage,
                                    @RequestParam(required = false) Integer pageSize
                                    );
}
