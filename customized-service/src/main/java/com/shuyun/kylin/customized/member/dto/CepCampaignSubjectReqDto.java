package com.shuyun.kylin.customized.member.dto;


import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class CepCampaignSubjectReqDto {

    @NotBlank(message = "活动编码不能为空")
    private String campaignCode;
    @NotBlank(message = "活动名称不能为空")
    private String campaignName;
    private String campaignRule;
    private String campaignBrand;
    @NotBlank(message = "活动Id不能为空")
    private String campaignId;
    private String campaignType;
    private String campaignSubType;
    private String campaignNature;
   // @NotBlank(message = "成本中心编号不能为空")
    private String campaignCostCenter;
    private String startTime;
    private String endTime;
    private String lastSync;
    private String status;
    private String remarks;
    private String channel;
    private String campaigngroup;

    private String type;
    private String campaignCategory;
    private String campaignScenario;
}
