package com.shuyun.kylin.customized.member.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class CepPointBehaviorRecordResponse {
    private Double point;
    private  String changeType;
    private  String channelType;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String changeTime;
}
