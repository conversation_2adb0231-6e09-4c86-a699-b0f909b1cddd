package com.shuyun.kylin.customized.project.strategy.router;

import com.shuyun.kylin.customized.project.strategy.*;

public enum SendCouponEnum {
    // costa南区
    COSTA_SOUTH("costaSouth",new CostaSouthSendCouponStrategy()),
    // ubr
    UBR("ubr",new UBRSendCouponStrategy()),
    // 常规权益
    CONVENTION("convention",new ConventionSendCouponStrategy()),
    // 三方券码
    THREE_PARTIES("threeParties",new ThreePartiesSendCouponStrategy()),
    // 爱奇艺
    IQIYI("iQiYi",new IQiYiSendCouponStrategy()),
    // 知而行
    ZERX("zerx",new ZerXSendCouponStrategy()),
    // 微信代金券
    WX("wx",new WxSendCouponStrategy()),
    // 麦当劳
    MCD("mcd",new McdCouponStrategy()),
    // BK汉堡王
    BK("bk",new BKCouponStrategy()),
    ;

    private final String code;
    private final SendCouponStrategy strategy;

    SendCouponEnum(String code, SendCouponStrategy strategy) {
        this.code = code;
        this.strategy = strategy;
    }

    // 根据code做路由，返回策略类
    public static SendCouponStrategy router(String code) {
        for (SendCouponEnum sendCouponEnum : SendCouponEnum.values()) {
            if (sendCouponEnum.getCode().equals(code)) {
                return sendCouponEnum.getStrategy();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public SendCouponStrategy getStrategy() {
        return strategy;
    }
}
