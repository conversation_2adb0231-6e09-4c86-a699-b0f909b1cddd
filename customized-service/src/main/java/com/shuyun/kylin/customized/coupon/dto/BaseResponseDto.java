package com.shuyun.kylin.customized.coupon.dto;

import com.fasterxml.jackson.annotation.JsonValue;

import com.shuyun.kylin.customized.base.exception.SystemException;
import lombok.*;
import org.apache.commons.lang.StringUtils;


@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BaseResponseDto<T> {

    private T data;
    private ResponseStatusEnum status;
    private String msg;
    private boolean customizedMsg = false;
    public BaseResponseDto(T data, ResponseStatusEnum status) {
        this.data = data;
        this.status = status;
    }
    public BaseResponseDto(T data, ResponseStatusEnum status, String msg) {
        if (StringUtils.isNotEmpty(msg)) {
            this.customizedMsg = true; }
        this.data = data;
        this.status = status;
        this.msg = msg;
    }
    public String getMsg() {
        if (customizedMsg) {
            return msg; }
        if (status == null) {
            throw new SystemException("Status of CommonResponseDTO object must be set if msg is not customized.");
        } else if (status == ResponseStatusEnum.SUCCESS) {
            this.msg = ResponseStatusEnum.SUCCESS.getMsg();
        } else if (status == ResponseStatusEnum.FAIL) {
            this.msg = ResponseStatusEnum.FAIL.getMsg();
        }
        return msg;
    }
    public T getData() {
        return data;
    }
    public void setData(T data) {
        this.data = data;
    }
    public ResponseStatusEnum getStatus() {
        return status;
    }
    public void setStatus(ResponseStatusEnum status) {
        this.status = status;
    }
    public void setMsg(String msg) {
        if (StringUtils.isNotEmpty(msg)) {
            this.customizedMsg = true;
        } else {
            this.customizedMsg = false;
        }
        this.msg = msg;
    }

    @AllArgsConstructor
    @Getter
    public enum ResponseStatusEnum {
        SUCCESS("200", "处理成功"),
        FAIL("400", "处理失败");
        @JsonValue
        private String code;
        private String msg;
    }
}
