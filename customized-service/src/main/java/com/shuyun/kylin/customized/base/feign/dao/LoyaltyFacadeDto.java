package com.shuyun.kylin.customized.base.feign.dao;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.ZonedDateTime;

@Data
public class LoyaltyFacadeDto {

    private String memberId;
    private String channelType;
    private Integer pointAccountId;
    private String shopId;
    private String businessId;
    private Integer idempotentMode;
    private String desc;
    private String actionName;
    private String changeMode;
    private boolean autoFillShopId;
    private Double point;
    private ZonedDateTime effectiveDate;
    private ZonedDateTime overdueDate;
    @JsonProperty("KZZD1")
    private String KZZD1;
    @JsonProperty("KZZD2")
    private String KZZD2;
    @JsonProperty("KZZD3")
    private String KZZD3;


    /**
     * {
     *   "memberId": "string",    *
     *   "channelType": "string", *
     *   "pointAccountId": 0,     *
     *   "shopId": "string",
     *   "businessId": "string",
     *   "idempotentMode": 0,
     *   "desc": "string",
     *   "actionName": "string",
     *   "changeMode": "string",
     *   "autoFillShopId": true,
     *   "point": 0,              *
     *   "effectiveDate": "2022-04-22T02:03:13.130Z",
     *   "overdueDate": "2022-04-22T02:03:13.130Z",
     *   "KZZD1": "string",
     *   "KZZD2": "string",
     *   "KZZD3": "string"
     * }
     */


    /**
     * {
     *   "memberId": "string",
     *   "channelType": "string",
     *   "pointAccountId": 0,
     *   "shopId": "string",
     *   "businessId": "string",
     *   "idempotentMode": 0,
     *   "desc": "string",
     *   "actionName": "string",
     *   "changeMode": "string",
     *   "autoFillShopId": true,
     *   "point": 0,
     *   "KZZD1": "string",
     *   "KZZD2": "string",
     *   "KZZD3": "string"
     * }
     */
}
