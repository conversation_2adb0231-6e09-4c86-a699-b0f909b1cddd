package com.shuyun.kylin.customized.base.feign.client;

import com.shuyun.kylin.customized.base.feign.dao.ProjectCountResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(name = "benefitClient")
@RequestMapping("benefit-mgmt/v1")
public interface BenefitClient {

    @GetMapping("/internal/project/{projectId}/count")
    ProjectCountResponse internalCount(@PathVariable("projectId") String projectId);

}
