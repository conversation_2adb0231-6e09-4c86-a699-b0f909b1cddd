package com.shuyun.kylin.customized.rpc.dto;

import lombok.Data;

import java.util.List;

@Data
public class UBRCouponResponseDto {
    /**
     * 兑换历史
     */
    private List<UBRCouponCodeActivity> activities;
    /**
     * 卡片序列号
     */
    private String cardSerialNumber;
    /**
     * 券实例所对应券产品编码
     */
    private String code;
    /**
     * 客户ID
     */
    private String customerId;
    /**
     * 票的第一次入园日期
     */
    private String firstUsageDate;
    /**
     * 券实例对应的证件号（如果在下单时提供了证件号）
     */
    private String govId;
    /**
     * 券实例对应的证件类型(如果在下单时提供了证件类型), 为以下取值:
     * GovernmentId - 身份证
     * TravelPermit - 港澳台居民来往内地通行证
     * ResidencePermit - 港澳台居民居住证
     * Passport - 护照
     * ChinaPR - 外国人永久居留证
     */
    private String govIdType;
    /**
     * false 为线上券，true 为线下券
     */
    private Boolean isOnsiteVoucher;
    /**
     * 卡类型
     */
    private Boolean isPhysical;
    /**
     * 与该券码关联的二维码,用于线下核销
     */
    private UBRCouponCodeQrData qrCode;
    /**
     * 券实例可退标识
     */
    private Boolean refundable;
    /**
     * 券实例状态:
     * - New [新创建]
     * - Distributed [已分发]
     * - Sold [已售出]
     * - Shared [已分享]
     * - Redeemed [已核销]
     * - Void [已失效]
     */
    private String status;
    /**
     * 券实例编码
     */
    private String uniqueCode;
    /**
     * 券实例开始时间（针对因为促销互动发放的券码,券码的生命周期和券种的生命周期有差异）
     */
    private String validDateFrom;
    /**
     * 券实例过期时间（针对因为促销互动发放的券码,券码的生命周期和券种的生命周期有差异）
     */
    private String validDateTo;

    @Data
    public static class UBRCouponCodeActivity {
        /**
         * 活动类型:
         * - New [新创建]
         * - Distributed [已分发]
         * - Sold [已售出]
         * - Shared [已分享]
         * - Redeemed [已核销]
         * - Void [已失效]
         */
        private String action;
        /**
         * 活动时间
         */
        private String time;
    }

    @Data
    public static class UBRCouponCodeQrData {
        private String imgPadUrl;
        private String imgPhoneUrl;
        private String qrBody;
        private String qrNo;
        private String qrNoSign;
        private String qrSign;
    }
}
