package com.shuyun.kylin.customized.member.dto;


import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

@Data
public class CepMemberPointDto {

    @NotBlank(message = "客户编号不能为空，格式：appid_openid")
    private String customerNo;
    @NotBlank(message = "会员渠道不能为空: TAOBAO：天猫,KO_MP：KO小程序,EC_SHOPPING：EC购物商城,EC_POINT：EC积分商城,H5：H5活动")
    private String channelType;
    private String campaignId;
    private String campaignName;
    private String campaignRuleDesc;
    private Integer totalPoint;
    private Integer consumePoint;
    private Integer validPoint;
    private String memberIDStr;

    private Map member;

}
