package com.shuyun.kylin.customized.swire.repository;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.base.util.DateHelper;
import com.shuyun.kylin.customized.member.dto.ConsumerPlatInfoDto;
import com.shuyun.kylin.customized.swire.dto.DailyLimitedPointsRuleDto;
import com.shuyun.kylin.customized.swire.dto.RulePointRecordDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class RulePointRecordRepository extends BaseDsRepository<RulePointRecordDto> {

    public Map<String, Double> querMemberPointDay(String memberId) {
        HashMap<String, Double> map = new HashMap<>();
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        queryMap.put("startTime", DateHelper.getZone(DateHelper.getDateDay()));
        queryMap.put("endTime", DateHelper.getZone(DateHelper.getEendTimeDay()));
        String sql = " SELECT scene,point FROM  " + ModelConstants.MEMBER_RULEPOINTRECORD + "  where memberId = :memberId and created >= :startTime and created <= :endTime";
        //log.info("查询规则daysql:{},startTime:{},endTime:{}",sql,DateHelper.getZone(DateHelper.getDateDay()),DateHelper.getZone(DateHelper.getEendTimeDay()));
        List<RulePointRecordDto> ruleDtos = executeSQL(sql, queryMap);
        log.info("查询的结果:{}", JSON.toJSONString(ruleDtos));
        if (CollectionUtils.isNotEmpty(ruleDtos)) {
            for (RulePointRecordDto ruleDto : ruleDtos) {
                log.info("查询的结果11:{}", JSON.toJSONString(ruleDtos));
                map.put(ruleDto.getScene(),Double.valueOf(String.valueOf(ruleDto.getPoint())));
            }
        }
        return map;
    }

    public HashMap<String, Double> querMemberPoint(String memberId) {
        HashMap<String, Double> map = new HashMap<>();
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        String sql = " SELECT scene,accumulatePoint FROM  " + ModelConstants.MEMBER_RULEPOINTRECORD + "  where memberId = :memberId ";
        List<RulePointRecordDto> ruleDtos = executeSQL(sql, queryMap);
        log.info("查询的结果:{}", JSON.toJSONString(ruleDtos));
        if (CollectionUtils.isNotEmpty(ruleDtos)) {
            for (RulePointRecordDto ruleDto : ruleDtos) {
                log.info("查询的结果11:{}", JSON.toJSONString(ruleDtos));
                map.put(ruleDto.getScene(),Double.valueOf(String.valueOf(ruleDto.getPoint())));
            }
        }
        return map;
    }

    public List<RulePointRecordDto> querMemberPoints(String memberId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        String sql = " SELECT scene,point,accumulatePoint,created FROM  " + ModelConstants.MEMBER_RULEPOINTRECORD + "  where memberId = :memberId ";
        List<RulePointRecordDto> ruleDtos = executeSQL(sql, queryMap);
        log.info("查询的结果:{}", JSON.toJSONString(ruleDtos));
        return ruleDtos;
    }


    public List<RulePointRecordDto> querRuleScene(Integer ruleCode) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("ruleCode", ruleCode);
        String sql = " SELECT scene,channelType FROM  " + ModelConstants.KAILY_LIMITED_POINTSRULE + "  where ruleCode = :ruleCode  ";
        List<RulePointRecordDto> ruleDtos = executeSQL(sql, queryMap);
        return ruleDtos;


    }
}
