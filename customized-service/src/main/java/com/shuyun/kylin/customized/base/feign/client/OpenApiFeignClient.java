package com.shuyun.kylin.customized.base.feign.client;

import com.shuyun.kylin.crm.openapi.core.dto.common.SaveDataSingleRequestDto;
import com.shuyun.kylin.crm.openapi.core.dto.spi.DataTransferValidationResultDto;
import com.shuyun.kylin.customized.base.feign.dao.MemberBingDto;
import com.shuyun.kylin.customized.base.feign.dao.MemberCustomDto;
import com.shuyun.kylin.customized.base.util.ActionResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@FeignClient(name = "openApiFeignClient")
@RequestMapping("openapi/v2")
public interface OpenApiFeignClient {

    @GetMapping("/member")
    MemberCustomDto queryMember(@RequestParam("memberType") String memberType,
                                @RequestParam("memberId") String memberId,
                                @RequestParam("optionalFields") List<String> optionalFields);
    @PostMapping("/dataapi/store")
    ActionResult<DataTransferValidationResultDto> store(@RequestBody SaveDataSingleRequestDto saveDataSingleRequestDto);

    @GetMapping("/member/channels")
    List<MemberBingDto> queryListChannels(@RequestParam("memberId") String memberId,
                                          @RequestParam("memberType") String memberType,
                                          @RequestParam(value = "channelType",required = false) String channelType,
                                          @RequestParam(value = "optionalFields",required = false) List<String> optionalFields);

    @GetMapping("/customer/channels")
    List<MemberBingDto> queryCustomer(@RequestParam("customerNo") String customerNo,
                                      @RequestParam("channelType") String channelType,
                                      @RequestParam("memberType") String memberType);

}