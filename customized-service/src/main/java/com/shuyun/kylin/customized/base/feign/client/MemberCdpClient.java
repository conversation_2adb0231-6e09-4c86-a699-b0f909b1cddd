package com.shuyun.kylin.customized.base.feign.client;

import com.shuyun.calc.service.base.rule.expression.CommonExpression;
import com.shuyun.cdp.tagExport.request.ExportTask;
import com.shuyun.cdp.tags.request.OriginTagRequest;
import com.shuyun.cdp.tags.response.OriginTagsResponse;
import com.shuyun.kylin.customized.base.feign.dao.ChannelBindOriginIdDto;
import com.shuyun.kylin.customized.base.feign.dao.CommonTagListResponse;
import com.shuyun.kylin.customized.base.feign.dao.QueryOriginIdRequest;
import com.shuyun.kylin.customized.customer.dto.CommonTagSingleResponse;
import com.shuyun.kylin.customized.customer.dto.TagDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


@FeignClient(name = "memberCdpClient")
@RequestMapping("cdp-mgmt/v1")
public interface MemberCdpClient {


    @PostMapping("/data/query/occId/originIds")
    CommonTagListResponse<ChannelBindOriginIdDto> getOccIdByOriginIds(@RequestBody QueryOriginIdRequest queryOriginIdRequest);

    @GetMapping("/meta/tags/{id}")
    CommonTagSingleResponse<TagDto> getTagInfo(@PathVariable("id") String tagId);

    @GetMapping("/meta/tags/{id}/rule")
    CommonTagSingleResponse<CommonExpression> getTagRuleInfo(@PathVariable("id") String tagId);

    @PostMapping("/data/query/occIds")
    CommonTagSingleResponse<List<String>> getOccIdByChannelId(@RequestBody OriginTagRequest originTagRequest);

    @PostMapping("/data/{brandId}/originId/tags")
    CommonTagSingleResponse<Map> checkCustomerTag(@PathVariable("brandId") String brandId, @RequestBody OriginTagRequest originTagRequest);

    @PostMapping("/openapi/tagExportTask")
    CommonTagSingleResponse tagExportTask(@RequestBody ExportTask exportTask);

}
