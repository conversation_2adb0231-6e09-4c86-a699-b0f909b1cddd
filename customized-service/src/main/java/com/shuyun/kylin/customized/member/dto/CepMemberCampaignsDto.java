package com.shuyun.kylin.customized.member.dto;


import lombok.Data;


import javax.validation.constraints.NotBlank;

import java.util.Map;

@Data
public class CepMemberCampaignsDto {

    private String id;

    @NotBlank(message = "客户编号不能为空，格式：appid_openid")
    private String customerNo;
    //Enum
    @NotBlank(message = "渠道不能为空: TAOBAO：天猫,KO_MP：KO小程序,EC_SHOPPING：EC购物商城,EC_POINT：EC积分商城,H5：H5活动")
    private String channelType;
    @NotBlank(message = "活动编码不能为空, 对应的活动code")
    private String campaignCode;
    //@NotBlank(message = "活动名称不能为空, 对应的活动名称")
    private String campaignName;
    private String joinTime;
    private Boolean isGetRewards;
    private Boolean isLuckyDraw;
    private String getRewardsTime;
    private String rewardsType;
    private String rewardsCode;
    private String rewardsName;
    private String rewardsCost;
    private String campaignRecordId;
    private String memberIDStr;
    private String remarks;
    private String lastSync;


    private Map member;
    private Map campaign;
}
