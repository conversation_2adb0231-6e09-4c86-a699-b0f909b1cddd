package com.shuyun.kylin.customized.rpc.dto;

import lombok.Data;

import javax.xml.bind.annotation.*;

/**
 *
 * <p>
 * 爱奇艺返回码格式说明（注意爱奇艺的xml是gb2312，在转换过程中如果有乱码需要匹配编码格式）：
 * </p>
 * <pre>
 * {@code
 * <?xml version="1.0" encoding="GB2312" ?>
 * <orderinfo>
 *     <err_msg/>
 *     <retcode>1</ retcode>
 *     <orderid>S0703300003</orderid>
 *     <cardid>221201</cardid>
 *     <cardnum>1</cardnum>
 *     <ordercash>1</ordercash>
 *     <cardname>剑侠情缘II15元直充</cardname>
 *     <sporder_id>2443</sporder_id>
 *     <game_userid>ybb</game_userid>
 *     <game_area>三区</game_area>
 *     <game_srv>天下一家</game_srv>
 *     <game_state>0</game_state>
 * </orderinfo>
 * }
 * </pre>*
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "orderinfo")
@XmlType(propOrder = {
        "errMsg", "retCode", "orderId", "cardNum",
        "orderCash", "cardName", "sporderId", "gameUserId",
        "gameArea", "gameSrv", "gameState"
})
public class IQiYiResponseDto {

    @XmlElement(name = "err_msg")
    private String errMsg;
    @XmlElement(name = "retcode")
    private String retCode;
    /**
     * 爱奇艺订单号
     */
    @XmlElement(name = "orderid")
    private String orderId;
    @XmlElement(name = "cardnum")
    private String cardNum;
    @XmlElement(name = "ordercash")
    private String orderCash;
    @XmlElement(name = "cardname")
    private String cardName;
    @XmlElement(name = "sporder_id")
    private String sporderId;
    @XmlElement(name = "game_userid")
    private String gameUserId;
    @XmlElement(name = "game_area")
    private String gameArea;
    @XmlElement(name = "game_srv")
    private String gameSrv;
    @XmlElement(name = "game_state")
    private String gameState;
}
