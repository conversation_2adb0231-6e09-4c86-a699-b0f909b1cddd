package com.shuyun.kylin.customized.coupon.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.shuyun.kylin.customized.coupon.domain.CouponConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface CouponConfigMapper extends BaseMapper<CouponConfig> {

    @Select("select * from tb_coupon_config where member_type = #{memberType}")
    CouponConfig selectCouponConfigByMemberType(String memberType);

}
