package com.shuyun.kylin.customized.behavior.service.impl;

import com.shuyun.kylin.customized.behavior.domain.Interactive;
import com.shuyun.kylin.customized.behavior.domain.InteractiveRecord;
import com.shuyun.kylin.customized.behavior.resource.po.CheckPO;
import com.shuyun.kylin.customized.behavior.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.shuyun.kylin.customized.behavior.domain.InteractiveRecord.CONSUMER_THREAD_LOCAL;

/**
 * 每日签到
 */
@Slf4j
@Service
public class CheckInResolver extends AbstractInteractionSceneResolver {

    private final String USED_ERROR_INFO = "已领取当日签到奖励";

    private final String OTHER_CHECK_IN = "已领取其他渠道签到奖励";

    @Override
    public CheckPO.SceneType supportScene() {
        return CheckPO.SceneType.CHECK_IN;
    }

    /**
     * 每日签到奖励积分计算
     * @param checkPO 前端传入用户签到账户、渠道、场景等信息
     * @param memberId 用户当前互动渠道的会员ID
     * @param mobile 用户手机号
     * @param interactive 当前互动渠道下的互动规则
     * @param lastRecord 当前互动渠道下用户在本次场景下最后一次获取积分记录
     * @return
     */
    @Override
    protected RewardInfo fetchPointInfo(CheckPO checkPO, String memberId, String mobile, Interactive interactive, InteractiveRecord lastRecord) {
        //校验该用户是否在其他渠道签到过
        if (isOtherChannelPointRecord(interactive.getRewardsRecordScope(), checkPO, memberId)) {
            log.info("不允许多渠道签到获取积分");
            RewardInfo refuse = RewardInfo.refuse(OTHER_CHECK_IN);
            refuse.alreadyCheckIn();
            return refuse;
        }

        //判断当前的签到类型，下面二者，只会有一个有值
        Interactive.DailyCheckIn dailyCheckIn = interactive.fetchDailyCheckIn();
        Interactive.ConsecutiveCheckIn consecutiveCheckIn = interactive.fetchConsecutiveCheckIn();

        RewardInfo rewardInfo = doDailyCheckIn(dailyCheckIn, lastRecord);
        if (rewardInfo == null) {
            rewardInfo = doConsecutiveCheckIn(consecutiveCheckIn, lastRecord);
        }

        return rewardInfo;
    }

    private RewardInfo doConsecutiveCheckIn(Interactive.ConsecutiveCheckIn consecutiveCheckIn, InteractiveRecord lastRecord) {
        log.info("调用连续签到逻辑");
        /*
         * 连续签到奖励规则：
         * 1. 每天只能领取一次
         * 2. 每 连续签到，是一种累计奖励，不是每日都送
         * 3. 不同互动渠道奖励分开计算
         * */
        Integer pointNum = consecutiveCheckIn.getPointNum();
        Integer extraDayNo = consecutiveCheckIn.getExtraDayNo();

        int consecutiveCheckinDays = getConsecutiveCheckinDays(lastRecord);

        log.info("连续签到天数：{}，奖励线为：{}，结果：{}", consecutiveCheckinDays, extraDayNo, consecutiveCheckinDays >= extraDayNo);

        //每隔多少天，就发放奖励
        if (consecutiveCheckinDays % extraDayNo == 0) {
            //设置清零标志
            CONSUMER_THREAD_LOCAL.set(x -> x.setSignTime(0));
            return RewardInfo.point(pointNum);
        }

        return RewardInfo.refuse(String.format("未达到累计签到天数，要求：%s 天，当前：%s 天", extraDayNo, consecutiveCheckinDays % extraDayNo));
    }

    private RewardInfo doDailyCheckIn(Interactive.DailyCheckIn dailyCheckIn, InteractiveRecord lastRecord) {
        log.info("调用每日签到逻辑");
        /*
         * 每日签到奖励规则：
         * 1. 每天只能领取一次
         * 2. 如果连续签到到指定天，则额外奖励
         * 3. 可以设置给不给奖励
         * 4. 不同互动渠道奖励分开计算
         */
        if (dailyCheckIn == null) {
            return null;
        }

        //查询它是否已获取过当天的奖励
        if (lastRecord != null && lastRecord.isGetRewardsToday()) {
            log.info(USED_ERROR_INFO);
            return RewardInfo.refuse(USED_ERROR_INFO);
        }

        //发放基础积分
        Integer pointNum = dailyCheckIn.getPointNum();
        RewardInfo rewardInfo = RewardInfo.point(pointNum);

        //如果上一条记录是非连续的，则当成第一次签到
        if(lastRecord != null) {
            Date lastJoinTime = Date.from(lastRecord.getJoinTime().toInstant());
            //如果是连续的，则 +1，否则，置为 1
            if (!DateUtils.isConsecutiveDates(lastJoinTime, new Date())) {
                return rewardInfo;
            }
        }

        //是否开启连续签到会送更多积分
        if (dailyCheckIn.isExtraAward()) {
            //查询当前天往前连续签到的天数是否达到预期值
            //要求连续签到的天数
            int consecutiveCheckinDays = getConsecutiveCheckinDays(lastRecord);

            Integer extraDayNo = dailyCheckIn.getExtraDayNo();

            log.info("连续签到天数：{}，奖励线为：{}，结果：{}", consecutiveCheckinDays, extraDayNo, consecutiveCheckinDays >= extraDayNo);
            if (consecutiveCheckinDays >= extraDayNo) { //如果达标，则增加奖励积分
                pointNum += dailyCheckIn.getExtraPoints();
                log.info("已满足连续签到天数条件，积分由：{} ---> {}，连续签到额外奖励：{}", dailyCheckIn.getPointNum(), pointNum, dailyCheckIn.getExtraPoints());
                //在领奖时，将连续签到天数清零
                CONSUMER_THREAD_LOCAL.set(x -> x.setSignTime(0));
                rewardInfo.setConsecutiveCheckin(true);
                rewardInfo.setPoint(pointNum);
            }
        }

        return rewardInfo;
    }

    private int getConsecutiveCheckinDays(InteractiveRecord lastRecord) {
        //上一次连续签到多少天了
        int consecutiveCheckinDays = 0;
        if (lastRecord != null) {
            consecutiveCheckinDays = lastRecord.getSignTime();
        }

        return ++consecutiveCheckinDays; //因为当前这次也算 1 次，所以 +1
    }
}
