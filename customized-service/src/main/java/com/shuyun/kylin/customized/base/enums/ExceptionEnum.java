package com.shuyun.kylin.customized.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/12/11
 */
@Getter
@AllArgsConstructor
public enum ExceptionEnum {

    /**
     * 自定义
     */
    RECORD_NOT_FOUND("10001", "没有找到对应记录"),
    BEAN_CONVERT_FAILED("10002", "对象转换失败"),
    ILLEGAL_ARGUMENT("10003", "非法参数"),
    BLACK_LIST("10004","黑名单"),

    /**
     * 系统
     */
    INVALID_PARAMETER("invalid-parameter","参数校验失败"),
    INTERNAL_ERROR("internal-error","Customize服务内部错误");


    private String code;
    private String msg;

}
