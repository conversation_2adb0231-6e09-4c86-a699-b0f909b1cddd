package com.shuyun.kylin.customized.base.stream.kafka.producer.coupon;

import com.shuyun.kylin.customized.base.stream.kafka.messaging.KafkaSink;
import com.shuyun.kylin.customized.base.stream.kafka.messaging.KafkaSource;
import com.shuyun.kylin.customized.coupon.dto.CustomerCouponDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@EnableBinding({KafkaSource.class, KafkaSink.class})
@ConditionalOnExpression("${system.kafka.enabled:true}")
public class CouponProducer {
    @Autowired
    private KafkaSource kafkaSource;

    public void sendMsg(CustomerCouponDto customerAndCouponDto){
        log.info("【 发送到主动营销 Topic：COUPON_NODE_RECEIVE_RESULT  开始=======> 送出参数: {} 】",customerAndCouponDto);
        kafkaSource.output().send(MessageBuilder.withPayload(customerAndCouponDto).build());
        log.info("【 发送到主动营销 Topic：COUPON_NODE_RECEIVE_RESULT 结束=======> 送出参数: {} 】", customerAndCouponDto);
    }
}
