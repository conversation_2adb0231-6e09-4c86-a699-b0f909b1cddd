package com.shuyun.kylin.customized.member.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.ZonedDateTime;
import java.util.Map;

@Data
public class CepMemberSubscriptionDto {

    @NotBlank(message = "客户编号不能为空，格式：appid_openid")
    private String customerNo;
    private String memberId;
    @NotBlank(message = "订阅流水id不能为空")
    private String subscribeId;
    @NotBlank(message = "订阅场景类型不能为空，枚举PLATFORM：平台通用CAMPAIGN：活动")
    private String type;
    @NotBlank(message = "订阅场景编号不能为空")
    private String campaignCode;
    private String koId;
    @NotBlank(message = "模板不能为空")
    private String templateId;
    private String customerChannel;
    private String commenCode;
    private String customerPlatformNo;
//    @NotBlank(message = "创建时间不能为空")
    private String created;
    private String lastSync;
    private Boolean status;
    private Map member;
    private Map campaign;
    private Map template;
    private String channelType;
    private String traceId;
    private String description;
}
