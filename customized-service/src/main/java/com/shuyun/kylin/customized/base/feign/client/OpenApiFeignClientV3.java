package com.shuyun.kylin.customized.base.feign.client;

import com.shuyun.kylin.customized.base.feign.dao.*;
import com.shuyun.kylin.customized.project.request.OfferProjectRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(name = "openApiFeignClientV3")
@RequestMapping("openapi/v3")
public interface OpenApiFeignClientV3 {

    /**
     * 获取券项目列表
     * @param offerProjectRequest
     * @return
     */
    @PostMapping("/offer/project/list")
    CouponProjectResponseDto getProjectList(@RequestBody OfferProjectRequest offerProjectRequest);

    /**
     * 获取券项目详情
     * @param offerProjectDetailClientRequest
     * @return
     */
    @PostMapping("/offer/project/detail")
    OfferProjectDetailClientResponse getProjectDetail(@RequestBody OfferProjectDetailClientRequest offerProjectDetailClientRequest);

    /**
     * 获取券实例列表
     * @param offerProjectClientRequest
     * @return
     */
    @PostMapping("/offer/instance/list")
    CouponInstanceResponseDto getInstanceList(@RequestBody OfferProjectClientRequest offerProjectClientRequest);

    /**
     * 激活
     */
    @PostMapping("/offer/instance/activate")
    ClientCommonResponseDto<OfferInstanceActivateClientResponseDto> instanceActivate(OfferInstanceActivateClientRequestDto offerInstanceActivateClientRequestDto);

    /**
     * 启用（激活）
     */
    @PostMapping("/offer/instance/effective")
    ClientCommonResponseDto<OfferInstanceEffectiveClientResponseDto> instanceEffective(OfferInstanceEffectiveClientRequestDto offerInstanceEffectiveClientRequestDto);

    /**
     * 核销
     */
    @PostMapping("/offer/instance/use")
    ClientCommonResponseDto<OfferInstanceUseClientResponseDto> instanceUse(OfferInstanceUseClientRequestDto offerInstanceUseClientRequestDto);

    /**
     * 核销
     */
    @PostMapping("/offer/instance/cancel_use")
    ClientCommonResponseDto<OfferInstanceCancelUseClientResponseDto> instanceCancelUse(OfferInstanceCancelUseClientRequestDto cancelUseClientRequestDto);

    /**
     * 批量发放-单人发放多张券 = 发放
     */
    @PostMapping("/offer/instance/batch_grant")
    ClientCommonResponseDto<OfferInstanceGrantClientResponseDto> instanceBatchGrant(OfferInstanceGrantClientRequestDto offerInstanceGrantClientRequestDto);
}