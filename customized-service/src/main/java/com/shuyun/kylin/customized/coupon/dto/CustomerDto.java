package com.shuyun.kylin.customized.coupon.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerDto implements Serializable {
    private  String  customerNo;
    private  String  coupon;
    private  String  status;

    @Override
    public boolean equals(Object o){
        if(o instanceof CustomerDto) {
            CustomerDto that = (CustomerDto) o;
            return this.customerNo.equalsIgnoreCase(that.customerNo);
        }
        return false;
    }

    @Override
    public int hashCode(){
        return this.customerNo.hashCode();
    }
}
