package com.shuyun.kylin.customized.wechat.service.impl;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.wechat.request.LwWechatCustomerRemove;
import com.shuyun.kylin.customized.wechat.request.LwWechatSubscribeRequest;
import com.shuyun.kylin.customized.wechat.service.WechatService;
import com.shuyun.kylin.starter.redis.ICache;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class WechatServiceImpl implements WechatService {

    @Autowired
    @Qualifier("redisCache")
    private ICache redisCache;

    private static final OkHttpClient CLIENT = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .build();


    private static final String wechatLwUrl = ConfigurationCenterUtil.wechatLwUrl;


    @Override
    public String getWechatLwToken() {
        try {
            String url = String.format("%s/api/platform-tenant/authentication/client?clientId=%s&secret=%s", wechatLwUrl, ConfigurationCenterUtil.wechatLwClientId, ConfigurationCenterUtil.wechatLwSecret);
            log.info("getWechatLwToken...url:{}",url);
            String body = HttpUtil.createPost(url).execute().body();
            if (ObjectUtil.isNotEmpty(body)) {
                log.info("联蔚小程序token返回结果：{}", body);
                JSONObject jsonObject = JSON.parseObject(body, JSONObject.class);
                if (jsonObject.get("accessToken") != null) {
                    String accessToken = jsonObject.getString("accessToken");
                    redisCache.put("lw.wechat.token", accessToken, 3600, TimeUnit.SECONDS);
                    log.info("联蔚小程序token：{}", accessToken);
                    return  accessToken;
                }
            }
        } catch (Exception e) {
            log.error("获取联蔚小程序token异常，", e);
        }
        return null;
    }


    /**
     * 联蔚小程序订阅消息
     */
    @Override
    public JSONObject lwWechatSubscribeConsumer(String decode) {
        String accessToken = redisCache.get("lw.wechat.token");
        if (Objects.isNull(accessToken)) {
            accessToken = getWechatLwToken();
        }
        log.info("lwWechatSubscribeConsumer...accessToken:{}", accessToken);
        String url = String.format("%s/api/icoke-subscribe/%s/subscribe/subscribeTemplate/encode", wechatLwUrl, ConfigurationCenterUtil.wechatLwBuCode);
        log.info("lwWechatSubscribeConsumer...url:{}", url);
        RequestBody body = RequestBody.create(
                decode,
                MediaType.parse("text/plain; charset=utf-8")
        );

        Request request = new Request.Builder()
                .url(url)
                .header("Authorization", accessToken)
                .post(body)
                .build();

        try (Response response = CLIENT.newCall(request).execute()) {
            String resp = response.body() == null ? "" : response.body().string();
            log.info("联蔚返回:{}", JSON.toJSONString(resp));
            return JSONObject.parseObject(resp);
        } catch (IOException e) {
            log.error("发送订阅消息失败", e);
            return new JSONObject().fluentPut("code", "FAIL").fluentPut("message", e.getMessage());
        }
    }

    /**
     * 联蔚-注销会员
     * /api/icoke-customer/
     */
    @Override
    public JSONObject lwWechatWithdrawConsumer(LwWechatCustomerRemove lwWechatCustomerRemove) {
        String accessToken = redisCache.get("lw.wechat.token");
        if (Objects.isNull(accessToken)) {
            accessToken = getWechatLwToken();
        }
        log.info("联蔚-注销会员...accessToken:{}", accessToken);
        String url = String.format("%s/api/icoke-customer/%s/customer/remove/customerRemove", wechatLwUrl, ConfigurationCenterUtil.wechatLwBuCode);
        log.info("联蔚-注销会员...url:{}", url);
        String jsonString = JSON.toJSONString(lwWechatCustomerRemove);
        try {
            String body = HttpUtil.createPost(url).body(jsonString).header("Authorization", accessToken).execute().body();
            if (ObjectUtil.isNotEmpty(body)) {
                log.info("联蔚-注销会员...body：{}", body);
                return JSON.parseObject(body, JSONObject.class);
            }
        } catch (Exception e) {
            log.error("联蔚-注销会员异常，", e);
        }
        return null;
    }
}
