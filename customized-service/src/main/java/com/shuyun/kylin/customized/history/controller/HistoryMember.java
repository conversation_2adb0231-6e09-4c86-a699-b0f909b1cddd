package com.shuyun.kylin.customized.history.controller;


import com.shuyun.epassport.sdk.register.RequiresPermissions;
import com.shuyun.kylin.crm.openapi.core.dto.wechat.WechatRegisterRequest;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.history.service.HistoryMemberService;
import com.shuyun.pip.component.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@Validated
@RestController
@RequestMapping("/history/member")
@RequiresPermissions(allowAnonymous = true)
public class HistoryMember {

    @Autowired
    private HistoryMemberService historyMemberService;

    /**
     * 会员注册—— 历史会员
     * @param taskStart
     * @return
     */
    @GetMapping("/register")
    public void memberRegister( @RequestParam String taskStart) {
        log.info("历史会员注册任务启动...源数据表:{}", ConfigurationCenterUtil.HISTORY_MEMBER);
        historyMemberService.memberRegister();
    }
}
