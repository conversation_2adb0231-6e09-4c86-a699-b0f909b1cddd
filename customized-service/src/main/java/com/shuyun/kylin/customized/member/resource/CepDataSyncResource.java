package com.shuyun.kylin.customized.member.resource;

import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.member.dto.*;
import com.shuyun.kylin.customized.member.dto.ec.EcNormalOrderDto;
import com.shuyun.kylin.customized.member.dto.ec.EcProductDto;
import com.shuyun.kylin.customized.member.dto.ec.EcRefundOrderDto;
import com.shuyun.kylin.customized.member.service.ICepDataSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description cep数据同步
 * @date 2021/12/11
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/data/sync")
public class CepDataSyncResource {

    @Autowired
    ICepDataSyncService iCepDataSyncService;


    /**
     * 会员人券关系保存
     *
     * @param cepMemberCouponDto
     * @return
     */
    @PostMapping("/memeber/coupon")
    public ResponseResult saveMemberCoupon(@Validated @RequestBody CepMemberCouponDto cepMemberCouponDto) {
        log.info("会员人券关系保存请求入参: {}", cepMemberCouponDto);
        return iCepDataSyncService.memberCouponSave(cepMemberCouponDto);
    }

    /**
     * 优惠券主信息保存
     *
     * @param cepMemberProjectDto
     * @return
     */
    @PostMapping("/coupon/project")
    public ResponseResult saveMemberProject(@Validated @RequestBody CepMemberProjectDto cepMemberProjectDto) {
        log.info("优惠券主信息保存请求入参: {}", cepMemberProjectDto);
        return iCepDataSyncService.memberProjectSave(cepMemberProjectDto);
    }

    /**
     * 优惠券发放结果回执保存
     *
     * @param cepCouponCallbackDto
     * @return
     */
    @PostMapping("/coupon/callback")
    public ResponseResult saveCouponCallbackResults(@Validated @RequestBody CepCouponCallbackDto cepCouponCallbackDto) {
        log.info("优惠券发放结果回执保存请求入参: {}", cepCouponCallbackDto);
        return iCepDataSyncService.couponCallbackResults(cepCouponCallbackDto);
    }


    /**
     * 活动积分(非瓶子)信息保存
     *
     * @param cepMemberPointDto
     * @return
     */
    @PostMapping("/save/point")
    public ResponseResult saveMemberPoint(@Validated @RequestBody CepMemberPointDto cepMemberPointDto) {
        log.info("活动积分(非瓶子)信息保存请求入参: {}", cepMemberPointDto);
        return iCepDataSyncService.memberPointSave(cepMemberPointDto);
    }

    /**
     * 历史会员积分信息保存
     *
     * @param cepHistoryPointDto
     * @return
     */
    @PostMapping("/history")
    public ResponseResult saveMemberHistoryPoint(@Validated @RequestBody CepHistoryPointDto cepHistoryPointDto) {
        log.info("历史会员积分信息保存请求入参: {}", cepHistoryPointDto);
        return iCepDataSyncService.saveMemberHistoryPoint(cepHistoryPointDto);
    }


    /**
     * 小程序订阅模板主数据保存
     * <p>
     *
     *
     * @param cepAppletTemplateDto
     * @return
     */
    @PostMapping("/applet/save/template")
    public ResponseResult saveAppletTemplate(@Validated @RequestBody CepAppletTemplateDto cepAppletTemplateDto) {
        log.info("小程序订阅模板主数据保存: {}", cepAppletTemplateDto);
        return iCepDataSyncService.appletTemplateSave(cepAppletTemplateDto);
    }

    /**
     * 会员订阅消息关系数据保存
     * <p>
     *
     *
     * @param cepMemberSubscriptionDto
     * @return
     */
    @PostMapping("/save/subscription")
    public ResponseResult saveMemberSubscription(@Validated @RequestBody CepMemberSubscriptionDto cepMemberSubscriptionDto) {
        log.info("会员订阅消息关系数据保存: {}", cepMemberSubscriptionDto);
        return iCepDataSyncService.memberSubscriptionSave(cepMemberSubscriptionDto);
    }


    /**
     * 活动主数据保存
     *
     * @param cepCampaignSubjectDto
     * @return
     */
    @PostMapping("/campaign/subject")
    public ResponseResult saveCampaingSubject(@Validated @RequestBody CepCampaignSubjectDto cepCampaignSubjectDto) {
        log.info("活动主数据保存: {}", cepCampaignSubjectDto);
        return iCepDataSyncService.campaingSubjectSave(cepCampaignSubjectDto);
    }

    /**
     * 会员参与活动信息保存
     *
     * @param cepMemberCampaignsDto
     * @return
     */
    @PostMapping("/member/campaigns")
    public ResponseResult saveMemberCampaigns(@Validated @RequestBody CepMemberCampaignsDto cepMemberCampaignsDto) {
        log.info("会员参与活动信息保存: {}", cepMemberCampaignsDto);
        return iCepDataSyncService.memberCampaignsSave(cepMemberCampaignsDto);
    }

    /**
     * 积分兑换订单信息保存
     *
     * @param cepConversionPointDto
     * @return
     */
    @PostMapping("/conversion/order")
    public ResponseResult saveCounversionPoint(@Validated @RequestBody CepConversionPointDto cepConversionPointDto) {
        log.info("积分兑换订单信息保存: {}", cepConversionPointDto);
        return iCepDataSyncService.saveCounversionPoint(cepConversionPointDto);
    }

    /**
     * 店铺主数据保存
     *
     * @param ecSyncShopDto
     * @return
     */
    @PostMapping("/shop")
    public ResponseResult saveSyncShop(@Validated @RequestBody EcSyncShopDto ecSyncShopDto) {
        log.info("店铺主数据保存: {}", ecSyncShopDto);
        return iCepDataSyncService.saveSyncShop(ecSyncShopDto);
    }

    /**
     * 新增/修改商品主数据
     *
     * @param EcProductDto
     * @return
     */
    @PostMapping("/product")
    public ResponseResult updateSyncProduct(@Validated @RequestBody EcProductDto EcProductDto) {
        log.info("新增/修改商品主数据: {}", EcProductDto);
        return iCepDataSyncService.updateSyncProduct(EcProductDto);
    }

    /**
     * 互动主数据保存
     * @param
     * @return
     */
    @PostMapping("/interactive")
    public ResponseResult saveInteractiveDto(@Validated @RequestBody InteractiveDto interactiveDto){
        log.info("互动主数据保存请求入参：{}", interactiveDto);
        return iCepDataSyncService.saveInteractive(interactiveDto);
    }
    /**
     * 互动行为记录保存
     * @param
     * @return
     */
    @PostMapping("/interactiveRecords")
    public ResponseResult saveInteractiveRecords(@Validated @RequestBody InteractiveRecordsDto interactiveRecordsDto){
        log.info("会员互动行为记录保存请求入参：{}", interactiveRecordsDto);
        return iCepDataSyncService.saveInteractiveRecords(interactiveRecordsDto);
    }
    /**
     * 新增/修改订单 (oms+ec商城)
     *
     * @param ecNormalOrderDto
     * @return
     */
    @PostMapping("/order")
    public ResponseResult updateSyncOrder(@Validated @RequestBody EcNormalOrderDto ecNormalOrderDto) {
        log.info("新增/修改订单数据: {}", ecNormalOrderDto);
        return iCepDataSyncService.updateSyncOrder(ecNormalOrderDto);
    }

    /**
     * 新增/修改退单 (oms+ec商城)
     *
     * @param ecRefundOrderDto
     * @return
     */
    @PostMapping("/refundOrder")
    public ResponseResult updateSyncRefundOrder(@Validated @RequestBody EcRefundOrderDto ecRefundOrderDto) {
        log.info("新增/修改退单数据: {}", ecRefundOrderDto);
        return iCepDataSyncService.updateSyncRefundOrder(ecRefundOrderDto);
    }


    /**
     * KPI值保存
     *
     * @param biKpiDto
     * @return
     */
    @PostMapping("/save/kpi")
    public ResponseResult saveBiKpi(@Validated @RequestBody BiKpiDto biKpiDto) {
        log.info("KPI值保存入参: {}", biKpiDto);
        return iCepDataSyncService.saveBikpi(biKpiDto);
    }


    /**
     * 授权地理位置轨迹记录保存
     *
     * @param memberLbsTrajectoryDto
     * @return
     */
    @PostMapping("/position")
    public ResponseResult savePosition(@Validated @RequestBody MemberLbsTrajectoryDto memberLbsTrajectoryDto) {
        log.info("授权地理位置轨迹记录保存入参: {}", memberLbsTrajectoryDto);
        String type = "LOGIN";
        return iCepDataSyncService.savePosition(memberLbsTrajectoryDto,type);
    }

}
