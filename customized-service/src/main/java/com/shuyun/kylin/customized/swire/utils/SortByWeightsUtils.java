package com.shuyun.kylin.customized.swire.utils;

import java.util.Arrays;
import java.util.HashMap;

/**
 * 英文字符串排序
 */
public class SortByWeightsUtils {
    /**
     * for 权重排序
     */
    public static String[] sortByWeights(String[] disorder) {
        double[] weightsArr = new double[disorder.length];
        HashMap recordMap = new HashMap<>();
        // 计算每个字符串的权重值
        for (int i = 0; i < weightsArr.length; i++) {
            weightsArr[i] = getStrWeights(disorder[i], 0);
            recordMap.put(weightsArr[i], i);//保存权重值对应的字符串所在数组位置
        }
        // 将获取的权重值排序
        Arrays.sort(weightsArr);
        String[] sorderStr = new String[disorder.length];
        int orderIndex = 0;
        for (int i = 0; i < sorderStr.length; i++) {
            //拿出排好序的索引
            orderIndex = (int) recordMap.get(weightsArr[i]);
            sorderStr[i] = disorder[orderIndex];
        }
        return sorderStr;
    }

    // 权重数值
    private final static double weightsNum = 53;

    /**
     * for 计算字符串的权重值算法
     *
     * @param deep
     * @param str
     * @return weights
     */
    public static double getStrWeights(String str, int deep) {
        int c = str.charAt(deep);
        boolean isUppercase = true;
        if (c >= 97) {
            c = c - 32;
            isUppercase = false;
        }
        c = c - 64; // A,a -> 1; B,b -> 2
        int molecular = c * 2;
        if (isUppercase) molecular--; //if(B) -> 3;if(b) -> 4; for end [Baby,baby]
// 这里考虑了大小写 如果字母是B 权重=3/53 小写b 权重=4/53 使小大小写也能排序
        double weights = molecular / (Math.pow(weightsNum, deep));
        return ++deep >= str.length() ? weights : weights + getStrWeights(str, deep); //递归到字符串末尾

    }

}
