package com.shuyun.kylin.customized.standard.resource;


import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.member.dto.CepMemberDeductionDto;
import com.shuyun.kylin.customized.standard.service.PointRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@RestController
@RequestMapping("/rule")
public class pointRuleResource {


    @Autowired
    private PointRuleService pointRuleService;

    /**
     * V2规则场景变更会员积分(瓶子)
     *
     * @param cepMemberDeductionDto
     * @return
     */
    @PostMapping("/point")
    public ResponseResult updateMemberPoint(@Validated @RequestBody CepMemberDeductionDto cepMemberDeductionDto) {
        log.info("V2规则场景变更会员积分请求入参: {}", cepMemberDeductionDto);
        return pointRuleService.updateRuleMemberPoint(cepMemberDeductionDto);
    }
}
