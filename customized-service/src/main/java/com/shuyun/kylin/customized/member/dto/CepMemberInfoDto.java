package com.shuyun.kylin.customized.member.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@ApiModel("会员基础信息")
public class CepMemberInfoDto {

    private String memberId;
    private String gender;
    private String birthDay;
    private String birthYear;
    private String registerTime;
    private String mobile;
    private String age;
    private String memberName;
    private String email;
    private String job;
    private Double pointsRequired;
    private String nickName;
    private String headImgUrl;
    private Double thisMonthExpiredPoint;
    private Double nextMonthExpiredPoint;
    private String isAgreeTerms;
    private String cityName;
    private String provinceName;
    private String districtName;
    private String income;
    private String agreementVersion;
    //联合会员渠道信息
    private List<ExternalMembership> externalMembership;
    @Data
    public static class ExternalMembership {
        private String channelType;
        private String channelName;
    }
}
