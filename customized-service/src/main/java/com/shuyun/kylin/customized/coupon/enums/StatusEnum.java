package com.shuyun.kylin.customized.coupon.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum StatusEnum {
    SUBMIT_SUCCESS("提交成功","SUBMIT_SUCCESS"),
    SUBMIT_FAILED("提交失败", "SUBMIT_FAILED"),
    GRANT_SUCCESS("发放成功", "GRANT_SUCCESS"),
    GRANT_FAILED("发放失败", "GRANT_FAILED");

    private String name;
    private String value;
    public static StatusEnum getEnumByValue(String value) {
        if (values().length > 0 && null != value) {
            for (StatusEnum en : values()) {
                if (en.value.equals(value)) {
                    return en;
                }
            }
        }

        return null;
    }

    public static StatusEnum getEnumByName(String name) {
        if (values().length > 0 && null != name) {
            for (StatusEnum en : values()) {
                if (en.name.equals(name)) {
                    return en;
                }
            }
        }

        return null;
    }

    public static String getValueByName(String name) {
        if (values().length > 0 && null != name) {
            for (StatusEnum en : values()) {
                if (en.name.equals(name)) {
                    return en.value;
                }
            }
        }
        return null;
    }
}
