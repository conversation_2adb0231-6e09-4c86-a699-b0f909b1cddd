package com.shuyun.kylin.customized.customer.resource;

import com.shuyun.kylin.crm.openapi.core.dto.wechat.WechatRegisterRequest;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.customer.dto.CustomerTagDto;
import com.shuyun.kylin.customized.customer.service.CustomerService;
import com.shuyun.pip.component.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/2/28 14:18
 * @description 消费者业务接口
 */
@Slf4j
@RestController
@RequestMapping("/customer")
public class CustomerResource {
    @Autowired
    private CustomerService customerService;
    /**
     * 校验用户所打标签
     * @param request
     * @return
     */
    @PostMapping("/tag")
    public ResponseResult customerTag(@Validated @RequestBody CustomerTagDto request) {
        log.info("校验用户所打标签入参:{}", JsonUtils.toJson(request));
        return customerService.customerTag(request);
    }
}
