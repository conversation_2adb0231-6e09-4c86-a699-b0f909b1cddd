package com.shuyun.kylin.customized.rpc.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

@Data
public class CostaSendCouponResponseDto {
    private Integer status;

    private String message;

    private RspData data;

    @Data
    public static class RspData {
        @JSONField(name = "member_card_no")
        private String memberCardNo;
        @JSONField(name = "full_name")
        private String fullName;
        @JSONField(name = "nick_name")
        private String nickName;
        @JSONField(name = "source")
        private Integer source;
        @JSONField(name = "grade")
        private String grade;
        @JSONField(name = "grade_invalid_time")
        private Date gradeInvalidTime;
        @JSONField(name = "coupon_code")
        private String couponCode;
    }
}
