package com.shuyun.kylin.customized.project.enums;

/**
 * 卡券处理结果状态(成功:SUCCESS)(待处理:WAITING)(处理中:HANDLING)(失败:FAIL)(重复:DUPLICATED)
 */
public enum CouponUseStatusEnum {
    SUCCESS("SUCCESS", "成功"),
    WAITING("WAITING", "待处理"),
    HANDLING("HANDLING", "处理中"),
    FAIL("FAIL", "失败"),
    DUPLICATED("DUPLICATED", "重复");

    private String code;
    private String desc;

    CouponUseStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
