package com.shuyun.kylin.customized.behavior.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum RewardsScopeEnum {

    ALL("ALL_CHANNEL", "全渠道"),

    SINGLE("SINGLE_CHANNEL", "单渠道");

    String str;

    String desc;

    public static boolean isAllChannel(String s) { return StringUtils.equals(RewardsScopeEnum.ALL.getStr(), s); }

}
