package com.shuyun.kylin.customized.project.dto;

import lombok.Data;

/**
 * @author: Jingwei
 * @date: 2024-12-13
 * @description: 目前仅通知核销的券，即status=2
 */
@Data
public class ZerXCallbackDto {

    /**
     * 券码，领券时返回的coupon参数
     * <p>
     * 示例值：1234567890123456789
     * 说明：这个券码是三方的券码，不是kylin的券码
     * </p>
     */
    private String barcode;
    /**
     * 2-已使⽤
     */
    private Integer status;
    /**
     * 核销时间 格式：yyyy-MM-dd HH:mm:ss
     */
    private String useDate;
    /**
     * 13位时间戳
     */
    private String timestamp;
    /**
     * 签名
     */
    private String sign;
}
