package com.shuyun.kylin.customized.base.stream.kafka.producer.coupon;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.shuyun.kylin.customized.base.stream.kafka.messaging.KafkaSink;
import com.shuyun.kylin.customized.base.stream.kafka.messaging.KafkaSource;
import com.shuyun.kylin.customized.swire.dto.SwireMemberDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.stereotype.Component;


@Slf4j
@Component
@EnableBinding({KafkaSource.class, KafkaSink.class})
@ConditionalOnExpression("${system.kafka.enabled:true}")
public class DataSyncMember {

    @Autowired
    private KafkaSource kafkaSource;

/*    public void sendMemberData(SwireMemberDto swireMemberDto) {
        log.info("【 ko注册会员同步太古 Topic： KAFKA_SEND_SWIRE_MEMBER_OUTPUT  开始");
        kafkaSource.swireMemberOutput().send(MessageBuilder.withPayload(swireMemberDto).build());
        log.info("【 ko注册会员同步太古 Topic：KAFKA_SEND_SWIRE_MEMBER_OUTPUT 结束=======> 送出参数: {} 】", JSON.toJSONString(swireMemberDto , SerializerFeature.WriteMapNullValue));
    }

    public void updateMemberData(SwireMemberDto swireMemberDto) {
        log.info("【 ko更新会员同步太古 Topic： KAFKA_UPDATE_SWIRE_MEMBER_OUTPUT  开始");
        kafkaSource.swireUpdateMemberOutput().send(MessageBuilder.withPayload(swireMemberDto).build());
        log.info("【 ko更新会员同步太古 Topic：KAFKA_UPDATE_SWIRE_MEMBER_OUTPUT 结束=======> 送出参数: {} 】", JSON.toJSONString(swireMemberDto , SerializerFeature.WriteMapNullValue));
    }*/

}
