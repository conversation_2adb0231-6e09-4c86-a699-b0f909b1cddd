package com.shuyun.kylin.customized.behavior.service.impl;

import com.google.common.base.Joiner;
import com.shuyun.kylin.customized.behavior.enums.InteractiveChannelTypeEnum;
import com.shuyun.kylin.customized.behavior.domain.Interactive;
import com.shuyun.kylin.customized.behavior.domain.InteractiveRecord;
import com.shuyun.kylin.customized.behavior.resource.po.CheckPO;
import com.shuyun.kylin.customized.behavior.util.DateUtils;
import com.shuyun.kylin.customized.behavior.util.StringUtils;
import com.shuyun.kylin.customized.member.dto.CepMemberInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 邀请注册
 */
@Slf4j
@Service
public class InviteRegisterResolver extends AbstractInteractionSceneResolver {

    @Override
    public CheckPO.SceneType supportScene() {
        return CheckPO.SceneType.INVITE_REGISTER;
    }

    @Override
    protected RewardInfo fetchPointInfo(CheckPO checkPO, String memberId, String mobile, Interactive interactive, InteractiveRecord lastRecord) {
        /*
         * 邀请注册规则
         * 1. 每月可以获取固定次的奖励
         * 2. 当天就可以获取完
         * 3. 判断邀请者
         *  - 他是会员
         *  - 他的手机号没被邀请过
         *  - 他的会员号没被邀请过
         */

        // 判断邀请者
        // - 他是会员
        // - 他的手机号没被邀请过
        // - 他的会员号没被邀请过
        String invitee = checkPO.getInvitee();

        //保存被邀请者的属性
        InteractiveRecord.CONSUMER_THREAD_LOCAL.set(interactiveRecord -> {
            interactiveRecord.setInvitee(invitee);
        });

        if (StringUtils.isNullOrEmpty(invitee)) {
            return RewardInfo.refuse("被邀请者参数为空");
        }

        //原代码就是默认用可乐互动渠道类型查询
        CepMemberInfoDto cepMemberDto = queryMemberByNoAndChannelType(invitee, InteractiveChannelTypeEnum.COCA_COLA.getStr());
        log.info("邀请者信息：{}", cepMemberDto);
        if (cepMemberDto == null) {
            return RewardInfo.refuse("被邀请者为非会员");
        }

        String inviteeMobile = cepMemberDto.getMobile();
        String inviteeMemberId = cepMemberDto.getMemberId();

        //保存被邀请者的属性
        InteractiveRecord.CONSUMER_THREAD_LOCAL.set(interactiveRecord -> {
            interactiveRecord.setInvitee(invitee);
            interactiveRecord.setInviteeMobile(inviteeMobile);
            interactiveRecord.setInviteeMember(inviteeMemberId);
        });

        if (Objects.equals(inviteeMemberId, memberId)) {
            return RewardInfo.refuse("被邀请人不能是自己");
        }

        Interactive.Invite invite = interactive.fetchInvite();
        //限制天数
        Integer inviteLimit = invite.getInviteLimit();
        //发放的积分
        Integer pointNum = invite.getPointNum();

        //查询当月已发放多少次奖励
        Date today = new Date();
        String where = Joiner.on(" And ").join(
                InteractiveRecord.Fields.memberIDStr.eq(memberId),
                InteractiveRecord.Fields.interactiveScene.eq(supportScene().name()),
                InteractiveRecord.Fields.joinTime.ge(DateUtils.GMT8.toGMT0(DateUtils.getMonthStart(today))),
                InteractiveRecord.Fields.joinTime.le(DateUtils.GMT8.toGMT0(DateUtils.getMonthEnd(today))),
                InteractiveRecord.Fields.isGetRewards.eq(true)
        );
        String query = InteractiveRecord.genALlFieldQuery(where);
        List<InteractiveRecord> interactiveRecords = dmService.queryForList(query, InteractiveRecord.class, "查询当月获取的邀请奖励数");
        log.info("当月已领取 {} 次邀请奖励了，当前设置每月最多可以领取：{} 次", interactiveRecords.size(), inviteLimit);
        if (interactiveRecords.size() >= inviteLimit) {
            log.info("已领取完当月的所有邀请奖励。");
            return RewardInfo.refuse("已领取完当月的所有邀请奖励");
        }

        //手机号 或者 会员号，没有被邀请过
        where = "Where " + Joiner.on(" And ").join(
                InteractiveRecord.Fields.isGetRewards.eq(true),
                InteractiveRecord.Fields.interactiveScene.eq(interactive.getScene())
        ) +
                " And (inviteeMobile='" + inviteeMobile + "' Or invitee = '" + invitee + "')";

        query = InteractiveRecord.genALlFieldQuery(where);
        interactiveRecords = dmService.queryForList(query, InteractiveRecord.class, "查询手机或者会员号是否已领取过推荐奖励");
        if (interactiveRecords.size() > 0) {
            log.info("当前推荐用户已领取过推荐奖励，用户:{}", invitee + "-" + inviteeMobile);
            return RewardInfo.refuse("当前推荐用户已被邀请过了");
        }

        return RewardInfo.point(pointNum);
    }
}
