package com.shuyun.kylin.customized.member.dm;


import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.base.enums.ModelNameEnum;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.member.dto.CepPointRecordDto;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class CepPointRecordReposiyory extends BaseDsRepository<CepPointRecordDto> {

    /**
     * 重新是否第一次变更
     * @param memberId
     * @param businessId
     * @return
     */
    public CepPointRecordDto getPointRelation(String memberId,String businessId){
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        queryMap.put("traceId", businessId);
        return queryByFilter(ConfigurationCenterUtil.MEMBER_POINTRECORD, null, queryMap);
    }
}
