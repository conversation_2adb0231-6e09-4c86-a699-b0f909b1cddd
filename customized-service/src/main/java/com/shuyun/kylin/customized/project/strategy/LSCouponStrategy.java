package com.shuyun.kylin.customized.project.strategy;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.feign.dao.MemberBingDto;
import com.shuyun.kylin.customized.base.feign.dao.OfferProjectDetailClientResponse;
import com.shuyun.kylin.customized.project.dto.SendCouponResultDto;
import com.shuyun.kylin.customized.project.request.GrantCouponRequest;
import com.shuyun.kylin.customized.rpc.dto.BKResponseDto;
import com.shuyun.kylin.customized.rpc.dto.LSResponseDto;
import com.shuyun.kylin.customized.rpc.template.BKRequestTemplate;
import com.shuyun.kylin.customized.rpc.template.LSRequestTemplate;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Objects;
import java.util.Optional;

/**
 * 罗森发券
 */
@Slf4j
public class LSCouponStrategy extends SendCouponStrategy{

    private static final String LS_CODE = "00000";

    @Override
    public SendCouponResultDto sendCoupon(String templateId, OfferProjectDetailClientResponse projectDetail, GrantCouponRequest grantCouponRequest, MemberBingDto memberBindInfo) {
        log.info("罗森券发放开始transactionId:{}",grantCouponRequest.getTransactionId());
        // 罗森券发放开始
        LSResponseDto lsResponseDto = null;
        try {
            lsResponseDto = LSRequestTemplate.sendCoupon(grantCouponRequest,projectDetail, memberBindInfo);
        } catch (IOException e) {
            log.error("罗森发券失败transactionId:{},grantCouponRequest={}",grantCouponRequest.getTransactionId(), JSON.toJSONString(grantCouponRequest));
            return new SendCouponResultDto(Boolean.FALSE, null, null,null);
        }
        if(Objects.isNull(lsResponseDto)) {
            log.error("罗森发券失败transactionId:{},grantCouponRequest={}",grantCouponRequest.getTransactionId(), JSON.toJSONString(grantCouponRequest));
            return new SendCouponResultDto(Boolean.FALSE, null, null,null);
        }
        if (lsResponseDto.getCode().equals(LS_CODE)){
            String lsCouponCode = lsResponseDto.getData().getCode();
            log.info("罗森券发放成功transactionId:{}，ticketCode={}",grantCouponRequest.getTransactionId(), lsCouponCode);
            return new SendCouponResultDto(Boolean.TRUE, null, lsCouponCode,null);
        }else {
            log.error("罗森发券失败transactionId:{},grantCouponRequest={}",grantCouponRequest.getTransactionId(), JSON.toJSONString(grantCouponRequest));
            return new SendCouponResultDto(Boolean.FALSE, null, null,null);
        }
    }
}
