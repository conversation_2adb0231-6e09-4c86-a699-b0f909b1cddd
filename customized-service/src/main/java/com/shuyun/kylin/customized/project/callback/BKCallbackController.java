package com.shuyun.kylin.customized.project.callback;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.project.response.BKInstanceUseOrUnUseRequest;
import com.shuyun.kylin.customized.project.service.SendCouponService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/BK")
public class BKCallbackController {

    @Resource
    private SendCouponService sendCouponService;
    /**
     * 汉堡王回调
     * 根据不同的回调事件类型区分核销和反核销   VERIFY:核销事件 ；  REVOKE:撤销核销事件
     * https://doc.verystar.net/share/oBAvINLD?id=10034
     */
    @PostMapping("/sendCouponCallback")
    public String  sendCouponCallback(@RequestBody BKInstanceUseOrUnUseRequest request) {
        log.info("汉堡王回调，入参requestDto={}", JSON.toJSONString(request));
        String str = sendCouponService.bKCallback(request);
        log.info("汉堡王回调，返回结果r={}", str);
        return str;
    }
}
