package com.shuyun.kylin.customized.medal.resource;


import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.medal.request.CampaignRecordRequest;
import com.shuyun.kylin.customized.medal.request.MedalObtainRequest;
import com.shuyun.kylin.customized.medal.response.*;
import com.shuyun.kylin.customized.medal.service.MedalService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.List;

/**
 * 勋章相关
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/medal")
public class MedalResource {

    @Autowired
    private MedalService medalService;

    /**
     * 2.47.参与活动记录保存
     */
    @PostMapping("/campaign/record")
    public ResponseResult<String> campaignRecord(@Validated @RequestBody CampaignRecordRequest campaignRecord) {
        log.info("medal...campaignRecord:{}", JSON.toJSONString(campaignRecord));
        return medalService.campaignRecord(campaignRecord);
    }

    /**
     * 2.48.查询勋章主数据列表
     */
    @GetMapping("")
    public  ResponseResult<List<MedalListResponse>> medal() {
       return medalService.medal();
    }


    /**
     * 2.49.查询会员已获取勋章列表
     */
    @GetMapping("/record")
    public ResponseResult<List<MemberMedalListResponse>> getMemberMadelList(@RequestParam("memberId") String memberId) {
        log.info("medal...getMemberMadelList...memberId:{}", memberId);
        return medalService.getMemberMadelList(memberId);
    }


    /**
     * 2.50.查询当前任务进度
     */
    @GetMapping("/progress")
    public ResponseResult<List<MedalProgressResponse>> progress(@RequestParam("memberId") String memberId) {
        log.info("medal...progress...memberId:{}", memberId);
        return medalService.progress(memberId);
    }


    /**
     * 2.51.查询勋章详情
     */
    @GetMapping("/details")
    public ResponseResult<MedalDetailsResponse> getMedalDetails(@RequestParam("medalDefinitionId") Integer medalDefinitionId ,@RequestParam("memberId") String memberId) throws ParseException {
        log.info("medal...getMedalDetails...medalDefinitionId:{} , memberId:{}", medalDefinitionId,memberId);
        return medalService.getMedalDetails(medalDefinitionId,memberId);
    }

    /**
     * 2.52.查询勋章等级
     */
    @GetMapping("/grade")
    public ResponseResult<MedalGradeResponse> grade(@RequestParam("memberId") String memberId) {
        log.info("medal...grade...memberId:{}", memberId);
        return medalService.grade(memberId);
    }

    /**
     * 2.53.勋章发放
     */
    @PostMapping("/obtain")
    public ResponseResult<String> obtain(@RequestBody MedalObtainRequest medalObtainRequest) {
        log.info("medal...obtain...medalObtainRequest:{}", JSON.toJSONString(medalObtainRequest));
        return medalService.obtain(medalObtainRequest);
    }
}
