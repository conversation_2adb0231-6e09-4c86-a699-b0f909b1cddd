package com.shuyun.kylin.customized.history.controller;


import com.shuyun.epassport.sdk.register.RequiresPermissions;
import com.shuyun.kylin.crm.openapi.core.dto.wechat.WechatRegisterRequest;
import com.shuyun.kylin.customized.base.feign.dao.LoyaltyFacadeDto;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.history.dao.LoyaltyPointDto;
import com.shuyun.kylin.customized.history.service.HistoryMemberPointService;
import com.shuyun.kylin.customized.history.service.HistoryMemberService;
import com.shuyun.pip.component.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/history/point")
@RequiresPermissions(allowAnonymous = true)
public class HistoryMemberPoint {

    @Autowired
    private HistoryMemberPointService historyMemberPointService;

    /**
     * 新增修改积分
     *
     * @param changeType
     * @return
     */
    @GetMapping("/send")
    public void memberSendPoint(@RequestParam String changeType) {
        log.info("历史积分清洗任务任务启动...changeType:{},源数据表:{}",changeType, ConfigurationCenterUtil.HISTORY_UPDATE_POINTLOG);
        historyMemberPointService.memberSendPoint();
    }

}
