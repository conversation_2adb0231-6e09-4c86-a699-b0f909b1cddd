package com.shuyun.kylin.customized.base.job;


import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.stream.kafka.dao.CampaignActionOutputDto;
import com.shuyun.kylin.customized.base.util.DateHelper;
import com.shuyun.kylin.customized.member.dm.CouponExpireRemindRecordRepository;
import com.shuyun.kylin.customized.member.dto.CouponExpireRemindRecordDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
@EnableScheduling
public class CepCouponSchedulng {

    @Autowired
    private CouponExpireRemindRecordRepository couponExpireRemindRecordRepository;

    //@Scheduled(cron = "0 */2 * * * ?")
    //@Scheduled(cron = "0 0 1 * * ?")
    public void getCoupon(){

        //清除昨天记录
        couponExpireRemindRecordRepository.deleteCouponById();

       //查询符合规则的优惠券  （已领取  +  到期时间+5天）
        List<CouponExpireRemindRecordDto> couponExpireRemind = couponExpireRemindRecordRepository.getCouponExpireRemind();

        //遍历插入
        List<CouponExpireRemindRecordDto> couponDtoList = JSON.parseArray(JSON.toJSONString(couponExpireRemind), CouponExpireRemindRecordDto.class);

        for (CouponExpireRemindRecordDto coupon : couponDtoList) {
            String projectName = couponExpireRemindRecordRepository.getCouponName(coupon.getProjectId());
            CouponExpireRemindRecordDto recordDto = new CouponExpireRemindRecordDto();
            recordDto.setProjectId(coupon.getProjectId());
            recordDto.setMemberId(coupon.getMemberId());
            recordDto.setExpiredDate(DateHelper.getZone(getStartTimeDate(coupon.getExpiredDate())));
            recordDto.setCouponCode(coupon.getCouponCode());
            recordDto.setCouponName(projectName);
            couponExpireRemindRecordRepository.insert(ModelConstants.MEMBER_COUPONREMIND,recordDto);
        }
    }

    public static String getStartTimeDate(String time){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeStrart = time.substring(0,19).replace("T"," ");
        String timeDate = "";
        try {
            Date dt=sdf.parse(timeStrart);
            Calendar rightNow = Calendar.getInstance();
            rightNow.setTime(dt);
            rightNow.add(Calendar.HOUR,8);
            Date nowTime = rightNow.getTime();
            timeDate = sdf.format(nowTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return timeDate;
    }
}
