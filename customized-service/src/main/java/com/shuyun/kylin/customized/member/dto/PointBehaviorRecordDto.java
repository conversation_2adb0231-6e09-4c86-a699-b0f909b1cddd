package com.shuyun.kylin.customized.member.dto;

import lombok.Data;

@Data
public class PointBehaviorRecordDto {

    /*private Double point;
    private String modified;
    private String recordType;
    private String channel;
    private String KZZD2;
    private String KZZD1;*/
    private Double point;
    private Double invalidPoint;
    private Double frozenPoint;
    private String changeTime;
    private String changeType;
    private String channelType;
    private String changeSource;
    private String changeSourceDetail;
    private String desc;
    private String actionId;
    private String actionNodeId;
    private String changeMode;
    private String id;
    private String shopId;
    private String costCenter;
    private String modified;
    private String displayDesc;
    private String displayPoint;

}
