package com.shuyun.kylin.customized.member.dto.ec;

import lombok.Data;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class EcRefundOrderDto {
    @NotBlank(message = "退单主订单ID不能为空")
    private String orderId;
    @NotBlank(message = "原始主订单ID不能为空")
    private String originOrderId;
    @NotBlank(message = "成本中心标识不能为空")
    private String costCenterMark;
    @NotBlank(message = "店铺编号不能为空")
    /**
     * 需和‘EC店铺主数据‘中shopCode对应
     */
    private String shopCode;
    @NotBlank(message = "店铺名称不能为空")
    private String shopName;
    @Digits(message = "数量过大不能超过9000000",integer = 8,fraction = 0)
    @Max(message = "数量过大不能超过9000000",value = 9000000)
    @NotNull(message = "数量不能为空")
    private Integer totalQuantity;
    @NotBlank(message = "订单状态不能为空")
    //REFUND_START: 退款开始
    //REFUNDING: 退款中
    //REFUND_FINISHED: 退款完成
    //REFUND_CANCEL: 退款取消
    //SELLING_REFUND_FINISHED: 售中退单完成
    //REFUND_REFUSED：商家拒绝退款
    //REFUND_FAILED：退款失败
    private String orderStatus;
    @NotBlank(message = "渠道类型不能为空")
    /**
     * TAOBAO：天猫
     * KO_MP：KO小程序
     * EC_SHOPPING：EC购物商城
     * EC_POINT：EC积分商城
     */
    private String channelType;
    @NotBlank(message = "会员类型不能为空,固定值KO")
    private String memberType;
    @NotBlank(message = "客户编号不能为空")
    private String customerNo;


    //订单退货时间 格式: yyyy-MM-dd HH:mm:ss
    private String refundTime;
    //订单收货时间 格式: yyyy-MM-dd HH:mm:ss
    private String finishTime;

    private String updateTime;

    //退款金额
    private Double refundFee;
    //运费
    private Double freight;
    //收货人姓名
    private String receiverName;
    //收货人手机号
    private String receiverMobile;
    //收货人固定电话
    private String receiverTelephone;
    //省
    private String receiverProvince;
    //市
    private String receiverCity;
    //区/县
    private String receiverDistrict;
    //收货人地址
    private String receiverAddress;
    //邮编
    private String receiverZipCode;
    //订单备注
    private String description;
    //会员id
    private String memberId;
    @NotNull(message = "退单子订单不能为空")
    private List<EcRefundOrderItemDto> refundOrderItems;
    //扩展字段
    private  EcCustomizedPropertiesDto customizedProperties;

}
