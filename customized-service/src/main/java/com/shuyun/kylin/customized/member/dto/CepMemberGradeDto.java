package com.shuyun.kylin.customized.member.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jetbrains.annotations.Nullable;
import java.util.Date;

@Data
@ApiModel("等级信息")
public class CepMemberGradeDto {
    //等级id
    private String id;
    //等级名称
    private String name;
    //等级生效时间
    private String effectiveTime;
    //等级过期时间
    private String expiredTime;


}
