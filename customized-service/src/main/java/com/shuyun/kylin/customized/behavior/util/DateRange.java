package com.shuyun.kylin.customized.behavior.util;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.text.ParseException;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@Setter
@Getter
@NoArgsConstructor
public class DateRange {
    private Date start;
    private Date end;

    public DateRange(Object start, Object end) {
        this.start = DateUtils.GMT0.toGMT8Date(start);
        this.end = DateUtils.GMT0.toGMT8Date(end);
    }

    public static DateRange buildOf(Date start, Date end) {
        DateRange dateRange = new DateRange();
        dateRange.setStart(start);
        dateRange.setEnd(end);
        return dateRange;
    }

    /**
     * 正常日期范围的定义： start 必须早于 end
     */
    public static boolean isValid(Date start, Date end) {
        return start != end && end != null && start.before(end);
    }

    public boolean isConflictRange(DateRange dateRange) {
        Set<String> dates = new HashSet<>();
        dates.add(DateUtils.formatDateTime(this.getStart()));
        dates.add(DateUtils.formatDateTime(this.getEnd()));
        dates.add(DateUtils.formatDateTime(dateRange.getStart()));
        dates.add(DateUtils.formatDateTime(dateRange.getEnd()));

        //有相同时间
        if (dates.size() != 4) {
            return true;
        }

        //比较4次，任一一次成立，即冲突
        return dateRange.inRange(this) || this.inRange(dateRange);
    }

    private boolean inRange(DateRange dateRange) {
        long startTime = this.start.getTime();
        long endTime = this.end.getTime();
        long otherStartTime = dateRange.getStart().getTime();
        long otherEndTime = dateRange.getEnd().getTime();


        boolean b1 = otherStartTime >= startTime && otherStartTime <= endTime;
        boolean b2 = otherEndTime >= startTime && otherEndTime <= endTime;
        return b1 || b2;
    }

    @Override
    public String toString() {
        return "[" + DateUtils.formatDateTime(start) + " - " + DateUtils.formatDateTime(end) + "]";
    }

    public static void main(String[] args) throws ParseException {
        DateRange dateRange1 = DateRange.buildOf(DateUtils.parseDateTime("2021-12-05 11:00:00"), DateUtils.parseDateTime("2021-12-15 18:00:00"));
        DateRange dateRange2 = DateRange.buildOf(DateUtils.parseDateTime("2021-12-15 18:00:01"), DateUtils.parseDateTime("2021-12-30 18:00:00"));
        System.out.println(dateRange1.isConflictRange(dateRange2));
    }
}