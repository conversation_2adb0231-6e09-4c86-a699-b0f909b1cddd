package com.shuyun.kylin.customized.dataflow.resource;


import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.repository.CustomTaskRepository;
import com.shuyun.kylin.customized.dataflow.dto.PmsCouponResponseDto;
import com.shuyun.kylin.customized.dataflow.service.CustomizedDataflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Validated
@RestController
@RequestMapping("/dataflow")
public class CustomizedDataflowRecource {


    public static final String EXECUTION_STATUS_INIT = "INIT";
    public static final String EXECUTION_STATUS_PROCESSING = "PROCESSING";
    public static final String EXECUTION_STATUS_FINISH = "FINISH";

    @Autowired
    private CustomizedDataflowService customizedDataflowService;

    @Autowired
    private CustomTaskRepository customTaskRepository;

    /**
     * 营销发券
     * @param pmsCouponRequest
     * @return
     */
    @PostMapping("/batch/coupon")
    public PmsCouponResponseDto pmsBatchCoupons(@RequestBody Map pmsCouponRequest) {
        log.info("批量推送优惠券，入参:{}", JSON.toJSONString(pmsCouponRequest));
        Map<String, String> map = new HashMap<>();
        //先去数据库查询状态，如果不是INIT，就返回
        if (customTaskRepository.existCustomizedTaskExecuteStatus(pmsCouponRequest.get("taskId").toString(), EXECUTION_STATUS_PROCESSING, EXECUTION_STATUS_FINISH)) {
            return new PmsCouponResponseDto(pmsCouponRequest.get("taskId").toString(), pmsCouponRequest.get("executeStatus").toString());
        }
        return customizedDataflowService.pmsBatchCoupons(pmsCouponRequest);
    }

    /**
     * 事件发券
     * @param pmsCouponRequest
     * @return
     */
    @PostMapping("/coupon")
    public PmsCouponResponseDto pmsCoupon(@RequestBody Map pmsCouponRequest) {
        log.info("事件发券优惠券，入参:{}", JSON.toJSONString(pmsCouponRequest));
        Map<String, String> map = new HashMap<>();
        //先去数据库查询状态，如果不是INIT，就返回
        if (customTaskRepository.existCustomizedTaskExecuteStatus(pmsCouponRequest.get("taskId").toString(), EXECUTION_STATUS_PROCESSING, EXECUTION_STATUS_FINISH)) {
            return new PmsCouponResponseDto(pmsCouponRequest.get("taskId").toString(), pmsCouponRequest.get("executeStatus").toString());
        }
        return customizedDataflowService.pmsCoupon(pmsCouponRequest);
    }



    /**
     * 事件微信订阅消息
     * @param pmsWechatSubRequest
     * @return
     */
    @PostMapping("/wechatSubScribe")
    public PmsCouponResponseDto pmsWechatSubScribe(@RequestBody Map pmsWechatSubRequest) {
        log.info("事件发送订阅消息入参:{}", JSON.toJSONString(pmsWechatSubRequest));
        Map<String, String> map = new HashMap<>();
        //先去数据库查询状态，如果不是INIT，就返回
        if (customTaskRepository.existCustomizedTaskExecuteStatus(pmsWechatSubRequest.get("taskId").toString(), EXECUTION_STATUS_PROCESSING, EXECUTION_STATUS_FINISH)) {
            return new PmsCouponResponseDto(pmsWechatSubRequest.get("taskId").toString(), pmsWechatSubRequest.get("executeStatus").toString());
        }
        return customizedDataflowService.pmsWechatSubScribe(pmsWechatSubRequest);
    }

    /**
     * 营销微信订阅消息
     * @param pmsWechatSubRequest
     * @return
     */
    @PostMapping("/batch/wechatSubScribe")
    public PmsCouponResponseDto pmsBatchWechatSubScribe(@RequestBody Map pmsWechatSubRequest) {
        log.info("批量发送订阅消息入参:{}", JSON.toJSONString(pmsWechatSubRequest));
        Map<String, String> map = new HashMap<>();
        //先去数据库查询状态，如果不是INIT，就返回
        if (customTaskRepository.existCustomizedTaskExecuteStatus(pmsWechatSubRequest.get("taskId").toString(), EXECUTION_STATUS_PROCESSING, EXECUTION_STATUS_FINISH)) {
            return new PmsCouponResponseDto(pmsWechatSubRequest.get("taskId").toString(), pmsWechatSubRequest.get("executeStatus").toString());
        }
        return customizedDataflowService.pmsBatchWechatSubScribe(pmsWechatSubRequest);
    }
}
