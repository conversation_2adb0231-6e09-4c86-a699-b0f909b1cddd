package com.shuyun.kylin.customized.base.config;

import lombok.extern.slf4j.Slf4j;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.*;


/**
 * 线程池配置
 * <AUTHOR>
 */
@EnableAsync
@Configuration
@Slf4j
public class ThreadPoolTaskExecutorConfig {

    // 核心线程数=cpu核数
    private int CORE_POOL_SIZE = 3;
    // 最大线程数
    private int MAX_POOL_SIZE = 5;
    // 队列容量
    private int QUEUE_CAPACITY = 100;
    // 线程活跃时间（秒）,超过时间就会被线程池回收
    private int KEEP_ALIVE_SECONDS = 1;
    //设置线程前缀
    private String THREAD_PREFIX_NAME = "SWIRE-";


    @Bean
    public Executor executorService() {
        ThreadPoolTaskExecutor threadPoolExecutor = new ThreadPoolTaskExecutor();
        threadPoolExecutor.setCorePoolSize(CORE_POOL_SIZE);
        threadPoolExecutor.setMaxPoolSize(MAX_POOL_SIZE);
        threadPoolExecutor.setQueueCapacity(QUEUE_CAPACITY);
        threadPoolExecutor.setKeepAliveSeconds(KEEP_ALIVE_SECONDS);
        threadPoolExecutor.setThreadNamePrefix(THREAD_PREFIX_NAME);
        threadPoolExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 手动初始化线程池
        threadPoolExecutor.initialize();
        log.info("------>== @Async 业务处理线程配置成功，核心线程池：[{}]，最大线程池：[{}]，队列容量：[{}]，线程名称前缀：[{}] ==<------", threadPoolExecutor.getCorePoolSize(), threadPoolExecutor.getMaxPoolSize(), QUEUE_CAPACITY, threadPoolExecutor.getThreadNamePrefix());
        return threadPoolExecutor;
    }

//    @Bean
//    public ExecutorService executorScheduledService(){
//        return  new ScheduledThreadPoolExecutor(CORE_POOL_SIZE);
//    }
}