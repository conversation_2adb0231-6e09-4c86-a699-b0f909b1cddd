package com.shuyun.kylin.customized.member.dm;

import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.base.enums.ResponseCodeEnum;
import com.shuyun.kylin.customized.base.util.DateHelper;
import com.shuyun.kylin.customized.member.dto.CepRegulationPointDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class CepRegulationPointRepository extends BaseDsRepository<CepRegulationPointDto> {

    /**
     * 查询活动主题
     *
     * @param scene
     */
    public Boolean getActivity(String scene){
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("scene", scene);
        String queryMemberSql = " select scene from " + ModelConstants.CAMPAIGN_POINTSRULE + " where  scene = :scene ";
        List<Map<String,Object>> list = execute(queryMemberSql, queryMap).getData();
        if (CollectionUtils.isEmpty(list)){
            return false;
        }
        return true;
    }

    /**
     * 查询活动主题
     *
     * @param scene
     */
    public CepRegulationPointDto getActivityTheme(String scene){
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("scene", scene);
        return queryByFilter(ModelConstants.CAMPAIGN_POINTSRULE, null, queryMap);
    }

}
