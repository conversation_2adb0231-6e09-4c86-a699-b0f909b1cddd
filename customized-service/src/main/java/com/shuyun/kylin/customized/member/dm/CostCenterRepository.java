package com.shuyun.kylin.customized.member.dm;

import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.member.dto.CostCenterDto;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class CostCenterRepository extends BaseDsRepository<CostCenterDto> {
    /**
     * 查询成本中心
     */
    public List<CostCenterDto> selectCostCenter() {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("1", 1);
        String queryMemberSql = " select costCenterName,parentCostCenterCode,isSettlement,costCenterCode,parentCostCenterName from " + ModelConstants.COSTCENTER +"  where 1 = :1 ";
        List<CostCenterDto> list =  executeSQL(queryMemberSql, queryMap);
        return list;
    }
}
