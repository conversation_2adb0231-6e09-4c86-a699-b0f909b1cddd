package com.shuyun.kylin.customized.member.service;

import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.common.ResponsesVo;
import com.shuyun.kylin.customized.member.dto.BlacListRequestDto;
import com.shuyun.kylin.customized.member.dto.PointFreezeDto;
import com.shuyun.kylin.customized.member.dto.ThresholdDto;

import java.util.List;

public interface IRiskControlBlacklistService {
    ResponsesVo freezePoint(PointFreezeDto request);

    ResponsesVo unfreezePoint(PointFreezeDto request);
    ResponsesVo addCheckList2(BlacListRequestDto request);
    ResponsesVo freezeMember(BlacListRequestDto request);
    ResponsesVo unfreezeMember(BlacListRequestDto request);
    ResponsesVo deleteMember(String  memberId);
    ResponsesVo setThreshold(ThresholdDto request);
    ResponsesVo queryWaringMember(String  memberId);
}
