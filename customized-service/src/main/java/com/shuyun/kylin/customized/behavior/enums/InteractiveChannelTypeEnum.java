package com.shuyun.kylin.customized.behavior.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum InteractiveChannelTypeEnum {

    COCA_COLA("KO_MP", "可口可乐吧"),

    SWIRE("SWIRE_MP", "每天有乐");

    String str;

    String desc;

}
