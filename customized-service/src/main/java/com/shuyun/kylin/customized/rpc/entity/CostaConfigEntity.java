package com.shuyun.kylin.customized.rpc.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * @author: <PERSON>wei
 * @date: 2024-12-17
 */
@Data
public class CostaConfigEntity {

    @JSONField(name = "costa.aesiv")
    private String aesIv;
    @JSONField(name = "costa.aeskey")
    private String aesKey;
    @JSONField(name = "costa.api.channel")
    private String channel;
    @JSONField(name = "costa.api.version")
    private String version;
    @JSONField(name = "costa.appId")
    private String appId;
    @JSONField(name = "costa.secret")
    private String secret;
    @JSONField(name = "costa.endpoint")
    private String endpoint;
}
