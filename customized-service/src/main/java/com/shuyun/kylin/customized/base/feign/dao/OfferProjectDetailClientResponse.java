package com.shuyun.kylin.customized.base.feign.dao;

import lombok.Data;

@Data
public class OfferProjectDetailClientResponse {

    private String code;

    private String msg;

    private RspData data;

    @Data
    public static class RspData {
        /**
         * 项目id
         */
        private String id;

        private GrantRestrict grantRestrict;

        @Data
        public static class GrantRestrict {
            /**
             * 发券渠道（如KO_MP，KO_Ali...）
             */
            private String platformsRef;
        }

        private ExtData extData;

        @Data
        public static class ExtData {
            /**
             * 积分
             */
            private Integer point;

            /**
             * 是否积分兑换
             */
            private Boolean isPointExchange;

            /**
             * 核券成本中心是否和LBS有关
             */
            private Boolean isLBSusedCostCenterCode;

            /**
             * 发券成本中心是否和LBS有关
             */
            private Boolean isLBSsendCostCenterCode;

            /**
             * 发券成本中心code
             * 反参格式样例："[\"C20220103002\"]"
             */
            private String sendCostCenterCode;
            /**
             * 核券成本中心code
             */
            private String usedCostCenterCode;

            /**
             * 外部券项目id
             */
            private String externalProjectId;
            /**
             * 发券商户号
             */
            private String stockMchid;
            /**
             * 制券商户号
             */
            private String stockCreatorMchid;

            /**
             * 权益成本
             */
            private String cost;

            /**
             * 使用方式（兑换类型）
             */
            private String redeemType;
            /**
             * 跳转参数
             */
            private String jumpParameter;
        }
    }
}
