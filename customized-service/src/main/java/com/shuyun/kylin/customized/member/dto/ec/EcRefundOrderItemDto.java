package com.shuyun.kylin.customized.member.dto.ec;

import lombok.Data;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class EcRefundOrderItemDto {
    //EC无此id, 数云待确认是否拼：退单主订单ID_原始子订单ID
    private String orderItemId;
    @NotBlank(message = "原始子订单ID不能为空")
    private String originOrderItemId;
    @NotBlank(message = "渠道类型不能为空")
    /**
     * TAOBAO：天猫
     * KO_MP：KO小程序
     * EC_SHOPPING：EC购物商城
     * EC_POINT：EC积分商城
     */
    private String channelType;
    @NotBlank(message = "订单状态不能为空")
    //CREATED: 下单 CANCELLED: 订单取消 DELIVERED: 已发货 CONFIRMED: 确认收货 FINISHED: 订单完成
    private String status;

    @NotNull(message = "退款金额不能为空")
    private Double refundFee;
    @NotBlank(message = "商品Code不能为空")
    private String productCode;
    @NotBlank(message = "商品名称不能为空")
    private String productName;
    @Digits(message = "OrderItems中数量过大不能超过9000000",integer = 8,fraction = 0)
    @Max(message = "OrderItems中数量过大不能超过9000000",value = 9000000)
    @NotNull(message = "OrderItems中数量不能为空")
    private Integer quantity;


    //原始主订单ID
    private String originOrderId;
    //订单退货时间 格式: yyyy-MM-dd HH:mm:ss
    private String refundTime;
    //订单完成时间, 格式: yyyy-MM-dd HH:mm:ss
    private String finishTime;
}

