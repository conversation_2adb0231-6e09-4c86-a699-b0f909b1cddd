package com.shuyun.kylin.customized.swire.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class PointRuleDto {

    private Integer ruleCode;
    private String channelType;
    private Boolean isDailyLimited;
    private Double pointsThreshold;
    private String ruleName;
    private String startTime;
    private String endTime;
    private String channelFrom;
    private Boolean isOpenQuery;

    private List<String> id;
}
