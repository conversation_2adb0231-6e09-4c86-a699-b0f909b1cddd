package com.shuyun.kylin.customized.project.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Setter;

/**
 * @author: Jingwei
 * @date: 2025-01-09
 */
@Data
public class McdCallbackRequestDto {

    /** 签名 */
    @Setter(onMethod_ = @JsonProperty("Sign"))
    private String sign;
    /** 订单号 */
    @JsonProperty("bill_code")
    private String billCode;
    /** 券id */
    @JsonProperty("coupon_id")
    private String couponId;
    /** 券码 */
    @JsonProperty("coupon_code")
    private String couponCode;
    /** 兑换时间 */
    @JsonProperty("redeem_time")
    private String redeemTime;
    /** 交易类型; 2.核销 */
    @JsonProperty("process_type")
    private String processType;
    /** 核销渠道; 03.麦当劳 App, 45.微信小程序,54.支付宝小程序 */
    @JsonProperty("use_channel")
    private String useChannel;
    /** 门店id */
    @JsonProperty("store_id")
    private String storeId;
}
