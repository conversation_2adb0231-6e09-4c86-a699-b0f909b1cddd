package com.shuyun.kylin.customized.rpc.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * @author: Jingwei
 * @date: 2024-12-14
 * @description 具体看微信官方文档：{@link <a href="https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter9_1_2.shtml"/>}
 */
@Data
public class WxSendCouponRequestDto {
    /**
     * 微信为每个批次分配的唯一id。
     * 校验规则：必须为代金券（全场券或单品券）批次号，不支持立减与折扣。
     * 示例值：9856000
     */
    @JSONField(name = "stock_id")
    private String stockId;
    /**
     * openid信息，用户在appid下的唯一标识。
     * 校验规则：该openid需要与接口传入中的appid有对应关系。
     * 示例值：2323dfsdf342342
     * 必填 是
     * <strong>放在请求路径中，请求题不需要传</strong>
     */
    @JSONField(name = "openid")
    @JsonIgnore
    private String openid;
    /**
     * 商户此次发放凭据号（格式：商户id+日期+流水号），
     * 可包含英文字母，数字，|，_，*，-等内容，不允许出现其他不合法符号，商户侧需保持唯一性。
     * 示例值： 89560002019101000121
     */
    @JSONField(name = "out_request_no")
    private String outRequestNo;
    /**
     * 微信为发券方商户分配的公众账号ID，
     * 接口传入的所有appid应该为公众号的appid或者小程序的appid
     * （在mp.weixin.qq.com申请的）或APP的appid（在open.weixin.qq.com申请的）。
     * 校验规则：
     * 1、该appid需要与接口传入中的openid有对应关系；
     * 2、该appid需要与调用接口的商户号（即请求头中的商户号）有绑定关系，若未绑定，
     * 可参考该指引完成绑定（商家商户号与AppID账号关联管理）
     * 示例值：wx233544546545989
     */
    @JSONField(name = "appid")
    private String appid;
    /**
     * 批次创建方商户号。
     * 校验规则：接口传入的批次号需由stock_creator_mchid所创建。
     * 示例值：8956000
     */
    @JSONField(name = "stock_creator_mchid")
    private String stockCreatorMchId;
    /**
     * 指定面额发券场景，券面额，其他场景不需要填，单位：分。（该字段暂未开放 ）
     * 校验规则：仅在发券时指定面额及门槛的场景才生效，常规发券场景请勿传入该信息。
     * 示例值：100
     */
    @JSONField(name = "coupon_value")
    @JsonIgnore
    private Integer couponValue;
    /**
     * 指定面额发券批次门槛，其他场景不需要，单位：分。 （该字段暂未开放 ）
     * 校验规则：仅在发券时指定面额及门槛的场景才生效，常规发券场景请勿传入该信息。
     * 示例值：100
     */
    @JSONField(name = "coupon_minimum")
    @JsonIgnore
    private Integer couponMinimum;
}
