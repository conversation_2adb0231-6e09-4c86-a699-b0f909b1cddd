package com.shuyun.kylin.customized.coupon.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shuyun.dm.api.response.InsertResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.dm.sdk.Options;
import com.shuyun.kylin.customized.base.common.Constants;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.config.BaseConfig;
import com.shuyun.kylin.customized.base.enums.ResponseCodeEnum;
import com.shuyun.kylin.customized.base.exception.ServiceException;
import com.shuyun.kylin.customized.base.stream.kafka.producer.coupon.CouponProducer;
import com.shuyun.kylin.customized.base.util.DataapiSdkUtil;
import com.shuyun.kylin.customized.base.util.DateHelper;
import com.shuyun.kylin.customized.base.util.RandomUtils;
import com.shuyun.kylin.customized.coupon.dm.OtherBenefitsRepository;
import com.shuyun.kylin.customized.coupon.domain.Coupon;
import com.shuyun.kylin.customized.coupon.domain.CouponConfig;
import com.shuyun.kylin.customized.coupon.dto.*;
import com.shuyun.kylin.customized.coupon.enums.CouponTypeEnum;
import com.shuyun.kylin.customized.coupon.enums.StatusEnum;
import com.shuyun.kylin.customized.coupon.mapper.CouponConfigMapper;
import com.shuyun.kylin.customized.coupon.mapper.CouponMapper;
import com.shuyun.kylin.customized.coupon.service.ICouponService;
import com.shuyun.kylin.customized.member.dm.MemberRepository;
import com.shuyun.kylin.starter.redis.ICache;
import com.shuyun.motor.common.cons.PropsUtil;
import com.shuyun.pip.component.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class CouponServiceImpl  extends ServiceImpl<CouponMapper, Coupon> implements ICouponService {

    @Resource
    private BaseConfig baseConfig;
    @Resource
    private CouponProducer couponProducer;
    @Autowired
    @Qualifier("redisCache")
    private ICache redisCache;

    @Autowired
    private CouponConfigMapper couponConfigMapper;

    final String dataApiService = PropsUtil.getSysOrEnv("dataapi/v1.ribbon.com.shuyun.motor.client.targetServer");

    @Autowired
    private MemberRepository memberRepository;

    @Autowired
    private OtherBenefitsRepository otherBenefitsRepository;
    private static DateTimeFormatter inputDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public CouponCodeResponseDto generateCode(@Valid CouponCodeRequestDto couponCodeRequestDto) {
        CouponCodeResponseDto couponCodeResponseDto = new CouponCodeResponseDto();
        CouponCodeRequestDto project =  redisCache.get(couponCodeRequestDto.getProjectId().toUpperCase());
        log.info("获取redis的值："+project);
        if(project!=null && baseConfig.getAccoutNo()==null){
            return  new CouponCodeResponseDto();
        }
        if(project!=null && baseConfig.getAccoutNo()!=null && baseConfig.getAccoutNo().equals(project.getGrantCount())){
            return  new CouponCodeResponseDto();
        }
        if(project!=null && baseConfig.getAccoutNo()!=null && baseConfig.getAccoutNo()>project.getGrantCount() && (baseConfig.getAccoutNo()-project.getGrantCount())>= couponCodeRequestDto.getGrantCount()){
            couponCodeRequestDto.setGrantCount(couponCodeRequestDto.getGrantCount());
        }
        if(project!=null && baseConfig.getAccoutNo()!=null && baseConfig.getAccoutNo()>project.getGrantCount() && (baseConfig.getAccoutNo()-project.getGrantCount())< couponCodeRequestDto.getGrantCount()){
            Integer actualCount  = baseConfig.getAccoutNo()-project.getGrantCount();
            couponCodeRequestDto.setGrantCount(actualCount);
        }
        if(baseConfig.getAccoutNo()!=null && couponCodeRequestDto.getGrantCount()>baseConfig.getAccoutNo()){
            couponCodeRequestDto.setGrantCount(baseConfig.getAccoutNo());
        }
        if (couponCodeRequestDto.getGrantCount() > 0 && !StringUtils.isEmpty(couponCodeRequestDto.getProjectId())) {
            List<String> codeList = new ArrayList<>();
            for (int i = 0; i < couponCodeRequestDto.getGrantCount(); i++) {
                String couponcode = RandomUtils.generateCode();
                codeList.add(couponcode);
            }
            //TODO  调用客户接口 要实现优惠券数据同步  暂时直接返回成功状态
            couponCodeResponseDto.setProjectId(couponCodeRequestDto.getProjectId());
            couponCodeResponseDto.setGrantCount(codeList.size());
            couponCodeResponseDto.setCode(Constants.SUCESSCODE);
            couponCodeResponseDto.setData(codeList);
            couponCodeResponseDto.setMessage(Constants.COMMONSUCCESSMSG);
            CouponCodeRequestDto redisCodeDto = new CouponCodeRequestDto();
            redisCodeDto.setProjectId(couponCodeRequestDto.getProjectId().toUpperCase());
            if(project!=null) {
                redisCodeDto.setGrantCount(codeList.size() + project.getGrantCount());
            }else {
                redisCodeDto.setGrantCount(codeList.size());
            }
            redisCache.put(redisCodeDto.getProjectId().toUpperCase(),redisCodeDto,7,TimeUnit.DAYS);
        }
        return couponCodeResponseDto;
    }
//
//    @Override
//    public void receive(CustomerCouponDto customerCouponDto) {
//        //1. 入库 : 做测试使用。默认入库，可以通过开关来控制是否入库. 建议不要入库，直接转发
//        //2. 客户提供人、劵绑定关系接口，直接调用客户人劵绑定关系接口转发，不需入库
//        //TODO  需要调用客户接口转发
//        List<CustomerDto> customerDtoList = Optional.ofNullable(customerCouponDto.getData()).orElse(Lists.newArrayList());
//        String type = customerCouponDto.getType();
//        if (StringUtils.isBlank(type)) {
//            log.error("解析的type不能为空");
//            return; }
//        CouponTypeEnum couponTypeEnum = CouponTypeEnum.getEnumByValue(type);
//        switch (couponTypeEnum){
//            //同步活动人群
//            case OUT_PUT_CUSTOM:
//                syncActivity(customerCouponDto,customerDtoList);
//                break;
//            //同步人券关系
//            case BEFORE_SYN_COUPON:
//                syncPersonCoupon(customerCouponDto,customerDtoList);
//                break;
//            //获取卡券后同步人券关系
//            case REAL_TIME_GENERATE_COUPON:
//                afterSyncPersonCoupon(customerCouponDto,customerDtoList);
//                break;
//            default:
//                log.error("暂不支持此类型 type=",type);
//                break;
//        }
//    }

    @Override
    public void searchDataList(List<Coupon> couponDatas) {
        int singleBatchCount = 0; //初始化单批次成功数量
        CustomerCouponDto customerAndCouponDto = new CustomerCouponDto();
        List<CustomerDto> customerList = new ArrayList<>();
        for (Coupon item : couponDatas) {
//        couponEntities.stream().forEach(item -> {
            CustomerDto customerDto = new CustomerDto();
            singleBatchCount++;
            if (item.getType().equals(CouponTypeEnum.REAL_TIME_GENERATE_COUPON.getValue())) {
                customerDto.setCoupon(item.getCoupon());
            }
            if (item.getType().equals(CouponTypeEnum.BEFORE_SYN_COUPON.getValue())) {
                customerDto.setCoupon(item.getCoupon());
            }
            customerDto.setCustomerNo(item.getCustomerNo());
            if (!StringUtils.isEmpty(baseConfig.getType()) && baseConfig.getType().equals("SUBMIT_SUCCESS")) {
                if (baseConfig.getSuccessNo() >= singleBatchCount) {
                    customerDto.setStatus(StatusEnum.SUBMIT_SUCCESS.getValue());
                } else {
                    customerDto.setStatus(StatusEnum.SUBMIT_FAILED.getValue());
                }
            }
            if (!StringUtils.isEmpty(baseConfig.getType()) && baseConfig.getType().equals("GRANT_SUCCESS")) {
                if (baseConfig.getSuccessNo() >= singleBatchCount) {
                    customerDto.setStatus(StatusEnum.GRANT_SUCCESS.getValue());
                } else {
                    customerDto.setStatus(StatusEnum.GRANT_FAILED.getValue());
                }
            }
            if (StringUtils.isEmpty(baseConfig.getType())) {
                customerDto.setStatus(StatusEnum.SUBMIT_SUCCESS.getValue());
            }
            customerList.add(customerDto);
            customerAndCouponDto.setBatchId(item.getBatchId());
            customerAndCouponDto.setSubjobId(item.getSubJobId());
            customerAndCouponDto.setNodeId(item.getNodeId());
            customerAndCouponDto.setType(item.getType());
            customerAndCouponDto.setProjectId(item.getProjectId());
        }
        customerAndCouponDto.setData(customerList);
        sendKafka(customerAndCouponDto);
    }

    @Override
    public List<Coupon> assemblyData(CustomerCouponDto customerCouponDto,List<CustomerDto> customerDtoList){
        List<Coupon> couponDatas = new ArrayList<>();
        customerDtoList.forEach(relationDto -> {
            Coupon coupon = new Coupon();
            coupon.setBatchId(customerCouponDto.getBatchId());
            coupon.setProjectId(customerCouponDto.getProjectId());
            coupon.setSubJobId(customerCouponDto.getSubjobId());
            coupon.setNodeId(customerCouponDto.getNodeId());
            coupon.setType(customerCouponDto.getType());
            coupon.setCoupon(relationDto.getCoupon());
            coupon.setCustomerNo(relationDto.getCustomerNo());
            coupon.setCreateBy(1L);
            coupon.setCreateTime(new Date());
            couponDatas.add(coupon);
        });
        log.info("获取kafka消息 重新绑定券关系：{} 结束", JsonUtils.toJson(couponDatas));
        return couponDatas;
    }

    @Override
    public void sendKafka(CustomerCouponDto customerCouponDto) {
        int batchNo = 0; //初始化批次数量
        String project_account_id = customerCouponDto.getProjectId().concat("_SHUYUN_BATCH").toUpperCase();
        BatchDto batchDto = redisCache.get(project_account_id);
        BatchDto batch = new BatchDto();
        batch.setProjectId(project_account_id);
        //batchDto为null 说明第一次查询redis 没有数据 count++ 写入redis
        if (batchDto == null) {
            batchNo++;
            batch.setCount(batchNo);
            redisCache.put(project_account_id, batch, 7, TimeUnit.DAYS);
        }
        //batch不为null 配置中心的批次数量不为空 且redis写入的数量不等于配置中心的数量 则count++ 写入redis
        if (batchDto != null && baseConfig.getBatchNo() != null && !Objects.equals(baseConfig.getBatchNo(), batchDto.getCount())) {
            Integer cnt = batchDto.getCount();
            cnt++;
            batch.setCount(cnt);
            redisCache.put(project_account_id, batch, 7, TimeUnit.DAYS);
        }
        if (baseConfig.getBatchNo() != null && batchDto!=null) {
            if (batchDto.getCount() != null && !Objects.equals(baseConfig.getBatchNo(), batch.getCount())) {
                couponProducer.sendMsg(customerCouponDto);
                log.info("写入kafka预备动作：{} 结束", JsonUtils.toJson(customerCouponDto));

            }else if (batchDto.getCount() != null && Objects.equals(baseConfig.getBatchNo(), batch.getCount())) {
                List<CustomerDto> customDtoList = customerCouponDto.getData();
                List<CustomerDto> customList = new ArrayList<>();
                customDtoList.stream().forEach(dto->{
                    CustomerDto customer = new CustomerDto();
                    BeanUtils.copyProperties(dto,customer);
                    if(baseConfig.getType().equals("SUBMIT_SUCCESS")) {
                        customer.setStatus(StatusEnum.SUBMIT_FAILED.getValue());
                    }
                    if(baseConfig.getType().equals("GRANT_SUCCESS")){
                        customer.setStatus(StatusEnum.GRANT_FAILED.getValue());
                    }
                    customList.add(customer);
                });

                customerCouponDto.setData(customList);
                couponProducer.sendMsg(customerCouponDto);
                log.info("写入kafka状态全部失效：{}", JsonUtils.toJson(customerCouponDto));
            }else if (batchDto.getCount() == null ){
                couponProducer.sendMsg(customerCouponDto);
                log.info("写入kafka预备动作：{} 结束", JsonUtils.toJson(customerCouponDto));
            }

        }else if(baseConfig.getBatchNo() != null || baseConfig.getBatchNo() == null && batchDto == null || baseConfig.getBatchNo() == null && batchDto != null){
            couponProducer.sendMsg(customerCouponDto);
        }
    }

    @Override
    public BaseResponseDto preCoupnModel(PreCouponModelDto preCouponModelDto) {
        Map<String, Object> couponmap = JsonUtils.objectToMap(preCouponModelDto);//JsonUtils.objectMapper().convertValue(preCouponModelDto, HashMap.class);
        String fqnName ="data.prctvmkt.coupon.Coupon";
        InsertResponse response =  this.operationDs(fqnName,couponmap);
        if(!StringUtils.isEmpty(response.getId())){
            return new BaseResponseDto(response, BaseResponseDto.ResponseStatusEnum.SUCCESS);
        }else {
            return new BaseResponseDto("", BaseResponseDto.ResponseStatusEnum.FAIL,"写入预置卡券模型数据失败");
        }
    }

    @Override
    public BaseResponseDto preEquityProject(PreCouponProjectModelDto preCouponProjectModelDto) {
        Map<String, Object> projectmap = JsonUtils.objectToMap(preCouponProjectModelDto);
        String fqnName ="data.prctvmkt.coupon.CouponProject";
        InsertResponse response =  this.operationDs(fqnName,projectmap);
        if(!StringUtils.isEmpty(response.getId())){
            return new BaseResponseDto(response, BaseResponseDto.ResponseStatusEnum.SUCCESS);
        }else {
            return new BaseResponseDto("", BaseResponseDto.ResponseStatusEnum.FAIL,"写入预置权益项目数据失败");
        }
    }

    public InsertResponse operationDs(String fqnName,Map<String, Object> paramsMap){
        InsertResponse response;
        final DataapiHttpSdk dataapiHttpSdk;
        if(StringUtils.isEmpty(dataApiService)) {
            dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();
        } else {
            Options options = Options.Companion.newBuilder().enableSign(true).caller(PropsUtil.getSysOrEnv("dataapi/v1.ribbon.com.shuyun.motor.client.name")).secret(PropsUtil.getSysOrEnv("dataapi/v1.ribbon.com.shuyun.motor.client.sign.secret")).version("v1").build();
            dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();
           // dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk(dataApiService, options, "customized-service");
        }
        try{
            response = dataapiHttpSdk.insert(fqnName, paramsMap, false, true);
        }catch (Exception ex){
            log.error("dataapiHttpSdk.insert ====> insert database error !,msg:{}", ex);
            throw new ServiceException("dataapiHttpSdk.insert ====> insert database error !", ex);
        }
        return  response;
    }

    @Override
    public BaseResponseDto pregenerateCode(OutCouponCodeNoDto outCouponCodeNoDto){
        List<String> codeList = new ArrayList<>();
        if (outCouponCodeNoDto.getGrantCount() > 0 && outCouponCodeNoDto.getLen()>0){
            for (int i = 0; i < outCouponCodeNoDto.getGrantCount(); i++) {
                String couponcode = RandomUtils.generateCodePlus(outCouponCodeNoDto.getLen());
                codeList.add(couponcode);
            }
        }
        return new BaseResponseDto(codeList, BaseResponseDto.ResponseStatusEnum.SUCCESS,"生成券码成功 共："+codeList.size()+"条");
    }


    /**
     * 初始化发券配置
     * @param couponConfigDto
     */
    @Override
    public void initCouponConfig(CouponConfigDto couponConfigDto){
        log.info("添加优惠券配置表开始，参数：{}",JsonUtils.toJson(couponConfigDto));
        Long begin = System.currentTimeMillis();
        Date nowTime = new Date();
        CouponConfig existCouponConfig = couponConfigMapper.selectCouponConfigByMemberType(couponConfigDto.getMemberType());
        if(null != existCouponConfig){
            existCouponConfig.setApiKey(couponConfigDto.getApiKey());
            existCouponConfig.setApiSecret(couponConfigDto.getApiSecret());
            existCouponConfig.setAppId(couponConfigDto.getAppId());
            existCouponConfig.setLastSync(nowTime);
            couponConfigMapper.updateById(existCouponConfig);
        }else{
            CouponConfig couponConfig = JsonUtils.convert(couponConfigDto,CouponConfig.class);
            couponConfig.setCreated(nowTime);
            couponConfig.setLastSync(nowTime);
            couponConfigMapper.insert(couponConfig);
        }
        log.info("添加优惠券配置表结束，耗时：{} ms",(begin - System.currentTimeMillis()));
    }

    @Override
    public CouponConfig getCouponConfigByMemberType(String memberType){
        return couponConfigMapper.selectCouponConfigByMemberType(memberType);
    }



    /**
     * 权益信息保存
     * @param otherBenefitsDto
     */
    @Override
    public ResponseResult saveOtherBenefits(OtherBenefitsDto otherBenefitsDto) {
        //查询会员id
        String memberId = memberRepository.queryMemberId(otherBenefitsDto.getCustomerNo());
        if (StringUtils.isBlank(memberId)) {
            return new ResponseResult(ResponseCodeEnum.MEMBER_FAILED);
        }
        HashMap<Object, Object> member = new HashMap<>();
        member.put("id", memberId);
        otherBenefitsDto.setMemberId(memberId);
        otherBenefitsDto.setMember(member);

        HashMap<Object, Object> campaign = new HashMap<>();
        campaign.put("id", otherBenefitsDto.getCampaignId());
        otherBenefitsDto.setCampaign(campaign);
//        Date startDate = Date.from(LocalDateTime.parse(otherBenefitsDto.getGetTime(), inputDtf).toInstant(ZoneOffset.of("+8")));
//        otherBenefitsDto.setGetTime( DateUtils.formatDate(startDate, "yyyy-MM-dd'T'HH:mm:ss"));
        otherBenefitsDto.setGetTime(StringUtils.isNotEmpty(otherBenefitsDto.getGetTime()) ? DateHelper.getZone(otherBenefitsDto.getGetTime()) : null);

        log.info("CouponServiceImpl...saveOtherBenefits...权益信息保存参数:{}", JSONObject.parseObject(JSON.toJSONString(otherBenefitsDto)));
        try {
            otherBenefitsRepository.insert(ModelConstants.OTHERBENEFITS, otherBenefitsDto);
        } catch (Exception e) {
            log.error("CouponServiceImpl...saveOtherBenefits...权益信息保存异常:{}",e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED);
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }


}