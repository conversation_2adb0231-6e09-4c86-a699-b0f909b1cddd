package com.shuyun.kylin.customized.behavior.service;

import com.google.common.collect.Maps;
import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.kylin.customized.behavior.enums.InteractiveChannelTypeEnum;
import com.shuyun.kylin.customized.base.util.DataapiSdkUtil;
import com.shuyun.kylin.customized.behavior.domain.Interactive;
import com.shuyun.kylin.customized.behavior.resource.po.CheckPO;
import com.shuyun.kylin.customized.behavior.resource.vo.CheckResponse;
import com.shuyun.kylin.customized.behavior.resource.vo.CommonApiResponse;
import com.shuyun.kylin.customized.behavior.resource.vo.InteractiveResponseVO;
import com.shuyun.kylin.customized.behavior.util.DateRange;
import com.shuyun.kylin.customized.behavior.util.StringUtils;
import com.shuyun.pip.component.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.shuyun.kylin.customized.behavior.resource.vo.InteractiveResponseVO.InteractiveDateRangeCheckCode.*;

@SuppressWarnings("unchecked")
@Slf4j
@Service
public class InteractionBehaviorServiceImpl {

    @Resource
    private DMService dmService;

    private DateRange existsOpenOther(DataapiHttpSdk dataapiHttpSdk, String id, String scene, String channelType, DateRange dateRange) {
        String sql = "select id,startTime,endTime from data.prctvmkt.KO.interactive where status in ('待生效', '已生效') and scene = '" + scene + "' and channelType = '" + channelType + "'" ;
        if (id != null) {
            sql += " and id != '" + id + "'";
        }

        BaseResponse response = dataapiHttpSdk.execute(sql, Maps.newHashMap());
        List<DateRange> dateRanges = new ArrayList<>();
        if (response.getIsSuccess() && response.getData().size() > 0) {
            List data = response.getData();
            for (Object datum : data) {
                Map<String, Object> map = (Map<String, Object>) datum;
                DateRange range = new DateRange(map.get("startTime"), map.get("endTime"));
                dateRanges.add(range);
            }
        }

        //检查是否有日期冲突
        for (DateRange r : dateRanges) {
            if (r.isConflictRange(dateRange)) {
                return r;
            }
        }

        return null;
    }

    public InteractiveResponseVO isValidDateRange(String id, String scene, String channelType, Date start, Date end) {
        //规则1：相同场景中，不能有处于：待生效、已生效 的状态
        DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();
        DateRange currentDateRange = DateRange.buildOf(start, end);
        DateRange conflictDateRange = existsOpenOther(dataapiHttpSdk, id, scene, channelType, currentDateRange);
        if (conflictDateRange != null) {
            log.warn("当前时间范围[{}]与相同类型的活动时间冲突，冲突范围：[{}]", currentDateRange, conflictDateRange);
            return InteractiveResponseVO.buildOf(DATE_RANGE_CONFLICT);
        }
        return InteractiveResponseVO.buildOf(SUCCESS);
    }

    /**
     * 检查互动活动，并根据规则发送积分、优惠券，且记录发送日志
     *
     * @param checkPO 受检查参数
     */
    public CommonApiResponse<CheckResponse> check(CheckPO checkPO) {
        //获取规则详情
        CheckPO.SceneType scene;
        //互动场景类型
        String channelType = checkPO.getChannelType();

        try {
            scene = CheckPO.SceneType.valueOf(checkPO.getScene());
        } catch (Exception ex) {
            return CommonApiResponse.buildOf(SCENE_NOT_EXISTS);
        }

        Interactive interactive = dmService.getInteractive(scene, channelType);

        log.info("获取活动详情 --> {}", JsonUtils.toJson(interactive));

        //如果当前无有效的活动，直接返回
        if (interactive == null) {
            return CommonApiResponse.buildOf(NO_VALID_ACTIVITY);
        }

        //调用相应的活动处理器来处理
        return InteractionSceneResolver.Manager.get(scene).resolver(checkPO, interactive);
    }

    public InteractiveResponseVO repairJoinRelation() {
        String sql = "select id,costCenter,costCenterField from data.prctvmkt.KO.interactive where costCenterField is not null";
        List<Interactive.CostCenterDomain> costCenterDomains = dmService.queryForList(sql, Interactive.CostCenterDomain.class, "查询costCenter为空的互动主数据");
        costCenterDomains.forEach(x -> {
            String costCenterField = x.getCostCenterField();
            String id = x.getId();

            if (!StringUtils.isNullOrEmpty(costCenterField)) {
                dmService.execute("update data.prctvmkt.KO.interactive " +
                        "set costCenter = '" + costCenterField + "' " +
                        "where id='" + id + "'", "修复互动主数据与成本中心的关联关系");
            }
        });
        return InteractiveResponseVO.buildOf(SUCCESS);
    }

    /**
     * 校验场景值
     *
     * @param scene
     * @return
     */
    private boolean checkScene(CheckPO.SceneType scene) {
        if (scene == null) {
            return false;
        }
        switch (scene) {
            case REGISTER:
            case COMPLETE_INFO:
            case CHECK_IN:
            case CONSECUTIVE_CHECK_IN:
            case INVITE_REGISTER:
                return true;
            default:
                return false;
        }
    }
}
