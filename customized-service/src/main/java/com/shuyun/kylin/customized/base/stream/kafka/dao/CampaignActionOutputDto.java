package com.shuyun.kylin.customized.base.stream.kafka.dao;

import lombok.Data;

@Data
public class CampaignActionOutputDto {

    private String id;
    private Boolean isSendCardsOffers;
    private String costCenter;
    private String coupon;

    /**
     * 触达身份标识
     */
    private String objectId;
    /**
     * 活动id
     */
    private String campaignId;

    /**
     * 	任务ID
     */
    private String taskId;
    private String occId;
    private String nodeId;
    private String nodeName;
    private String campaignName;
    private String testRun;
    private String type;
    private String scene;
    private String ProjectId;

}
