package com.shuyun.kylin.customized.base.util;

import org.apache.commons.lang3.StringUtils;

/**
 *  接口返回模型
 * <AUTHOR>
 * @create 2020/1/3
 */
public class ActionResult<T> {

    public final static int DEFAULT_CODE = 200;

    private int code = DEFAULT_CODE;
    private T payload;
    private String message;

    /**
     * Instantiates a new Action result.
     */
    public ActionResult() {
    }

    public ActionResult(T payload) {
        this(DEFAULT_CODE, null, payload);
    }

    public ActionResult(int code, String message) {
        this(code, message, null);
    }

    public ActionResult(int code, String message, T payload) {
        this.code = code;
        this.message = StringUtils.trimToNull(message);
        this.payload = payload;
    }


    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public T getPayload() {
        return payload;
    }

    public void setPayload(T payload) {
        this.payload = payload;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
