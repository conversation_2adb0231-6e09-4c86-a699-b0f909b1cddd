package com.shuyun.kylin.customized.rpc.entity;

import lombok.Data;

/**
 * 爱奇艺三方接口基础配置
 *
 * <pre>
 * {
 *     "userId": "A08566",
 *     "userPwd": "of111111",
 *     "signKey": "OFCARD",
 *     "version": "6.0",
 *     "retUrl": "",
 *     "orderUrl": "http://apitest.ofpay.com/onlineorder.do"
 * }
 * </pre>
 */
@Data
public class IQiYiConfigEntity {
    /**
     * SP项目编码
     */
    private String userId;
    /**
     * SP项目密钥
     * 新建项目在审核通过后系统会自动生成项目密码发送短信至创建人手机上
     */
    private String userPwd;
    /**
     * 签名密钥
     */
    private String signKey;
    /**
     * 接口版本号
     */
    private String version;
    /**
     * 接口回调地址
     */
    private String retUrl;
    /**
     * 接口请求地址
     */
    private String orderUrl;

}
