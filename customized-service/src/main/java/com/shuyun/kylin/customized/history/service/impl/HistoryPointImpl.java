package com.shuyun.kylin.customized.history.service.impl;


import com.shuyun.kylin.crm.openapi.core.dto.wechat.WechatRegisterRequest;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.base.util.DataApiUtil;
import com.shuyun.kylin.customized.history.dao.LoyaltyPointDto;
import com.shuyun.kylin.customized.history.service.HistoryPoint;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;

@Slf4j
@Service
public class HistoryPointImpl implements HistoryPoint {


    @Override
    public void updateHistoryPoint(LoyaltyPointDto loyaltyPointDto, String message,String changeType) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("code","500");
        map.put("msg",message);
        map.put("memberId",loyaltyPointDto.getMemberId());
        map.put("channelType",loyaltyPointDto.getChannelType());
        map.put("changeType",changeType);
        map.put("pointAccountId",loyaltyPointDto.getPointAccountId());
        map.put("shopId",loyaltyPointDto.getShopId());
        map.put("businessId",loyaltyPointDto.getBusinessId());
        map.put("desc",loyaltyPointDto.getDesc());
        map.put("changeMode",loyaltyPointDto.getChangeMode());
        map.put("changePoint",loyaltyPointDto.getChangePoint());
        map.put("changeSourceDetail",loyaltyPointDto.getChangeSourceDetail());
        map.put("changeSource",loyaltyPointDto.getChangeSource());
        map.put("costTracing",loyaltyPointDto.getCostTracing());
        map.put("effectiveDate",loyaltyPointDto.getEffectiveDate());
        map.put("overdueDate",loyaltyPointDto.getOverdueDate());
        DataApiUtil.upsertIsData(ConfigurationCenterUtil.HISTORY_POINT_ERROR,loyaltyPointDto.getId().toString(), map);

    }

    @Override
    public void updateHistoryMember(String s, WechatRegisterRequest wechatRegisterRequest, String message) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("code","500");
        map.put("msg",message);
        map.put("unionId",wechatRegisterRequest.getUnionId());
        map.put("appId",wechatRegisterRequest.getAppId());
        map.put("openId",wechatRegisterRequest.getOpenId());
        if (StringUtils.isNotBlank(wechatRegisterRequest.getBindingExtProperties().get("memberSource").toString())){
            map.put("memberSource",wechatRegisterRequest.getBindingExtProperties().get("memberSource"));
        }
        map.put("channelType",wechatRegisterRequest.getChannelType());
        if (!"自然流量".equals(wechatRegisterRequest.getBindingExtProperties().get("memberSource").toString())){
            map.put("memberSourceDetail",wechatRegisterRequest.getBindingExtProperties().get("memberSourceDetail"));
        }
        map.put("gender",wechatRegisterRequest.getGender());
        if (StringUtils.isNotBlank(wechatRegisterRequest.getNickname())){
            map.put("nickname",wechatRegisterRequest.getNickname());
        }
        if (StringUtils.isNotBlank(wechatRegisterRequest.getBirthDay())){
            map.put("birthDay",wechatRegisterRequest.getBirthDay());
        }
        if (StringUtils.isNotBlank(wechatRegisterRequest.getBirthYear())){
            map.put("birthYear",wechatRegisterRequest.getBirthYear());
        }
        if (StringUtils.isNotBlank(wechatRegisterRequest.getCustomizedProperties().get("isKoMember").toString())){
            map.put("isKoMember",wechatRegisterRequest.getCustomizedProperties().get("isKoMember"));
        }
        map.put("mobile",wechatRegisterRequest.getMobile());
        map.put("appType",wechatRegisterRequest.getAppType());
        //map.put("cityName",wechatRegisterRequest.getCity());
        if (StringUtils.isNotBlank(wechatRegisterRequest.getEmail())){
            map.put("email",wechatRegisterRequest.getEmail());
        }
        map.put("registerTime",wechatRegisterRequest.getRegisterTime());
        if (StringUtils.isNotBlank(wechatRegisterRequest.getHeadImgUrl())){
            map.put("headImgUrl",wechatRegisterRequest.getHeadImgUrl());
        }
        map.put("appType",wechatRegisterRequest.getAppType());
        map.put("memberType",wechatRegisterRequest.getMemberType());
        //loyaltyMemberRepository.insert(ConfigurationCenterUtil.HISTORY_MEMBER_ERROR,map);
        DataApiUtil.insert(ConfigurationCenterUtil.HISTORY_MEMBER_ERROR, map);

    }

}
