package com.shuyun.kylin.customized.rpc.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 可口可乐权益 BK汉堡王发券响应
 */
@Data
public class BKResponseDto {

    private String code;

    private String msg;

    private String detail;

    private List<RspCode> codes;

    @Data
    public static class RspCode {
        //券码
        @JsonProperty("ticket_code")
        private String ticketCode;
        //券批次id
        @JsonProperty("ticket_id")
        private String ticketId;
        //券可用次数
        @JsonProperty("total_use_times")
        private String totalUseTimes;
    }


}
