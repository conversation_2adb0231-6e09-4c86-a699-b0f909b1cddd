package com.shuyun.kylin.customized.base.common;


import com.shuyun.kylin.customized.base.enums.ExceptionEnum;
import com.shuyun.kylin.customized.base.enums.ResponseCodeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/12/11
 */
@Getter
@Setter
public class ResponseResult<T> implements Serializable {

    private String code;
    //private String error_code;
    private String msg;
    private T data;

    public ResponseResult() {
    }

    public ResponseResult(String code, String message) {
        this.code = code;
        this.msg = message;
    }

    public ResponseResult(String code, String message, T data) {
        this.code = code;
        this.msg = message;
        this.data = data;
    }
    public ResponseResult( T data) {
        this.data = data;
    }

    public ResponseResult(ResponseCodeEnum replyCode, T data) {
        this.code = replyCode.getCode();
        //this.error_code = replyCode.getError_code();
        this.msg = replyCode.getMsg();
        this.data = data;
    }

    public ResponseResult(ResponseCodeEnum replyCode, String message) {
        this.code = replyCode.getCode();
        //this.error_code = replyCode.getError_code();
        this.msg = message != null ? message : replyCode.getMsg();
    }

    public ResponseResult(ExceptionEnum replyCode) {
        this.code = replyCode.getCode();
        this.msg = replyCode.getMsg();
    }

    public ResponseResult(ExceptionEnum replyCode, String message) {
        this.code = replyCode.getCode();
        this.msg = message;
    }

    public ResponseResult(ExceptionEnum replyCode, T data) {
        this.code = replyCode.getCode();
        this.msg = replyCode.getMsg();
        this.data = data;
    }

    public ResponseResult(ExceptionEnum replyCode, String message, T data) {
        this.code = replyCode.getCode();
        this.msg = message;
        this.data = data;
    }

    public ResponseResult(ResponseCodeEnum replyCode) {
        this.code = replyCode.getCode();
        this.msg = replyCode.getMsg();
    }

    public static <T> ResponseResult<T> response(ExceptionEnum responseCodeEnum, T data) {
        return new ResponseResult<T>(responseCodeEnum, data);
    }

    public static ResponseResult response(ExceptionEnum responseCodeEnum) {
        return new ResponseResult(responseCodeEnum);
    }

    public static <T> ResponseResult responseSuccess(String code, String message, T data) {
        return new ResponseResult(code, message, data);
    }

    public static <T> ResponseResult responseSuccess(String code, String message) {
        return new ResponseResult(code, message);
    }

    public static <T> ResponseResult responseSuccess(T data) {
        return new ResponseResult("200", "操作成功", data);
    }

    public static <T> ResponseResult responseError(String code, String message, T data) {
        return new ResponseResult(code, message, data);
    }

    public static <T> ResponseResult<T> response(ResponseCodeEnum responseCodeEnum, T data) {
        return new ResponseResult<T>(responseCodeEnum, data);
    }

    public static ResponseResult response(ResponseCodeEnum responseCodeEnum) {
        return new ResponseResult(responseCodeEnum);
    }
}