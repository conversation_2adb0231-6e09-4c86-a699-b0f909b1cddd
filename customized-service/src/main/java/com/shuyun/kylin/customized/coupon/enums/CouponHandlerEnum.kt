package com.shuyun.kylin.customized.coupon.enums

enum class CouponHandlerEnum(val handlerName : String) {

    OUT_PUT_CUSTOM("outPutCustomHandler"),
    BEFORE_SYN_COUPON("beforeSyncCouponHandler"),
    REAL_TIME_GENERATE_COUPON("realTimeGenerateCouponHandler");

}

enum class CouponSendStrategyEnum(val desc:String ,val strategyName:String){
    KKD("酷客多发券策略","kkdCouponSendStrategy"),
    OTHER("其他第三方发券策略","thirdCouponSendStrategy"),
}