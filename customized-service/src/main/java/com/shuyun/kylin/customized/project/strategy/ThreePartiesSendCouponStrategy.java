package com.shuyun.kylin.customized.project.strategy;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.kylin.customized.base.feign.dao.MemberBingDto;
import com.shuyun.kylin.customized.base.feign.dao.OfferProjectDetailClientResponse;
import com.shuyun.kylin.customized.base.stream.kafka.dao.CampaignActionOutputDto;
import com.shuyun.kylin.customized.base.util.DataapiSdkUtil;
import com.shuyun.kylin.customized.base.util.DateHelper;
import com.shuyun.kylin.customized.project.dto.SendCouponResultDto;
import com.shuyun.kylin.customized.project.request.GrantCouponRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class ThreePartiesSendCouponStrategy extends SendCouponStrategy {

    static final DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();

    @Override
    public SendCouponResultDto sendCoupon(String templateId, OfferProjectDetailClientResponse projectDetail, GrantCouponRequest grantCouponRequest, MemberBingDto memberBindInfo) {
        log.info("threeParties发券策略入参，templateId={}，projectDetail={}，grantCouponRequest={}", templateId, JSON.toJSONString(projectDetail), JSON.toJSONString(grantCouponRequest));
        // 1. 根据projectId和券状态查询三方券码模型 select id,externalCode from data.prctvmkt.KO.ExternalCouponCode where  projectId = projectId and status = '0' limit  1
        /*String sql = String.format("select id,externalCode from data.prctvmkt.KO.ExternalCouponCode where  projectId = '%s' and status = '0' limit  500", grantCouponRequest.getProjectId());
        log.info("策略ThreePartiesSendCouponStrategy.sql={}", sql);
        BaseResponse execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
        List<Map> data = execute.getData();
        //    如果未查到数据直接返回三方券码不足
        if(CollectionUtils.isEmpty(data)) {
            throw new RuntimeException("三方券码不足");
        }*/
        String id = null;
        String code = null;
        int maxAttempts = 300; // 最大尝试次数
        int attempts = 0;
        Boolean result = true;
        while (attempts < maxAttempts) {
            attempts++;
            log.info("while循环获取券码查询次数:{},:{}",attempts,memberBindInfo.getMemberId());

            //查询可用券码
            List<ConcurrentHashMap> data = saveExternalCouponCode(grantCouponRequest.getProjectId());
            if(CollectionUtils.isEmpty(data)) {
                result = false;
                log.info("跳出循环,三方券码不足");
                break;
            }
            // 打乱列表顺序
            Collections.shuffle(data);
            boolean foundValidCoupon = false;
            for (Map coupon : data) {
                log.info("随机获取一个券码:{},:{},:{}",JSON.toJSONString(coupon),coupon.get("externalCode"));
                if (upDateExternalCouponCode(coupon.get("id").toString()) == 0 ){
                    log.info("更新券码失败:{}",coupon.get("id"));  // 尝试下一次
                    continue;
                }
                id = coupon.get("id").toString();
                code = coupon.get("externalCode").toString();
                foundValidCoupon = true;
                break; // 成功后跳出循环
            }
            if (foundValidCoupon) {
                break; // 如果找到有效的券码，跳出 while 循环
            }
        }
        if (StringUtils.isBlank(id)){
            throw new RuntimeException("三方券码不足");
        }
        // 2. (该步骤由上层实现，这里不做处理,只记录)调用我们自己的产品券发放接口(把查到的三方券码externalCode 放入发放接口的扩展字段里面)、然后调用产品激活
        // 3. (该步骤由上层实现，这里不做处理,只记录)crm发券成功后修改三方券模型的这张券的状态为1=已匹配  update data.prctvmkt.KO.ExternalCouponCode  set status = '1' where  id=id
        log.info("更新冻结券成功id:{},externalCode:{}",id,code);
        return new SendCouponResultDto(result, id, code,null);
    }

    public static int upDateExternalCouponCode(String id) {
        String upSql = String.format("update data.prctvmkt.KO.ExternalCouponCode  set status = '2' , lastSync ='%s' where  id='%s' and status ='%s' ",
                DateHelper.getZone(DateHelper.getDateTimeFormat()), id,"0");
        BaseResponse upexecute = dataapiHttpSdk.execute(upSql, Collections.emptyMap());
        log.info("更新冻结券匹配状态sqlexternalCode:{},sql={},状态:{}", id,upSql,upexecute.getAffectedRows());
        return upexecute.getAffectedRows();
    }

    public static List<ConcurrentHashMap> saveExternalCouponCode(String projectId) {
        String sql = String.format("select id,externalCode from data.prctvmkt.KO.ExternalCouponCode where  projectId = '%s' and status = '0' limit  500", projectId);
        log.info("策略ThreePartiesSendCouponStrategy.sql={}", sql);
        BaseResponse execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
        return execute.getData();
    }
}
