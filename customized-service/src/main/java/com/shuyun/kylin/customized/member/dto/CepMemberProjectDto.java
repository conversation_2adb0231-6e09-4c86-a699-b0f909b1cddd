package com.shuyun.kylin.customized.member.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CepMemberProjectDto {

    @NotBlank(message = "券ID不能为空")
    private String projectId;
    @NotBlank(message = "券名称不能为空")
    private String projectName;
    @NotBlank(message = "券类型不能为空")
    private String projectType;
    @NotBlank(message = "券商不能为空")
    private String projectBusiness;
    @NotBlank(message = "券发放方式不能为空")
    private String projectGrantType;
    private String ruleDesc;
    private Integer stock;
    private String startTime;
    private String endTime;
    private String remarks;
    private String channel;
    private String created;
    private String lastSync;

}
