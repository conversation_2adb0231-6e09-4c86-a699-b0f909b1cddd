package com.shuyun.kylin.customized.spi.resource;

import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.member.dto.CepLbsMemberPointRecordDto;
import com.shuyun.kylin.customized.member.dto.CepMemberPointItemDto;
import com.shuyun.kylin.customized.member.service.ICepMemberService;
import com.shuyun.pip.component.json.JsonUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description CustomizedMemberLbsResource
 * @PACKAGE_NAME com.shuyun.kylin.customized.spi.resource
 * @date 2022/11/10 16:37
 */
@Tag(name = "定制注册lbs接口", description = "用于定制化 lbs   注册业务逻辑")
@RestController
@Slf4j
@RequestMapping("/lbs")
public class CustomizedMemberLbsResource {

    @Autowired
    ICepMemberService cepMemberService;


    /**
     * 变更会员积分(瓶子)
     *
     * @param cepLbsMemberPointRecordDto
     * @return
     */
    @PostMapping("/member/changePoint")
    public ResponseResult changeMemberScenePoint(@Validated @RequestBody CepLbsMemberPointRecordDto cepLbsMemberPointRecordDto){
        log.info("变更会员积分(瓶子)request:{}", JsonUtils.toJson(cepLbsMemberPointRecordDto));
        return cepMemberService.changeMemberScenePoint(cepLbsMemberPointRecordDto) ;
    }

    /**
     * 积分明细查询(瓶子)
     *
     * @return
     */
    @GetMapping("/member/point/records")
    public CepMemberPointItemDto getPointRecords(@RequestParam(value = "customerNo") String customerNo,
                                                     @RequestParam(value = "channelType") String channelType,
                                                     @RequestParam(value = "startTime", required = false) String startTime,
                                                     @RequestParam(value = "endTime", required = false) String endTime,
                                                     @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                     @RequestParam(value = "pageSize", required = false, defaultValue = "20") Integer pageSize) {

        log.info("积分明细查询请求入参customerNo: {},startTime:{},endTime:{},page:{},pageSize:{},channelType:{}", customerNo, startTime, endTime, page, pageSize,channelType);
        return cepMemberService.getPointRecords(customerNo, startTime, endTime, page, pageSize,channelType);
    }

}
