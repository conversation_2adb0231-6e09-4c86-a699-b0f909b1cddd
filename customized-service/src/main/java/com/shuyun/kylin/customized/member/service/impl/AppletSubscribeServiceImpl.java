package com.shuyun.kylin.customized.member.service.impl;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.stream.kafka.consumer.coupon.CustomizedCouponConsumer;
import com.shuyun.kylin.customized.base.stream.kafka.dao.NotificationCouponDto;
import com.shuyun.kylin.customized.member.dm.MemberRepository;
import com.shuyun.kylin.customized.member.service.AppletSubscribeService;
import com.shuyun.kylin.starter.redis.ICache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class AppletSubscribeServiceImpl implements AppletSubscribeService {

    @Autowired
    @Qualifier("redisCache")
    private ICache redisCache;

    @Autowired
    private MemberRepository memberRepository;

    @Autowired
    private CustomizedCouponConsumer customizedCouponConsumer;

    @Override
    public Map<String, String> sendAppletSubscriber(Map request) {

        Map<String, String> map = new HashMap<>();
        //更新该任务状态
        memberRepository.updateAppletTask(request.get("taskId").toString(), "new", "execution");
        NotificationCouponDto notificationCouponDto = new NotificationCouponDto();
        notificationCouponDto.setId(request.get("id").toString());
        notificationCouponDto.setTaskId(request.get("taskId").toString());
        notificationCouponDto.setTenant(request.get("tenant").toString());
        notificationCouponDto.setCampaignId(request.get("campaignId").toString());
        notificationCouponDto.setCampaignName(request.get("campaignName").toString());
        notificationCouponDto.setStatus(request.get("status").toString());
        notificationCouponDto.setProcessId(request.get("processId").toString());
        notificationCouponDto.setNodeId(request.get("nodeId").toString());
        notificationCouponDto.setNodeName(request.get("nodeName").toString());
        try {
                /*customRunnable runnable = new customRunnable(notificationCouponDto);
                new Thread(runnable,"asyncMainThread_"+notificationCouponDto.getTaskId()).start();*/
            customizedCouponConsumer.sendAppletSubscriberRunnable(notificationCouponDto);
        } catch (Exception e) {
            log.error("小程序批量接口报错:{}", e.getMessage());
        }
        map.put("taskId", notificationCouponDto.getTaskId());
        return map;
    }

    /*public class customRunnable implements Runnable {
        NotificationCouponDto notificationCouponDto;

        private customRunnable(NotificationCouponDto notificationCouponDto) {
            this.notificationCouponDto = notificationCouponDto;
        }

        @Override
        public void run() {
            try {
                customizedCouponConsumer.sendAppletSubscriberRunnable(notificationCouponDto);
            } catch (Exception e) {
                log.error("自定义子线程执行失败:{}", JSON.toJSONString(notificationCouponDto));
            }
        }
    }*/
}
