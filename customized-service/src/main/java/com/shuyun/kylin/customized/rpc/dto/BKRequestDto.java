package com.shuyun.kylin.customized.rpc.dto;

import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * BK汉堡王发券请求入参
 */
@Data
public class BKRequestDto {
    // 发券请求的唯一编号，同样的唯一编号，券码保持唯一，即：满足幂等性
    @JsonProperty("send_request_sn")
    @JSONField(name = "send_request_sn")
    private String sendRequestSn;

    //手机号
    @JsonProperty("mobile")
    @JSONField(name = "mobile")
    private String mobile;

    //用户身份ID
    @JsonProperty("openid")
    @JSONField(name = "openid")
    private String openId;

    //发码来源 需要汉堡王定义
    @JsonProperty("send_from")
    @JSONField(name = "send_from")
    private String sendFrom;

    //发码平台需要汉堡王定义
    @JsonProperty("platform_id")
    @JSONField(name = "platform_id")
    private String platformId;

    //单张券码信息
    @JsonProperty("code_info")
    @JSONField(name = "code_info")
    private List<CodeInfo> codeInfo;

    @JsonProperty("notify_info")
    @JSONField(name = "notify_info")
    private NotifyInfo notifyInfo;

    @Data
    public static class NotifyInfo {
        //电子券事件回调地址
        @JsonProperty("notify_url")
        @JSONField(name = "notify_url")
        private String notifyUrl;


    }
    @Data
    public static class CodeInfo {
        //费芮电子券ID
        @JsonProperty("ticket_id")
        @JSONField(name = "ticket_id")
        private String ticketId;
        //发放N张券
        @JsonProperty("send_num")
        @JSONField(name = "send_num")
        private Integer sendNum;
        //订单信息
        @JsonProperty("order_info")
        @JSONField(name = "order_info")
        private OrderInfo orderInfo;
    }

    @Data
    public static class OrderInfo {
        @JsonProperty("order_id")
        @JSONField(name = "order_id")
        private String orderId;
        @JsonProperty("original_amount")
        @JSONField(name = "original_amount")
        private String originalAmount;
        @JsonProperty("sale_amount")
        @JSONField(name = "sale_amount")
        private String saleAmount;
        @JsonProperty("merchant_cash_amount")
        @JSONField(name = "merchant_cash_amount")
        private String merchantCashAmount;
        @JsonProperty("user_payment")
        @JSONField(name = "user_payment")
        private String userPayment;

    }

}
