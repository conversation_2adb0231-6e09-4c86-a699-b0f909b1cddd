package com.shuyun.kylin.customized.base.stream.kafka.messaging;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.stream.annotation.Input;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @Description 此类定义Kafka的接受队列
 * @date 2019/5/15
 */
@Component
@ConditionalOnExpression("${system.kafka.enabled:true}")
public interface KafkaSink {
     String KAFKA_INPUT = "kafka_input"; // 对应application.yml中的 kafka_input
     @Input(KAFKA_INPUT)
    SubscribableChannel input();


    String KAFKA_INPUT_ADD_CHECKLIST = "kafka_input_add_checklist"; // 对应application.yml中的 kafka_input
    @Input(KAFKA_INPUT_ADD_CHECKLIST)
    SubscribableChannel addChecklist();

    String KAFKA_INPUT_BEYOND_POINT_ADD_CHECKLIST = "kafka_input_beyond_point_add_checklist"; // 对应application.yml中的 kafka_input
    @Input(KAFKA_INPUT_BEYOND_POINT_ADD_CHECKLIST)
    SubscribableChannel beyondPointAddChecklist();

    String KAFKA_INPUT_BEYOND_POINT_ADD_APPLETSUBSCRIBE = "kafka_input_beyond_point_add_appletsuBscribe"; // 对应application.yml中的 kafka_input
    @Input(KAFKA_INPUT_BEYOND_POINT_ADD_APPLETSUBSCRIBE)
    SubscribableChannel beyondAppletsuBscribe();


    String KAFKA_SEND_SWIRE_MEMBER_INPUT = "kafka_send_swire_member_input"; // 推送太古注册消费
    @Input(KAFKA_SEND_SWIRE_MEMBER_INPUT)
    SubscribableChannel sendSwireMemberInput();

    String KAFKA_UPDATE_SWIRE_MEMBER_INPUT = "kafka_update_swire_member_input"; // 推送太古更新消费
    @Input(KAFKA_UPDATE_SWIRE_MEMBER_INPUT)
    SubscribableChannel updateSwireMemberInput();

    String KAFKA_CUSTOM_MEMBER_COUPON_INPUT = "kafka_custom_member_coupon_input"; // 发券
    @Input(KAFKA_CUSTOM_MEMBER_COUPON_INPUT)
    SubscribableChannel snedCustomMemberCouponInput();


//    String KAFKA_MEDAL_PROGRESS_CAMPAIGN_INPUT = "kafka_medal_progress_campaign_input"; // 勋章等级进度计算_会员参与活动
//    @Input(KAFKA_MEDAL_PROGRESS_CAMPAIGN_INPUT)
//    SubscribableChannel medalProgressCampaignInput();
//
//
//
//    String KAFKA_MEDAL_PROGRESS_POINT_INPUT = "kafka_medal_progress_point_input"; // 勋章等级进度计算_累计积分
//    @Input(KAFKA_MEDAL_PROGRESS_POINT_INPUT)
//    SubscribableChannel medalProgressPointInput();
}
