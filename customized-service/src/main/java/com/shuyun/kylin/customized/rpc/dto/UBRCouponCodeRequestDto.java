package com.shuyun.kylin.customized.rpc.dto;

import lombok.Data;

@Data
public class UBRCouponCodeRequestDto {
    /**
     * 券核销所需的证件号码
     */
    private String govId;
    /**
     * 券核销所需的证件类型,为以下取值:
     * GovernmentId - 身份证
     * ChinaPR - 外国人永久居留证
     * TravelPermit - 港澳台居民来往内地通行证
     * ResidencePermit - 港澳台居民居住证
     * Passport - 护照;缺省为身份证
     */
    private String govIdType;
    /**
     * 客户姓名
     */
    private String guestName;
    /**
     * 券核销所需要的手机号码
     */
    private String mobile;
    /**
     * 幂等请求id
     */
    private String requestId;
    /**
     * 领券人信息
     */
    private UBRUserInfo userInfo;

    @Data
    public static class UBRUserInfo {
        /**
         * 手机号码
         */
        private String cellphone;
        /**
         * 电邮地址
         */
        private String email;
        /**
         * 名字
         */
        private String firstname;
        /**
         * 姓氏，如果无法区分姓名，可将姓名写入本字段中
         */
        private String lastname;
        /**
         * 外部系统用户ID
         */
        private String uid;

    }
}
