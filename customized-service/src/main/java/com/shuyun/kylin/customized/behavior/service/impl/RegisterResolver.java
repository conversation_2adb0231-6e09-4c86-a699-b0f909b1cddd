package com.shuyun.kylin.customized.behavior.service.impl;

import com.shuyun.kylin.customized.behavior.domain.Interactive;
import com.shuyun.kylin.customized.behavior.domain.InteractiveRecord;
import com.shuyun.kylin.customized.behavior.resource.po.CheckPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 注册送礼
 */
@Slf4j
@Service
public class RegisterResolver extends AbstractInteractionSceneResolver {

    private final String REPEAT_ERROR_INFO = "已获取过注册奖励";

    @Override
    public CheckPO.SceneType supportScene() {
        return CheckPO.SceneType.REGISTER;
    }

    @Override
    protected RewardInfo fetchPointInfo(CheckPO checkPO, String memberId, String mobile, Interactive interactive, InteractiveRecord lastRecord) {
        //如果不奖励积分，则直接返回0
        if (interactive.isRewardNothing()) {
            return RewardInfo.refuse("规则设置奖励积分为 0");
        }

        /* 规则：
         * 每个渠道会员只允许获取一次注册奖励
         * （允许注销后重新创建账号领取注册奖励）
         */
        if (lastRecord != null) {
            return RewardInfo.refuse(REPEAT_ERROR_INFO);
        }

        if (isOtherChannelPointRecord(interactive.getRewardsRecordScope(), checkPO, memberId)) {
            return RewardInfo.refuse(REPEAT_ERROR_INFO);
        }

        //根据配置发放奖励积分
        return RewardInfo.point(interactive.getPointNum());
    }
}
