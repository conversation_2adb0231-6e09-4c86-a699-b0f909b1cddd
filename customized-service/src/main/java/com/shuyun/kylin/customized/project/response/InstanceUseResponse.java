package com.shuyun.kylin.customized.project.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
public class InstanceUseResponse {
    /**
     * 返回code
     */
    private String code;

    /**
     * 返回信息
     */
    private String msg;

    /**
     * 权益处理结果状态(成功:SUCCESS)(待处理:WAITING)(处理中:HANDLING)(失败:FAIL)(重复:DUPLICATED)
     */
    private String status;

}
