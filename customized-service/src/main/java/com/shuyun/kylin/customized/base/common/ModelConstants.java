package com.shuyun.kylin.customized.base.common;

import com.shuyun.motor.common.cons.PropsUtil;

/**
 * <AUTHOR>
 * @date 2021/12/11
 */
public class ModelConstants {

    /**
     * 模型id, 应用场景: 根据id查询业务模型属性映射关系
     */
    public static final Integer MEMBER_MODEL_ID = 1;

    public static final String MEMBER_TYPE = "KO";

    /**
     * OpenAPI自动生成的模型
     */
    public static final String Member = "data.prctvmkt.KO.Member";

    /**
     * cosmos用户信息
     */
    public static final String COSMOS_CONSUMER = "data.prctvmkt.KO.Consumer";

    /**
     * omn
     */
    public static final String OMNICHANNER_CUSTOMER = "data.prctvmkt.OmniChannelCustomer";


    /**
     * cosmos用户信息
     */
    public static final String COSMOS_CONSUMERPLASTINFO = "data.prctvmkt.KO.ConsumerPlatInfo";

    /**
     * 互动/活动积分规则数据
     */
    public static final String INTERACTIVE_CAMPAIGN_POINT_RULE = "data.prctvmkt.KO.InteractiveAndCampaignPointsRule";


    /**
     * 可乐消费者模型
     */
    public static final String CUSTOMER = "data.prctvmkt.KO.Customer";

    /**
     * 自定义模型
     */
    public static final String CampaignPoints = "data.prctvmkt.KO.CampaignPoints";

    public static final String MEMBER_CONPON = "data.prctvmkt.KO.Coupon";

    public static final String WECHAT_COUPON_INFORMATION = "data.prctvmkt.KO.WeChatMerchantInformation";

    public static final String POINT_CDP_INTERFACLOG = "data.prctvmkt.KO.CdpInterfacelog";

    public static final String WSIRE_MEMBER_RECORDS = "data.prctvmkt.KO.UpdateMemberRecords";

    public static final String MEMBER_CONPONTOTAL = "data.prctvmkt.KO.CouponTotal";

    public static final String CONVERT_COSTCOENTER = "data.prctvmkt.KO.ConvertCostCenter";

    public static final String MEMBER_ISVALIDAGE = "data.prctvmkt.KO.isValidAgelog";

    public static final String MEMBER_MEMBERBINDING = "data.prctvmkt.KO.MemberBinding";

    public static final String MEMBER_MEMBERRIDENTIFY = "data.prctvmkt.KO.MemberIdentify";

    public static final String MEMBER_COUPONREMIND = "data.prctvmkt.KO.CouponExpireRemindRecord";

    public static final String MEMBER_PROJECT = "data.prctvmkt.KO.CouponDetail";

    public static final String COUPON_RESPONS = "data.prctvmkt.KO.CouponResponse";

    public static final String CAMPAIGN_SUBJECT = "data.prctvmkt.KO.Campaign";

    public static final String HISTORY_POINT = "data.prctvmkt.KO.HistoryPoint";

    public static final String CBL_MEMBERSYNC = "data.prctvmkt.KO.LbsMemberSync";

    public static final String CBL_GRADESYNC = "data.prctvmkt.KO.LbsGradeSync";

    public static final String CAMPAIGN_CAMPAIGNRECORDS = "data.prctvmkt.KO.CampaignRecords";

    public static final String CAMPAIGN_CAMPAIGNPOINTSRULE =  "data.prctvmkt.KO.CampaignPointsRule";

    public static final String CAMPAIGN_POINTSRULE = "data.prctvmkt.KO.CampaignPointsRule";

    public static final String CAMPAIGN_POINTEXCHANGE = "data.prctvmkt.KO.PointExchange";

    public static final String CAMPAIGN_SHOP = "data.prctvmkt.KO.Shop";

    public static final String CAMPAIGN_COSTCENTER = "data.prctvmkt.KO.CostCenterMappingRule";

    public static final String CAMPAIGN_SCENE_BINDINGLOG= "data.prctvmkt.KO.MemberSceneBindingLog";

    public static final String POINT_LBS_LOG= "data.prctvmkt.KO.Pointlbs";

    public static final String COSTCENTER = "data.prctvmkt.KO.CostCenter";

    public static final String LBS_COSTCENTER = "data.prctvmkt.KO.LbsOuMapping";

    public static final String ECPRODUCT = "data.prctvmkt.KO.EcProduct";

    public static final String KAILY_LIMITED_POINTSRULE = "data.prctvmkt.KO.DailyLimitedPointsRule";

    public static final String MEMBER_RULEPOINTRECORD = "data.prctvmkt.KO.RulePointRecord";

    public static final String MEMBER_EXCESSPOINTSLOG = "data.prctvmkt.KO.ExcessPointslog";

    public static final String SWIRE_MEMBER_EXPERIENEC = "data.prctvmkt.KO.SwireMemberExperience";

    public static final String MEMBER_POINT_DOBEYONDPOINTLOG = "data.prctvmkt.KO.GoBeyondPointlog";


    public static final String BLACKILST = "data.prctvmkt.KO.Blacklist";

    public static final String INTERACTIVE = "data.prctvmkt.KO.interactive";

    public static final String INTERACTIVERECORDS = "data.prctvmkt.KO.interactiveRecords";

    public static final String BIKPI = "data.prctvmkt.KO.BiKpi";

    public static final String MEMBER_LBS_TRAJECTORY = "data.prctvmkt.KO.MemberLbsTrajectory";

    public static final String POINT_BEHAVIOR_RECORD = "data.prctvmkt.KO.PointBehaviorRecord";

    public static final String POINT_BEHAVIOR = "data.prctvmkt.KO.PointRecord";

    public static final String ORIGIANLPOINTITEM = "data.loyalty.member.account.OriginalPointRecordItem";

    public static final String SUBSCRIPTIONS = "data.prctvmkt.KO.SubscriptionMessage";

    public static final String MEMBER_CANCEL = "data.prctvmkt.KO.Cancel";

    public static final String CONSUMER_CANCEL = "data.prctvmkt.KO.ConsumerCancel";

    public static final String MEMBER_SUBSCRIPTIONS = "data.prctvmkt.KO.MemberSubscribe";

    public static final String SHOP = "data.prctvmkt.KO.Shop";

    public static final String APPLETTASK = "data.prctvmkt.KO.AppletTask";

    public static final String COUPONYASK = "data.prctvmkt.KO.CouponTask";

    public static final String THRESHOLD = "data.prctvmkt.KO.threshold";

    public static final String CONFIG_LIST = "data.prctvmkt.KO.ConfigList";

    public static final String WARNING_LIST = "data.prctvmkt.KO.WarningList";

    public static final String POINT_INTERFACE_LIMITEDPOINTS = "data.prctvmkt.KO.InterfaceLimitedPointsRule";

    //其他权益获取记录
    public static final String OTHERBENEFITS = "data.prctvmkt.KO.OtherBenefits";

    //活动举办方
    public static final String CAMPAIGN_ORGANIZER = "data.prctvmkt.KO.Organizer";
    //会员积分获取记录
    public static final String MEMBER_ACTIVITY_POINT_RECORDS = "data.prctvmkt.KO.MemberActivityPointRecords";
    //会员同步太古注册记录
    public static final String SWIRE_MEMBER_SYNC_LOG = "data.prctvmkt.KO.SwireMemberSyncLog";
    //会员同步太古更新记录
    public static final String SWIRE_UPDATEMEMBEBER_SYNC_LOG = "data.prctvmkt.KO.SwireUpdateMemberSyncLog";
    //会员积分同步太古记录
    public static final String SWIRE_MEMBERPOINT_SYNC_LOG = "data.prctvmkt.KO.SwireRulePointRecordLog";
    //会员积分同步太古记录
    public static final String SWIRE_HIATORYPOINT_SYNC_LOG = "data.prctvmkt.KO.SwireHistoryPintLog";


    private static final String  pointAccountId= PropsUtil.getSysOrEnv("loyalty.pointAccountId", "60087");

    public static final String POINT= "data.loyalty.member.account.Point"+pointAccountId;

    private static final String  loyaltyGardGradeId= PropsUtil.getSysOrEnv("loyalty.cardGradeId", "60088");

    public static final String GRADE= "data.loyalty.member.hierarchy.Grade"+loyaltyGardGradeId;

    private static final String  loyaltyPointRecord= PropsUtil.getSysOrEnv("loyalty.loyaltyPointRecord", "60003");


    public static final String MEMBER_POINTRECORD= "data.loyalty.member.account.PointRecord"+loyaltyPointRecord;
    /**
     * 品牌
     */
    public static final String BRAND = PropsUtil.getSysOrEnv("cdp.brand", "1");
    /**
     * cosmos渠道信息
     */
    public static final String COSMOS_CHANNEL = PropsUtil.getSysOrEnv("cdp.cosmos.channle", "COSMOS");
    /**
     * CDP人群包生成任务相关配置
     */
    public static final String TAG_EXPORT_ADDRESS_TYPE = PropsUtil.getSysOrEnv("cdp.tag.export.address.type", "azureStorage");
    public static final String TAG_EXPORT_ADDRESS_CONNECTION = PropsUtil.getSysOrEnv("cdp.tag.export.address.connection", "");
    public static final String TAG_EXPORT_ADDRESS_CONTAIN_NAME = PropsUtil.getSysOrEnv("cdp.tag.export.address.containName", "taggingdata");
    public static final String TAG_EXPORT_ADDRESS_BASE_DIR = PropsUtil.getSysOrEnv("cdp.tag.export.address.baseDir", "");
    public static final String TAG_EXPORT_TASK_TYPE = PropsUtil.getSysOrEnv("cdp.tag.export.taskType", "FULL");
    public static final String TAG_EXPORT_EXPORT_TAGS_TYPE = PropsUtil.getSysOrEnv("cdp.tag.export.exportTagsType", "ALL");


    /**
     * 手工标签
     */
    public static final String TAG_TYPE_MANUAL = "MANUAL";
    /**
     * 规则标签
     */
    public static final String TAG_TYPE_WITHIN_RULE = "WITHIN_RULE";


    //会员参与活动信息
    public static final String MEMBER_CAMPAIGN = "data.prctvmkt.KO.MemberCampaign";

    //主体勋章
    public static final String MEMBER_MEDAL = PropsUtil.getSysOrEnv("loyalty.medal", "data.loyalty.member.hierarchy.Medal60001");

    //勋章主数据
    public static final String MEDAL_RULE = "data.prctvmkt.KO.MedalRule";

    //会员勋章任务进度
    public static final String MEDAL_TASK= "data.prctvmkt.KO.MedalTask";

    //积分明细
//    public static final String POINT_RECORD= "data.loyalty.member.account.PointRecord60003";

    //积分明细
    public static final String POINT_RECORD= "data.loyalty.member.account.PointRecord"+pointAccountId;

    public static final String CUSTOMIZEDTASK = "data.prctvmkt.KO.CustomizedTask";
}
