package com.shuyun.kylin.customized.rpc.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * 可口可乐权益 costa发券请求dto
 */
@Data
public class CostaSendCouponRequestDto {
    /**
     * 【必填】
     * 会员手机号码（AES加密）
     */
    @JSONField(name = "mix_phone")
    private String mixPhone;
    /**
     * 第三方账号Id，如微信openid，支付宝id等
     */
    @JSONField(name = "tp_account_id")
    private String tpAccountId;
    /**
     * 【必填】
     * 来源
     * *********  -可口可乐
     */
    @JSONField(name = "source")
    private Integer source;
    /**
     * 【必填】
     * 活动Code
     */
    @JSONField(name = "campaign_item_code")
    private String campaignItemCode;
    /**
     * 昵称
     */
    @JSONField(name = "nick_name")
    private String nickName;
    /**
     * 【必填】
     * 注册时间
     */
    @JSONField(name = "register_time")
    private Date registerTime;
    /**
     * 【必填】
     * 字母加数字随机字符,作为幂等字段
     */
    @JSONField(name = "rand_num")
    private String randNum;
}
