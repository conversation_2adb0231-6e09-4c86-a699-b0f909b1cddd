package com.shuyun.kylin.customized.base.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.util.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.TimeZone;

/**
 * 日期 工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class DateHelper {
    /**
     * The constant DATE_FORMAT.
     */
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String DATE_FORMAT_COMPACT = "yyyyMMdd";
    /**
     * The constant DATE_TIME_FORMAT.
     */
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_TIME_FORMAT_COMPACT = "yyyyMMddHHmmss";
    // 世界标准时间UTC
    public static final String DATE_FORMAT_T = "yyyy-MM-dd'T'HH:mm:ss";
    // 事件服务DateTime格式
    public static final String ES_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX";
    public static final String DATE_ZONE = "yyyy-MM-dd'T'HH:mm:ss.SSS";
    // 数据服务DateTime格式
    public static final String DS_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";


    /**
     * Parse date gracefully.
     *
     * @param date the date
     * @return the date
     */
    public static Date parseDateGracefully(String date) {
        if (!StringUtils.hasText(date)) {
            return null;
        }
        try {
            return DateUtils.parseDate(date, new String[]{
                    DATE_TIME_FORMAT,
                    DATE_FORMAT,
            });
        } catch (ParseException e) {
            throw new RuntimeException("date=" + date + "不是合法的日期");
        }
    }

    public static String formatZonedDateTime(Date date, String dateFormat) {
        if (StringUtils.isEmpty(dateFormat)) {
            dateFormat = DATE_FORMAT_T;
        }
        SimpleDateFormat df = new SimpleDateFormat(dateFormat);
        return df.format(date);
    }

    public static Date localToUTC(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(date.getTime());
        /** 取得时间偏移量 */
        int zoneOffset = calendar.get(java.util.Calendar.ZONE_OFFSET);
        /** 取得夏令时差 */
        int dstOffset = calendar.get(java.util.Calendar.DST_OFFSET);
        /** 从本地时间里扣除这些差量，即可以取得UTC时间*/
        calendar.add(java.util.Calendar.MILLISECOND, -(zoneOffset + dstOffset));
        /** 取得的时间就是UTC标准时间 */
        return new Date(calendar.getTimeInMillis());
    }

    /**
     * 获取0时区时间
     *
     * @param value
     * @return
     */
    public static String getZone1(String value) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DateFormat df = new SimpleDateFormat(DS_DATE_FORMAT);
        df.setTimeZone(TimeZone.getTimeZone("GMT"));
        try {
            return df.format(format.parse(value));
        } catch (ParseException e) {
            log.info("错误：", e);
        }
        return null;
    }

    /**
     * 获取0时区时间
     *
     * @param value
     * @return
     */
    public static String getZone(String value) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
        df.setTimeZone(TimeZone.getTimeZone("GMT"));
        try {
            return df.format(format.parse(value));
        } catch (ParseException e) {
            log.info("时间格式错误：", e);
        }
        return null;
    }

    /**
     * 获取当天时间
     *
     * @return
     */
    public static String getDateDay() {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String format = sdf.format(calendar.getTime());
        String a = format + " 00:00:00";
        return a;
    }

    public static String getEendTimeDay() {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String format = sdf.format(calendar.getTime());
        String a = format + " 23:59:59";
        return a;
    }

    /**
     * localDate格式化为yyyy-MM-dd
     * @return
     */
    public static String localDate(LocalDate localDate) {
        return localDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    /**
     * 获取当天时间
     *
     * @return
     */
    public static String getDateTimeFormat() {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format = sdf.format(calendar.getTime());
        return format;
    }

    /**
     * 获取当天时间到毫秒
     *
     * @return
     */
    public static String getDateMilliseconds() {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.sss");
        String format = sdf.format(calendar.getTime());
        return format;
    }


    public static String getDateToDay() {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String format = sdf.format(calendar.getTime());
        return format;
    }

    public static Date transferString2Date(String s) {
        Date date = new Date();
        try {
            date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(s);
        } catch (ParseException e) {
            //LOGGER.error("时间转换错误, string = {}", s, e);
        }
        return date;
    }


    /**
     * 获取当天时间
     *
     * @return
     */
    public static Date getDate() {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String format = sdf.format(calendar.getTime());
        String a = format + " 00:00:00";
        Date date = strToDateLong(a);
        return date;
    }

    /**
     * 获取当天时间
     *
     * @return
     */
    public static Date newDate() {
        String format = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        Date date = strToDateLong(format);
        return date;
    }
    /**
     * 获取当天时间
     *
     * @return
     */
    public static String getCepDate() {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        String format = sdf.format(calendar.getTime());
        return format;
    }
    /**
     * 获取当天时间
     *
     * @return
     */
    public static String getCepAllDate() {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        String format = sdf.format(calendar.getTime());
        return format;
    }

    /**
     * 获取明天时间
     *
     * @return
     */
    public static Date getDateTime() {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        calendar.add(GregorianCalendar.DATE, 1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String format = sdf.format(calendar.getTime());
        String a = format + " 00:00:00";
        Date date = strToDateLong(a);
        return date;
    }

    /**
     * 获取明天时间
     *
     * @return
     */
    public static String getTomorrowTime() {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        calendar.add(GregorianCalendar.DATE, 1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String format = sdf.format(calendar.getTime());
        String a = format + " 00:00:00";
        return a;
    }

    /**
     * 本月第一天
     */
    public static String getThisMonthDay() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        //获取前月的第一天
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        String firstDay = format.format(calendar.getTime());
        String a = firstDay + " 00:00:00";
        return a;
    }

    /**
     * 获取指定年月的最后一天
     *
     * @return
     */
    public static Date getLastDayOfMonth() {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.add(Calendar.YEAR, 1);
        cal.set(Calendar.YEAR, cal.get(Calendar.YEAR));
        //设置月份
        cal.set(Calendar.MONTH, cal.get(Calendar.MONTH));
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DATE);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String format = sdf.format(cal.getTime());
        String a = format + " 23:59:59";
        Date date = strToDateLong(a);
        return date;
    }
    public static String formatDateStr(String date,String format) throws ParseException {
        if (null == date) {
            return null;
        }
        SimpleDateFormat sdf=new SimpleDateFormat(format);
        SimpleDateFormat sdf1=new SimpleDateFormat(DATE_TIME_FORMAT);
        Date parse = sdf.parse(date);
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(parse);
       rightNow.add(Calendar.HOUR, 8);
        Date dt1=rightNow.getTime();
        return  sdf1.format(dt1);
    }
    /**
     * 获取指定年月的最后一天
     *
     * @return
     */
    public static String getDayOfMonth() {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.add(Calendar.YEAR, 1);
        cal.set(Calendar.YEAR, cal.get(Calendar.YEAR));
        //设置月份
        cal.set(Calendar.MONTH, cal.get(Calendar.MONTH));
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DATE);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String format = sdf.format(cal.getTime());
        String a = format + " 23:59:59";
        return a;
    }

    public static ZonedDateTime changeShanghaiToUTC(String beijingDateTimeStr){
        DateTimeFormatter beijingFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").withZone(ZoneId.of("Asia/Shanghai"));
        if(io.micrometer.core.instrument.util.StringUtils.isBlank(beijingDateTimeStr)){
            return null;
        }
        ZonedDateTime beijingDateTime = ZonedDateTime.parse(beijingDateTimeStr, beijingFormatter);
        return beijingDateTime.withZoneSameInstant(ZoneId.of("UTC"));
    }

    public static String getNowZone() {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return DateHelper.getZone(dateFormat.format(date));
    }

    public static String getNowZoneDay() {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        return DateHelper.getZone(dateFormat.format(date));
    }
    public static Date strToDateLong(String strDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ParsePosition pos = new ParsePosition(0);
        Date strtodate = formatter.parse(strDate, pos);
        return strtodate;
    }

    public static Date toDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static LocalDateTime toLocalDateTime(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }


    public static String transferString2Date1(String effectDate) {
        // 定义输入日期时间格式
        DateTimeFormatter inputFormatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
        // 解析输入字符串为 LocalDateTime 对象
        LocalDateTime dateTime = LocalDateTime.parse(effectDate, inputFormatter);
        // 定义输出日期时间格式
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 格式化 LocalDateTime 对象为输出字符串
        return dateTime.format(outputFormatter);
    }

    /**
     * @Auther zhangzengzeng
     * @Description  加小时
     * @Date 2021/8/30 13:53
     * @param date:
     * @return void
     **/
    public static Date addHours(Date date,int amount){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR,amount);
        return calendar.getTime();
    }
}
