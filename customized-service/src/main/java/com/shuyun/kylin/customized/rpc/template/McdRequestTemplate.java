package com.shuyun.kylin.customized.rpc.template;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.cbl.utils.MD5Util;
import com.shuyun.kylin.customized.project.dto.McdCallbackRequestDto;
import com.shuyun.kylin.customized.project.strategy.router.SendCouponEnum;
import com.shuyun.kylin.customized.rpc.dto.McdSendCouponRequestDto;
import com.shuyun.kylin.customized.rpc.dto.McdSendCouponResponseDto;
import com.shuyun.kylin.customized.rpc.entity.McdConfigEntity;
import com.shuyun.kylin.customized.rpc.enums.McdErrorResponseEnum;
import com.shuyun.kylin.customized.rpc.exception.KoOutRequestException;
import com.shuyun.kylin.customized.rpc.template.base.KoRequestTemplate;
import com.shuyun.motor.common.cons.PropsUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * 麦当劳rpc请求
 *
 * @author: Jingwei
 * @date: 2025-01-08
 */
@Slf4j
public class McdRequestTemplate extends KoRequestTemplate {

    static final McdConfigEntity mcdConfig = JSON.parseObject(PropsUtil.getSysOrEnv("ko.mcd.api.config"), McdConfigEntity.class);

    private static final String SEND_COUPON_URL = "/coupon/customer/bind";

    public static McdSendCouponResponseDto sendCoupon(McdSendCouponRequestDto requestDto, String transactionId) {
        String timestamp = Long.toString(System.currentTimeMillis());
        log.info("mcd发券请求入参，requestDto={},transactionId={},timestamp={}", JSON.toJSONString(requestDto), transactionId, timestamp);
        String sendCouponUrl = mcdConfig.getBaseUrl().concat(SEND_COUPON_URL);
        try {
            String responseBodyStr = HttpUtil.createPost(sendCouponUrl)
                    .header("AppId", mcdConfig.getAppId())
                    .header("MerchantId", mcdConfig.getMerchantId())
                    .header("Timestamp", timestamp)
                    .header("Sign", getSign(requestDto, timestamp))
                    .header("Version", mcdConfig.getApiVersion())
                    .header("TraceId", transactionId)
                    .body(JSON.toJSONString(requestDto))
                    .execute().body();
            log.info("mcd发券请求原始结果，body={}", responseBodyStr);
            pushKoRpcRequestLog(sendCouponUrl, requestDto, responseBodyStr, transactionId, SendCouponEnum.MCD.getCode());
            if (ObjectUtil.isEmpty(responseBodyStr)) {
                throw new KoOutRequestException("mcd response is empty!");
            }
            McdSendCouponResponseDto mcdSendCouponResponseDto = JSON.parseObject(responseBodyStr, McdSendCouponResponseDto.class);
            if (ObjectUtil.notEqual(McdErrorResponseEnum.SUCCESS.getCode(), mcdSendCouponResponseDto.getCode())) {
                log.info("mcd发券请求失败，code={},msg={}", mcdSendCouponResponseDto.getCode(), mcdSendCouponResponseDto.getMsg());
                McdErrorResponseEnum mcdErrorResponseEnum = McdErrorResponseEnum.getByMsg(mcdSendCouponResponseDto.getMsg());
                throw new KoOutRequestException(ObjectUtil.isNotNull(mcdErrorResponseEnum) ? mcdErrorResponseEnum.getDesc() : "mcd response code is not success!");
            }
            McdSendCouponResponseDto.RspData data = mcdSendCouponResponseDto.getData();
            if (ObjectUtil.isNull(data)) {
                log.info("mcd券码返回空，data={}", JSON.toJSONString(data));
                throw new KoOutRequestException("mcd response data is empty!");
            }
            List<McdSendCouponResponseDto.RspData.Coupon> coupons = data.getCoupons();
            if (CollectionUtils.isEmpty(coupons)
                    || ObjectUtil.isNull(coupons.get(0))
                    || CollectionUtils.isEmpty(coupons.get(0).getCouponCodes())
                    || ObjectUtil.isEmpty(coupons.get(0).getCouponCodes().get(0))
                    || ObjectUtil.isEmpty(coupons.get(0).getCouponCodes().get(0).getCouponCode())) {
                log.info("mcd券码返回空，coupons={}", JSON.toJSONString(coupons));
                throw new KoOutRequestException("mcd response coupons is empty!");
            }
            return mcdSendCouponResponseDto;
        } catch (KoOutRequestException koExp) {
            log.error("mcd请求异常,koExp:{}", koExp.getMessage());
            throw new KoOutRequestException(koExp.getMessage());
        } catch (Exception e) {
            log.error("mcd请求异常,e:{}", e.getMessage());
            pushKoRpcRequestLog(sendCouponUrl, requestDto, e.getMessage(), transactionId, SendCouponEnum.MCD.getCode());
            throw new KoOutRequestException("内部错误，mcd请求异常");
        }
    }

    /**
     * 获取签名
     *
     * @param requestDto 请求入参
     * @param timestamp  时间戳，与请求header中的时间戳公用
     * @return sign
     */
    private static String getSign(McdSendCouponRequestDto requestDto, String timestamp) {
        String signOrigin = String.format("AppId=%s&Body=%s&MerchantId=%s&Timestamp=%s&key=%s", mcdConfig.getAppId(),
                JSON.toJSONString(requestDto), mcdConfig.getMerchantId(), timestamp, mcdConfig.getKey());
        return MD5Util.encrypt(signOrigin).toUpperCase();
    }

    public static String getCallbackSign(McdCallbackRequestDto mcdCallbackRequestDto) {
        StringBuilder sb = new StringBuilder();
        Map<String, Object> requestMap = BeanUtil.beanToMap(mcdCallbackRequestDto, false, true);
        TreeMap<Object, Object> sortMap = new TreeMap<>(requestMap);
        for (Map.Entry<Object, Object> entry : sortMap.entrySet()) {
            if("Sign".equals(entry.getKey()) || "sign".equals(entry.getKey()) || entry.getValue() == null) {
                continue;
            }
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        // 去除sb最后一个&符号
        sb.deleteCharAt(sb.length() - 1);
        sb.append(mcdConfig.getKey());
        log.info("mcdCallback签名原始参数，sb={}", sb);
        String sign = MD5.create().digestHex(sb.toString());
        log.info("mcdCallback签名，sign={}", sign);
        return sign;
    }
}
