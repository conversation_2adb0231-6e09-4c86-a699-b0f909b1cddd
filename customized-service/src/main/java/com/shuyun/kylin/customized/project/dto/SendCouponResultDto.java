package com.shuyun.kylin.customized.project.dto;

import lombok.Data;

@Data
public class SendCouponResultDto {
    public SendCouponResultDto() {}

    public SendCouponResultDto(Boolean result, String externalCouponId, String externalCode, String couponCode) {
        this.result = result;
        this.externalCouponId = externalCouponId;
        this.externalCode = externalCode;
        this.couponCode = couponCode;
    }

    public SendCouponResultDto(Boolean result, String externalCouponId, String externalCode, String couponCode,String code,String msg) {
        this.result = result;
        this.externalCouponId = externalCouponId;
        this.externalCode = externalCode;
        this.couponCode = couponCode;
        this.msg = msg;
        this.code = code;
    }

    /**
     * 发券结果
     */
    private Boolean result;
    /**
     * 外部券code
     */
    private String externalCode;
    /**
     * 外部券ID
     */
    private String externalCouponId;

    /**
     * 内部券码
     */
    private String couponCode;

    /**
     * 错误描述
     */
    private String msg;

    /**
     * 错误code
     */
    private String code;
}
