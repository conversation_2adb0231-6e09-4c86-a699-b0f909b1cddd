package com.shuyun.kylin.customized.project.callback;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.project.dto.WechatCallbackDto;
import com.shuyun.kylin.customized.project.service.SendCouponService;
import com.shuyun.kylin.customized.rpc.dto.WxSendCouponResponseDto;
import com.shuyun.kylin.customized.rpc.template.WxRequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;

@Slf4j
@RestController
@RequestMapping("/weChat")
public class WeChatCallbackController {

    @Resource
    private SendCouponService sendCouponService;

    @RequestMapping("/sendCouponCallback")
    public ResponseResult<String> sendCouponCallback(@RequestBody WechatCallbackDto weChatCallbackDto) {
        log.info("微信核销回调接口入参，weChatCallbackDto:{}", JSON.toJSONString(weChatCallbackDto));
        return sendCouponService.weChatCallback(weChatCallbackDto);
    }

    @RequestMapping("/{merchantId}/sendCouponCallback")
    public ResponseResult<String> couponCallback(@RequestBody WechatCallbackDto weChatCallbackDto,@PathVariable("merchantId") String merchantId) {
        log.info("微信核销回调入参couponCallback.....merchantId:{},weChatCallbackDto:{}",merchantId, JSON.toJSONString(weChatCallbackDto));
        return sendCouponService.weChatCouponCallback(merchantId,weChatCallbackDto);
    }

    @RequestMapping("/callbackUrl")
    public ResponseResult<String> sendCouponCallbackUrl(@RequestBody HashMap<String,Object> map) {
        log.info("设置微信核销回调接口入参url，weChatCallbackDto:{}", JSON.toJSONString(map));
        return sendCouponService.weChatCallbackUrl(map);
    }


}
