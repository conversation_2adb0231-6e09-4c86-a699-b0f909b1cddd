package com.shuyun.kylin.customized.coupon.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum CouponTypeEnum {

    BEFORE_SYN_COUPON("预先生成券","BEFORE_SYN_COUPON"),//同步人券关系
    OUT_PUT_CUSTOM("只提交需要发送的客户信息", "OUT_PUT_CUSTOM"),//同步活动人群
    REAL_TIME_GENERATE_COUPON("实时生成券", "REAL_TIME_GENERATE_COUPON");//获取卡券后同步人券关系

    private String name;
    private String value;
    public static CouponTypeEnum getEnumByValue(String value) {
        if (values() != null && values().length > 0 && null != value) {
            for (CouponTypeEnum en : values()) {
                if (en.value.equals(value)) {
                    return en;
                }
            }
        }
        return null;
    }

    public static CouponTypeEnum getEnumByName(String name) {
        if (values() != null && values().length > 0 && null != name) {
            for (CouponTypeEnum en : values()) {
                if (en.name.equals(name)) {
                    return en;
                }
            }
        }
        return null;
    }

    public static String getValueByName(String name) {
        if (values() != null && values().length > 0 && null != name) {
            for (CouponTypeEnum en : values()) {
                if (en.name.equals(name)) {
                    return en.value;
                }
            }
        }
        return null;
    }
}
