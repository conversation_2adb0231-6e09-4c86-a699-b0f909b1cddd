package com.shuyun.kylin.customized.base.util;

import org.joda.time.DateTime;

import java.text.SimpleDateFormat;
import java.lang.*;
import java.util.UUID;

public class GenerateCode {

    public static String generateCode() {
        int machineId = 1;
        int hashCodeV = UUID.randomUUID().toString().hashCode();
        if(hashCodeV < 0) {
            hashCodeV = - hashCodeV; }
        System.out.println(machineId+ String.format("%015d", hashCodeV));
        return  machineId+ String.format("%015d", hashCodeV);
    }

    public static  String generatecouponcode(){
//        生成券码  data数据  时间 + 用户id + 优惠券所属商品/店铺id + 随机码
        DateTime dateTime  = DateTime.now();
        String formateDate = "yyy-MM-dd HH:mm:ss";
        SimpleDateFormat sdf = new SimpleDateFormat(formateDate);
        System.out.println(sdf);
        return sdf.toString();
    }

    public static void main(String [] args){
        generateCode();
        generatecouponcode();
    }
}
