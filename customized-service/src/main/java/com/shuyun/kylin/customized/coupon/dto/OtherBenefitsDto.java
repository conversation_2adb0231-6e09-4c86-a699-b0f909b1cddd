package com.shuyun.kylin.customized.coupon.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @description OtherBenefitsDto
 * @PACKAGE_NAME com.shuyun.kylin.customized.coupon.dto
 * @date 2022/11/1 11:22
 */
@Data
public class OtherBenefitsDto {

    //流水ID，非空且唯一
    @NotBlank(message = "流水ID不能为空")
    private String transactionId;

    //凭证ID(能和积分凭证关联)
    @NotBlank(message = "凭证ID不能为空")
    private String businessId;

    private String memberId;

    //渠道ID
    private String customerNo;

    //权益编号
    private String benefitCode;

    //权益名称
    private String benefitName;

    //权益类型 枚举： 实物商品，虚拟权益
    private String benefitType;

    //获得时间
    private String getTime;

    //渠道
    private String getChannel;

    //对应活动/互动ID
    private String campaignId;

    private Map member;

    private Map campaign;

}
