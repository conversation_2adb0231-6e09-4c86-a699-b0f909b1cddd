package com.shuyun.kylin.customized.rpc.template;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.project.strategy.router.SendCouponEnum;
import com.shuyun.kylin.customized.rpc.dto.CostaSendCouponRequestDto;
import com.shuyun.kylin.customized.rpc.dto.CostaSendCouponResponseDto;
import com.shuyun.kylin.customized.rpc.entity.CostaConfigEntity;
import com.shuyun.kylin.customized.rpc.template.base.KoRequestTemplate;
import com.shuyun.motor.common.cons.PropsUtil;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * costa.url=https://uat-crm-api.costa.net.cn/tp
 * costa.secret=costa&secret
 * costa.appId=costatp
 * costa.aeskey=681cb8c0388c4615
 * costa.aesiv=9e39ae6dc8f14c3b
 */
@Slf4j
public class CostaRequestTemplate extends KoRequestTemplate {

    static CostaConfigEntity costaConfig = JSON.parseObject(PropsUtil.getSysOrEnv("costa.api.config"), CostaConfigEntity.class);

    private static final String SEND_COUPON_URL_TEMPLATE = "/api/%s/member/sendcoupon?channel=%s";

    public static Map<String, String> pathMap() {
        Map<String, String> requestPathMap = new HashMap<>();
        requestPathMap.put("channel", costaConfig.getChannel());
        return requestPathMap;
    }

    public static Map<String, String> getHeader() {
        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("appid", costaConfig.getAppId());
        return requestHeader;
    }

    public static String getSignature(String requestBody) {
        Map<String, String> requestHeader = getHeader();
        Map<String, String> requestPathMap = pathMap();
        List<String> keyList = new ArrayList<>();
        List<String> valueList = new ArrayList<>();
        requestHeader.forEach((key, value) -> {
            keyList.add(key);
            valueList.add(value);
        });
        requestPathMap.forEach((key, value) -> {
            keyList.add(key);
            valueList.add(value);
        });
        Collections.sort(keyList);
        Collections.sort(valueList);
        String keyParams = String.join(CharSequenceUtil.EMPTY, keyList);
        String valueParams = String.join(CharSequenceUtil.EMPTY, valueList);
        return costaConfig.getSecret().concat(keyParams).concat(valueParams).concat(requestBody).concat(costaConfig.getSecret());
    }

    public static CostaSendCouponResponseDto sendCoupon(CostaSendCouponRequestDto sendCouponRequestDto, String transactionId) {
        log.info("costa-template请求入参，sendCouponRequestDto={}", JSON.toJSONString(sendCouponRequestDto));
        String requestUrl = costaConfig.getEndpoint().concat(String.format(SEND_COUPON_URL_TEMPLATE, costaConfig.getVersion(), costaConfig.getChannel()));
        try {
            AES aes = new AES(Mode.CBC, Padding.PKCS5Padding, costaConfig.getAesKey().getBytes(), costaConfig.getAesIv().getBytes());
            log.info("costa手机加密前，phone={}", sendCouponRequestDto.getMixPhone());
            String mixPhone = aes.encryptBase64(sendCouponRequestDto.getMixPhone(), StandardCharsets.UTF_8);
            sendCouponRequestDto.setMixPhone(mixPhone);
            String jsonString = JSON.toJSONString(sendCouponRequestDto);
            String originSignParam = getSignature(jsonString);
            log.info("costa调用-签名原始参数，originSignParam={}", originSignParam);
            String md5Sign = MD5.create().digestHex(originSignParam);
            log.info("costa调用-签名结果，md5Sign={}", md5Sign);
            String body = HttpUtil.createPost(requestUrl)
                    .header("sign", md5Sign)
                    .header("appId", costaConfig.getAppId())
                    .body(jsonString)
                    .execute().body();
            // 推送日志监听
            pushKoRpcRequestLog(requestUrl, sendCouponRequestDto, body, transactionId, SendCouponEnum.COSTA_SOUTH.getCode());
            log.info("costa调用-返回结果，body={}", body);
            return JSON.parseObject(body, CostaSendCouponResponseDto.class);
        } catch (Exception e) {
            log.error("costa调用-异常", e);
            // 推送日志监听
            pushKoRpcRequestLog(requestUrl, sendCouponRequestDto, e.getMessage(), transactionId, SendCouponEnum.COSTA_SOUTH.getCode());
            CostaSendCouponResponseDto costaSendCouponResponseDto = new CostaSendCouponResponseDto();
            costaSendCouponResponseDto.setStatus(-1);
            costaSendCouponResponseDto.setMessage(e.getMessage());
            return costaSendCouponResponseDto;
        }
    }
}
