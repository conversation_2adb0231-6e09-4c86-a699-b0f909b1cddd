package com.shuyun.kylin.customized.member.resource;

import com.alibaba.fastjson.JSON;
import com.shuyun.epassport.sdk.register.RequiresPermissions;
import com.shuyun.kylin.customized.member.dm.MemberRepository;
import com.shuyun.kylin.customized.member.service.AppletSubscribeService;
import com.shuyun.kylin.customized.member.service.MemberCouponsService;
import com.shuyun.kylin.starter.swagger.annotation.WebApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/custom")
public class sendCouponsRecource {

    @Autowired
    private MemberRepository memberRepository;

    @Autowired
    private MemberCouponsService memberCouponsService;


    /**
     * 触发批量  优惠券发送
     *
     * @param request
     * @return
     */
    @PostMapping("/coupon")
    public Map<String, String> customSendCoupons(@RequestBody Map request){
        log.info("批量优惠券请求入参: {}", JSON.toJSONString(request));
        Map<String, String> map = new HashMap<>();
        String keys = "customSendCoupons"+request.get("taskId").toString();
        //查询状态
        String state = memberRepository.getCouponsTask(request.get("taskId").toString());
        if (!"new".equals(state)){
            map.put("taskId",keys);
            map.put("state",state);
            //map.put("id",keys);
            return map;
        }
        return memberCouponsService.customSendCoupons(request);
    }

}
