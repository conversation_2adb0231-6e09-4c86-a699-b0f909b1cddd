package com.shuyun.kylin.customized.member.service;

import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.member.dto.*;
import com.shuyun.kylin.customized.member.dto.ec.EcNormalOrderDto;
import com.shuyun.kylin.customized.member.dto.ec.EcProductDto;
import com.shuyun.kylin.customized.member.dto.ec.EcRefundOrderDto;

public interface ICepDataSyncService {

    ResponseResult memberCouponSave(CepMemberCouponDto cepMemberCouponDto);

    ResponseResult couponCallbackResults(CepCouponCallbackDto cepCouponCallbackDto);

    ResponseResult memberProjectSave(CepMemberProjectDto cepMemberProjectDto);

    ResponseResult memberPointSave(CepMemberPointDto cepMemberPointDto);

    ResponseResult appletTemplateSave(CepAppletTemplateDto cepAppletTemplateDto);

    ResponseResult saveMemberHistoryPoint(CepHistoryPointDto cepHistoryPointDto);

    ResponseResult memberSubscriptionSave(CepMemberSubscriptionDto cepMemberSubscriptionDto);

    ResponseResult campaingSubjectSave(CepCampaignSubjectDto cepCampaignSubjectDto);

    ResponseResult memberCampaignsSave(CepMemberCampaignsDto cepMemberCampaignsDto);

    ResponseResult saveCounversionPoint(CepConversionPointDto cepConversionPointDto);


    ResponseResult saveSyncShop(EcSyncShopDto ecSyncShopDto);

    ResponseResult updateSyncProduct(EcProductDto ecProductDto);

    ResponseResult saveInteractive(InteractiveDto interactiveDto);

    ResponseResult saveInteractiveRecords(InteractiveRecordsDto interactiveRecordsDto);

    ResponseResult updateSyncOrder(EcNormalOrderDto ecNormalOrderDto);

    ResponseResult updateSyncRefundOrder(EcRefundOrderDto ecRefundOrderDto);

    ResponseResult saveBikpi(BiKpiDto biKpiDto);

    ResponseResult savePosition(MemberLbsTrajectoryDto memberLbsTrajectoryDto,String Type);
}
