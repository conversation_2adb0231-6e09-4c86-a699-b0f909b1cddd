package com.shuyun.kylin.customized.member.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.dm.api.response.DMLResponse;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.enums.ResponseCodeEnum;
import com.shuyun.kylin.customized.base.feign.client.OrderSaveClient;
import com.shuyun.kylin.customized.base.feign.dao.MemberOrderDao;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.base.util.DataApiUtil;
import com.shuyun.kylin.customized.base.util.DateHelper;
import com.shuyun.kylin.customized.member.dao.CouponResponseDao;
import com.shuyun.kylin.customized.member.dm.*;
import com.shuyun.kylin.customized.member.dto.*;
import com.shuyun.kylin.customized.member.dto.ec.*;
import com.shuyun.kylin.customized.member.service.ICepDataSyncService;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.time.ZonedDateTime;
import java.util.*;

@Slf4j
@Service
public class CepDataSyncServiceImpl implements ICepDataSyncService {


    @Autowired
    private MemberRepository memberRepository;

    @Autowired
    private InteractiveRepository interactiveRepository;
    @Autowired
    private InteractiveRecordsRepository interactiveRecordsRepository;

    @Autowired
    private CepCostCenterRepository cepCostCenterRepository;

    @Autowired
    private OrderSaveClient orderClient;

    @Autowired
    private MemberLbsTrajectoryRepository memberLbsTrajectoryRepository;

    @Override
    public ResponseResult memberCouponSave(CepMemberCouponDto cepMemberCouponDto) {
        //查询会员id
        String memberId = memberRepository.queryMemberId(cepMemberCouponDto.getCustomerNo());
        if (null == cepMemberCouponDto.getIsSendCardsOffers()) {
            cepMemberCouponDto.setIsSendCardsOffers(false);
        }
        cepMemberCouponDto.setGrantTime(StringUtils.isNotEmpty(cepMemberCouponDto.getGrantTime()) ? DateHelper.getZone(cepMemberCouponDto.getGrantTime()) : null);
        cepMemberCouponDto.setExpireTime(StringUtils.isNotEmpty(cepMemberCouponDto.getExpireTime()) ? DateHelper.getZone(cepMemberCouponDto.getExpireTime()) : null);
        cepMemberCouponDto.setUseTime(StringUtils.isNotEmpty(cepMemberCouponDto.getUseTime()) ? DateHelper.getZone(cepMemberCouponDto.getUseTime()) : null);
        cepMemberCouponDto.setLastSync(DateHelper.getNowZone());
        HashMap<Object, Object> camepaignMap = new HashMap<>();
        camepaignMap.put("id", cepMemberCouponDto.getCampaignId());
        cepMemberCouponDto.setCampaign(camepaignMap);
        try {
            if (StringUtils.isNotBlank(memberId)) {
                HashMap<Object, Object> memberPro = new HashMap<>();
                memberPro.put("id", cepMemberCouponDto.getProjectId());
                cepMemberCouponDto.setProject(memberPro);
                HashMap<Object, Object> send = new HashMap<>();
                send.put("id", cepMemberCouponDto.getSendCostCenterCode());
                cepMemberCouponDto.setSendCostCenter(send);
                HashMap<Object, Object> usedMap = new HashMap<>();
                usedMap.put("id", cepMemberCouponDto.getUsedCostCenterCode());
                cepMemberCouponDto.setUsedCostCenter(usedMap);
                cepMemberCouponDto.setMemberIDStr(memberId);
                //封装对象关系
                HashMap<Object, Object> memberCoup = new HashMap<>();
                memberCoup.put("id", memberId);
                cepMemberCouponDto.setMember(memberCoup);
                cepMemberCouponDto.setProjectId(null);
                DMLResponse response = DataApiUtil.upsertIsData(ModelConstants.MEMBER_CONPON, cepMemberCouponDto.getId(), JSONObject.parseObject(JSON.toJSONString(cepMemberCouponDto)));
                if (!response.getIsSuccess()) {
                    log.error("会员人券关系保存Coupon:{}",response.getOperation());
                    return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, response.getOperation());
                }
            }
            cepMemberCouponDto.setProjectId(null);
            cepMemberCouponDto.setUsedCostCenter(null);
            cepMemberCouponDto.setProject(null);
            cepMemberCouponDto.setMember(null);
            cepMemberCouponDto.setSendCostCenter(null);
            DMLResponse res = DataApiUtil.upsertIsData(ModelConstants.MEMBER_CONPONTOTAL, cepMemberCouponDto.getId(), JSONObject.parseObject(JSON.toJSONString(cepMemberCouponDto)));
            if (!res.getIsSuccess()) {
                log.error("会员人券关系保存CouponDetail:{}",res.getOperation());
                return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, res.getOperation());
            }
        } catch (Exception e) {
            log.error("会员人券关系保存e:{}",e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED);
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }

    @Override
    public ResponseResult memberProjectSave(CepMemberProjectDto cepMemberProjectDto) {
        try {
            cepMemberProjectDto.setStartTime(StringUtils.isNotEmpty(cepMemberProjectDto.getStartTime()) ? DateHelper.getZone(cepMemberProjectDto.getStartTime()) : null);
            cepMemberProjectDto.setEndTime(StringUtils.isNotEmpty(cepMemberProjectDto.getEndTime()) ? DateHelper.getZone(cepMemberProjectDto.getEndTime()) : null);
            cepMemberProjectDto.setLastSync(DateHelper.getNowZone());
            cepMemberProjectDto.setCreated(DateHelper.getNowZone());
            DMLResponse response = DataApiUtil.upsertIsData(ModelConstants.MEMBER_PROJECT, cepMemberProjectDto.getProjectId(), JSONObject.parseObject(JSON.toJSONString(cepMemberProjectDto)));
            if (!response.getIsSuccess()) {
                log.error("优惠券主信息保存:{}",response.getOperation());
                return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, response.getOperation());
            }
        } catch (Exception e) {
            log.error("优惠券主信息保存e:{}",e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED);
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }

    @Override
    public ResponseResult couponCallbackResults(CepCouponCallbackDto cepCouponCallbackDto) {
        //查询会员id
        String memberId = memberRepository.queryMemberId(cepCouponCallbackDto.getUid());
        if (CollectionUtils.isEmpty(cepCouponCallbackDto.getExtra())) {
            return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, "extra回传字段不能为空");
        }
        CouponResponseDao couponResponseDao = cepCouponCallbackDto.getCouponResponseDao();
        cepCouponCallbackDto.setCreated(DateHelper.getNowZone());
        cepCouponCallbackDto.setLastSync(DateHelper.getNowZone());
        cepCouponCallbackDto.setMemberId(memberId);
        String upperCase = DigestUtils.md5Hex(cepCouponCallbackDto.getUid() + cepCouponCallbackDto.getProjectId()).toUpperCase();
        try {
            DMLResponse response = DataApiUtil.upsertIsData(ModelConstants.COUPON_RESPONS, upperCase, JSONObject.parseObject(JSON.toJSONString(couponResponseDao)));
            if (!response.getIsSuccess()) {
                log.error("优惠券发放结果回执保存:{}",response.getOperation());
                return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, response.getOperation());
            }
            //cepCouponCallbackRepository.insert(ModelConstants.COUPON_RESPONS, couponResponseDao);
        } catch (Exception e) {
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED);
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }

    @Override
    public ResponseResult memberPointSave(CepMemberPointDto cepMemberPointDto) {
        //查询会员id
        String memberId = memberRepository.queryMemberId(cepMemberPointDto.getCustomerNo());
        if (StringUtils.isBlank(memberId)) {
            return new ResponseResult(ResponseCodeEnum.MEMBER_FAILED);
        }
        cepMemberPointDto.setMemberIDStr(memberId);
        HashMap<Object, Object> member = new HashMap<>();
        member.put("id", memberId);
        cepMemberPointDto.setMember(member);
        try {
            // DMLResponse response = DataApiUtil.upsertIsData(ModelConstants.CampaignPoints, cepMemberPointDto.getCustomerNo()+cepMemberPointDto.getCampaignId(), JSONObject.parseObject(JSON.toJSONString(cepMemberPointDto)));
            DMLResponse response = DataApiUtil.upsertIsData(ModelConstants.CampaignPoints, UUID.randomUUID().toString().replaceAll("-", ""), JSONObject.parseObject(JSON.toJSONString(cepMemberPointDto)));
            if (!response.getIsSuccess()) {
                log.error("活动积分(非瓶子)信息保存:{}",response.getOperation());
                return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, response.getOperation());
            }
        } catch (Exception e) {
            log.error("活动积分(非瓶子)信息保存e:{}",e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED);
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }

    @Override
    public ResponseResult saveMemberHistoryPoint(CepHistoryPointDto cepHistoryPointDto) {
        //查询会员id
        String memberId = memberRepository.queryMemberId(cepHistoryPointDto.getCustomerNo());
        if (StringUtils.isBlank(memberId)) {
            return new ResponseResult(ResponseCodeEnum.MEMBER_FAILED);
        }
        HashMap<Object, String> hashMap = new HashMap<>();
        hashMap.put("id", memberId);
        cepHistoryPointDto.setMember(hashMap);
        try {
            DMLResponse response = DataApiUtil.upsertIsData(ModelConstants.HISTORY_POINT, cepHistoryPointDto.getCustomerNo(), JSONObject.parseObject(JSON.toJSONString(cepHistoryPointDto)));
            if (!response.getIsSuccess()) {
                log.error("历史会员积分信息保存:{}",response.getOperation());
                return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, response.getOperation());
            }
        } catch (Exception e) {
            log.error("历史会员积分信息保存e:{}",e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED);
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }


    @Override
    public ResponseResult appletTemplateSave(CepAppletTemplateDto cepAppletTemplateDto) {

        try {
            DMLResponse response = DataApiUtil.upsertIsData(ModelConstants.SUBSCRIPTIONS, cepAppletTemplateDto.getTemplateId(), JSONObject.parseObject(JSON.toJSONString(cepAppletTemplateDto)));
            if (!response.getIsSuccess()) {
                log.error("小程序订阅模板主数据保存:{}", response.getOperation());
                return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, response.getOperation());
            }
        } catch (Exception e) {
            log.error("小程序订阅模板主数据保存e:{}", e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED);
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }

    @Override
    public ResponseResult memberSubscriptionSave(CepMemberSubscriptionDto cepMemberSubscriptionDto) {
        //created不能为空
        if (StringUtils.isEmpty(cepMemberSubscriptionDto.getTraceId())){
            if (StringUtils.isEmpty(cepMemberSubscriptionDto.getCreated())) {
                return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, "创建时间created不能为空");
            }
        }
        //MA发送小程序订阅回调
        if (StringUtils.isNotEmpty(cepMemberSubscriptionDto.getTraceId())){
            //更新MA回调状态
            sendsSubscriptionCallback(cepMemberSubscriptionDto);
            //更新小程序流水状态
             Map<String, Object> paramsMap = new HashMap<>();
             paramsMap.put("status",cepMemberSubscriptionDto.getStatus());
             paramsMap.put("lastSync",DateHelper.getNowZone());
            DMLResponse response = DataApiUtil.update(ModelConstants.MEMBER_SUBSCRIPTIONS, cepMemberSubscriptionDto.getSubscribeId(), paramsMap);
            if (!response.getIsSuccess()) {
                log.error("会员订阅消息关系数据更新失败:{}",response.getOperation());
                return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, response.getOperation());
            }
        }else {
            //新增保存小程序订阅流水
            CepMemberSubscriptionModel cepMemberSubscriptionModel = JSON.parseObject(JSON.toJSONString(cepMemberSubscriptionDto), CepMemberSubscriptionModel.class);
            //查询会员id
            String memberId = memberRepository.queryMemberId(cepMemberSubscriptionDto.getCustomerNo());
            if (null == cepMemberSubscriptionDto.getStatus()) {
                return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT);
            }
            if ("PLATFORM".equals(cepMemberSubscriptionDto.getType())) {
                cepMemberSubscriptionModel.setCommenCode(cepMemberSubscriptionDto.getCampaignCode());
                cepMemberSubscriptionModel.setCampaignCode("");
            }
            if ("CAMPAIGN".equals(cepMemberSubscriptionDto.getType())) {
                HashMap<String, String> map = new HashMap<>();
                map.put("id", cepMemberSubscriptionDto.getCampaignCode());
                cepMemberSubscriptionModel.setCampaign(map);
            }
            cepMemberSubscriptionModel.setLastSync(DateHelper.getNowZone());
            if (StringUtils.isNotBlank(cepMemberSubscriptionDto.getCreated())){
                cepMemberSubscriptionModel.setCreated(DateHelper.getZone(cepMemberSubscriptionDto.getCreated()));
            }
            HashMap<Object, Object> map = new HashMap<>();
            map.put("id", cepMemberSubscriptionDto.getTemplateId());
            cepMemberSubscriptionModel.setTemplate(map);
            try {

                if(StringUtils.isNotBlank(cepMemberSubscriptionDto.getChannelType())){
                    cepMemberSubscriptionModel.setCustomerChannel(cepMemberSubscriptionDto.getChannelType());
                    cepMemberSubscriptionModel.setCustomerPlatformNo(cepMemberSubscriptionDto.getKoId());
                }else {
                    cepMemberSubscriptionModel.setCustomerChannel("COSMOS");
                    cepMemberSubscriptionModel.setCustomerPlatformNo(cepMemberSubscriptionDto.getKoId());
                }
                if (StringUtils.isNotBlank(memberId)) {
                    cepMemberSubscriptionModel.setMemberId(memberId);
                    HashMap<String, String> hashMap = new HashMap<>();
                    hashMap.put("id", memberId);
                    cepMemberSubscriptionModel.setMember(hashMap);
                    cepMemberSubscriptionModel.setCustomerChannel("MEMBER");
                    cepMemberSubscriptionModel.setCustomerPlatformNo(memberId);
                    cepMemberSubscriptionModel.setChannelType("MEMBER");
                }
                DMLResponse response = DataApiUtil.upsertIsData(ModelConstants.MEMBER_SUBSCRIPTIONS, cepMemberSubscriptionDto.getSubscribeId(), JSONObject.parseObject(JSON.toJSONString(cepMemberSubscriptionModel)));
                if (!response.getIsSuccess()) {
                    log.error("会员订阅消息关系数据保存:{}",response.getOperation());
                    return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, response.getOperation());
                }
            } catch (Exception e) {
                log.error("会员订阅消息关系数据保存e:{}",e.getMessage());
                return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED);
            }
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }

    private void sendsSubscriptionCallback(CepMemberSubscriptionDto cepMemberSubscriptionDto){
        HashMap<String, Object> paramsMap = new HashMap<>();
        if (cepMemberSubscriptionDto.getStatus()){
            paramsMap.put("koResult","Y");
            paramsMap.put("koResultDesc","成功");
        }else {
            paramsMap.put("koResult","N");
            paramsMap.put("koResultDesc",cepMemberSubscriptionDto.getDescription());
        }
        paramsMap.put("executeTime", ZonedDateTime.now());
        DMLResponse response = DataApiUtil.update(ConfigurationCenterUtil.MEMBER_CAMPAIGN_ACTION_NEW, cepMemberSubscriptionDto.getTraceId(), paramsMap);
        if (!response.getIsSuccess()) {
            log.error("MA发送小程序订阅回调记录更新失败:{}",response.getOperation());
        }
    }



    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseResult campaingSubjectSave(CepCampaignSubjectDto cepCampaignSubjectDto) {

        //转换时区
        cepCampaignSubjectDto.setStartTime(StringUtils.isNotEmpty(cepCampaignSubjectDto.getStartTime()) ? DateHelper.getZone(cepCampaignSubjectDto.getStartTime()) : null);
        cepCampaignSubjectDto.setEndTime(StringUtils.isNotEmpty(cepCampaignSubjectDto.getEndTime()) ? DateHelper.getZone(cepCampaignSubjectDto.getEndTime()) : null);
//        cepCampaignSubjectDto.setEndTime(DateHelper.getNowZone());
        try {
            log.info("ICepDataSyncService...campaingSubjectSave...cepCampaignSubjectDto:{}",JSONObject.parseObject(JSON.toJSONString(cepCampaignSubjectDto)));
            List<OrganizerDto> organizerList = cepCampaignSubjectDto.getOrganizerList();
            ArrayList<String> arrayList = new ArrayList<>();
            if (organizerList!=null && organizerList.size() > 0){
                for (OrganizerDto list : organizerList){
                    HashMap<String, Object> objectObjectHashMap = new HashMap<>();
                    objectObjectHashMap.put("organizerCode",list.getOrganizerCode());
                    objectObjectHashMap.put("organizerName",list.getOrganizerName());
                    DataApiUtil.upsertIsData(ModelConstants.CAMPAIGN_ORGANIZER,list.getOrganizerCode(),objectObjectHashMap);
                    arrayList.add(list.getOrganizerCode());
                }
            }

            // 对入参数据进行校验
            ResponseResult checkResponse = check(cepCampaignSubjectDto);
            if (checkResponse != null) {
                return checkResponse;
            }

            CepCampaignSubjectBigDto cepCampaignSubjectBigDto = new CepCampaignSubjectBigDto();
            BeanUtils.copyProperties(cepCampaignSubjectDto,cepCampaignSubjectBigDto);
            cepCampaignSubjectBigDto.setOrganizerList(arrayList);
            if(StringUtils.isNotBlank(cepCampaignSubjectDto.getCampaigngroup())){
                cepCampaignSubjectBigDto.setCampaigngroup(cepCampaignSubjectDto.getCampaigngroup());
            }
            log.info("ICepDataSyncService...campaingSubjectSave...cepCampaignSubjectBigDto:{}",JSONObject.parseObject(JSON.toJSONString(cepCampaignSubjectBigDto)));
            DMLResponse response = DataApiUtil.upsertIsData(ModelConstants.CAMPAIGN_SUBJECT, cepCampaignSubjectBigDto.getCampaignId(), JSONObject.parseObject(JSON.toJSONString(cepCampaignSubjectBigDto)));
            if (!response.getIsSuccess()) {
                log.error("活动主数据保存:{}",response.getOperation());
                return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, response.getOperation());
            }
        } catch (Exception e) {
            log.error("活动主数据保存e:{}",e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED);
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }

    private ResponseResult check(CepCampaignSubjectDto cepCampaignSubjectDto) {
        String campaignBrand = cepCampaignSubjectDto.getCampaignBrand();
        // 对指定客户渠道品牌字段做必填校验
        if (StringUtils.equalsAny(cepCampaignSubjectDto.getChannel(), "KO_MP")) {
            if (StringUtils.isEmpty(campaignBrand)) {
                return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, "活动品牌不能为空");
            }
        }
        return null;
    }

    @Override
    public ResponseResult memberCampaignsSave(CepMemberCampaignsDto cepMemberCampaignsDto) {
        //查询会员id
        String memberId = memberRepository.queryMemberId(cepMemberCampaignsDto.getCustomerNo());
        if (StringUtils.isBlank(memberId)) {
            return new ResponseResult(ResponseCodeEnum.MEMBER_FAILED);
        }
        if (StringUtils.isBlank(cepMemberCampaignsDto.getJoinTime())) {
            return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, "参与会员活动时间不能为空");
        }
        //查询活动主数据
        Map<String, String> campaignMap = memberRepository.queryCampaign(cepMemberCampaignsDto.getCampaignCode());
        if (StringUtils.isBlank(campaignMap.get("campaignCode"))) {
            return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, "参与会员活动不存在");
        }
       /* if ("已发布".equals(campaignMap.get("status"))){
            return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT,"参与会员活动不存在");
        }*/


        //封装对象关系
        HashMap<Object, Object> campaign = new HashMap<>();
        HashMap<Object, Object> member = new HashMap<>();
        member.put("id", memberId);
        campaign.put("id", cepMemberCampaignsDto.getCampaignCode());
        cepMemberCampaignsDto.setMember(member);
        cepMemberCampaignsDto.setCampaign(campaign);
        cepMemberCampaignsDto.setJoinTime(StringUtils.isNotEmpty(cepMemberCampaignsDto.getJoinTime()) ? DateHelper.getZone(cepMemberCampaignsDto.getJoinTime()) : DateHelper.getNowZone());
        cepMemberCampaignsDto.setGetRewardsTime(StringUtils.isNotEmpty(cepMemberCampaignsDto.getGetRewardsTime()) ? DateHelper.getZone(cepMemberCampaignsDto.getGetRewardsTime()) : DateHelper.getNowZone());
        cepMemberCampaignsDto.setId(cepMemberCampaignsDto.getCustomerNo() + cepMemberCampaignsDto.getCampaignCode());
        cepMemberCampaignsDto.setMemberIDStr(memberId);
        cepMemberCampaignsDto.setLastSync(DateHelper.getNowZone());
        try {
            DMLResponse response = DataApiUtil.upsertIsData(ModelConstants.CAMPAIGN_CAMPAIGNRECORDS, cepMemberCampaignsDto.getId(), JSONObject.parseObject(JSON.toJSONString(cepMemberCampaignsDto)));
            //DMLResponse response = DataApiUtil.insert(ModelConstants.CAMPAIGN_CAMPAIGNRECORDS, JSONObject.parseObject(JSON.toJSONString(cepMemberCampaignsDto)));
            if (!response.getIsSuccess()) {
                log.error("会员参与活动信息保存:{}",response.getOperation());
                return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, response.getOperation());
            }
        } catch (Exception e) {
            log.error("会员参与活动信息保存e:{}",e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED);
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }


    @Override
    public ResponseResult saveCounversionPoint(CepConversionPointDto cepConversionPointDto) {
        //查询会员id
        String memberId = memberRepository.queryMemberId(cepConversionPointDto.getCustomerNo());
        if (StringUtils.isBlank(memberId)) {
            return new ResponseResult(ResponseCodeEnum.MEMBER_FAILED);
        }
        cepConversionPointDto.setMemberIDStr(memberId);
        HashMap<Object, Object> map = new HashMap<>();
        map.put("id", memberId);
        cepConversionPointDto.setMember(map);
        cepConversionPointDto.setExchangeId(cepConversionPointDto.getExchangeId());
        cepConversionPointDto.setConsumePoint(cepConversionPointDto.getConsumePoint());
        cepConversionPointDto.setQuantity(cepConversionPointDto.getQuantity());
        cepConversionPointDto.setLastSync(DateHelper.getNowZone());
        cepConversionPointDto.setExchangeTime(StringUtils.isNotEmpty(cepConversionPointDto.getExchangeTime()) ? DateHelper.getZone(cepConversionPointDto.getExchangeTime()) : null);
        cepConversionPointDto.setReceiveTime(StringUtils.isNotEmpty(cepConversionPointDto.getReceiveTime()) ? DateHelper.getZone(cepConversionPointDto.getReceiveTime()) : null);
        cepConversionPointDto.setShippingTime(StringUtils.isNotEmpty(cepConversionPointDto.getShippingTime()) ? DateHelper.getZone(cepConversionPointDto.getShippingTime()) : null);
        cepConversionPointDto.setMemberIDStr(memberId);
        HashMap<Object, Object> costCenterId = new HashMap<>();
        costCenterId.put("id", cepConversionPointDto.getCostCenterStr());
        cepConversionPointDto.setCostCenter(costCenterId);
        try {
            DMLResponse response = DataApiUtil.upsertIsData(ModelConstants.CAMPAIGN_POINTEXCHANGE, cepConversionPointDto.getExchangeId(), JSONObject.parseObject(JSON.toJSONString(cepConversionPointDto)));
            if (!response.getIsSuccess()) {
                log.error("积分兑换订单信息保存:{}",response.getOperation());
                return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, response.getOperation());
            }
        } catch (Exception e) {
            log.error("积分兑换订单信息保存e:{}",e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED);
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }

    @Override
    public ResponseResult saveSyncShop(EcSyncShopDto ecSyncShopDto) {

        try {
            ecSyncShopDto.setMemberType(ModelConstants.MEMBER_TYPE);
            ecSyncShopDto.setLastSync(DateHelper.getNowZone());
            DMLResponse response = DataApiUtil.upsertIsData(ModelConstants.CAMPAIGN_SHOP, ecSyncShopDto.getShopCode(), JSONObject.parseObject(JSON.toJSONString(ecSyncShopDto)));
            if (!response.getIsSuccess()) {
                log.error("店铺主数据保存:{}", response.getOperation());
                return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, response.getOperation());
            }
        } catch (Exception e) {
            log.error("店铺主数据保存e:{}", e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED);
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);

    }


    @Override
    public ResponseResult updateSyncProduct(EcProductDto ecProductDto) {
        ecProductDto.setLastSync(DateHelper.getNowZone());
        ecProductDto.setCreated(DateHelper.getNowZone());
        try {
            DMLResponse response = DataApiUtil.upsertIsData(ModelConstants.ECPRODUCT, ecProductDto.getSkuCode(), JSONObject.parseObject(JSON.toJSONString(ecProductDto)));
            if (!response.getIsSuccess()) {
                log.error("新增/修改商品主数据:{}",response.getOperation());
                return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, response.getOperation());
            }
        } catch (Exception e) {
            log.error("新增/修改商品主数据e:{}",e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED);
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }

    /**
     * 互动主数据保存
     *
     * @param interactiveDto
     * @return
     * @throws ParseException
     */
    @Override
    public ResponseResult saveInteractive(InteractiveDto interactiveDto) {

        interactiveDto.setStartTime(StringUtils.isNotEmpty(interactiveDto.getStartTime()) ? DateHelper.getZone(interactiveDto.getStartTime()) : null);
        interactiveDto.setEndTime(StringUtils.isNotEmpty(interactiveDto.getEndTime()) ? DateHelper.getZone(interactiveDto.getEndTime()) : null);

        interactiveDto.setLastSync(DateHelper.formatZonedDateTime(new Date(), null));

        interactiveDto.setCreated(DateHelper.formatZonedDateTime(new Date(), null));
        try {
            interactiveRepository.insert(ModelConstants.INTERACTIVE, interactiveDto);
        } catch (Exception e) {
            log.error("互动主数据保存e:{}",e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, ResponseCodeEnum.OPENAPI_FAILED.getMsg());
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }

    /**
     * 会员互动行为记录保存
     *
     * @param interactiveRecordsDto
     * @return
     * @throws ParseException
     */
    @Override
    public ResponseResult saveInteractiveRecords(InteractiveRecordsDto interactiveRecordsDto) {

        //查询会员id
        String memberId = memberRepository.queryMemberId(interactiveRecordsDto.getCustomerNo());
        if (StringUtils.isBlank(memberId)) {
            return new ResponseResult(ResponseCodeEnum.MEMBER_FAILED, ResponseCodeEnum.MEMBER_FAILED.getMsg());
        }
        InteractiveRecordsModeDto interactiveRecords = new InteractiveRecordsModeDto();
        HashMap<Object, Object> member = new HashMap<>();
        member.put("id", memberId);
        HashMap<Object, Object> interactive = new HashMap<>();
        interactive.put("id", interactiveRecordsDto.getInteractiveId());
        HashMap<Object, Object> inviteeMember = new HashMap<>();
        inviteeMember.put("id", interactiveRecordsDto.getInvitee());

        interactiveRecords.setCustomerNo(interactiveRecordsDto.getCustomerNo());
        interactiveRecords.setRefuseReason(interactiveRecordsDto.getRefuseReason());
        interactiveRecords.setInteractiveType(interactiveRecordsDto.getInteractiveType());
        interactiveRecords.setChannelType(interactiveRecordsDto.getChannelType());
        interactiveRecords.setInteractiveCode(interactiveRecordsDto.getInteractiveId());
        interactiveRecords.setInteractiveRecordId(interactiveRecordsDto.getInteractiveRecordId());
        interactiveRecords.setInteractiveName(interactiveRecordsDto.getInteractiveName());
        interactiveRecords.setGrantStatus(interactiveRecordsDto.getGrantStatus());
        interactiveRecords.setMemberIntegral(interactiveRecordsDto.getMemberIntegral());
        interactiveRecords.setIsGetRewards(interactiveRecordsDto.getIsGetRewards());
        interactiveRecords.setCoupon(interactiveRecordsDto.getCoupon());
        interactiveRecords.setConsumePoint(interactiveRecordsDto.getConsumePoint());
        interactiveRecords.setRewardsCode(interactiveRecordsDto.getRewardsCode());
        interactiveRecords.setRewardsType(interactiveRecordsDto.getRewardsType());
        interactiveRecords.setRewardsName(interactiveRecordsDto.getRewardsName());
        interactiveRecords.setRewardsCost(interactiveRecordsDto.getRewardsCost());
        interactiveRecords.setInvitee(interactiveRecordsDto.getInvitee());
        interactiveRecords.setSignTime(interactiveRecordsDto.getSignTime());
        interactiveRecords.setMobile(interactiveRecordsDto.getMobile());
        interactiveRecords.setRuleDesc(interactiveRecordsDto.getRuleDesc());
        interactiveRecords.setMemberIDStr(memberId);
        interactiveRecords.setMember(member);
        interactiveRecords.setInteractive(interactive);
        interactiveRecords.setInviteeMember(inviteeMember);
        interactiveRecords.setJoinTime(StringUtils.isNotEmpty(interactiveRecordsDto.getJoinTime()) ? DateHelper.getZone(interactiveRecordsDto.getJoinTime()) : null);
        interactiveRecords.setLastSync(DateHelper.formatZonedDateTime(new Date(), null));
        interactiveRecords.setIsLuckyDraw(interactiveRecordsDto.getIsLuckyDraw());
        interactiveRecords.setCreated(DateHelper.formatZonedDateTime(new Date(), null));
        try {
            interactiveRecordsRepository.insert(ModelConstants.INTERACTIVERECORDS, interactiveRecords);
        } catch (Exception e) {
            log.error("互动行为记录保存:{}",e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, e.getMessage());
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }

    /**
     * 新增/修改订单 (oms+ec商城)
     *
     * @param ecNormalOrderDto
     * @return
     * @throws ParseException
     */
    @Override
    public ResponseResult updateSyncOrder(EcNormalOrderDto ecNormalOrderDto) {
        //查询会员id
        String memberId = memberRepository.queryMemberId(ecNormalOrderDto.getCustomerNo());
        if (StringUtils.isBlank(memberId)) {
            return new ResponseResult(ResponseCodeEnum.MEMBER_FAILED, ResponseCodeEnum.MEMBER_FAILED.getMsg());
        }
        String costCenterMark = ecNormalOrderDto.getCostCenterMark();
        String costCenter = cepCostCenterRepository.getCostCenter(costCenterMark);
        if (StringUtils.isBlank(costCenter)) {
            return new ResponseResult(ResponseCodeEnum.RECORD_NOT_FOUND, "成本中心标识不存在");
        }
        Double discountRate = ecNormalOrderDto.getDiscountRate();
       /* if(discountRate > 1){
            return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, "discountRate折扣率不能大于1");
        }*/
        Map<String, String> shopInfo = memberRepository.queryShopInfo(ecNormalOrderDto.getShopCode());
        log.info("根据店铺编码查询店铺信息。。。。。。。。。:{}", shopInfo);
        if (!shopInfo.isEmpty()) {
            if (!ecNormalOrderDto.getShopName().equals(shopInfo.get("shopName"))) {
                return new ResponseResult(ResponseCodeEnum.RECORD_NOT_FOUND, "店铺名称不存在");
            }
        } else {
            return new ResponseResult(ResponseCodeEnum.RECORD_NOT_FOUND, "店铺信息未在EC店铺主数据中查询到对应信息");
        }
        List<EcNormalOrderItemDto> orderItems = ecNormalOrderDto.getOrderItems();
        for (EcNormalOrderItemDto orderItemList : orderItems) {
            String productCode = orderItemList.getProductCode();
            Map<String, String> productInfo = memberRepository.queryProduct(productCode);
            if (!productInfo.isEmpty()) {
                if (!orderItemList.getProductName().equals(productInfo.get("prodName"))) {
                    return new ResponseResult(ResponseCodeEnum.RECORD_NOT_FOUND, "商品名称不存在");
                }
            }
            orderItemList.setOrderType("NORMAL");
            orderItemList.setFinishTime((StringUtils.isNotEmpty(orderItemList.getFinishTime()) ? DateHelper.getZone1(orderItemList.getFinishTime()) : null));
            orderItemList.setOrderTime((StringUtils.isNotEmpty(orderItemList.getOrderTime()) ? DateHelper.getZone1(orderItemList.getOrderTime()) : null));
            orderItemList.setPayTime((StringUtils.isNotEmpty(orderItemList.getPayTime()) ? DateHelper.getZone1(orderItemList.getPayTime()) : null));
        }
        ecNormalOrderDto.setMemberId(memberId);
        ecNormalOrderDto.setOrderTime((StringUtils.isNotEmpty(ecNormalOrderDto.getOrderTime()) ? DateHelper.getZone1(ecNormalOrderDto.getOrderTime()) : null));
        ecNormalOrderDto.setUpdateTime((StringUtils.isNotEmpty(ecNormalOrderDto.getUpdateTime()) ? DateHelper.getZone1(ecNormalOrderDto.getUpdateTime()) : null));
        ecNormalOrderDto.setPayTime((StringUtils.isNotEmpty(ecNormalOrderDto.getPayTime()) ? DateHelper.getZone1(ecNormalOrderDto.getPayTime()) : null));
        ecNormalOrderDto.setReceiveTime((StringUtils.isNotEmpty(ecNormalOrderDto.getReceiveTime()) ? DateHelper.getZone1(ecNormalOrderDto.getReceiveTime()) : null));
        ecNormalOrderDto.setShippingTime((StringUtils.isNotEmpty(ecNormalOrderDto.getShippingTime()) ? DateHelper.getZone1(ecNormalOrderDto.getShippingTime()) : null));
        ecNormalOrderDto.setFinishTime((StringUtils.isNotEmpty(ecNormalOrderDto.getFinishTime()) ? DateHelper.getZone1(ecNormalOrderDto.getFinishTime()) : null));
        EcCustomizedPropertiesDto customizedProperties = new EcCustomizedPropertiesDto();
        customizedProperties.setRsvField1(costCenterMark);
        customizedProperties.setRsvField2(costCenter);
        if (ecNormalOrderDto.getChannelType().equals("EC_SHOPPING")) {
            MemberOrderDao memberOrder = orderClient.queryMemberOrder(ecNormalOrderDto.getMemberType(), memberId, null, null, null, null, "EC_SHOPPING", null, null, null, null);
            Integer totalCount = memberOrder.getTotalCount();
            if (totalCount == 0) {
                customizedProperties.setRsvField3("0");//0表示为EC_SHOPPING渠道的首单
            } else {
                customizedProperties.setRsvField3("1");//1表示非EC_SHOPPING渠道的首单
            }
        } else {
            customizedProperties.setRsvField3("2");//2表示非EC_SHOPPING渠道的订单
        }
        ecNormalOrderDto.setOrderType("NORMAL");
        ecNormalOrderDto.setCustomizedProperties(customizedProperties);
        log.info("订单保存入参。。。。。。。。。:{}", ecNormalOrderDto.toString());
        try {
            //保存订单
            orderClient.saveNormalOrder(ecNormalOrderDto);
        } catch (Exception e) {
            log.error("订单保存接口异常。。。。。。。。。:{}", e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, e.getMessage());
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }

    /**
     * 新增/修改退单 (oms+ec商城)
     *
     * @param ecRefundOrderDto
     * @return
     * @throws ParseException
     */
    @Override
    public ResponseResult updateSyncRefundOrder(EcRefundOrderDto ecRefundOrderDto) {
        //查询会员id
        String memberId = memberRepository.queryMemberId(ecRefundOrderDto.getCustomerNo());
        if (StringUtils.isBlank(memberId)) {
            return new ResponseResult(ResponseCodeEnum.MEMBER_FAILED, ResponseCodeEnum.MEMBER_FAILED.getMsg());
        }
        String costCenterMark = ecRefundOrderDto.getCostCenterMark();
        String costCenter = cepCostCenterRepository.selectCostCenter(costCenterMark);
        if (StringUtils.isBlank(costCenter)) {
            return new ResponseResult(ResponseCodeEnum.RECORD_NOT_FOUND, "成本中心标识不存在");
        }
        Map<String, String> shopInfo = memberRepository.queryShopInfo(ecRefundOrderDto.getShopCode());
        log.info("根据店铺编码查询店铺信息。。。。。。。。。:{}", shopInfo);
        if (!shopInfo.isEmpty()) {
            if (!ecRefundOrderDto.getShopName().equals(shopInfo.get("shopName"))) {
                return new ResponseResult(ResponseCodeEnum.RECORD_NOT_FOUND, "店铺名称不存在");
            }
        } else {
            return new ResponseResult(ResponseCodeEnum.RECORD_NOT_FOUND, "店铺信息未在EC店铺主数据中查询到对应信息");
        }
        ecRefundOrderDto.setMemberId(memberId);
        ecRefundOrderDto.setRefundTime((StringUtils.isNotEmpty(ecRefundOrderDto.getRefundTime()) ? DateHelper.getZone1(ecRefundOrderDto.getRefundTime()) : null));
        ecRefundOrderDto.setFinishTime((StringUtils.isNotEmpty(ecRefundOrderDto.getFinishTime()) ? DateHelper.getZone1(ecRefundOrderDto.getFinishTime()) : null));
        ecRefundOrderDto.setUpdateTime((StringUtils.isNotEmpty(ecRefundOrderDto.getUpdateTime()) ? DateHelper.getZone1(ecRefundOrderDto.getUpdateTime()) : null));
        EcCustomizedPropertiesDto customizedProperties = new EcCustomizedPropertiesDto();
        customizedProperties.setRsvField1(costCenterMark);
        customizedProperties.setRsvField2(costCenter);
        ecRefundOrderDto.setCustomizedProperties(customizedProperties);
        List<EcRefundOrderItemDto> refundOrderItems = ecRefundOrderDto.getRefundOrderItems();
        for (EcRefundOrderItemDto orderItem : refundOrderItems) {
            String productCode = orderItem.getProductCode();
            Map<String, String> productInfo = memberRepository.queryProduct(productCode);
            if (!productInfo.isEmpty()) {
                if (!orderItem.getProductName().equals(productInfo.get("prodName"))) {
                    return new ResponseResult(ResponseCodeEnum.RECORD_NOT_FOUND, "商品名称不存在");
                }
            }
            orderItem.setOriginOrderId(ecRefundOrderDto.getOriginOrderId());
            orderItem.setRefundTime(ecRefundOrderDto.getRefundTime());
            orderItem.setFinishTime(ecRefundOrderDto.getFinishTime());
        }
        log.info("退单保存入参。。。。。。。。。:{}", ecRefundOrderDto.toString());
        try {
            //保存订单
            orderClient.saveRefundOrder(ecRefundOrderDto);
        } catch (Exception e) {
            log.error("退单保存接口异常。。。。。。。。。:{}", e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, e.getMessage());
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }

    @Override
    public ResponseResult saveBikpi(BiKpiDto biKpiDto) {
        String id = biKpiDto.getDate() + "_" + biKpiDto.getType();
        try {
            biKpiDto.setCreated(DateHelper.getZone(biKpiDto.getCreated()));
            biKpiDto.setLastSync(DateHelper.getZone(biKpiDto.getLastSync()));
            DataApiUtil.upsertIsData(ModelConstants.BIKPI, id, JSONObject.parseObject(JSON.toJSONString(biKpiDto)));
        } catch (Exception e) {
            log.error("KPI值保存e:{}",e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, e.getMessage());
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }

    @Override
    public ResponseResult savePosition(MemberLbsTrajectoryDto memberLbsTrajectoryDto,String type) {
        log.info("授权地理位置轨迹记录保存入参: {},type:{}", memberLbsTrajectoryDto,type);
        //查询授权信息是否存在
        MemberLbsTrajectoryDto trajectoryDto = memberLbsTrajectoryRepository.queryByMemberId(memberLbsTrajectoryDto.getMemberId(), memberLbsTrajectoryDto.getOriginLbsCity());
        if (null != trajectoryDto){
            return new ResponseResult(ResponseCodeEnum.SUCCESS);
        }
        try {
            memberLbsTrajectoryDto.setType(type);
            DMLResponse response = DataApiUtil.upsertIsData(ModelConstants.MEMBER_LBS_TRAJECTORY, UUID.randomUUID().toString().replaceAll("-", ""), JSONObject.parseObject(JSON.toJSONString(memberLbsTrajectoryDto)));
            if (!response.getIsSuccess()) {
                log.error("授权地理位置轨迹记录保存保存失败customerNo:{},:{}",memberLbsTrajectoryDto.getCustomerNo(),response.getOperation());
                return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, response.getOperation());
            }
        } catch (Exception e) {
            log.error("授权地理位置轨迹记录保存保存失败customerNo:{},:{}",memberLbsTrajectoryDto.getCustomerNo(),e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, e.getMessage());
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }

}
