package com.shuyun.kylin.customized.member.dm;

import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.base.util.DataApiUtil;
import com.shuyun.kylin.customized.member.dto.CepMemberSumDto;
import com.shuyun.kylin.customized.member.dto.CepRegulationPointDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class CepStatisticRepository extends BaseDsRepository<CepMemberSumDto> {

    public List<Map<String, String>> getByHourMember(String memberSourceDetail, String startTime, String endTime) {

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("startTime", startTime);
        queryMap.put("endTime", endTime);
        queryMap.put("memberSource", "活动");
        queryMap.put("memberSourceDetail", memberSourceDetail);
        String queryMemberSql = " select DATE_FORMAT(add_hours(registerTime, 8),'yyyy-MM-dd HH') as time,count(1) AS countNum from " + ModelConstants.Member + " where memberSource = :memberSource and memberSourceDetail = :memberSourceDetail and registerTime >= :startTime and registerTime <= :endTime GROUP BY DATE_FORMAT(add_hours(registerTime, 8),'yyyy-MM-dd HH') ORDER BY DATE_FORMAT(add_hours(registerTime, 8),'yyyy-MM-dd HH') ";
        //List<Map> list = DataApiUtil.queryFromOlap(queryMemberSql, queryMap);
        List<Map<String, String>> list = DataApiUtil.queryAnalysis(queryMemberSql, queryMap);
        //List<Map<String, String>> list = execute(queryMemberSql, queryMap).getData();
        return list;
    }

    public List<Map<String, String>> getByDayMember(String memberSourceDetail, String startTime, String endTime) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("startTime", startTime);
        queryMap.put("endTime", endTime);
        queryMap.put("memberSource", "活动");
        queryMap.put("memberSourceDetail", memberSourceDetail);
        String queryMemberSql = " select DATE_FORMAT(add_hours(registerTime, 8),'yyyy-MM-dd') as time,count(1) AS countNum from " + ModelConstants.Member + " where memberSource = :memberSource and memberSourceDetail = :memberSourceDetail and registerTime >= :startTime and registerTime <= :endTime GROUP BY DATE_FORMAT(add_hours(registerTime, 8),'yyyy-MM-dd') ORDER BY DATE_FORMAT(add_hours(registerTime, 8),'yyyy-MM-dd') ";
        //List<Map<String, String>> list = execute(queryMemberSql, queryMap).getData();
        List<Map<String, String>> list = DataApiUtil.queryAnalysis(queryMemberSql, queryMap);
        //List<Map<String, String>> list = execute(queryMemberSql, queryMap).getData();
        return list;
    }

    public List<Map<String, String>> getAccumulatePointsNum(String scene, String changeSourceDetail, String startTime, String endTime) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("scene", scene + "#");
        queryMap.put("changeSourceDetail", changeSourceDetail + "_");
        queryMap.put("startTime", startTime);
        queryMap.put("endTime", endTime);
        queryMap.put("channel", "FANS_DAY");
        queryMap.put("KZZD2", "参与活动");
        String queryMemberSql = " select sum(changePoint) AS accumulatePoints,count(1) AS accumulatePointsNum from " + ConfigurationCenterUtil.MEMBER_POINTRECORD + " where KZZD2 = :KZZD2 and substring(`KZZD1`,1,locate('_',`KZZD1`)) =:changeSourceDetail and substring(`desc`,1,locate('#',`desc`)) =:scene and changePoint > 0 and channel  = :channel and  created  >= :startTime and  created  <= :endTime ";
        log.info("queryMemberSql:{}", queryMemberSql);
        //List<Map<String, String>> list = execute(queryMemberSql, queryMap).getData();
        List<Map<String, String>> list = DataApiUtil.queryAnalysis(queryMemberSql, queryMap);
        return list;

    }

    public List<Map<String, String>> getAccumulatePointsNum(String changeSourceDetail, String startTime, String endTime) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("changeSourceDetail", changeSourceDetail + "_");
        queryMap.put("startTime", startTime);
        queryMap.put("endTime", endTime);
        queryMap.put("channel", "FANS_DAY");
        queryMap.put("KZZD2", "参与活动");
        String queryMemberSql = " select sum(changePoint) AS accumulatePoints,count(1) AS accumulatePointsNum from " + ConfigurationCenterUtil.MEMBER_POINTRECORD + " where KZZD2 = :KZZD2 and substring(`KZZD1`,1,locate('_',`KZZD1`)) =:changeSourceDetail and changePoint > 0 and channel = :channel and  created  >= :startTime and  created  <= :endTime ";
        //List<Map<String, String>> list = execute(queryMemberSql, queryMap).getData();
        List<Map<String, String>> list = DataApiUtil.queryAnalysis(queryMemberSql, queryMap);
        return list;

    }

    public List<Map<String, String>> getDeductionPointsNum(String scene, String changeSourceDetail, String startTime, String endTime) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("scene", scene + "#");
        queryMap.put("changeSourceDetail", changeSourceDetail + "_");
        queryMap.put("startTime", startTime);
        queryMap.put("endTime", endTime);
        queryMap.put("channel", "FANS_DAY");
        queryMap.put("KZZD2", "参与活动");
        String queryMemberSql = " select sum(changePoint) AS deductionPoints,count(1) AS deductionPointsNum from " + ConfigurationCenterUtil.MEMBER_POINTRECORD + " where KZZD2 = :KZZD2 and substring(`KZZD1`,1,locate('_',`KZZD1`)) =:changeSourceDetail and substring(`desc`,1,locate('#',`desc`)) =:scene and changePoint < 0 and channel  = :channel and  created  >= :startTime and  created  <= :endTime ";
        //List<Map<String, String>> list = execute(queryMemberSql, queryMap).getData();
        List<Map<String, String>> list = DataApiUtil.queryAnalysis(queryMemberSql, queryMap);
        return list;
    }

    public List<Map<String, String>> getDeductionPointsNum(String changeSourceDetail, String startTime, String endTime) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("changeSourceDetail", changeSourceDetail + "_");
        queryMap.put("startTime", startTime);
        queryMap.put("endTime", endTime);
        queryMap.put("channel", "FANS_DAY");
        queryMap.put("KZZD2", "参与活动");
        String queryMemberSql = " select sum(changePoint) AS deductionPoints,count(1) AS deductionPointsNum from " + ConfigurationCenterUtil.MEMBER_POINTRECORD + " where KZZD2 = :KZZD2 and substring(`KZZD1`,1,locate('_',`KZZD1`)) =:changeSourceDetail and changePoint < 0 and channel  = :channel and  created  >= :startTime and  created  <= :endTime ";
        //List<Map<String, String>> list = execute(queryMemberSql, queryMap).getData();
        List<Map<String, String>> list = DataApiUtil.queryAnalysis(queryMemberSql, queryMap);
        return list;
    }

    public List<Map<String, String>> getExchangeNum(String startTime, String endTime) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("startTime", startTime);
        queryMap.put("endTime", endTime);
        queryMap.put("channel", "FANS_DAY");
        queryMap.put("KZZD2", "积分兑换");
        String queryMemberSql = " select sum(changePoint) AS exchangePoints,count(1) AS exchangeNum from " + ConfigurationCenterUtil.MEMBER_POINTRECORD + " where KZZD2 = :KZZD2 and channel  = :channel and  created  >= :startTime and  created  <= :endTime ";
        //List<Map<String, String>> list = execute(queryMemberSql, queryMap).getData();
        List<Map<String, String>> list = DataApiUtil.queryAnalysis(queryMemberSql, queryMap);
        return list;
    }


    public List<Map<String, String>> getCancelNum(String startTime, String endTime) {
        ArrayList<String> kzzd2 = new ArrayList<>();
        kzzd2.add("兑换取消");
        kzzd2.add("兑换退还");
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("startTime", startTime);
        queryMap.put("endTime", endTime);
        queryMap.put("channel", "FANS_DAY");
        queryMap.put("KZZD2", kzzd2);
        String queryMemberSql = " select sum(changePoint) AS cancelPoints,count(1) AS cancelNum from " + ConfigurationCenterUtil.MEMBER_POINTRECORD + " where KZZD2 in (:KZZD2) and channel  = :channel and  created  >= :startTime and  created  <= :endTime ";
        //List<Map<String, String>> list = execute(queryMemberSql, queryMap).getData();
        List<Map<String, String>> list = DataApiUtil.queryAnalysis(queryMemberSql, queryMap);
        return list;
    }
}
