package com.shuyun.kylin.customized.member.dm;

import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.member.dto.CepAppletTemplateDto;
import com.shuyun.kylin.customized.member.dto.ConsumerPlatInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class ConsumerPlatInfoRepository extends BaseDsRepository<ConsumerPlatInfoDto> {


    /**
     * 查询所以info对象
     * @param koId
     * @return
     */
    public List<ConsumerPlatInfoDto> getConsumerPlatInfo(String koId, ArrayList<String> id) {
        Map<String, Object> map = new HashMap<>();
        map.put("koId",koId);
        map.put("id",id);
        String querySql = " select id from " + ModelConstants.COSMOS_CONSUMERPLASTINFO + " where  koId = :koId and id not in (:id) ";
        List<ConsumerPlatInfoDto> consumerPlatInfoDtoList = executeSQL(querySql,map);
        return consumerPlatInfoDtoList;
    }

    /**
     * 清除consumer模型openid
     *
     * @param koId
     */
    public void updateOpenId(String koId) {
        Map<String, Object> map = new HashMap<>();
        map.put("koId",koId);
        String querySql = " UPDATE " + ModelConstants.COSMOS_CONSUMER + " SET openId = null where  koId = :koId ";
        execute(querySql,map);
    }

    /**
     *  清除consumer模型phone
     *
     * @param koId
     */
    public void updatePhone(String koId) {
        Map<String, Object> map = new HashMap<>();
        map.put("koId",koId);
        String querySql = " UPDATE " + ModelConstants.COSMOS_CONSUMER + " SET phone = null where  koId = :koId ";
        execute(querySql,map);
    }

    /**
     * 删除ConsumerPlatInfo
     * @param koId
     */
    public void deleteConsumerPlatInfo(String koId) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("id",koId);
        String querySql = " DELETE FROM " + ModelConstants.COSMOS_CONSUMERPLASTINFO + "  where  id = :id ";
        execute(querySql,map);
    }
}
