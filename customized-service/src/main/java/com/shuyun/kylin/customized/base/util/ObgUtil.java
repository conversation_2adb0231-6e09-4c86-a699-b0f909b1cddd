package com.shuyun.kylin.customized.base.util;

import com.google.common.collect.Lists;
import com.shuyun.kylin.customized.coupon.dto.kkd.*;
import com.shuyun.kylin.starter.okhttp.OKHttpClient;
import com.shuyun.kylin.starter.okhttp.response.ResponseDto;
import com.shuyun.motor.common.cons.PropsUtil;
import com.shuyun.obg.sdk.ApiSignUtils;
import com.shuyun.pip.component.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class ObgUtil {

    private final static String obgUrl = PropsUtil.getSysOrEnv("system.obg.address","https://qa-obg.shuyun.com/obg/v1");
    private final static String kkdUrl = (obgUrl.indexOf("obg/v1") > 0? obgUrl : obgUrl + "/obg/v1") + "/kkd/api";
    private final static String kkdSendCouponUrl = kkdUrl + "/openplatform/coupon/sendCoupon";

    public static void sendKkdCoupon(SendCouponDto sendCouponDto,Map<String,String> sendResultMap,Map<String,String> errorResultMap){

//        String apiKey = "w9yrgpib";
//        String apiSecret = "VwgxANMLQX2uIXge";
//        String appId = "47267c98-d901-47ce-866a-2aef9b37910a";

        String apiKey = sendCouponDto.getApiKey();
        String apiSecret = sendCouponDto.getApiSecret();
        String appId = sendCouponDto.getAppId();

        OKHttpClient.Builder builder = new OKHttpClient.Builder().builder();

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json");
        Map<String, String> parameters = new HashMap<>();
        parameters.put("appId", appId);

        try {
            log.info("发券url:{}",kkdSendCouponUrl);
            String url = ApiSignUtils.buildUrl(kkdSendCouponUrl, parameters, apiKey, apiSecret);
            log.info("入参：{}",JsonUtils.toJson(sendCouponDto));
            ResponseDto responseDto = builder.header(headerMap).url(url).body(JsonUtils.objectToMap(sendCouponDto)).build().post();
            log.info("返回值：" + responseDto.getData());
            CouponSendResult couponSendResult = null;
            if(!StringUtils.isEmpty(responseDto.getData())){
                couponSendResult = JsonUtils.parse(responseDto.getData(),CouponSendResult.class);
                if(null != couponSendResult.getData() && null != couponSendResult.getData().getSuccessCouponOutputModels()){
                    for(SuccessCouponOutput successCouponOutput : couponSendResult.getData().getSuccessCouponOutputModels()){
                        sendResultMap.put(successCouponOutput.getMemberId(),successCouponOutput.getCouponItemNo());
                    }
                }
                if(null != couponSendResult.getData() && null != couponSendResult.getData().getErrorCouponOutputModels()){
                    for(ErrorCouponOutput errorCouponOutput : couponSendResult.getData().getErrorCouponOutputModels()){
                        errorResultMap.put(errorCouponOutput.getMemberId(),errorCouponOutput.getCouponId());
                    }
                }
            }
        }catch (Exception e){
            log.warn("调用obg异常，异常原因:",e);
        }
    }

    public static void main(String... args) throws Exception {
        SendCouponDto sendCouponDto = new SendCouponDto();
        sendCouponDto.setCouponIds(listOf("11953"));
        sendCouponDto.setMemberIds(listOf("320891","32089"));
        sendCouponDto.setSendCount(1);
        sendCouponDto.setAppId("47267c98-d901-47ce-866a-2aef9b37910a");
        sendCouponDto.setApiKey("w9yrgpib");
        sendCouponDto.setApiSecret("VwgxANMLQX2uIXge");

        Map<String,String> sendResultMap = new HashMap<>();
        Map<String,String> errorResultMap = new HashMap<>();
        sendKkdCoupon(sendCouponDto,sendResultMap,errorResultMap);
        System.out.println(JsonUtils.toJson(sendResultMap));
    }

    public static <T> List<T> listOf(T... t){
        List<T> list = Lists.newArrayList();
        for(T t1: t){
            list.add(t1);
        }
        return list;
    }

}
