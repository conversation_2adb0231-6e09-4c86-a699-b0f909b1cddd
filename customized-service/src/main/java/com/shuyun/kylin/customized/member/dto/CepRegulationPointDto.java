package com.shuyun.kylin.customized.member.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CepRegulationPointDto {

    @NotBlank(message = "场景值唯一标识不能为空")
    private String scene;
    @NotBlank(message = "活动不能为空")
    private String type;
    //enum
    @NotBlank(message = "积分变更类型不能为空: 增加积分/扣减积分")
    private String ruleType;
    @NotBlank(message = "任务不能为空")
    private String task;
    @NotBlank(message = "生效时间不能为空")
    private String startTime;
    @NotBlank(message = "失效时间不能为空")
    private String endTime;
    private Boolean isTransactionRelated;
    private Double sendOrDeductValuePerTime;
    //enum
    private String sendPointTimeliness;
    private Integer sendTimeLimitPerDay;
    private Integer sendTimeLimitPerMonth;
    private Integer sendTimeTotalLimit;
    private String lastSync;
    private String channelType;
    private String costCenterCode;
    private String isLBSCostCenterCode;

}
