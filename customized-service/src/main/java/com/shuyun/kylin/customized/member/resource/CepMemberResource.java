package com.shuyun.kylin.customized.member.resource;

import com.alibaba.fastjson.JSON;
import com.shuyun.epassport.sdk.register.RequiresPermissions;
import com.shuyun.kylin.crm.openapi.core.dto.common.PointRequestDto;
import com.shuyun.kylin.crm.openapi.core.dto.customer.UpdateMemberByChannelDto;
import com.shuyun.kylin.crm.openapi.core.dto.wechat.WechatRegisterRequest;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.common.ResponsesVo;
import com.shuyun.kylin.customized.member.dto.*;
import com.shuyun.kylin.customized.member.service.ICepMemberService;
import com.shuyun.kylin.starter.swagger.annotation.WebApi;
import com.shuyun.pip.component.json.JsonUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Base64;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @Description OCP小程序会员接口
 * @date 2021/12/11
 */
@Slf4j
@RestController
@RequestMapping("/wechat/member")
@WebApi
@RequiresPermissions(allowAnonymous = true)
public class CepMemberResource {

    @Autowired
    ICepMemberService cepMemberService;

    @PostMapping("/register")
    public ResponseResult wechatReg(@Validated @RequestBody WechatRegisterRequestDto request) {
        log.info("微信会员注册入参:{}", JsonUtils.toJson(request));
        return cepMemberService.wechatReg(request);
    }

    @PostMapping("/info/modify")
    public ResponseResult updateMemberByChannelId(@RequestBody CepUpdateMemberDto request) {
        log.info("微信会员修改入参:{}", JsonUtils.toJson(request));
        return cepMemberService.updateMemberByChannelId(request);
    }

    /**
     * 查询会员信息（仅含会员信息）
     *
     * @param customerNo
     * @param memberType
     * @param channelType
     * <AUTHOR>
     * @Date 2021/12/18 10:23
     */
    @GetMapping("/query")
    public ResponseResult queryMember(@RequestParam(value = "customerNo") String customerNo,
                                      String memberId,
                                      @RequestParam(value = "memberType", required = true) String memberType,
                                      @RequestParam(value = "channelType", required = true) String channelType) {
        log.info("查询会员信息（仅含会员信息）: {},{},{},{}", customerNo ,memberId,memberType ,channelType);
        return cepMemberService.queryMember(customerNo,memberId, memberType, channelType);
    }

    /**
     * 查询会员等级
     *
     * @param customerNo
     * @param memberType
     * @param channelType
     * <AUTHOR>
     * @Date 2021/12/18 10:23
     */
    @GetMapping("/grade/query")
    public ResponseResult queryMemberGrade(@RequestParam(value = "customerNo", required = true) String customerNo,
                                           @RequestParam(value = "memberType", required = true) String memberType,
                                           @RequestParam(value = "channelType", required = true) String channelType) {
        log.info("查询会员等级 : {}", customerNo + "," + memberType + "," + channelType);
        return cepMemberService.queryMemberGrade(customerNo, memberType, channelType);
    }

    /**
     * 查询成本中心数据
     *
     * <AUTHOR>
     * @Date 2021/12/18 10:23
     */
    @GetMapping("/cost/query")
    public ResponseResult queryCostCenter() {
        log.info("查询成本中心数据: {}");
        return cepMemberService.queryCostCenter();
    }

    /**
     * 变更会员积分(瓶子)--支持取消/退还场景
     *
     * @param cepMemberPointRecordDto
     * @return
     */
    @PostMapping("/point")
    public ResponseResult updateMemberScenePoint(@Validated @RequestBody CepMemberPointRecordDto cepMemberPointRecordDto) {
        log.info("变更会员积分 --支持取消/退还场景请求入参: {}", cepMemberPointRecordDto);
        return cepMemberService.updateMemberScenePoint(cepMemberPointRecordDto);
    }


    /**
     * 会员注销
     *
     * @param memberCancelDto
     * @return
     */
    //@DeleteMapping("/unsubscribe")
    @PostMapping("/unsubscribe")
    @Operation(summary = "会员注销", tags = "会员注销")
    @ApiResponses(value = {@ApiResponse(description = "会员注销", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = ResponsesVo.class))})})
    public ResponsesVo deleteMember(@RequestBody MemberCancelDto memberCancelDto) {
        log.info("会员注销请求入参: {}", memberCancelDto);
        return cepMemberService.deleteMember(memberCancelDto);
    }

    /**
     * 查询积分变更明细
     *
     * @return
     */
    @GetMapping("/point/records")
    public CepMemberPointItemDto getMemberPointItems(@RequestParam(value = "customerNo") String customerNo,
                                                     @RequestParam(value = "scene",required = false) List<String> scene,
                                                     @RequestParam(value = "channelType",required = false) String channelType,
                                                     @RequestParam(value = "startTime", required = false) String startTime,
                                                     @RequestParam(value = "endTime", required = false) String endTime,
                                                     @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                     @RequestParam(value = "pageSize", required = false, defaultValue = "20") Integer pageSize) {

        log.info("查询积分变更明细请求入参customerNo: {},startTime:{},endTime:{},page:{},pageSize:{},scene:{},channelType:{}", customerNo, startTime, endTime, page, pageSize,scene,channelType);
        return cepMemberService.getMemberPointItems(customerNo, startTime, endTime, page, pageSize,scene,channelType);
    }

    /**
     * 查询会员积分
     *
     * @return
     */
    @GetMapping("/point")
    public ResponseResult<Map<String, Object>> getMemberPoint(@RequestParam(value = "channelType") String channelType,
                                              @RequestParam(value = "customerNo") String customerNo,
                                              @RequestParam(value = "memberType") String memberType){
        log.info("查询会员积分channelType: {},customerNo:{},memberType:{}", channelType, customerNo, memberType);
        return cepMemberService.getMemberPoint(channelType, customerNo, memberType);
    }

    /**
     *查询公共user
     *
     * @return
     */
    @GetMapping("/fetch/user")
    @Operation(summary = "查询公共user", tags = "查询公共user")
    @ApiResponses(value = {@ApiResponse(description = "查询公共user", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = ResponsesVo.class))})})
    public ResponsesVo getCommonUser(@RequestParam(value = "appid") String appid,
                                                       @RequestParam(value = "user_id") String user_id){
        log.info("appid: {},user_id:{}", appid, user_id);
        return cepMemberService.getCommonUser(appid, user_id);
    }


    /**
     * 积分变更记录查询
     *
     * @param cepPointBehaviorRecordDto
     * @return
     */
    @PostMapping("/query/pointRecord")
    public CepPointRecordListPageResponse queryPointRecord(@Validated @RequestBody CepPointBehaviorRecordDto cepPointBehaviorRecordDto) {
        log.info("积分变更记录查询请求入参: {}", cepPointBehaviorRecordDto);
        return cepMemberService.queryPointRecord(cepPointBehaviorRecordDto);
    }


    /**
     * 中台会员数据同步
     *
     * @param ocpMemberBindingDto
     * @return
     */
    @PostMapping("/memberuat")
    public Map<String, Object> queryMemberuat(@Validated @RequestBody OcpMemberBindingDto ocpMemberBindingDto){
        log.info("中台会员数据同步请求入参: {}", JSON.toJSONString(ocpMemberBindingDto));
        return cepMemberService.queryMemberuat(ocpMemberBindingDto);
    }

     /**
     * 查询会员衍生字段
     *
     * @param request
     * @return
     */
    @PostMapping("/customizedProperties")
    public Map<String, Object> queryCustomizedPropertiest(@RequestBody Map request){
        log.info("查询会员衍生字段请求入参: {}", JSON.toJSONString(request));
        return cepMemberService.queryCustomizedPropertiest(request);
    }


    /**
     * 用户注销
     * @param consumerCancelDto
     * @return
     */
    @PostMapping("/consumer/cancel")
    @Operation(summary = "用户注销", tags = "用户注销")
    @ApiResponses(value = {@ApiResponse(description = "用户注销", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = ResponsesVo.class))})})
    public ResponsesVo deleteUser(@RequestBody ConsumerCancelDto consumerCancelDto) {
        log.info("用户注销请求入参: {}", JSON.toJSONString(consumerCancelDto));

        return cepMemberService.consumerCancel(consumerCancelDto,null,null);
    }


    /**
     * 删除ConsumerInfo模型中的记录
     *
     * @param consumerInfoDto
     * @return
     */
    @PostMapping("/consumer/delete")
    public Map<String, String> deleteConsumerInfo(@RequestBody ConsumerInfoDto consumerInfoDto) {
        log.info("删除ConsumerInfo请求入参: {}", JSON.toJSONString(consumerInfoDto));
        return cepMemberService.deleteConsumerInfo(consumerInfoDto);
    }



    /**
     * 手动接口补积分 （内部）
     * @param pointRequestDto
     * @return
     */
    @PostMapping("/customized/manual/point")
    public ResponseResult saveManualPoint(@Validated @RequestBody Map<String, String> pointRequestDto) {
        log.info("补积分pointRequestDto:{}", pointRequestDto);
        return cepMemberService.saveManualPoint(pointRequestDto);
    }


    /**
     *  国际手机号校验 （内部）
     * @param
     * @return
     */
    @PostMapping("/check/mobile")
    public ResponseResult checkMobile(@RequestParam String mobile) {
        log.info("国际手机号校验:{}", mobile);
        return cepMemberService.getCheckMobile(mobile);
    }

}