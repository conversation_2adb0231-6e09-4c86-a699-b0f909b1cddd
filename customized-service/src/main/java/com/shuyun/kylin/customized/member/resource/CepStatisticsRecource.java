package com.shuyun.kylin.customized.member.resource;

import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.member.service.CepStatisticService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Validated
@RestController
@RequestMapping("/statistics")
public class CepStatisticsRecource {

    @Autowired
    private CepStatisticService cepStatisticService;


    /**
     * 会员注册人数统计
     * @param memberSourceDetail
     * @param startTime
     * @param endTime
     * @param groupingTime
     * @return
     */
    @GetMapping("/memberSum")
    public ResponseResult getmemberSum(@RequestParam(value = "memberSourceDetail") String memberSourceDetail,
                                       @RequestParam(value = "startTime") String startTime,
                                       @RequestParam(value = "endTime") String endTime,
                                       @RequestParam(value = "groupingTime") String groupingTime) {
        log.info("会员注册人数统计memberSourceDetail:{},startTime:{},endTime:{},groupingTime:{}", memberSourceDetail,startTime,endTime,groupingTime);
        return cepStatisticService.getmemberSum(memberSourceDetail,startTime,endTime,groupingTime);
    }

    /**
     * 参与活动积分统计
     * @param scene
     * @param changeSourceDetail
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/activitySum")
    public ResponseResult getActivitySum(@RequestParam(value = "scene",required = false) String scene,
                                         @RequestParam(value = "changeSourceDetail") String changeSourceDetail,
                                         @RequestParam(value = "startTime") String startTime,
                                         @RequestParam(value = "endTime") String endTime) {
        log.info("参与活动积分统计changeSourceDetail:{},startTime:{},endTime:{},scene:{}", changeSourceDetail,startTime,endTime,scene);
        return cepStatisticService.getActivitySum(scene,changeSourceDetail,startTime,endTime);
    }

    /**
     * 参与积分兑换统计
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/exchangeSum")
    public ResponseResult getExchangeSum(@RequestParam(value = "startTime") String startTime,
                                         @RequestParam(value = "endTime") String endTime) {
        log.info("参与活动积分统计startTime:{},endTime:{}", startTime,endTime);
        return cepStatisticService.getExchangeSum(startTime,endTime);
    }


}
