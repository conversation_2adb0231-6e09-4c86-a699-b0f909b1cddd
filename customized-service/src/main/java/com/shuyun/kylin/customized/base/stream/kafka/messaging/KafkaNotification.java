package com.shuyun.kylin.customized.base.stream.kafka.messaging;

import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.stream.annotation.Input;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnExpression("${system.kafka.enabled:true}")
public interface KafkaNotification {

    String KAFKA_INPUT = "kafka_notification";
    @Input(KAFKA_INPUT)
    SubscribableChannel input();

}
