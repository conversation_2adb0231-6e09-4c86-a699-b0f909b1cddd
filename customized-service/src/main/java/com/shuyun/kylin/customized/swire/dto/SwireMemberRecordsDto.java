package com.shuyun.kylin.customized.swire.dto;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class SwireMemberRecordsDto {

    private String id;
    private Integer code;
    private String msg;
    private String status;
    private String lastSync;

    private String sid;
    private String mobile;
    private String gender;
    private String openId;
    private String appId;
    private String registerTime;

    private String nickName;
    private String avatarUrl;
    private String memberName;
    private String birthDay;
    private String birthYear;
    private String registerCity;
    private String registerArea;
    private String unionId;
    private String channel;
    private String loginCity;
    private String loginArea;
    private String privacyVersion;

    private String companyCode;
    private String buNo;
    private String source;
    private String sourceDetails;
    private String updateTime;
    private String memberId;


    public void setResultNull() {
        this.code = null;
        this.msg = null;
        this.lastSync = null;
    }

    public void isNotResultNull() {
        if (StringUtils.isEmpty(this.nickName)) {
            this.nickName = null;
        }
        if (StringUtils.isEmpty(this.avatarUrl)) {
            this.avatarUrl = null;
        }
        if (StringUtils.isEmpty(this.memberName)) {
            this.memberName = null;
        }
        if (StringUtils.isEmpty(this.unionId)) {
            this.unionId = null;
        }
        if (StringUtils.isEmpty(this.birthDay)) {
            this.birthDay = null;
        }
        if (StringUtils.isEmpty(this.birthYear)) {
            this.birthYear = null;
        }
        if (StringUtils.isEmpty(this.registerCity)) {
            this.registerCity = null;
        }
        if (StringUtils.isEmpty(this.registerArea)) {
            this.registerArea = null;
        }
        if (StringUtils.isEmpty(this.companyCode)) {
            this.companyCode = null;
        }
        if (StringUtils.isEmpty(this.buNo)) {
            this.buNo = null;
        }
        if (StringUtils.isEmpty(this.source)) {
            this.source = null;
        }


    }
}
