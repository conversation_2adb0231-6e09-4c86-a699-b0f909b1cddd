package com.shuyun.kylin.customized.base.feign.dao;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.ZonedDateTime;

@Data
public class MemberPointModifyRequest {

    @JsonProperty("KZZD1")
    private String KZZD1;
    @JsonProperty("KZZD2")
    private String KZZD2;
    @JsonProperty("KZZD3")
    private String KZZD3;
    private String actionName;
    private String changeMode;
    private String channelType;
    private String desc;
    private ZonedDateTime effectDate;
    private Integer idempotentMode;
    private String memberId;
    private ZonedDateTime overdueDate;
    private Double point;
    private Integer pointAccountId;
    private String recordType;
    private String shopId;
    private String tradeId;
    private String triggerId;
}
