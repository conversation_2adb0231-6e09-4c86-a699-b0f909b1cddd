package com.shuyun.kylin.customized.member.dm;


import com.alibaba.fastjson.JSON;
import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.kylin.crm.openapi.core.dto.common.MemberDto;
import com.shuyun.kylin.crm.openapi.core.dto.common.UnbindMemberDto;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.base.util.DateHelper;
import com.shuyun.kylin.customized.member.dto.CepMemberGradeDto;
import com.shuyun.kylin.customized.member.dto.PointBehaviorRecordDto;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class MemberRepository extends BaseDsRepository<MemberDto> {


    /**
     * 查询会员id
     */
    public String queryMemberId(String customerNo) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("customerNo", customerNo);
        String queryMemberSql = " select memberId from " + ModelConstants.MEMBER_MEMBERBINDING + " where  customerNo = :customerNo LIMIT 1";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        String memberId = null;
        for (Map map : list) {
            memberId = map.get("memberId").toString();
        }
        return memberId;
    }

    /**
     * 查询会员customerNo
     */
    public String queryCustomerNo(String memberId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        String queryMemberSql = " select customerNo from " + ModelConstants.MEMBER_MEMBERBINDING + " where  memberId = :memberId LIMIT 1 ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        String customerNo = null;
        for (Map map : list) {
            customerNo = map.get("customerNo").toString();
        }
        return customerNo;
    }

    public Map<String,String> queryCustomerNoChannType(String querySql,String channelType) {
        Map<String,String> map = new HashMap<>();

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("channelType", channelType);
        String queryMemberSql = " select memberId,customerNo,openId,appId,mobile,memberName,gender  from " + ModelConstants.MEMBER_MEMBERBINDING + " where  "+querySql+" and channelType = :channelType LIMIT 1";
        log.info("自定义会员查询sql:{}",queryMemberSql);
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        if (!CollectionUtils.isEmpty(list)){
            for (Map<String, Object> objectMap : list) {
                map.put("memberId", String.valueOf(objectMap.get("memberId")));
                map.put("openId", String.valueOf(objectMap.get("openId")));
                map.put("appId", String.valueOf(objectMap.get("appId")));
                map.put("mobile", String.valueOf(objectMap.get("mobile")));
                map.put("gender", String.valueOf(objectMap.get("gender")));
                map.put("customerNo",String.valueOf(objectMap.get("customerNo")));
            }
        }
        return map;
    }


    public Map<String,String> queryMemberByCustomerNo(String querySql) {
        Map<String,String> map = new HashMap<>();
        Map<String, Object> queryMap = new HashMap<>();
        String queryMemberSql = " select memberId,customerNo,openId,appId,mobile,memberName,gender,memberType  from " + ModelConstants.MEMBER_MEMBERBINDING + " where  "+querySql+"  LIMIT 1";
        log.info("queryMemberByCustomerNo查询sql:{}",queryMemberSql);
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        if (!CollectionUtils.isEmpty(list)){
            log.info("queryMemberByCustomerNo查询结果:{}",JSON.toJSONString(list));
            for (Map<String, Object> objectMap : list) {
                map.put("memberId", String.valueOf(objectMap.get("memberId")));
                map.put("openId", String.valueOf(objectMap.get("openId")));
                map.put("appId", String.valueOf(objectMap.get("appId")));
                map.put("mobile", String.valueOf(objectMap.get("mobile")));
                map.put("gender", String.valueOf(objectMap.get("gender")));
                map.put("customerNo",String.valueOf(objectMap.get("customerNo")));
                map.put("memberType",String.valueOf(objectMap.get("memberType")));
                map.put("memberName",String.valueOf(objectMap.get("memberName")));
            }
            return map;
        }
        return null;
    }





    public List<Map<String, String>> queryCustomerNoList(String memberId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        String queryMemberSql = " select customerNo,channelType,openId from " + ModelConstants.MEMBER_MEMBERBINDING + " where  memberId = :memberId and relType !=2  ";
        List<Map<String, String>> list = execute(queryMemberSql, queryMap).getData();
        return list;
    }

    /**
     * 查询会员customerNo,memberId
     */
    public Map<String, String> getCustomerNo(String memberId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        queryMap.put("channelType", "KO_MP");
        String queryMemberSql = " select memberId,customerNo,openId,appId,mobile,memberName,gender,memberType from " + ModelConstants.MEMBER_MEMBERBINDING + " where memberId = :memberId and channelType = :channelType and relType != '2' LIMIT 1";
        List<Map<String, String>> list = execute(queryMemberSql, queryMap).getData();
        log.info("查询的customerNo:{}", JSON.toJSONString(list));
        String customerNo = null;
        String openId = null;
        String appId = null;
        String mobile = null;
        String gender = null;
        String memberType = null;
        String memberName = null;
        for (Map<String, String> map : list) {
            customerNo = map.get("customerNo");
            memberId = map.get("memberId");
            openId=map.get("openId");
            appId=map.get("appId");
            mobile=map.get("mobile");
            gender=map.get("gender");
            memberType=map.get("memberType");
            memberName=map.get("memberName");
        }
        Map<String, String> hashMap = new HashMap<>();
        hashMap.put("customerNo", customerNo);
        hashMap.put("memberId", memberId);
        hashMap.put("openId", openId);
        hashMap.put("appId", appId);
        hashMap.put("mobile", mobile);
        hashMap.put("gender", gender);
        hashMap.put("memberType", memberType);
        hashMap.put("memberName", memberName);
        return hashMap;
    }


    /**
     * 查询会员customerNo
     */
    public String queryMember(String customerNo,String channelType,String mobile) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("customerNo", customerNo);
        queryMap.put("channelType", channelType);
        queryMap.put("mobile", mobile);
        String queryMemberSql = " select memberId from " + ModelConstants.MEMBER_MEMBERBINDING + " where  customerNo = :customerNo and mobile = :mobile and channelType = :channelType and relType !=2 ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        String memberId = null;
        for (Map map : list) {
            memberId = map.get("memberId").toString();
        }
        return memberId;
    }


    /**
     * 查询会员customerNo
     */
    public String queryCustomerNoUnioId(String unionId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("unionId", unionId);
        String queryMemberSql = " select memberId from " + ModelConstants.MEMBER_MEMBERBINDING + " where  unionId = :unionId and relType !=2";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        String customerNo = null;
        for (Map map : list) {
            customerNo = map.get("memberId").toString();
        }
        return customerNo;
    }


    /**
     * 查询会员可以积分
     */
    public Map<String,Object> getMemberPoint(String mobile) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("mobile", mobile);
        String queryMemberSql = " select rsvField7,experiencePoints,rsvField8,rsvField9,firstRegisterChannelType from " + ModelConstants.Member + " where  mobile = :mobile";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        HashMap<String, Object> hashMap = new HashMap<>();
        for (Map map : list) {
            hashMap.put("experiencePoints",map.get("experiencePoints") == null ? 0.0 : Double.valueOf(map.get("experiencePoints").toString()));
            hashMap.put("frozenPoint",map.get("rsvField7") == null ? 0.0 : Double.valueOf(map.get("rsvField7").toString()));
            hashMap.put("usedPoint",map.get("rsvField8") == null ? 0.0 : Double.valueOf(map.get("rsvField8").toString()));
            hashMap.put("totalPoint",map.get("rsvField9") == null ? 0.0 : Double.valueOf(map.get("rsvField9").toString()));
            hashMap.put("firstRegisterChannelType", map.get("firstRegisterChannelType").toString());
        }
        return hashMap;
    }

    /**
     * 查询所以经验值
     *
     * @param memberId
     */
    public String getTotalEmpirical(String memberId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        String queryMemberSql = " select experiencePoints from " + ModelConstants.Member + " where  memberId = :memberId ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        String totalEmpirical = "0.0";
        for (Map map : list) {
            totalEmpirical = map.get("experiencePoints").toString();
        }
        return totalEmpirical;
    }


    /**
     * 查询会员occid
     */
    public String getMemberByOccId(String memberId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        String queryMemberSql = " select occId from " + ModelConstants.Member + " where  memberId = :memberId ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        String occId = null;
        for (Map map : list) {
            occId = map.get("occId").toString();
        }
        return occId;
    }

    /**
     * 查询会员occid
     */
    public String getOmniChannelCustomerByOccId(String originId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("originId", originId);
        String queryMemberSql = " select id from " + ModelConstants.OMNICHANNER_CUSTOMER + " where  originId = :originId ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        String occId = null;
        for (Map map : list) {
            occId = map.get("id").toString();
        }
        return occId;
    }

    /**
     * 更新会员mobileCancel ，isCancel
     *
     * @param memberId
     */
    public void updateMemberDto(String memberId, Map<String, Object> params) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        update(ModelConstants.Member, queryMap, params);
    }

    /**
     * 查询冻结积分
     */
    public List<String> queryfreezePointTraceId(String memberId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        queryMap.put("status", "FROZEN");
        String queryfreezePointTraceId = " select traceId from " + ConfigurationCenterUtil.MEMBER_POINTRECORD + " where  memberId = :memberId and status = :status order by created desc limit 1 ";
        log.info("查询积分SQL语句:{}", queryfreezePointTraceId);
        log.info("查询积分SQL语句参数:{}", queryMap.toString());
        List<Map<String, Object>> list = execute(queryfreezePointTraceId, queryMap).getData();
        log.info("查询积分SQL结果:{}", list);
        List<String> traceIdList = new ArrayList();
        //String traceId = null;
        for (Map map : list) {
            String traceId = map.get("traceId") == null ? "" : map.get("traceId").toString();
            traceIdList.add(traceId);
        }
        return traceIdList;
    }

    /**
     * 查询occId
     */
    public String queryOccId(String memberId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        String queryMemberSql = " select occId from " + ModelConstants.Member + " where  memberId = :memberId ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        String occId = null;
        for (Map map : list) {
            occId = map.get("occId").toString();
        }
        return occId;
    }

    public String queryPointReord(String memberId, String recordType) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        queryMap.put("recordType", recordType);
        queryMap.put("created1", DateHelper.getZone(DateHelper.getDateDay()));
        queryMap.put("created2", DateHelper.getZone(DateHelper.getDateTimeFormat()));
        queryMap.put("changeMode", "INTERFACE");
        queryMap.put("changeMode1", "INTERFACE_BATCH");
        String queryfreezePointTraceId = " select sum(changePoint) point from " + ConfigurationCenterUtil.MEMBER_POINTRECORD + " where  memberId = :memberId and recordType = :recordType and created >= :created1 and created <= :created2 and ( changeMode = :changeMode or changeMode = :changeMode1) ";
        log.info("查询积分SQL语句:{}", queryfreezePointTraceId);
        log.info("查询积分SQL语句参数:{}", queryMap.toString());
        List<Map<String, Object>> list = execute(queryfreezePointTraceId, queryMap).getData();
        log.info("积分明细查询结果:{}", list);
        String point = "0";
        for (Map map : list) {
            if (map.get("point") != null) {
                point = map.get("point").toString();
            }
        }
        return point;
    }

    public String queryByondPointReord(String memberId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        String queryfreezePointTraceId = " select count(distinct(KZZD1)) total from " + ConfigurationCenterUtil.BEYOND_POINTRECORD + " where  memberId = :memberId ";
        log.info("查询积分SQL语句:{}", queryfreezePointTraceId);
        log.info("查询积分SQL语句参数:{}", queryMap.toString());
        List<Map<String, Object>> list = execute(queryfreezePointTraceId, queryMap).getData();
        String total = "0";
        for (Map map : list) {
            if (map.get("total") != null) {
                total = map.get("total").toString();
            }

        }
        return total;
    }

    public String queryByondPointCount(String memberId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        String queryfreezePointTraceId = " select sum(diffPoint) countPoint from " + ConfigurationCenterUtil.BEYOND_POINTRECORD + " where  memberId = :memberId ";
        log.info("查询积分SQL语句:{}", queryfreezePointTraceId);
        log.info("查询积分SQL语句参数:{}", queryMap.toString());
        List<Map<String, Object>> list = execute(queryfreezePointTraceId, queryMap).getData();
        String countPoint = "0";
        for (Map map : list) {
            if (map.get("countPoint") != null) {
                countPoint = map.get("countPoint").toString();
            }
        }
        return countPoint;
    }

    //查询等级
    public CepMemberGradeDto queryMemberGrade(String memberId) throws ParseException {
        CepMemberGradeDto cepMemberGradeDto = new CepMemberGradeDto();
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        String queryfreezePointTraceId = " select currentGradeDefinitionId,currentGradeName,effectDate,overdueDate from " + ConfigurationCenterUtil.MEMBER_GRADE + " where  memberId = :memberId ";
        log.info("查询积分SQL语句:{}", queryfreezePointTraceId);
        log.info("查询积分SQL语句参数:{}", queryMap.toString());
        List<Map<String, Object>> list = execute(queryfreezePointTraceId, queryMap).getData();
        for (Map map : list) {
            cepMemberGradeDto.setId(map.get("currentGradeDefinitionId").toString());
            cepMemberGradeDto.setName(map.get("currentGradeName").toString());
            String effectDate = map.get("effectDate") == null ? "" : map.get("effectDate").toString();
            if (StringUtil.isNotBlank(effectDate)) {
                cepMemberGradeDto.setEffectiveTime(DateHelper.formatDateStr(effectDate, DateHelper.DATE_ZONE));
            }
            String overdueDate = map.get("overdueDate") == null ? "" : map.get("overdueDate").toString();
            if (StringUtil.isNotBlank(overdueDate)) {
                cepMemberGradeDto.setExpiredTime(DateHelper.formatDateStr(overdueDate, DateHelper.DATE_ZONE));
            }

        }
        return cepMemberGradeDto;
    }

    /**
     * 查询店铺信息
     */
    public Map<String, String> queryShopInfo(String shopCode) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("shopCode", shopCode);
        String queryMemberSql = " select shopCode,shopName from " + ModelConstants.SHOP + " where  shopCode = :shopCode ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        Map<String, String> shopInfo = new HashMap<>();
        for (Map map : list) {
            shopInfo.put("shopCode", map.get("shopCode").toString());
            shopInfo.put("shopName", map.get("shopName").toString());
        }
        return shopInfo;
    }

    /**
     * 查询商品
     */
    public Map<String, String> queryProduct(String productCode) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("skuCode", productCode);
        String queryMemberSql = " select skuCode,prodName from " + ModelConstants.ECPRODUCT + " where  skuCode = :skuCode ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        Map<String, String> productInfo = new HashMap<>();
        for (Map map : list) {
            productInfo.put("skuCode", map.get("skuCode").toString());
            productInfo.put("prodName", map.get("prodName").toString());
        }
        return productInfo;
    }

    public Map<String, String> queryThreshold() {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("1", 1);
        String querySql = " select dailyPointsThreshold,timesThreshold,refundPointsThreshold from " + ModelConstants.CONFIG_LIST + " where  1 = :1 ";
        log.info("查询阈值SQL语句:{}", querySql);
        log.info("查询阈值SQL语句参数:{}", queryMap.toString());
        List<Map<String, Object>> list = execute(querySql, queryMap).getData();
        Map<String, String> valueMap = new HashMap<>();
        for (Map map : list) {
            valueMap.put("dailyPointsThreshold", map.get("dailyPointsThreshold").toString());
            valueMap.put("timesThreshold", map.get("timesThreshold").toString());
            valueMap.put("refundPointsThreshold", map.get("refundPointsThreshold").toString());
        }
        return valueMap;
    }

    /**
     *
     */
    public String queryBlackList(String memberId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("id", memberId);
        String queryMemberSql = " select id from " + ModelConstants.BLACKILST + " where  id = :id ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        String id = null;
        for (Map map : list) {
            id = map.get("id").toString();
        }
        return id;
    }

    /**
     * 会员注销 更新MemberBinding
     *
     * @param s
     */
    public void updateMemberBinding(String s,String customerNo,String channelType) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", s);
        queryMap.put("customerNo", customerNo);
        queryMap.put("channelType", channelType);
        String queryMemberSql = "UPDATE " + ModelConstants.MEMBER_MEMBERBINDING + " SET customerNoCancel = :customerNo,customerNo = '',relType = '2'  where  memberId = :memberId and channelType = :channelType";
        execute(queryMemberSql, queryMap).getData();
    }

    /**
     * 会员注销 删除MemberIdentify
     *
     * @param s
     */
    public void updateMembrIdtify(String s) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", s);
        String queryMemberSql = "DELETE FROM " + ModelConstants.MEMBER_MEMBERRIDENTIFY + " where  memberId = :memberId ";
        execute(queryMemberSql, queryMap).getData();
    }

    /**
     * 查询会员memberId
     */
    public String queryOpenId(String unionId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("unionId", unionId);
        String queryMemberSql = " select memberId from " + ModelConstants.MEMBER_MEMBERBINDING + " where  unionId = :unionId and relType != '2'  LIMIT 1 ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        String memberId = null;
        for (Map map : list) {
            memberId = map.get("memberId").toString();
        }
        return memberId;
    }


    /**
     * 查询会员openId
     */
    public String queryMemberOpneId(String memberId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        queryMap.put("channelType", "KO_MP");
        String queryMemberSql = " select id,openId from " + ModelConstants.MEMBER_MEMBERBINDING + " where  memberId = :memberId and channelType = :channelType and relType !=2";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        String openId = null;
        for (Map map : list) {
            openId = map.get("openId").toString();
        }
        return openId;
    }

    /**
     * 查询会员信息
     */
    public Map<String, String> queryMemberInfo(String memberId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        queryMap.put("channelType", "KO_MP");
        String queryMemberSql = " select openId,unionId,mobile from " + ModelConstants.MEMBER_MEMBERBINDING + " where  memberId = :memberId and channelType = :channelType ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        Map<String, String> memberInfo = new HashMap<>();
        for (Map map : list) {
            memberInfo.put("openId",map.get("openId").toString()) ;
            memberInfo.put("unionId",map.get("unionId").toString()) ;
            memberInfo.put("mobile",map.get("mobile").toString()) ;
        }
        return memberInfo;
    }

    /**
     * 查询是否在用户注销列表
     * @param koId
     * @return
     */
    public Boolean getConsumerCancel(String koId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("koId", koId);
        String queryMemberSql = " select count(1) count from " + ModelConstants.CONSUMER_CANCEL +" where  status = 'Y' and koId = :koId ";
        log.info("查询是否在用户注销列表:{}",queryMemberSql);
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        Integer count = null;
        for (Map map : list) {
            count = Integer.decode(map.get("count").toString());
        }
        return count>0;
    }

    //查询活动主数据
    public Map<String, String> queryCampaign(String campaignCode) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("campaignCode", campaignCode);
        String queryMemberSql = " select campaignCode,status,startTime,endTime from " + ModelConstants.CAMPAIGN_SUBJECT + " where  campaignCode = :campaignCode ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        Map<String, String> campaignMap = new HashMap<>();
        for (Map map : list) {
            campaignMap.put("campaignCode", map.get("campaignCode").toString());
            campaignMap.put("status", map.get("status").toString());
            campaignMap.put("startTime", map.get("startTime").toString());
            campaignMap.put("endTime", map.get("endTime").toString());
        }
        return campaignMap;
    }

    /**
     * 修改活动次数为 null
     *
     * @param sum
     * @param scene
     */
    public void queryCampaignNum(String sum, String scene) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("scene", scene);
        String queryMemberSql = " UPDATE " + ModelConstants.CAMPAIGN_CAMPAIGNPOINTSRULE + " SET " + sum + " =null where  scene = :scene ";
        execute(queryMemberSql, queryMap);
    }

    /**
     * 查询订阅会员
     *
     * @param id
     */
    public String getSubscribeId(String id) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("id", id);
        String queryMemberSql = " select id from " + ModelConstants.MEMBER_SUBSCRIPTIONS + " where  id = :id ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        String openId = null;
        for (Map map : list) {
            openId = map.get("id").toString();
        }
        return openId;
    }

    /**
     * 更新订阅会员koid
     *
     * @param id
     * @param koId
     */
    public void updateSubscrId(String id, String koId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("id", id);
        queryMap.put("koId", koId);
        String queryMemberSql = " UPDATE " + ModelConstants.MEMBER_SUBSCRIPTIONS + " SET koId =:koId where  id = :id ";
        execute(queryMemberSql, queryMap);
    }


    /**
     * 查询会员绑定信息是否存在
     */
    public Boolean queryCustomerNoOrMobile(String customerNo, String mobile,String channelType) {
        Map<String, Object> queryMap = new HashMap<>();
        if(StringUtils.isBlank(mobile)){
            queryMap.put("customerNo", customerNo);
            queryMap.put("channelType", channelType);
            String querySql = " select count(1) count from " + ModelConstants.MEMBER_MEMBERBINDING + " where customerNo = :customerNo and  channelType = :channelType and relType != '2' ";
            List<Map<String, Object>> list = execute(querySql, queryMap).getData();
            Integer count = null;
            for (Map map : list) {
                count = Integer.decode(map.get("count").toString());
            }
            return count > 0;
        }else {
            queryMap.put("customerNo", customerNo);
            queryMap.put("mobile", mobile);
            queryMap.put("channelType", channelType);
            String querySql = " select count(1) count from " + ModelConstants.MEMBER_MEMBERBINDING + " where channelType = :channelType and relType != '2' and (customerNo = :customerNo or mobile = :mobile) ";
            List<Map<String, Object>> list = execute(querySql, queryMap).getData();
            Integer count = null;
            for (Map map : list) {
                count = Integer.decode(map.get("count").toString());
            }
            return count > 0;
        }
    }

    /**
     * 查询会员绑定信息是否存在
     */
    public Boolean queryCustomerNoOrMobile(String customerNo, String mobile,String channelType,String appId) {
        Map<String, Object> queryMap = new HashMap<>();
        if(StringUtils.isBlank(mobile)){
            queryMap.put("customerNo", customerNo);
            queryMap.put("channelType", channelType);
            String querySql = " select count(1) count from " + ModelConstants.MEMBER_MEMBERBINDING + " where customerNo = :customerNo and  channelType = :channelType and relType != '2' ";
            List<Map<String, Object>> list = execute(querySql, queryMap).getData();
            Integer count = null;
            for (Map map : list) {
                count = Integer.decode(map.get("count").toString());
            }
            return count > 0;
        }else {
            queryMap.put("appId", appId);
            queryMap.put("mobile", mobile);
            queryMap.put("channelType", channelType);
            String querySql = " select count(1) count from " + ModelConstants.MEMBER_MEMBERBINDING + " where mobile = :mobile and channelType = :channelType and relType != '2' and appId = :appId ";
            List<Map<String, Object>> list = execute(querySql, queryMap).getData();
            Integer count = null;
            for (Map map : list) {
                count = Integer.decode(map.get("count").toString());
            }
            return count > 0;
        }
    }

    /**
     * 查询会员koid
     */
    public String queryKoidByOpenId(String openId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("openId", openId);
        String querySql = " select koId  from " + ModelConstants.COSMOS_CONSUMER + " where openId = :openId ";
        List<Map<String, Object>> list = execute(querySql, queryMap).getData();
        String koid = null;
        for (Map map : list) {
            koid = map.get("koId") == null ? null : map.get("koId").toString();
        }
        return koid;
    }


    /**
     * 查询会员绑定信息
     */
    public UnbindMemberDto queryCustomerByCustomerNo(String customerNo) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("customerNo", customerNo);
        String querySql = " select customerNo,channelType,shopCode,memberType  from " + ModelConstants.MEMBER_MEMBERBINDING + " where   customerNo = :customerNo ";
        List<Map<String, Object>> list = execute(querySql, queryMap).getData();
        UnbindMemberDto unbindMemberDto = new UnbindMemberDto();
        for (Map map : list) {
            unbindMemberDto.setCustomerNo(map.get("customerNo").toString());
            unbindMemberDto.setChannelType(map.get("channelType").toString());
            unbindMemberDto.setShopCode(map.get("shopCode") == null ? null : map.get("shopCode").toString());
            unbindMemberDto.setMemberType(map.get("memberType").toString());
        }
        return unbindMemberDto;
    }


    /**
     * 查询会员openId
     */
    public Map queryOpneIdByKoid(String koId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("koId", koId);
        String queryMemberSql = " select openId  from " + ModelConstants.COSMOS_CONSUMER + " where koId = :koId ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        Map<String, String> returnMap = new HashMap<>();
        for (Map map : list) {
            returnMap.put("openId", map.get("openId").toString());
        }
        return returnMap;
    }

    /**
     * 查询会员blacklistFrozenPoint
     */
    public double queryBlacklistFrozenPoint(String memberId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        String queryMemberSql = " select blacklistFrozenPoint from " + ModelConstants.Member + " where  memberId = :memberId LIMIT 1 ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        log.info("查询会员blacklistFrozenPointSQL语句:{}", queryMemberSql);
        double blacklistFrozenPoint = 0.0;
        for (Map map : list) {
            String Point = map.get("blacklistFrozenPoint")== null ? null : map.get("blacklistFrozenPoint").toString();
            if(!StringUtils.isBlank(Point)){
                blacklistFrozenPoint=Double.parseDouble(Point);
            }
        }
        log.info("查询会员blacklistFrozenPointSQL:{}", blacklistFrozenPoint);
        return blacklistFrozenPoint;
    }


    /**
     * 更新小程序订阅批量任务状态
     * @param taskId
     * @param beforeState  更新前的状态
     * @param afterState    更新后的状态
     */
    public void updateAppletTask(String taskId,String beforeState,String afterState) {

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId",taskId);
        queryMap.put("beforeState",beforeState);
        queryMap.put("afterState",afterState);
        String queryOrderSql = " UPDATE " + ModelConstants.APPLETTASK + " SET consumptionStatus = :afterState WHERE taskId = :taskId and consumptionStatus = :beforeState ";
        log.info("更新小程序订阅批量任务状态taskId:{},beforeState:{},afterState:{},sql:{}",taskId,beforeState,afterState,queryOrderSql);
        execute(queryOrderSql, queryMap);
    }

    /**
     * 查询任务是否存在
     * @param taskId
     */
    public String getSubscribeTaskId(String taskId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId",taskId);
        String queryOrderSql = " select taskId from " + ModelConstants.APPLETTASK + " WHERE taskId = :taskId";
        log.info("查询任务是否存在taskId:{},sql:{}",taskId,queryOrderSql);
        List<Map<String, Object>> list = execute(queryOrderSql, queryMap).getData();
        String id = null;
        for (Map map : list) {
            id = String.valueOf(map.get("taskId"));
        }
        return id;
    }

    /**
     * 更新优惠券任务状态
     *
     * @param taskId
     * @param beforeState
     * @param afterState
     */
    public void updateCouponsTask(String taskId, String beforeState,String afterState) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId",taskId);
        queryMap.put("beforeState",beforeState);
        queryMap.put("afterState",afterState);
        String queryOrderSql = " UPDATE " + ModelConstants.COUPONYASK + " SET consumptionStatus = :afterState WHERE taskId = :taskId and consumptionStatus = :beforeState ";
        log.info("更新优惠券批量任务状态taskId:{},beforeState:{},afterState:{},sql:{}",taskId,beforeState,afterState,queryOrderSql);
        execute(queryOrderSql, queryMap);
    }


    /**
     * 查询优惠券任务是否存在
     * @param taskId
     */
    public String getCouponsTaskId(String taskId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId",taskId);
        String queryOrderSql = " select id from " + ModelConstants.COUPONYASK + " WHERE taskId = :taskId";
        log.info("查询任务是否存在taskId:{},sql:{}",taskId,queryOrderSql);
        List<Map<String, Object>> list = execute(queryOrderSql, queryMap).getData();
        String id = null;
        for (Map map : list) {
            id = String.valueOf(map.get("id"));
        }
        return id;
    }

    /**
     *
     * @param taskId
     */
    public String getCouponsTask(String taskId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId",taskId);
        String queryOrderSql = " select consumptionStatus from " + ModelConstants.COUPONYASK + " WHERE taskId = :taskId";
        List<Map<String, Object>> list = execute(queryOrderSql, queryMap).getData();
        String consumptionStatus = "0";
        for (Map map : list) {
            consumptionStatus = String.valueOf(map.get("consumptionStatus"));
        }
        return consumptionStatus;
    }

    public String getAppletSubscribe(String taskId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId",taskId);
        String queryOrderSql = " select consumptionStatus from " + ModelConstants.APPLETTASK + " WHERE taskId = :taskId";
        List<Map<String, Object>> list = execute(queryOrderSql, queryMap).getData();
        String consumptionStatus = "0";
        for (Map map : list) {
            consumptionStatus = String.valueOf(map.get("consumptionStatus"));
        }
        return consumptionStatus;
    }

    public String queryConsumerOpenId(String unionId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("unionId", unionId);
        String queryMemberSql = " select openId from " + ModelConstants.COSMOS_CONSUMER + " where  unionId = :unionId  LIMIT 1 ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        String openId = null;
        for (Map map : list) {
            openId = map.get("openId").toString();
        }
        return openId;
    }

    /**
     * 查询ConsumerPlatInfo记录
     *
     * @param koid
     */
    public List<Map<String, Object>> getConsumerInfo(Object koid) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("koid", koid);
        String queryMemberSql = " select koid,platform,type,value from " + ModelConstants.COSMOS_CONSUMERPLASTINFO + " where  koid = :koid ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        return list;

    }

    /**
     * 根据traceId 查询积分记录
     *
     * @param traceId
     * @return
     */
    public Double queryMemberTraceId(String traceId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("traceId", traceId);
        String queryMemberSql = " select changePoint from " + ModelConstants.MEMBER_POINTRECORD + " where  traceId = :traceId LIMIT 1 ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        Double changePoint = 0.0;
        if (!CollectionUtils.isEmpty(list)){
            for (Map map : list) {
                changePoint  = Double.valueOf(String.valueOf(map.get("changePoint"))) ;
            }
        }
        return changePoint;
    }

    public Double queryBeyondPoint(String channelType, String changeType) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("channelType", channelType);
        queryMap.put("isEnable", "1");
        String sql = null;
        if ("SEND".equals(changeType)){
            sql = " select upperLimit as changePoint from " + ModelConstants.POINT_INTERFACE_LIMITEDPOINTS + " where  channel = :channelType and isEnable = 1 ";
        }
        if ("DEDUCT".equals(changeType)){
            sql = " select abs(lowerLimit) as changePoint from " + ModelConstants.POINT_INTERFACE_LIMITEDPOINTS + " where  channel = :channelType and isEnable = 1 ";
        }
        List<Map<String, Object>> list = execute(sql, queryMap).getData();
        Double changePoint = null;
        if (!CollectionUtils.isEmpty(list)){
            for (Map map : list) {
                changePoint  = Double.valueOf(String.valueOf(map.get("changePoint"))) ;
            }
        }
        return changePoint;
    }
}
