package com.shuyun.kylin.customized.spi.resource;

import com.shuyun.kylin.crm.openapi.core.dto.common.*;
import com.shuyun.kylin.crm.openapi.core.dto.spi.CreateCardNoDto;
import com.shuyun.kylin.crm.openapi.core.dto.spi.CreateMemberIdDto;
import com.shuyun.kylin.crm.openapi.core.dto.spi.MemberFieldRequest;
import com.shuyun.kylin.crm.openapi.core.dto.spi.MemberFieldResponse;
import com.shuyun.kylin.crm.openapi.spi.ISupportService;
import com.shuyun.pip.component.json.JsonUtils;
import com.shuyun.pip.util.DateUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Description 用于定制化 Openapi 一些特殊业务逻辑,如: 按客户要求生成卡号或生成会员ID
 * 前提:  /customized-service/customized-service.openapi.spi.enabled=true
 * @date 2020/4/12
 */
@Tag(name = "定制化逻辑", description = "用于定制化 Openapi 一些特殊业务逻辑,如: 按客户要求生成卡号或生成会员ID")
@RestController
@RequestMapping("/openapi")
@ConditionalOnExpression("${customized-service.openapi.spi.enabled}")
public class CustomizedSupportSpiResource implements ISupportService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());


    @Override
    @PostMapping("/generateCardNo")
    @Operation(summary = "定制卡号生成器", description = "应用场景: 用于根据客户规则生成卡号")
    public String generateCardNo(@Valid @RequestBody CreateCardNoDto createCardNoDto) {
        logger.info("接收到卡号生成请求,params={}",JsonUtils.toJson(createCardNoDto));
        return null;
    }

    @Override
    @PostMapping("/generateMemberId")
    @Operation(summary = "定制会员ID生成器", description = "应用场景: 用于根据客户规则生成会员ID")
    public String generateMemberId(@Valid @RequestBody CreateMemberIdDto createMemberIdDto) {
        logger.info("接收到会员ID生成请求,params={}",JsonUtils.toJson(createMemberIdDto));
        return null;
    }


    @Override
    @PostMapping("queryPointEffectPeriod")
    @Operation(summary = "定制积分生效/失效时间", description = "应用场景: 用于根据客户规则生成积分生效/失效时间")
    public PointEffectPeriodDto queryPointEffectPeriod(@Valid @RequestBody PointRequestDto pointRequestDto) {
        logger.info("接收到定制积分有效期请求,params:{}",JsonUtils.toJson(pointRequestDto));
        PointEffectPeriodDto pointEffectPeriodDto = new PointEffectPeriodDto();
        pointEffectPeriodDto.setEffectTime(DateUtils.parseDateTime("2020-11-16 00:00:00"));
        pointEffectPeriodDto.setExpiredTime(DateUtils.parseDateTime("2020-11-22 00:00:00"));
        return pointEffectPeriodDto;
    }


    @Override
    @PostMapping("queryGradeEffectInfo")
    @Operation(summary = "定制等级过期时间", description = "应用场景: 用于根据客户规则生成等级过期时间")
    public GradeEffectPeriodDto queryGradeEffectInfo(@Valid @RequestBody GradeRequestDto request) {
        return null;
    }

    @Override
    @PostMapping("queryThirdMemberInfo")
    @Operation(summary = "定制获取第三方会员信息", description = "应用场景: 用于合卡时获取第三方会员信息")
    public MemberFieldResponse queryThirdMemberInfo(@Valid @RequestBody MemberFieldRequest memberFieldRequest) {
        return null;
    }

    @Override
    @PostMapping("convertAccessData")
    @Operation(summary = "转换接入数据", description = "应用场景: 用于配置接口 数据接入时，保存到数据服务前 对数据进行转化")
    public SaveDataRequestDto convertAccessData(@Valid @RequestBody SaveDataRequestDto saveDataRequestDto) {
        logger.info("接收到来自于openapi的数据接入 转换请求，参数:{}", JsonUtils.toJson(saveDataRequestDto));
        return saveDataRequestDto;
    }

    @Override
    @PostMapping("processDataTransferPostBizLogic")
    @Operation(summary = "处理数据对接后置业务逻辑", description = "应用场景: 数据保存后，执行额外业务逻辑处理")
    public void processDataTransferPostBizLogic(@Valid @RequestBody SaveDataRequestDto request) {
        logger.info("接收到来自于openapi的数据接入-处理数据对接后置业务逻辑请求，参数:{}", JsonUtils.toJson(request));
        //TODO

    }
}
