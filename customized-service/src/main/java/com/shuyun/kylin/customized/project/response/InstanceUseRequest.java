package com.shuyun.kylin.customized.project.response;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class InstanceUseRequest {

    /**
     * 品牌名称 固定值KO
     */
    @NotEmpty(message = "品牌名称不能为空")
    private String brand;
    /**
     * KO_MP：微信可口可乐吧
     */
    @NotEmpty(message = "渠道类型不能为空")
    private String channelType;
    /**
     * 全局唯一
     */
    @NotEmpty(message = "transactionId不能为空")
    private String transactionId;
    /**
     * 劵码
     */
    @NotEmpty(message = "券码不能为空")
    private String couponCodes;
    /**
     * 外部券码
     */
    private String externalCode;
    /**
     * 项目Id
     */
    @NotEmpty(message = "项目ID不能为空")
    private String projectId;
    /**
     * 外部项目Id
     */
    private String externalProjectId;
    /**
     * 收货人姓名
     */
    private String memberName;
    /**
     * 收货省
     */
    private String province;
    /**
     * 收货市
     */
    private String city;
    /**
     * 收货区
     */
    private String district;
    /**
     * 收货地址
     */
    private String address;
    /**
     * 收货邮编
     */
    private String postalCode;
    /**
     * 收货手机号
     */
    private String recipientmobile;
    /**
     * 核销对应的lbs省
     */
    private String usedLBSProvince;
    /**
     * 核销对应的lbs市
     */
    private String usedLBSCity;
    /**
     * 核销对应的lbs区
     */
    private String usedLBSDistrict;

}
