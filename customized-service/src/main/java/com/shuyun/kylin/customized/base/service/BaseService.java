package com.shuyun.kylin.customized.base.service;


import com.shuyun.api.hub.client.ApiMgmtHttpClientFactory;
import com.shuyun.api.hub.mgmt.request.HttpApiRegisterRequest;
import com.shuyun.motor.common.cons.App;
import com.shuyun.motor.common.cons.PropsUtil;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/*@Service*/
public class BaseService {




   /* @PostConstruct
    @Order(1000)*/
    public void init(){
        String json ="{\"openapi\":\"3.0.1\",\"info\":{\"title\":\"OpenAPI definition\",\"version\":\"v0\"},\"servers\":[{\"url\":\"http://localhost:8080\",\"description\":\"Generated server url\"}],\"tags\":[{\"name\":\"成本中心11\",\"description\":\"成本中心11\"}],\"paths\":{\"/cost/center/upset\":{\"post\":{\"tags\":[\"成本中心保存修改接口11\"],\"summary\":\"成本中心保存修改11\",\"operationId\":\"upsetCostCenter\",\"requestBody\":{\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/CostCenterSaveDto\"}}},\"required\":true},\"responses\":{\"default\":{\"description\":\"OK\",\"content\":{\"*/*\":{\"schema\":{\"$ref\":\"#/components/schemas/ResponseResult\"}}}}}}}},\"components\":{\"schemas\":{\"CostCenterSaveDto\":{\"type\":\"object\",\"properties\":{\"costCenterCode\":{\"type\":\"string\"},\"costCenterName\":{\"type\":\"string\"},\"parentCostCenterCode\":{\"type\":\"string\"},\"isSettlement\":{\"type\":\"string\"},\"parentCostCenterName\":{\"type\":\"string\"},\"id\":{\"type\":\"string\"},\"lastSync\":{\"type\":\"string\"},\"created\":{\"type\":\"string\"}}},\"ResponseResult\":{\"type\":\"object\",\"properties\":{\"code\":{\"type\":\"string\"},\"msg\":{\"type\":\"string\"},\"data\":{\"type\":\"string\"}}}}}}";
        HttpApiRegisterRequest request=new HttpApiRegisterRequest();
        request.setApiVersion(App.getApiVersion());
        request.setServiceName(App.getServiceName());
        request.setDescription(App.getServiceName());
        request.setRootPath(App.getContextPath());
        String var10001 = PropsUtil.getSysOrEnv("open.api.mgmt.register.service.title");
        if (var10001 == null) {
            var10001 = App.getServiceName();
        }
        request.setTitle(var10001);
        request.setOpenApiDoc(json);
        ApiMgmtHttpClientFactory.apiMgmt().register(request);
    }

}
