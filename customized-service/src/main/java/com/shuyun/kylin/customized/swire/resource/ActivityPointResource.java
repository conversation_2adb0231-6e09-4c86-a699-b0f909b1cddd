package com.shuyun.kylin.customized.swire.resource;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.common.ResponsesVo;
import com.shuyun.kylin.customized.base.enums.PointLimitEnum;
import com.shuyun.kylin.customized.member.dm.CampaignPointsRuleRepository;
import com.shuyun.kylin.customized.member.dto.CampaignPointsRuleDto;
import com.shuyun.kylin.customized.swire.dto.PointLimitDto;
import com.shuyun.kylin.customized.swire.dto.PointRuleDto;
import com.shuyun.kylin.customized.swire.service.ISwireDataSyncService;
import com.shuyun.kylin.starter.redis.ICache;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping(("/activity/point"))
public class ActivityPointResource {
    @Autowired
    @Qualifier("redisCache")
    private ICache iCache;

    @Resource
    private CampaignPointsRuleRepository campaignPointsRuleRepository;

    @Autowired
    private ISwireDataSyncService iSwireDataSyncService;

    @PostMapping("/setPointLimit")
    @Operation(summary = "设置活动积分上限", tags = "设置活动积分上限")
    @ApiResponses(value = {@ApiResponse(description = "设置活动积分上限", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = ResponsesVo.class))})})
    public ResponsesVo setPointLimit(@RequestBody PointLimitDto integralLimit) {
        log.info("设置活动积分上限: {}", integralLimit.getIntegralLimitVal());
        iCache.put(PointLimitEnum.INTEGRAL_LIMIT_KEY.getKey(), integralLimit.getIntegralLimitVal());
        return new ResponsesVo(200, "设置成功，会员单日获取积分上限："+iCache.get(PointLimitEnum.INTEGRAL_LIMIT_KEY.getKey()));
    }

    @PostMapping("/getPointLimit")
    @Operation(summary = "获取活动积分上限", tags = "获取活动积分上限")
    @ApiResponses(value = {@ApiResponse(description = "获取活动积分上限", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = PointLimitDto.class))})})
    public PointLimitDto getPointLimit() {
        Integer point = iCache.get(PointLimitEnum.INTEGRAL_LIMIT_KEY.getKey());
        log.info("获取活动积分上限: {}", point);
        return new PointLimitDto(point);
    }
    @PostMapping("/setCampaignPointLimit")
    @Operation(summary = "设置活动是否积分上限", tags = "设置活动是否积分上限")
    @ApiResponses(value = {@ApiResponse(description = "设置活动是否积分上限", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = ResponsesVo.class))})})
    public ResponsesVo setCampaignPointLimit(@RequestBody CampaignPointsRuleDto campaignPointsRuleDto) {
       return new ResponsesVo(200, "设置成功");
    }

    @PostMapping("/pointRule")
    @Operation(summary = "创建积分规则", tags = "创建积分规则")
    @ApiResponses(value = {@ApiResponse(description = "创建积分规则", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = ResponsesVo.class))})})
    public ResponsesVo setPointRule(@RequestBody PointRuleDto pointRuleDto) {
        log.info("创建积分规则: {}", JSON.toJSONString(pointRuleDto));
        return iSwireDataSyncService.sendPointRule(pointRuleDto);
        //return new ResponsesVo(200, "设置成功，会员单日获取积分上限："+iCache.get(PointLimitEnum.INTEGRAL_LIMIT_KEY.getKey()));
    }
}
