package com.shuyun.kylin.customized.member.service.impl;

import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuyun.dm.api.dataapi.criteria.CriteriaBuilder;
import com.shuyun.dm.api.dataapi.request.QueryDataRequest;
import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.dm.api.response.DMLResponse;
import com.shuyun.dm.api.vo.PageQueryResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.kylin.crm.openapi.core.dto.common.*;
import com.shuyun.kylin.crm.openapi.core.dto.customer.CustomerBaseDto;
import com.shuyun.kylin.crm.openapi.core.dto.customer.CustomerQueryDto;
import com.shuyun.kylin.crm.openapi.core.dto.customer.SimpleMemberDto;
import com.shuyun.kylin.crm.openapi.core.dto.customer.UpdateMemberByChannelDto;
import com.shuyun.kylin.crm.openapi.core.dto.wechat.WechatRegisterRequest;
import com.shuyun.kylin.crm.openapi.core.dto.wechat.WechatRegisterResponse;
import com.shuyun.kylin.crm.openapi.sdk.client.common.IMemberClient;
import com.shuyun.kylin.crm.openapi.sdk.client.customer.ICustomerClient;
import com.shuyun.kylin.crm.openapi.sdk.client.wechat.IWechatClient;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.common.ResponsesVo;
import com.shuyun.kylin.customized.base.enums.ChannelTypeEnum;
import com.shuyun.kylin.customized.base.enums.ModelNameEnum;
import com.shuyun.kylin.customized.base.enums.ResponseCodeEnum;
import com.shuyun.kylin.customized.base.exception.CustomizeException;
import com.shuyun.kylin.customized.base.feign.client.*;
import com.shuyun.kylin.customized.base.feign.dao.ChecklistDto;
import com.shuyun.kylin.customized.base.feign.dao.MemberBingDto;
import com.shuyun.kylin.customized.base.feign.dao.MemberCustomDto;
import com.shuyun.kylin.customized.base.feign.dao.MemberPointModifyRequest;
import com.shuyun.kylin.customized.base.job.CepCouponSchedulng;
import com.shuyun.kylin.customized.base.util.*;
import com.shuyun.kylin.customized.behavior.service.impl.AbstractInteractionSceneResolver;
import com.shuyun.kylin.customized.behavior.util.LoggingRequestInterceptor;
import com.shuyun.kylin.customized.medal.request.MedalObtainRequest;
import com.shuyun.kylin.customized.member.dm.*;
import com.shuyun.kylin.customized.member.dto.*;
import com.shuyun.kylin.customized.member.dto.ec.AttributeDto;
import com.shuyun.kylin.customized.member.service.ICepDataSyncService;
import com.shuyun.kylin.customized.member.service.ICepMemberService;
import com.shuyun.kylin.customized.swire.dto.DailyLimitedPointsRuleDto;
import com.shuyun.kylin.customized.swire.dto.RulePointRecordDto;
import com.shuyun.kylin.customized.swire.repository.DailyLimitedPointsRuleRepository;
import com.shuyun.kylin.customized.swire.repository.RulePointRecordRepository;
import com.shuyun.kylin.customized.wechat.request.LwWechatCustomerRemove;
import com.shuyun.kylin.customized.wechat.service.WechatService;
import com.shuyun.kylin.starter.redis.ICache;
import com.shuyun.pip.component.json.JsonUtils;
import com.shuyun.pip.util.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.shuyun.kylin.customized.base.util.AuthenticationUtil.SHA256;

@Slf4j
@Service
public class CepMemberServiceImpl implements ICepMemberService {

    @Autowired
    private IWechatClient iWechatClient;

    @Autowired
    private ICustomerClient iCustomerClient;

    @Autowired
    private IMemberClient memberClient;

    @Autowired
    private ICustomerClient customerClient;

    @Autowired
    private OpenApiFeignClient openApiFeignClient;

    @Autowired
    private MemberChecklistClient memberChecklistClient;

    @Autowired
    @Qualifier("redisCache")
    private ICache redisCache;

    @Autowired
    public MemberPointModifyClient memberPointModifyClient;

    @Autowired
    private MemberRepository memberRepository;

    @Autowired
    private UnionMemberChannelRepository unionMemberChannelRepository;

    @Autowired
    private CostCenterRepository costCenterRepository;

    @Autowired
    private CepCostCenterRepository cepCostCenterRepository;

    @Autowired
    private MemberCancelRepository memberCancelRepository;

    @Autowired
    private PointBehaviorRecordRepository pointBehaviorRecordRepository;

    @Autowired
    private DailyLimitedPointsRuleRepository dailyLimitedPointsRuleRepository;

    @Autowired
    private ConsumerPlatInfoRepository consumerPlatInfoRepository;

    @Autowired
    private RulePointRecordRepository rulePointRecordRepository;

    @Autowired
    private LoyaltyFacade loyaltyFacade;

    @Autowired
    private EpassportFeignClient epassportFeignClient;

    @Autowired
    ICepDataSyncService iCepDataSyncService;

    @Autowired
    private WechatService wechatService;


    final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();

    final static String CHANGE_SOURCE_ACTIVITY = "参与活动";

    final static String CHANGE_SOURCE_INTERACTIVE = "参与互动";

    final static String CHANGE_SOURCE_DEDUCT_CANCEL = "扣减取消";

    final static String REDIS_BNSID = "bns_key";

    private static volatile ExecutorService executorService;

    private static final String NATURAL_FLOW = "自然流量";
    private static final String YUE_XI_HUI = "悦喜荟注册";

//    @Override
//    public ResponseResult wechatReg(WechatRegisterRequestDto request) {
//        WechatRegisterResponse response = null;
//        WechatRegisterRequest requests =null;
//        String memberId = "";
//        //判断 externalAppId、externalOpenId、externalChannelType三个参数是否为空
//        if (StringUtils.isNotBlank(request.getExternalAppId()) || StringUtils.isNotBlank(request.getExternalOpenId()) || StringUtils.isNotBlank(request.getExternalChannelType())) {
//            //判断channelType是否是KO
//            if ("KO".equals(request.getChannelType())) {
//                //根据appId和openId查询会员
//                String querySQL = "customerNo ='"+request.getAppId() +"_"+ request.getOpenId()+"'";
//                //查询会员
//                Map<String, String> memberDto = memberRepository.queryCustomerNoChannType(querySQL, request.getChannelType());
//                if (memberDto != null) {
//                    //调用注册接口
//                    String gradeLockKey = request.getExternalAppId() + request.getExternalOpenId();
//                    //获取的分布式锁id:
//                    Lock lock = null;
//                    try {
//                        lock = redisCache.getLock(gradeLockKey);
//                        if (lock.tryLock(3, TimeUnit.SECONDS)) {
//                            request.setAppId(request.getExternalAppId());
//                            request.setOpenId(request.getExternalOpenId());
//                            request.setChannelType(request.getExternalChannelType());
//                            requests = JsonUtils.parseJavaType(JsonUtils.toJson(request), WechatRegisterRequest.class);
//                            log.info("联合会员注册开始wechatRegisterRequest:{}", JSONObject.toJSONString(requests));
//                            response = iWechatClient.register(requests);
//                            memberId = response.getMemberId();
//                            //写入联合会员模型
//                            if (response != null && StringUtils.isNotBlank(response.getMemberId())) {
//                                HashMap<String, Object> map = new HashMap<>();
//                                MD5 md5 = MD5.create();
//                                map.put("id",md5.digestHex16( request.getAppId() + request.getOpenId()));
//                                map.put("memberId", memberId);
//                                map.put("channelType", request.getChannelType());
//                                map.put("externalAppId", request.getAppId());
//                                map.put("externalOpenId", request.getOpenId());
//                                map.put("createTime",  ZonedDateTime.now());
//                                log.info("写入联合会员模型:{}", JSONObject.toJSONString(map));
//                                DataApiUtil.insert(ModelConstants.UNION_MEMBER_CHANNEL, map);
//                            }
//                        } else {
//                            log.info("联合会员注册 获取锁超时:{}", gradeLockKey);
//                            //查询会员
//                            memberId = memberRepository.queryMember(request.getAppId() + "_" + request.getOpenId(), request.getChannelType(), request.getMobile());
//                            if (StringUtils.isNotBlank(memberId)) {
//                                return new ResponseResult(ResponseCodeEnum.SUCCESS, "REGISTERED");
//                            }
//                            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED);
//                        }
//                        log.info("联合会员注册响应:{}", JsonUtils.toJson(response));
//                    } catch (Exception e) {
//                        log.error("联合会员注册入参:{}", e.getMessage());
//                        return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, e.getMessage());
//                    } finally {
//                        //释放锁
//                        lock.unlock();
//                    }
//                }
//            }
//        }else {
//            if (!"KO_Ali".equals(request.getChannelType())) {
//                if (StringUtils.isBlank(request.getUnionId())) {
//                    return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, "入参UnionId不能为空");
//                }
//            }
//            if (request.getCustomizedProperties() == null) {
//                return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, "入参isKoMember是否原KO+会员不能为空");
//            }
//            if (request.getBindingExtProperties() == null) {
//                return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, "入参memberSource注册来源不能为空");
//            }
//            if (request.getBindingExtProperties().get("memberSource") == null) {
//                return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, "入参memberSource注册来源不能为空");
//            }
//            if ("".equals(request.getBindingExtProperties().get("memberSource")) || " ".equals(request.getBindingExtProperties().get("memberSource"))) {
//                return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, "入参memberSource注册来源不能为空字符串");
//            }
//            if (request.getBindingExtProperties().get("memberSource") != null && !"自然流量".equals(request.getBindingExtProperties().get("memberSource").toString()) && !"悦喜荟注册".equals(request.getBindingExtProperties().get("memberSource").toString())) {
//                if (request.getBindingExtProperties().get("memberSourceDetail") == null) {
//                    return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, "当memberSource为非自然流量注册时memberSourceDetail字段不能为空");
//                }
//            }
//            requests = JsonUtils.parseJavaType(JsonUtils.toJson(request), WechatRegisterRequest.class);
//            //去除手机号带+字符 或国内手机号前面的86
//            HashMap<String, Object> bindingExtProperties = new HashMap<>();
//            HashMap<String, Object> customizedProperties = new HashMap<>();
//            if (requests.getMobile().contains("+")) {
//                requests.setMobile(requests.getMobile().replace("+", ""));
//                bindingExtProperties.put("rsvField6", request.getMobile());
//            }
//            bindingExtProperties.put("memberSource", request.getBindingExtProperties().get("memberSource"));
//            customizedProperties.put("memberSource", request.getBindingExtProperties().get("memberSource"));
//            if (null != request.getBindingExtProperties().get("memberSourceDetail")) {
//                bindingExtProperties.put("memberSourceDetail", request.getBindingExtProperties().get("memberSourceDetail"));
//                customizedProperties.put("memberSourceDetail", request.getBindingExtProperties().get("memberSourceDetail"));
//            }
//            if ("自然流量".equals(request.getBindingExtProperties().get("memberSource").toString())){
//                if (null == request.getBindingExtProperties().get("memberSourceDetail")){
//                    bindingExtProperties.put("memberSourceDetail", "");
//                    customizedProperties.put("memberSourceDetail", "");
//                }
//            }
//            if (StringUtils.isNotBlank(request.getAgreementVersion())){
//                bindingExtProperties.put("rsvField11", request.getAgreementVersion());
//                customizedProperties.put("rsvField11",request.getAgreementVersion());
//            }
//            requests.setBindingExtProperties(bindingExtProperties);
//            requests.setCustomizedProperties(customizedProperties);
//            if (requests.getMobile().startsWith("86") && requests.getMobile().length() == 13) {
//                requests.setMobile(requests.getMobile().substring(2));
//            }
//            //查询会员
//            memberId = memberRepository.queryMember(requests.getAppId() + "_" + requests.getOpenId(), requests.getChannelType(), requests.getMobile());
//            if (StringUtils.isNotBlank(memberId)) {
//                return new ResponseResult(ResponseCodeEnum.SUCCESS, "REGISTERED");
//            }
//
//            if (memberRepository.queryCustomerNoOrMobile(requests.getAppId() + "_" + requests.getOpenId(), requests.getMobile(), requests.getChannelType(),requests.getAppId())) {
//                return new ResponseResult(ResponseCodeEnum.MEMBER_BINDING_EXIST, "会员mobile："+  request.getMobile() + "已经在appid："+  request.getAppId() + "绑定"+ request.getChannelType() +"渠道注册。");
//            }
//            log.info("封装注册的入参:{}", JSON.toJSONString(requests));
//
//            String gradeLockKey = requests.getMobile() + requests.getChannelType();
//            //获取的分布式锁id:
//            Lock lock = null;
//            try {
//                lock = redisCache.getLock(gradeLockKey);
//                if (lock.tryLock(3, TimeUnit.SECONDS)) {
//                    response = iWechatClient.register(requests);
//                    memberId = response.getMemberId();
//                } else {
//                    log.info("微信会员注册 获取锁超时:{}", gradeLockKey);
//                    //查询会员
//                    memberId = memberRepository.queryMember(requests.getAppId() + "_" + requests.getOpenId(), requests.getChannelType(), requests.getMobile());
//                    if (StringUtils.isNotBlank(memberId)) {
//                        return new ResponseResult(ResponseCodeEnum.SUCCESS, "REGISTERED");
//                    }
//                    return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED);
//                }
//                log.info("微信会员注册响应:{}", JsonUtils.toJson(response));
//            } catch (Exception e) {
//                log.error("微信会员注册入参:{}", e.getMessage());
//                return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, e.getMessage());
//            } finally {
//                //释放锁
//                lock.unlock();
//            }
//        }
//        //记录授权轨迹
//        if (StringUtils.isNotBlank(request.getOriginLbsCity())){
//            MemberLbsTrajectoryDto lbsTrajectoryDto = new MemberLbsTrajectoryDto();
//            lbsTrajectoryDto.setCreateTime(StringUtils.isNotEmpty(request.getRegisterTime()) ? DateHelper.getZone(request.getRegisterTime()) : DateHelper.getNowZone());
//            lbsTrajectoryDto.setChannelType(request.getChannelType());
//            lbsTrajectoryDto.setOriginLbsCity(request.getOriginLbsCity());
//            if (StringUtils.isNotBlank(request.getOriginLbsProvince())){
//                lbsTrajectoryDto.setOriginLbsProvince(request.getOriginLbsProvince());
//            }
//            if (StringUtils.isNotBlank(request.getOriginLbsDistrict())){
//                lbsTrajectoryDto.setOriginLbsDistrict(request.getOriginLbsDistrict());
//            }
//            lbsTrajectoryDto.setCustomerNo(request.getAppId()+"_"+requests.getOpenId());
//            lbsTrajectoryDto.setMemberId(response.getMemberId());
//            lbsTrajectoryDto.setType("REGISTER");
//            iCepDataSyncService.savePosition(lbsTrajectoryDto,lbsTrajectoryDto.getType());
//        }
//
//        try {
//            if (ChannelTypeEnum.SWIRE_MP.getValue().equals(request.getChannelType())) {
//                if (StringUtils.isNotBlank(request.getUpdateTime())) {
//                    //封装对象关系
//                    HashMap<String, Object> map = new HashMap<>();
//                    map.put("updateTime", request.getUpdateTime());
//                    if (StringUtils.isNotBlank(request.getGender())) {
//                        map.put("gender", request.getGender());
//                    }
//                    if (StringUtils.isNotBlank(request.getBirthYear())) {
//                        map.put("birthYear", request.getBirthYear());
//                        map.put("birthDay", request.getBirthDay());
//                    }
//                    map.put("channelType", request.getChannelType());
//                    map.put("lastSync", ZonedDateTime.now());
//                    DMLResponse res = DataApiUtil.upsertIsData(ModelConstants.WSIRE_MEMBER_RECORDS, request.getMobile(), map);
//                    if (!res.getIsSuccess()) {
//                        log.error("更新存量会员信息失败:{}", res.getOperation());
//                    }
//                }
//            }
//        } catch (Exception e) {
//            log.error("更新存量会员信息失败:{},msg:{}", JsonUtils.toJson(response), e.getMessage());
//        }
//        HashMap<String, Object> map = new HashMap<>();
//        map.put("memberId",memberId);
//        return new ResponseResult(ResponseCodeEnum.SUCCESS.getCode(), response.getStatus().toString(),map);
//    }

    @Override
    public ResponseResult wechatReg(WechatRegisterRequestDto request) {
        try {
            //处理联合会员注册逻辑
            if (isUnionMemberRegistration(request)) {
                log.info("处理联合会员注册逻辑:{}", JsonUtils.toJson(request));
                return handleUnionMemberRegistration(request);
            }
            // 参数校验
            ResponseResult validationResult = validateRequest(request);
            if (validationResult != null) {
                return validationResult;
            }
            //只处理普通会员注册逻辑
            log.info("处理普通会员注册逻辑:{}", JsonUtils.toJson(request));
            return handleNormalRegistration(request);
        } catch (Exception e) {
            log.error("会员注册异常: {}", e.getMessage(), e);
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, e.getMessage());
        }
    }


    // 参数校验方法
    private ResponseResult validateRequest(WechatRegisterRequestDto request) {
        if (!"KO_Ali".equals(request.getChannelType())) {
            if (StringUtils.isBlank(request.getUnionId())) {
                return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, "入参UnionId不能为空");
            }
        }
        if (request.getCustomizedProperties() == null) {
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, "入参isKoMember是否原KO+会员不能为空");
        }
        if (request.getMobile() == null) {
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, "入参Mobile手机号码不能为空");
        }
        if (request.getGender() == null) {
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, "入参Gender性别不能为空");
        }

        if (request.getChannelType() == null) {
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, "入参ChannelType渠道类型不能为空");
        }

        if (request.getBindingExtProperties() == null ||
                request.getBindingExtProperties().get("memberSource") == null) {
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, "入参memberSource注册来源不能为空");
        }

        String memberSource = request.getBindingExtProperties().get("memberSource").toString();
        if (StringUtils.isBlank(memberSource)) {
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, "入参memberSource注册来源不能为空字符串");
        }

        if (!NATURAL_FLOW.equals(memberSource) && !YUE_XI_HUI.equals(memberSource) &&
                request.getBindingExtProperties().get("memberSourceDetail") == null) {
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED,
                    "当memberSource为非自然流量注册时memberSourceDetail字段不能为空");
        }

        return null;
    }

    // 判断是否是联合会员注册
    private boolean isUnionMemberRegistration(WechatRegisterRequestDto request) {
        return StringUtils.isNotBlank(request.getExternalAppId()) ||
                StringUtils.isNotBlank(request.getExternalOpenId()) ||
                StringUtils.isNotBlank(request.getExternalChannelType());
    }


    // 处理联合会员注册
    private ResponseResult handleUnionMemberRegistration(WechatRegisterRequestDto request) {
        Map<String, String> memberDto = null;
        if(ObjectUtil.isNotEmpty(request.getMemberId())){
            //注册入参新增memberId字段 需要根据会员id查询会员信息
            log.info("handleUnionMemberRegistration...request:{}", JsonUtils.toJson(request));
            memberDto = memberRepository.getCustomerNo(request.getMemberId());
            //request移除会员id字段
            request.setMemberId(null);
        }else {
            String customerNo = request.getAppId() + "_" + request.getOpenId();
            memberDto = memberRepository.queryMemberByCustomerNo(
                    "customerNo ='" + customerNo + "'");
        }
        log.info("registerUnionMember...memberDto:{}", JsonUtils.toJson(memberDto));
        if (!Objects.isNull(memberDto)) {
            log.info("ko_mp会员已存在进行联合会员注册memberDto:{}", JsonUtils.toJson(memberDto));
            //注册联合会员
            return registerUnionMember(request,memberDto);
        } else {
            log.info("ko_mp会员不存在进行ko和联合注册memberDto:{}", JsonUtils.toJson(memberDto));
            // 参数校验
            ResponseResult validationResult = validateRequest(request);
            if (validationResult != null) {
                return validationResult;
            }
            //注册KO会员+绑定联合会员
            return registerKoAndUnionMember(request);
        }
    }

    // 注册联合会员
    private ResponseResult registerUnionMember(WechatRegisterRequestDto request, Map<String, String> memberDto) {
        String lockKey = request.getExternalAppId() + request.getExternalOpenId();
        return executeWithLock(lockKey, () -> {
            request.setUnionId(memberDto.get("unionId"));
            request.setChannelType(request.getExternalChannelType());
            request.setAppId(request.getExternalAppId());
            request.setOpenId(request.getExternalOpenId());
            request.setMemberType(memberDto.get("memberType"));
            request.setMemberName(memberDto.get("memberName"));
            request.setMobile(memberDto.get("mobile"));
            request.setAppType("WECHAT_MINI_PROGRAM");
            WechatRegisterRequest registerRequest = buildRegisterRequest(request);
            log.info("registerUnionMember注册联合会员开始registerRequest:{}", JsonUtils.toJson(registerRequest));
            WechatRegisterResponse response = iWechatClient.register(registerRequest);
            if (response != null && StringUtils.isNotBlank(response.getMemberId())) {
                String customerNo = request.getExternalAppId() + "_" + request.getExternalOpenId();
                saveUnionMemberModel(request, response.getMemberId());
                recordLbsTrajectory(request, response.getMemberId(), customerNo);
                return buildSuccessResponse(response);
            }
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED);
        });
    }
    private ResponseResult registerKoAndUnionMember(WechatRegisterRequestDto request) {
        //先注册可口可乐吧会员
        ResponseResult responseResult = handleNormalRegistration(request);
        if (responseResult.getMsg().equals("NEW")) {
            Map data = (HashMap) responseResult.getData();
            String memberId = data.get("memberId").toString();
            //根据会员id查询会员信息
            Map<String, String> memberDto = memberRepository.getCustomerNo(memberId);
            log.info("registerKoAndUnionMember...customerNo：{},memberDto:{}",memberDto.get("customerNo"), JsonUtils.toJson(memberDto));
            registerUnionMember(request,memberDto);
        }
        return responseResult;
    }

    // 处理普通会员注册
    private ResponseResult handleNormalRegistration(WechatRegisterRequestDto request) {
        log.info("handleNormalRegistration...request:{}", JsonUtils.toJson(request));
        WechatRegisterRequest registerRequest = buildRegisterRequest(request);
        processMobileNumber(registerRequest);
        processMemberSource(request, registerRequest);

        String customerNo = registerRequest.getAppId() + "_" + registerRequest.getOpenId();
        String memberId = memberRepository.queryMember(customerNo,
                registerRequest.getChannelType(), registerRequest.getMobile());

        if (StringUtils.isNotBlank(memberId)) {
            return new ResponseResult(ResponseCodeEnum.SUCCESS, "REGISTERED");
        }

        if (memberRepository.queryCustomerNoOrMobile(customerNo, registerRequest.getMobile(),
                registerRequest.getChannelType(), registerRequest.getAppId())) {
            return new ResponseResult(ResponseCodeEnum.MEMBER_BINDING_EXIST,
                    String.format("会员mobile：%s已经在appid：%s绑定%s渠道注册。",
                            request.getMobile(), request.getAppId(), request.getChannelType()));
        }

        String lockKey = registerRequest.getMobile() + registerRequest.getChannelType();
        return executeWithLock(lockKey, () -> {
            WechatRegisterResponse response = iWechatClient.register(registerRequest);
            if (response != null && StringUtils.isNotBlank(response.getMemberId())) {
                recordLbsTrajectory(request, response.getMemberId(), customerNo);
                updateSwireMemberIfNeeded(request, response);
                return buildSuccessResponse(response);
            }
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED);
        });
    }

    // 构建注册请求
    private WechatRegisterRequest buildRegisterRequest(WechatRegisterRequestDto request) {
        return JsonUtils.parseJavaType(JsonUtils.toJson(request), WechatRegisterRequest.class);
    }

    // 处理手机号格式
    private void processMobileNumber(WechatRegisterRequest request) {
        if (request.getMobile().contains("+")) {
            request.setMobile(request.getMobile().replace("+", ""));
        }
        if (request.getMobile().startsWith("86") && request.getMobile().length() == 13) {
            request.setMobile(request.getMobile().substring(2));
        }
    }

    // 处理会员来源信息
    private void processMemberSource(WechatRegisterRequestDto request, WechatRegisterRequest registerRequest) {
        HashMap<String, Object> bindingExtProperties = new HashMap<>();
        HashMap<String, Object> customizedProperties = new HashMap<>();

        if (registerRequest.getMobile().contains("+")) {
            bindingExtProperties.put("rsvField6", request.getMobile());
        }

        bindingExtProperties.put("memberSource", request.getBindingExtProperties().get("memberSource"));
        customizedProperties.put("memberSource", request.getBindingExtProperties().get("memberSource"));

        if (request.getBindingExtProperties().get("memberSourceDetail") != null) {
            bindingExtProperties.put("memberSourceDetail", request.getBindingExtProperties().get("memberSourceDetail"));
            customizedProperties.put("memberSourceDetail", request.getBindingExtProperties().get("memberSourceDetail"));
        } else if (NATURAL_FLOW.equals(request.getBindingExtProperties().get("memberSource").toString())) {
            bindingExtProperties.put("memberSourceDetail", "");
            customizedProperties.put("memberSourceDetail", "");
        }

        if (StringUtils.isNotBlank(request.getAgreementVersion())) {
            bindingExtProperties.put("rsvField11", request.getAgreementVersion());
            customizedProperties.put("rsvField11", request.getAgreementVersion());
        }

        registerRequest.setBindingExtProperties(bindingExtProperties);
        registerRequest.setCustomizedProperties(customizedProperties);
    }

    // 保存联合会员模型
    private void saveUnionMemberModel(WechatRegisterRequestDto request, String memberId) {
        HashMap<String, Object> map = new HashMap<>();
        MD5 md5 = MD5.create();
        String id = md5.digestHex16(request.getAppId() + request.getOpenId());
        map.put("id", id);
        map.put("memberId", memberId);
        map.put("channelType", request.getChannelType());
        map.put("externalAppId", request.getAppId());
        map.put("externalOpenId", request.getOpenId());
        map.put("createTime", ZonedDateTime.now());
        map.put("isFirstBinding", request.getIsFirstBinding());
        DataApiUtil.upsertIsData(ModelConstants.UNION_MEMBER_CHANNEL,id, map);
    }

    // 记录LBS轨迹
    private void recordLbsTrajectory(WechatRegisterRequestDto request, String memberId, String customerNo) {
        if (StringUtils.isNotBlank(request.getOriginLbsCity())) {
            MemberLbsTrajectoryDto lbsTrajectoryDto = new MemberLbsTrajectoryDto();
            lbsTrajectoryDto.setCreateTime(StringUtils.isNotEmpty(request.getRegisterTime()) ?
                    DateHelper.getZone(request.getRegisterTime()) : DateHelper.getNowZone());
            lbsTrajectoryDto.setChannelType(request.getChannelType());
            lbsTrajectoryDto.setOriginLbsCity(request.getOriginLbsCity());

            if (StringUtils.isNotBlank(request.getOriginLbsProvince())) {
                lbsTrajectoryDto.setOriginLbsProvince(request.getOriginLbsProvince());
            }
            if (StringUtils.isNotBlank(request.getOriginLbsDistrict())) {
                lbsTrajectoryDto.setOriginLbsDistrict(request.getOriginLbsDistrict());
            }

            lbsTrajectoryDto.setCustomerNo(customerNo);
            lbsTrajectoryDto.setMemberId(memberId);
            lbsTrajectoryDto.setType("REGISTER");
            iCepDataSyncService.savePosition(lbsTrajectoryDto, lbsTrajectoryDto.getType());
        }
    }

    // 更新Swire会员信息
    private void updateSwireMemberIfNeeded(WechatRegisterRequestDto request, WechatRegisterResponse response) {
        if (ChannelTypeEnum.SWIRE_MP.getValue().equals(request.getChannelType()) &&
                StringUtils.isNotBlank(request.getUpdateTime())) {
            try {
                HashMap<String, Object> map = new HashMap<>();
                map.put("updateTime", request.getUpdateTime());

                if (StringUtils.isNotBlank(request.getGender())) {
                    map.put("gender", request.getGender());
                }
                if (StringUtils.isNotBlank(request.getBirthYear())) {
                    map.put("birthYear", request.getBirthYear());
                    map.put("birthDay", request.getBirthDay());
                }

                map.put("channelType", request.getChannelType());
                map.put("lastSync", ZonedDateTime.now());
                DMLResponse res = DataApiUtil.upsertIsData(ModelConstants.WSIRE_MEMBER_RECORDS,
                        request.getMobile(), map);

                if (!res.getIsSuccess()) {
                    log.error("更新存量会员信息失败: {}", res.getOperation());
                }
            } catch (Exception e) {
                log.error("更新存量会员信息失败: {}, msg: {}", JsonUtils.toJson(response), e.getMessage());
            }
        }
    }

    // 构建成功响应
    private ResponseResult buildSuccessResponse(WechatRegisterResponse response) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("memberId", response.getMemberId());
        return new ResponseResult(ResponseCodeEnum.SUCCESS.getCode(), response.getStatus().toString(), map);
    }

    // 带锁执行
    private ResponseResult executeWithLock(String lockKey, Supplier<ResponseResult> action) {
        Lock lock = null;
        try {
            lock = redisCache.getLock(lockKey);
            if (lock.tryLock(3, TimeUnit.SECONDS)) {
                return action.get();
            } else {
                log.info("获取锁超时: {}", lockKey);
                return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, "获取锁超时");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取锁被中断: {}", e);
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, "获取锁被中断");
        } catch (Exception e) {
            log.error("执行加锁操作异常: {}", e);
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, e.getMessage());
        } finally {
            if (lock != null) {
                try {
                    lock.unlock();
                } catch (Exception e) {
                    log.error("释放锁异常: {}", e);
                }
            }
        }
    }


    @Override
    public ResponseResult updateMemberByChannelId(CepUpdateMemberDto request) {
        if (StringUtils.isBlank(request.getCustomerNo()) &&StringUtils.isBlank(request.getMemberId())) {
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, "客户编号或会员ID不能为空，格式：appid_openid/memberId");
        }
        String querySQL ;
        if (!ObjectUtils.isEmpty(request.getMemberId())){
            querySQL = "memberId ='"+request.getMemberId()+"'";
        }else{
            querySQL = "customerNo ='"+request.getCustomerNo()+"'";
        }
        //查询会员
        Map<String, String> memberDto = memberRepository.queryCustomerNoChannType(querySQL, request.getChannelType());
        if (null == memberDto.get("memberId")) {
            return new ResponseResult(ResponseCodeEnum.MEMBER_FAILED);
        }
        request.setCustomerNo(memberDto.get("customerNo"));

        //记录授权轨迹
        if (StringUtils.isNotBlank(request.getOriginLbsCity()) && StringUtils.isNotBlank(request.getOriginLbsProvince()) && StringUtils.isNotBlank(request.getCreateTime())){
            MemberLbsTrajectoryDto lbsTrajectoryDto = new MemberLbsTrajectoryDto();
            lbsTrajectoryDto.setCreateTime(DateHelper.getZone(request.getCreateTime()));
            lbsTrajectoryDto.setChannelType(request.getChannelType());
            lbsTrajectoryDto.setOriginLbsCity(request.getOriginLbsCity());
            lbsTrajectoryDto.setOriginLbsProvince(request.getOriginLbsProvince());
            if (StringUtils.isNotBlank(request.getOriginLbsDistrict())){
                lbsTrajectoryDto.setOriginLbsDistrict(request.getOriginLbsDistrict());
            }
            lbsTrajectoryDto.setCustomerNo(request.getCustomerNo());
            lbsTrajectoryDto.setMemberId(memberDto.get("memberId"));
            lbsTrajectoryDto.setType("LOGIN");
            iCepDataSyncService.savePosition(lbsTrajectoryDto,lbsTrajectoryDto.getType());
        }



        UpdateMemberByChannelDto updateMember = new UpdateMemberByChannelDto();
        if (StringUtils.isNotBlank(request.getCityName())) {
            updateMember.setCityName(request.getCityName());
        }
        if (StringUtils.isNotBlank(request.getJob())) {
            updateMember.setJob(request.getJob());
        }

        if (StringUtils.isNotBlank(request.getProvinceName())) {
            updateMember.setProvinceName(request.getProvinceName());
        }
        if (StringUtils.isNotBlank(request.getDistrictName())) {
            updateMember.setDistrictName(request.getDistrictName());
        }
        if (StringUtils.isNotBlank(request.getHeadImgUrl())) {
            updateMember.setHeadImgUrl(request.getHeadImgUrl());
        }
        if (StringUtils.isNotBlank(request.getGender())) {
            updateMember.setGender(request.getGender());
        }
        if (StringUtils.isNotBlank(request.getMemberName())) {
            updateMember.setMemberName(request.getMemberName());
        }
        if (StringUtils.isNotBlank(request.getBirthYear())) {
            if (StringUtils.isBlank(request.getBirthDay())) {
                return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, "生日，月日不能为空");
            }
            LocalDate birthDate = LocalDate.of(Integer.parseInt(request.getBirthYear()), Integer.valueOf(request.getBirthDay().substring(0, 2)), Integer.valueOf(request.getBirthDay().substring(3, 5)));
            boolean isValid = isValidAge(birthDate);
            if (!isValid) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("memberId", memberDto.get("memberId"));
                map.put("birthYear", request.getBirthYear());
                map.put("birthDay", request.getBirthDay());
                map.put("lastSync", ZonedDateTime.now());
                //记录会员更新不在14岁至99岁之间
                DMLResponse res = DataApiUtil.upsertIsData(ModelConstants.MEMBER_ISVALIDAGE, UUID.randomUUID().toString().replaceAll("-", ""), map);
                if (!res.getIsSuccess()) {
                    log.error("记录会员更新不在14岁至99岁之间:{}", res.getOperation());
                    return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, res.getOperation());
                }
                return new ResponseResult(ResponseCodeEnum.MEMBER_ISVAIID_AGE);
            }
            updateMember.setBirthYear(request.getBirthYear());
            updateMember.setBirthDay(request.getBirthDay());
        }
        Map<String, Object> customizedProperties = new HashMap<>();
        Map<String, Object> bindingExtProperties = new HashMap<>();
        if (StringUtils.isNotBlank(request.getNickName())) {
            bindingExtProperties.put("nick", request.getNickName());
            customizedProperties.put("wechatNick", request.getNickName());
        }
        if (StringUtils.isNotBlank(request.getLbsIdentifying())) {
            customizedProperties.put("rsvField1", request.getLbsIdentifying());
        }
        if (StringUtils.isNotBlank(request.getIncome())) {
            customizedProperties.put("rsvField2", request.getIncome());
        }
        if (!customizedProperties.isEmpty()){
            updateMember.setCustomizedProperties(customizedProperties);
        }
        if (!bindingExtProperties.isEmpty()){
            updateMember.setBindingExtProperties(bindingExtProperties);
        }
        if (StringUtils.isNotBlank(request.getAgreementVersion())) {
            customizedProperties.put("rsvField11", request.getAgreementVersion());
            bindingExtProperties.put("rsvField11",request.getAgreementVersion());
        }

        updateMember.setBindingExtProperties(bindingExtProperties);
        updateMember.setCustomizedProperties(customizedProperties);
        if (CepUpdateMemberDto.hasValues(request)){
            updateMember.setChannelType(request.getChannelType());
            updateMember.setMemberType(request.getMemberType());
            updateMember.setCustomerNo(request.getCustomerNo());
            log.info("更新会员信息:{}",JSON.toJSONString(updateMember));
            iCustomerClient.updateMemberByChannelId(updateMember);
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }

    public static boolean isValidAge(LocalDate birthDate) {
        long age = ChronoUnit.YEARS.between(birthDate, LocalDate.now());
        return age >= 14 && age < 99;
    }

    /**
     * 变更会员积分（互动变更积分）
     *
     * @param pointRequestDto
     * @param tradeNo
     */
    @Override
    public ResponseResult updatePoint(PointRequestDto pointRequestDto, String tradeNo, String scene, String channelType) {
        Map<Object, Object> resultMap = new HashMap<>();
        ZonedDateTime created = ZonedDateTime.now();
        List<RulePointRecordDto> rulePointRecorList;
        //查询会员累计获取的积分
        Map<String, Double> rulePointRecordDtos = new HashMap();
        //查询会员当天获取的积分
        Map<String, Double> rulePointRecordDayDtos = new HashMap();
        //规则
        List<DailyLimitedPointsRuleDto> ruleDtoList = new ArrayList<>();

        //积分
        Double point = pointRequestDto.getPoint();
        //超额积分
        Double excessPoints = 0.0;

        //根据场景值查询规则
        List<DailyLimitedPointsRuleDto> ruleDtos = dailyLimitedPointsRuleRepository.querySceneRule(scene, channelType);
        if (CollectionUtils.isNotEmpty(ruleDtos)) {
            ruleDtoList = JSON.parseArray(JSON.toJSONString(ruleDtos), DailyLimitedPointsRuleDto.class);
        }
        log.info("根据场景值查询规则:{},traceId:{}", JSON.toJSONString(ruleDtoList), tradeNo);
        //查询会员当前获取的积分
        rulePointRecorList = rulePointRecordRepository.querMemberPoints(pointRequestDto.getMemberId());
        if (CollectionUtils.isNotEmpty(rulePointRecorList)) {
            List<RulePointRecordDto> rulePoint = JSON.parseArray(JSON.toJSONString(rulePointRecorList), RulePointRecordDto.class);
            for (RulePointRecordDto pointRecord : rulePoint) {
                rulePointRecordDtos.put(pointRecord.getScene(), pointRecord.getAccumulatePoint());
                log.info("rulePointRecordDtos:{}", JSON.toJSONString(rulePointRecordDtos));
                boolean effectiveDate = DateUtil.isTimeDate(pointRecord.getCreated());
                if (effectiveDate) {
                    rulePointRecordDayDtos.put(pointRecord.getScene(), pointRecord.getPoint());
                    log.info("rulePointRecordDayDtos当天获取的积分:{}", JSON.toJSONString(rulePointRecordDayDtos));
                }
            }
        } else {
            //第一次记录
            if (CollectionUtils.isNotEmpty(ruleDtos)) {
                for (DailyLimitedPointsRuleDto ruleDto : ruleDtoList) {
                    //根据规则查询场景值
                    log.info("第一次积分计算traceId:{},规则积分:{},当前传输的积分:{}", tradeNo, ruleDto.getPointsThreshold(), point);
                    if (ruleDto.getPointsThreshold() < point) {
                        Double pointsum = ruleDto.getPointsThreshold();
                        if (pointsum < point) {
                            point = pointsum;
                            excessPoints = subPoint(pointRequestDto.getPoint(), point);
                        }
                    }
                    log.info("第一次积分计算traceId:{},获取积分:{}", tradeNo, point);
                }
            }
        }


        try {
            if (CollectionUtils.isNotEmpty(ruleDtoList)) {
                // 是否存累计上限规则
                boolean flag = ruleDtoList.stream().anyMatch(dailyLimitedPointsRuleDto -> "false".equals(dailyLimitedPointsRuleDto.getIsDailyLimited().toString()));
                //log.info("累计计算开始:{}", flag);
                if (flag) {
                    if (null != rulePointRecordDtos.get(scene) + channelType) {
                        //计算实际加积分额度
                        for (DailyLimitedPointsRuleDto ruleDto : ruleDtoList) {
                            Double sum = 0.0;
                            //根据规则查询场景值
                            List<RulePointRecordDto> list = rulePointRecordRepository.querRuleScene(ruleDto.getRuleCode());
                            List<RulePointRecordDto> rulePointlist = JSON.parseArray(JSON.toJSONString(list), RulePointRecordDto.class);
                            //log.info("rulePointRecordDtos获取的积分:{}", JSON.toJSONString(rulePointlist));
                            for (RulePointRecordDto secents : rulePointlist) {
                                if (null != rulePointRecordDtos.get(secents.getScene() + secents.getChannelType())) {
                                    sum = addPoint(rulePointRecordDtos.get(secents.getScene() + secents.getChannelType()), sum);
                                }
                            }
                            log.info("积分计算traceId:{},规则积分:{},获取积分:{}", tradeNo, ruleDto.getPointsThreshold(), sum);
                            if (ruleDto.getPointsThreshold() <= sum) {
                                return new ResponseResult(ResponseCodeEnum.MEMBER_POINT_LIMIT);
                            }
                            //规则计算获取积分 + 当前积分
                            double v = addPoint(sum, point);
                            if (ruleDto.getPointsThreshold() < v) {
                                //规则积分 - 获取积分
                                Double pointsum = subPoint(ruleDto.getPointsThreshold(), sum);
                                if (pointsum < point) {
                                    point = pointsum;
                                    excessPoints = subPoint(pointRequestDto.getPoint(), point);
                                }
                                log.info("积分计算traceId:{},获取积分:{}", tradeNo, point);
                            }
                        }
                    }
                }

                if (!flag) {
                    if (null != rulePointRecordDayDtos.get(scene) + channelType) {
                        for (DailyLimitedPointsRuleDto ruleDto : ruleDtoList) {
                            Double sum = 0.0;
                            //根据规则查询场景值
                            List<RulePointRecordDto> list = rulePointRecordRepository.querRuleScene(ruleDto.getRuleCode());
                            List<RulePointRecordDto> rulePointlist = JSON.parseArray(JSON.toJSONString(list), RulePointRecordDto.class);

                            //log.info("rulePointlist当天获取的积分:{}", JSON.toJSONString(rulePointlist));
                            for (RulePointRecordDto secents : rulePointlist) {

                                if (null != rulePointRecordDayDtos.get(secents.getScene() + secents.getChannelType())) {
                                    sum = addPoint(rulePointRecordDayDtos.get(secents.getScene() + secents.getChannelType()), sum);
                                }
                            }
                            log.info("当天积分计算traceId:{},规则积分:{},获取积分:{}", tradeNo, ruleDto.getPointsThreshold(), sum);
                            if (ruleDto.getPointsThreshold() <= sum) {
                                return new ResponseResult(ResponseCodeEnum.MEMBER_POINT_LIMIT);
                            }
                            //规则计算获取积分 + 当前积分
                            double v = addPoint(sum, point);
                            if (ruleDto.getPointsThreshold() < v) {
                                //规则积分 - 获取积分
                                Double pointsum = subPoint(ruleDto.getPointsThreshold(), sum);
                                if (pointsum < point) {
                                    point = pointsum;
                                    excessPoints = subPoint(pointRequestDto.getPoint(), point);
                                }
                                log.info("当天积分计算traceId:{},获取积分:{}", tradeNo, point);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("积分计算异常traceId:{},msg:{}", tradeNo, e.getMessage());
        }

        try {
            log.info("加积分traceId:{},获取积分:{}", tradeNo, point);
            pointRequestDto.setPoint(point);
            memberClient.updatePoint(pointRequestDto, tradeNo);
        } catch (Exception e) {
            if (e.getMessage().contains("重复请求")) {
                Double changePoints = memberRepository.queryMemberTraceId(tradeNo);
                if (0.0 != changePoints) {
                    resultMap.put("changePoint", changePoints);
                    return new ResponseResult(ResponseCodeEnum.SUCCESS, resultMap);
                }
            }
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, e.getMessage());
        }
        try {
            String id = DigestUtils.md5Hex(pointRequestDto.getMemberId() + scene + channelType).toUpperCase();
            HashMap<String, Object> map = new HashMap<>();
            if (null == rulePointRecordDtos.get(scene + channelType)) {
                map.put("accumulatePoint", point);
            } else {
                Double pointSum = addPoint(rulePointRecordDtos.get(scene + channelType), point);
                map.put("accumulatePoint", pointSum);
            }
            if (null == rulePointRecordDayDtos.get(scene + channelType)) {
                map.put("point", point);
            } else {
                Double pointDaySum = addPoint(rulePointRecordDayDtos.get(scene + channelType), point);
                map.put("point", pointDaySum);
            }
            map.put("memberId", pointRequestDto.getMemberId());
            map.put("scene", scene + channelType);
            map.put("created", created);
            DMLResponse response = DataApiUtil.upsertIsData(ModelConstants.MEMBER_RULEPOINTRECORD, id, map);
            if (!response.getIsSuccess()) {
                log.error("记录积分信息创建失败traceId:{},msg:{}", tradeNo, response.getOperation());
            }
            if (excessPoints != 0.0) {
                Map<String, Object> maps = new HashMap<>();
                maps.put("memberId", pointRequestDto.getMemberId());
                double v = addPoint(point, excessPoints);
                maps.put("excessPoints", "应发" + v + "积分，由于每日获取积分上限，实发" + point + "积分");
                maps.put("created", ZonedDateTime.now());
                //记录超额积分
                DMLResponse resultResponse = DataApiUtil.upsertIsData(ModelConstants.MEMBER_EXCESSPOINTSLOG, tradeNo, maps);
                if (!resultResponse.getIsSuccess()) {
                    log.error("记录超额积分创建失败traceId:{},msg:{}", tradeNo, response.getOperation());
                }
            }
        } catch (Exception e) {
            //日志进行记录失败
            log.error("记录积分信息创建失败1traceId:{},msg:{}", tradeNo, e.getMessage());
        }
        log.info("加结束traceId:{},获取积分:{}", tradeNo, point);
        resultMap.put("changePoint", pointRequestDto.getPoint());
        return new ResponseResult(ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getMsg(), resultMap);
    }


    @Override
    public ResponseResult getablePoint(String memberId, Integer ruleCode, String scene,String channelType, String channelFrom){
        List<RulePointRecordDto> rulePointRecorList;
        //查询会员累计获取的积分
        Map<String, Double> rulePointRecordDtos = new HashMap();
        //查询会员当天获取的积分
        Map<String, Double> rulePointRecordDayDtos = new HashMap();
        rulePointRecorList = rulePointRecordRepository.querMemberPoints( memberId );
        if (CollectionUtils.isNotEmpty(rulePointRecorList)) {
            List<RulePointRecordDto> rulePoint = JSON.parseArray(JSON.toJSONString(rulePointRecorList), RulePointRecordDto.class);
            for (RulePointRecordDto pointRecord : rulePoint) {
                rulePointRecordDtos.put(pointRecord.getScene(), pointRecord.getAccumulatePoint());
                log.info("getable会员累计获得积分:{}", JSON.toJSONString(rulePointRecordDtos));
                boolean effectiveDate = DateUtil.isTimeDate(pointRecord.getCreated());
                if (effectiveDate) {
                    rulePointRecordDayDtos.put(pointRecord.getScene(), pointRecord.getPoint());
                    log.info("getable会员当天获取的积分:{}", JSON.toJSONString(rulePointRecordDayDtos));
                }
            }
        }
        ArrayList<HashMap<String,Object>> resultList = Lists.newArrayList();
        //根据场景值查询规则
        List<HashMap<String,Object>> ruleDtos = dailyLimitedPointsRuleRepository.queryRuleByRSCC(ruleCode, scene, channelType, channelFrom);
        ruleDtos.forEach( ruleDto -> {
            boolean isDailyLimited = (Boolean) ruleDto.get("isDailyLimited");
            Map<String, Double> ruleRecord = isDailyLimited ? rulePointRecordDayDtos : rulePointRecordDtos;
            Double sum = 0.0;
            //根据规则查询场景值
            List<RulePointRecordDto> list = rulePointRecordRepository.querRuleScene( (Integer) ruleDto.get("ruleCode") );
            List<RulePointRecordDto> rulePointlist = JSON.parseArray(JSON.toJSONString(list), RulePointRecordDto.class);
            for (RulePointRecordDto secents : rulePointlist) {
                if ( null != ruleRecord.get(secents.getScene() + secents.getChannelType()) ) {
                    sum = addPoint(ruleRecord.get(secents.getScene() + secents.getChannelType()), sum);
                }
            }
            Double pointsThreshold = ( (BigDecimal) ruleDto.get("pointsThreshold") ).doubleValue();
            Double getable = subPoint(pointsThreshold, sum);
            getable = getable < 0 ? 0 : getable;
            HashMap<String,Object> result = Maps.newHashMap();
            result.put("ruleCode", ruleDto.get("ruleCode") );
            result.put("ruleName", ruleDto.get("ruleName") );
            result.put("getablePoint", getable);
            result.put("memberGotPoint", sum);
            result.put("isDailyLimited", isDailyLimited);
            result.put("pointsThreshold",pointsThreshold);
            result.put("startTime",ruleDto.get("startTime"));
            result.put("endTime",ruleDto.get("endTime"));
            resultList.add(result);
        });
        return new ResponseResult(ResponseCodeEnum.SUCCESS.getCode(), org.springframework.util.ObjectUtils.isEmpty(ruleDtos)
                ? "没有生效且可查的规则!" : ResponseCodeEnum.SUCCESS.getMsg(), resultList);
    }

    public static double addPoint(double v1, double v2) {
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return b1.add(b2).doubleValue();
    }


    public static double subPoint(double v1, double v2) {
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return b1.subtract(b2).doubleValue();
    }


    @Override
    public ResponseResult queryMember(String customerNo,String memberId, String memberType, String channelType,String unionId) {
        CepMemberInfoDto cepMemberDto = new CepMemberInfoDto();
        //unionId不为空进行绑定逻辑处理
        if (StringUtils.isNotEmpty(unionId)){
            try {
                cepMemberDto = bindingRegistration(customerNo, memberType, channelType, unionId);
                getUnionMemberChannel(cepMemberDto);
            }catch (Exception e){
                log.error("静默绑定失败unionid:{},msg:{}",unionId,e.getMessage());
                return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED);
            }
            return new ResponseResult("200", ResponseCodeEnum.SUCCESS.getMsg(), cepMemberDto);
        }
        try {
            if (!ObjectUtils.isEmpty(memberId)){
                CepMemberInfoDto cepMemberInfoDto = memberIdQuery(memberId, memberType, channelType);
                getUnionMemberChannel(cepMemberDto);
                return new ResponseResult("200", ResponseCodeEnum.SUCCESS.getMsg(), cepMemberInfoDto);

            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            CustomerQueryDto customerQueryDto = new CustomerQueryDto();
            customerQueryDto.setCustomerNo(customerNo);
            customerQueryDto.setChannelType(channelType);
            customerQueryDto.setMemberType(memberType);
            //指定要查询的字段(含衍生/扩展)
            List<String> selectFieldList = Arrays.asList("memberId", "memberName", "age", "birthDay", "birthYear", "email", "cityName", "provinceName", "districtName", "rsvField2", "gender", "job",
                    "mobile", "registerTime", "wechatNick", "headImgUrl", "pointsRequired", "thisMonthExpiredPoint", "nextMonthExpiredPoint","rsvField11");
            customerQueryDto.setSelectFields(selectFieldList);
            SimpleMemberDto memberDto = customerClient.queryMemberByChannelId(customerQueryDto);
            if (memberDto != null) {
                log.info("查询出来的会员信息:{}", JSONObject.toJSONString(memberDto));
                //查询会员渠道信息
                List<MemberBingDto> memberBingDtos = openApiFeignClient.queryListChannels(memberDto.getMemberId(), "KO", null, null);
                cepMemberDto.setIsAgreeTerms("否");
                if (memberBingDtos.stream().anyMatch(m -> "SWIRE_MP".equals(m.getChannelType()))) {
                    cepMemberDto.setIsAgreeTerms("是");
                }
                cepMemberDto.setMemberId(memberDto.getMemberId());
                cepMemberDto.setMemberName(memberDto.getMemberName());
                if (memberDto.getAge() == null) {
                    cepMemberDto.setAge("");
                } else {
                    cepMemberDto.setAge(memberDto.getAge() + "");
                }
                if (StringUtils.isBlank(memberDto.getBirthDay())) {
                    cepMemberDto.setBirthDay("");
                } else {
                    cepMemberDto.setBirthDay(memberDto.getBirthDay());
                }
                if (StringUtils.isBlank(memberDto.getBirthYear())) {
                    cepMemberDto.setBirthYear("");
                } else {
                    cepMemberDto.setBirthYear(memberDto.getBirthYear());
                }
                if (!"SWIRE_MP".equals(channelType)) {
                    if (memberDto.getCustomizedProperties().get("rsvField2") == null) {
                        cepMemberDto.setIncome("");
                    } else {
                        cepMemberDto.setIncome(memberDto.getCustomizedProperties().get("rsvField2").toString());
                    }
                    if (StringUtils.isBlank(memberDto.getCityName())) {
                        cepMemberDto.setCityName("");
                    } else {
                        cepMemberDto.setCityName(memberDto.getCityName());
                    }
                    if (StringUtils.isBlank(memberDto.getProvinceName())) {
                        cepMemberDto.setProvinceName("");
                    } else {
                        cepMemberDto.setProvinceName(memberDto.getProvinceName());
                    }
                    if (StringUtils.isBlank(memberDto.getDistrictName())) {
                        cepMemberDto.setDistrictName("");
                    } else {
                        cepMemberDto.setDistrictName(memberDto.getDistrictName());
                    }
                }

                if (StringUtils.isNotBlank(memberDto.getEmail())) {
                    cepMemberDto.setEmail(memberDto.getEmail());
                } else {
                    cepMemberDto.setEmail("");
                }
                if (StringUtils.isNotBlank(memberDto.getGender())) {
                    cepMemberDto.setGender(memberDto.getGender());
                } else {
                    cepMemberDto.setGender("");
                }
                if (StringUtils.isNotBlank(memberDto.getJob())) {
                    cepMemberDto.setJob(memberDto.getJob());
                } else {
                    cepMemberDto.setJob("");
                }
                cepMemberDto.setMobile(memberDto.getMobile());
                if (memberDto.getRegisterTime() != null) {
                    cepMemberDto.setRegisterTime(sdf.format(memberDto.getRegisterTime()));
                } else {
                    cepMemberDto.setRegisterTime("");
                }
                if (memberDto.getCustomizedProperties().get("pointsRequired") == null) {
                    cepMemberDto.setPointsRequired(0.0);
                } else {
                    cepMemberDto.setPointsRequired(Double.parseDouble(memberDto.getCustomizedProperties().get("pointsRequired").toString()));
                }
                if (memberDto.getCustomizedProperties().get("thisMonthExpiredPoint") == null) {
                    cepMemberDto.setThisMonthExpiredPoint(0.0);
                } else {
                    cepMemberDto.setThisMonthExpiredPoint(Double.parseDouble(memberDto.getCustomizedProperties().get("thisMonthExpiredPoint").toString()));
                }
                if (memberDto.getCustomizedProperties().get("nextMonthExpiredPoint") == null) {
                    cepMemberDto.setNextMonthExpiredPoint(0.0);
                } else {
                    cepMemberDto.setNextMonthExpiredPoint(Double.parseDouble(memberDto.getCustomizedProperties().get("nextMonthExpiredPoint").toString()));
                }
                if (memberDto.getCustomizedProperties().get("rsvField11") == null) {
                    cepMemberDto.setAgreementVersion("");
                } else {
                    cepMemberDto.setAgreementVersion(memberDto.getCustomizedProperties().get("rsvField11").toString());
                }
                cepMemberDto.setNickName(memberDto.getCustomizedProperties().get("wechatNick") == null ? "" : memberDto.getCustomizedProperties().get("wechatNick").toString());
                cepMemberDto.setHeadImgUrl(memberDto.getCustomizedProperties().get("headImgUrl") == null ? "" : memberDto.getCustomizedProperties().get("headImgUrl").toString());
                getUnionMemberChannel(cepMemberDto);
            } else {
                log.info("查询出来的会员信息返回customerNo:{},body:{}",customerNo,JSON.toJSONString(cepMemberDto));
                return new ResponseResult("200", ResponseCodeEnum.SUCCESS.getMsg(), cepMemberDto);
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.info("查询出来的会员信息异常信息:{}", e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, e.getMessage());
        }
        log.info("查询出来的会员信息返回customerNo:{},:{}",customerNo,JSON.toJSONString(cepMemberDto));
        return new ResponseResult("200", ResponseCodeEnum.SUCCESS.getMsg(), cepMemberDto);
    }

    private void getUnionMemberChannel(CepMemberInfoDto cepMemberDto) {
        //根据会员id查询联合会员表数据
        List<Map<String, String>> maps = unionMemberChannelRepository.queryUnionMemberChannel(cepMemberDto.getMemberId());
        log.info("查询出来的联合会员渠道信息maps:{}", maps);
        if (maps != null && !maps.isEmpty()) {
            List<CepMemberInfoDto.ExternalMembership> externalMemberships = maps.stream()
                    .map(map -> {
                        CepMemberInfoDto.ExternalMembership membership = new CepMemberInfoDto.ExternalMembership();
                        membership.setChannelType(map.get("channelType"));
                        membership.setChannelName(map.get("channelName"));
                        return membership;
                    })
                    .collect(Collectors.toList());
            cepMemberDto.setExternalMembership(externalMemberships);
        }else {
            cepMemberDto.setExternalMembership(new ArrayList<>());
        }
    }

    private CepMemberInfoDto bindingRegistration(String customerNo, String memberType, String channelType, String unionId) {
        CepMemberInfoDto cepMemberDto = new CepMemberInfoDto();
        //根据unionId查询 判断是否已经注册在相同开放平台下注册过会员
        String querySQL = "unionId ='"+unionId+"' and relType !=2";
        Map<String, String> memberDto = memberRepository.queryCustomerNoChannType(querySQL, channelType);
        //如果为null该会员没有注册过
        if (null == memberDto.get("memberId")) {
            return cepMemberDto;
        }
        //查询该会员是否已绑定新小程序
        String bindingSql = "customerNo ='"+customerNo+"' and unionId ='"+unionId+"'  and relType !=2";
        Map<String, String> memberBindingDto = memberRepository.queryCustomerNoChannType(bindingSql, channelType);
        if (null == memberBindingDto.get("memberId")){
            String appId = customerNo.substring(0, customerNo.indexOf('_')); // 从开头到第一个 '_'
            String openId = customerNo.substring(customerNo.indexOf('_') + 1);
            //进行绑定
            WechatRegisterRequest wechatRegisterRequest = new WechatRegisterRequest();
            wechatRegisterRequest.setMemberType(memberType);
            wechatRegisterRequest.setChannelType(channelType);
            wechatRegisterRequest.setAppId(appId);
            wechatRegisterRequest.setOpenId(openId);
            wechatRegisterRequest.setUnionId(unionId);
            wechatRegisterRequest.setMobile(memberDto.get("mobile"));
            wechatRegisterRequest.setAppType("WECHAT_MINI_PROGRAM");
            log.info("进行静默绑定注册:{}",JSON.toJSONString(wechatRegisterRequest));
            iWechatClient.register(wechatRegisterRequest);
        }
        cepMemberDto = memberIdQuery(memberDto.get("memberId"), memberType, channelType);
        return cepMemberDto;
    }

    private CepMemberInfoDto memberIdQuery(String memberId, String memberType,String channelType){
        CepMemberInfoDto cepMemberDto = new CepMemberInfoDto();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //指定要查询的字段(含衍生/扩展)
        List<String> selectFieldList = Arrays.asList("memberId", "memberName", "age", "birthDay", "birthYear", "email", "cityName", "provinceName", "districtName", "rsvField2", "gender", "job",
                "mobile", "registerTime", "wechatNick", "headImgUrl", "pointsRequired", "thisMonthExpiredPoint", "nextMonthExpiredPoint","rsvField11");
        MemberCustomDto memberDto = openApiFeignClient.queryMember(memberType ,memberId , selectFieldList);
        log.info("查询出来的会员信息:{}", JSONObject.toJSONString(memberDto));
        if (!ObjectUtils.isEmpty(memberDto)) {
            if (memberDto.getOptionalFieldData().get("memberName") == null) {
                cepMemberDto.setMemberName("");
            } else {
                cepMemberDto.setMemberName(memberDto.getOptionalFieldData().get("memberName") + "");
            }
            cepMemberDto.setMemberId(memberDto.getMemberId());
            if (memberDto.getOptionalFieldData().get("age") == null) {
                cepMemberDto.setAge("");
            } else {
                cepMemberDto.setAge(memberDto.getOptionalFieldData().get("age") + "");
            }
            if (memberDto.getOptionalFieldData().get("birthDay") == null) {
                cepMemberDto.setBirthDay("");
            } else {
                cepMemberDto.setBirthDay(memberDto.getOptionalFieldData().get("birthDay").toString());
            }
            if (memberDto.getOptionalFieldData().get("birthYear")==null) {
                cepMemberDto.setBirthYear("");
            } else {
                cepMemberDto.setBirthYear(memberDto.getOptionalFieldData().get("birthYear").toString());
            }
            if (!"SWIRE_MP".equals(channelType)) {
                if (memberDto.getOptionalFieldData().get("rsvField2") == null) {
                    cepMemberDto.setIncome("");
                } else {
                    cepMemberDto.setIncome(memberDto.getOptionalFieldData().get("rsvField2").toString());
                }
                if (memberDto.getOptionalFieldData().get("cityName") ==null) {
                    cepMemberDto.setCityName("");
                } else {
                    cepMemberDto.setCityName(memberDto.getOptionalFieldData().get("cityName").toString());
                }
                if (memberDto.getOptionalFieldData().get("provinceName") ==null) {
                    cepMemberDto.setProvinceName("");
                } else {
                    cepMemberDto.setProvinceName(memberDto.getOptionalFieldData().get("provinceName").toString());
                }
                if (memberDto.getOptionalFieldData().get("districtName")==null) {
                    cepMemberDto.setDistrictName("");
                } else {
                    cepMemberDto.setDistrictName(memberDto.getOptionalFieldData().get("districtName").toString());
                }
            }

            if (memberDto.getOptionalFieldData().get("email") != null) {
                cepMemberDto.setEmail(memberDto.getOptionalFieldData().get("email").toString());
            } else {
                cepMemberDto.setEmail("");
            }
            if (StringUtils.isNotBlank(memberDto.getGender())) {
                cepMemberDto.setGender(memberDto.getGender());
            } else {
                cepMemberDto.setGender("");
            }
            if (memberDto.getOptionalFieldData().get("job") !=null) {
                cepMemberDto.setJob(memberDto.getOptionalFieldData().get("job").toString());
            } else {
                cepMemberDto.setJob("");
            }
            if (memberDto.getOptionalFieldData().get("mobile") != null) {
                cepMemberDto.setMobile(memberDto.getOptionalFieldData().get("mobile").toString());
            } else {
                cepMemberDto.setMobile("");
            }
            if (memberDto.getOptionalFieldData().get("registerTime") != null) {
                try {
                    cepMemberDto.setRegisterTime(DateHelper.formatDateStr(memberDto.getOptionalFieldData().get("registerTime").toString(), DateHelper.DATE_ZONE));
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            } else {
                cepMemberDto.setRegisterTime("");
            }
            if (memberDto.getOptionalFieldData().get("pointsRequired") == null) {
                cepMemberDto.setPointsRequired(0.0);
            } else {
                cepMemberDto.setPointsRequired(Double.parseDouble(memberDto.getOptionalFieldData().get("pointsRequired").toString()));
            }
            if (memberDto.getOptionalFieldData().get("thisMonthExpiredPoint") == null) {
                cepMemberDto.setThisMonthExpiredPoint(0.0);
            } else {
                cepMemberDto.setThisMonthExpiredPoint(Double.parseDouble(memberDto.getOptionalFieldData().get("thisMonthExpiredPoint").toString()));
            }
            if (memberDto.getOptionalFieldData().get("nextMonthExpiredPoint") == null) {
                cepMemberDto.setNextMonthExpiredPoint(0.0);
            } else {
                cepMemberDto.setNextMonthExpiredPoint(Double.parseDouble(memberDto.getOptionalFieldData().get("nextMonthExpiredPoint").toString()));
            }
            if (memberDto.getOptionalFieldData().get("rsvField11") == null) {
                cepMemberDto.setAgreementVersion("");
            } else {
                cepMemberDto.setAgreementVersion(memberDto.getOptionalFieldData().get("rsvField11").toString());
            }
            cepMemberDto.setNickName(memberDto.getOptionalFieldData().get("wechatNick") == null ? "" : memberDto.getOptionalFieldData().get("wechatNick").toString());
            cepMemberDto.setHeadImgUrl(memberDto.getOptionalFieldData().get("headImgUrl") == null ? "" : memberDto.getOptionalFieldData().get("headImgUrl").toString());
        } else {
            return cepMemberDto;
        }
        return  cepMemberDto;
    }
    @Override
    public ResponseResult queryMemberGrade(String customerNo, String memberType, String channelType) {
        CepMemberGradeDto cepMemberGradeDto = new CepMemberGradeDto();
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            CustomerBaseDto customerBaseDto = new CustomerBaseDto();
            customerBaseDto.setCustomerNo(customerNo);
            customerBaseDto.setChannelType(channelType);
            customerBaseDto.setMemberType(memberType);
            GradeDto gradeDto = customerClient.queryMemberGradeByChannelId(customerBaseDto);
            if (gradeDto != null) {
                log.info("查询出来的会员等级信息:{}", JSONObject.toJSONString(gradeDto));
                if(StringUtils.isNotBlank(gradeDto.getName())){
                    cepMemberGradeDto.setName(gradeDto.getName());
                    if (gradeDto.getName().equals("快乐萌新")) {
                        cepMemberGradeDto.setName("青铜");
                        cepMemberGradeDto.setId("1");
                    }
                    if (gradeDto.getName().equals("青铜")) {
                        cepMemberGradeDto.setId("1");
                    }
                    if (gradeDto.getName().equals("白银")) {
                        cepMemberGradeDto.setId("2");
                    }
                    if (gradeDto.getName().equals("黄金")) {
                        cepMemberGradeDto.setId("3");
                    }
                }else {
                    cepMemberGradeDto.setName("青铜");
                    cepMemberGradeDto.setId("1");
                }
                if (gradeDto.getExpiredTime() != null) {
                    log.info("查询出来的会员等级ExpiredTime:{}", sdf.format(gradeDto.getExpiredTime()));
                    cepMemberGradeDto.setExpiredTime(sdf.format(gradeDto.getExpiredTime()));
                } else {
                    cepMemberGradeDto.setExpiredTime("");
                }
                if (gradeDto.getEffectiveTime() != null) {
                    log.info("查询出来的会员等级EffectiveTime:{}", sdf.format(gradeDto.getEffectiveTime()));
                    cepMemberGradeDto.setEffectiveTime(sdf.format(gradeDto.getEffectiveTime()));
                } else {
                    cepMemberGradeDto.setEffectiveTime("");
                }
            } else {
                cepMemberGradeDto.setId("1");
                cepMemberGradeDto.setName("青铜");
                log.info("查询出来的会员等级信息为空直接返回:{}", JSON.toJSONString(cepMemberGradeDto));
                return new ResponseResult("200", ResponseCodeEnum.SUCCESS.getMsg(), cepMemberGradeDto);
            }

        } catch (Exception e) {
            log.info("查询出来的会员等级异常信息:{}", e.getMessage());
            if ("会员不存在".equals(e.getMessage())) {
                return new ResponseResult("200", ResponseCodeEnum.SUCCESS.getMsg(), cepMemberGradeDto);
            }
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, e.getMessage());
        }
        return new ResponseResult("200", ResponseCodeEnum.SUCCESS.getMsg(), cepMemberGradeDto);
    }

    @Override
    public ResponseResult queryCostCenter() {
        List<CostCenterDto> CostCenterDtoList = null;

        try {
            CostCenterDtoList = costCenterRepository.selectCostCenter();
        } catch (Exception e) {
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, ResponseCodeEnum.OPENAPI_FAILED.getMsg());
        }
        return new ResponseResult("200", ResponseCodeEnum.SUCCESS.getMsg(), CostCenterDtoList);
    }

    @Override
    public ResponseResult updateMemberScenePoint(CepMemberPointRecordDto cepMemberPointRecordDto) {

        if (null == cepMemberPointRecordDto.getIsCostMapping()) {
            cepMemberPointRecordDto.setIsCostMapping(false);
        }
        MemberPointModifyRequest memberPointDto = new MemberPointModifyRequest();
        //查询会员
        String memberId = memberRepository.queryMemberId(cepMemberPointRecordDto.getCustomerNo());
        if (StringUtils.isBlank(memberId)) {
            return new ResponseResult(ResponseCodeEnum.MEMBER_FAILED);
        }
        log.info("查询会员id: {}", memberId);
        memberPointDto.setShopId(cepMemberPointRecordDto.getCostCenter());
        if (!"SEND".equals(cepMemberPointRecordDto.getChangeType()) && !"DEDUCT".equals(cepMemberPointRecordDto.getChangeType())) {
            return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, "积分变更类型不对： SEND: 立即发放，DEDUCT: 扣减");
        }
        String costCenter = cepCostCenterRepository.getCostCenter(cepMemberPointRecordDto.getCostCenter());
        //判断成本中心是否存在
        log.info("查询成本中心:{}", costCenter);
        if (StringUtils.isBlank(costCenter)) {
            return new ResponseResult(ResponseCodeEnum.MEMBER_COStCENTER);
        }
        //封装实体
        memberPointDto.setKZZD1(cepMemberPointRecordDto.getChangeSourceDetail());
        memberPointDto.setKZZD2(cepMemberPointRecordDto.getChangeSource());
        memberPointDto.setKZZD3(cepMemberPointRecordDto.getCostTracing());
        // memberPointDto.setActionName(cepMemberPointRecordDto.);
        memberPointDto.setChangeMode("INTERFACE");
        memberPointDto.setChannelType(cepMemberPointRecordDto.getChannelType());
        memberPointDto.setDesc(cepMemberPointRecordDto.getDesc());
        memberPointDto.setMemberId(memberId);
        log.info("忠诚度积分账号类型id:{}", ConfigurationCenterUtil.ACCOUNT_POINT);
        memberPointDto.setPointAccountId(Integer.valueOf(ConfigurationCenterUtil.ACCOUNT_POINT));
        memberPointDto.setIdempotentMode(1);
        try {
            if ("DEDUCT".equals(cepMemberPointRecordDto.getChangeType())) {
                //查询会员积分
               /* PointDto pointInfo = memberClient.getPointInfo(memberId, null, ModelNameEnum.MEMBER_TYPE.getValue(), null, null);
                BigDecimal bigPoint = new BigDecimal(cepMemberPointRecordDto.getPoint());
                BigDecimal bigPointInfo = new BigDecimal(pointInfo.getPoint());
                //判断积分是否足够扣减
                if (bigPointInfo.compareTo(bigPoint) >= 0) {*/
                memberPointDto.setPoint(cepMemberPointRecordDto.getPoint());
                memberPointDto.setEffectDate((DateHelper.changeShanghaiToUTC(DateHelper.getDateTimeFormat())));
                memberPointDto.setRecordType(cepMemberPointRecordDto.getChangeType());
                memberPointDto.setOverdueDate(DateHelper.changeShanghaiToUTC(DateHelper.getDayOfMonth()));
                memberPointDto.setTriggerId(cepMemberPointRecordDto.getBusinessId());
                log.info("封装的实体: {}", memberPointDto);
                memberPointModifyClient.memberModifyPoint(memberPointDto);
                log.info("请求成功变更会员积分(瓶子)memberId:{},businessId:{}", memberId, cepMemberPointRecordDto.getBusinessId());
                return new ResponseResult(ResponseCodeEnum.SUCCESS);
                /*} else {
                    return new ResponseResult(ResponseCodeEnum.MEMBER_POINT);
                }*/
            }
        } catch (Exception e) {
            if ("此X-Business-Token已执行".equals(e.getMessage())) {
                return new ResponseResult(ResponseCodeEnum.SUCCESS_IDEMPOTENT);
            }
            log.error("变更会员积分(瓶子)--支持取消/退还场景_memberId:{},businessId:{},e:{}", memberId, cepMemberPointRecordDto.getBusinessId(), e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED.getCode(), e.getMessage());
        }
        try {
            if ("SEND".equals(cepMemberPointRecordDto.getChangeType())) {
                memberPointDto.setRecordType("DEDUCT");
                memberPointDto.setTradeId(cepMemberPointRecordDto.getBusinessId());
                memberPointDto.setTriggerId(UUID.randomUUID().toString().replaceAll("-", ""));
                log.info("封装的实体: {}", memberPointDto);
                CustomizeException map = memberPointModifyClient.memberReversePoint(memberPointDto);
                log.info("请求成功忠诚度返回值: {}", JSON.toJSONString(map));
                if (!Objects.isNull(map)) {
                    return new ResponseResult(map.getError_code(), map.getMsg());
                }
            }
        } catch (Exception e) {
            if ("未找到正向操作".equals(e.getMessage())) {
                return new ResponseResult(ResponseCodeEnum.MEMBER_POINT_ERROR);
            }
            if ("正向积分已经被使用".equals(e.getMessage())) {
                return new ResponseResult(ResponseCodeEnum.MEMBER_POINT_USED);
            }
            log.error("变更会员积分(瓶子)--支持取消/退还场景memberId:{},businessId:{},e:{}", memberId, cepMemberPointRecordDto.getBusinessId(), e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED.getCode(), e.getMessage());
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }


    @Override
    public CepPointRecordListPageResponse queryPointRecord(CepPointBehaviorRecordDto cepPointBehaviorRecordDto) {
        String memberId = memberRepository.queryMemberId(cepPointBehaviorRecordDto.getCustomerNo());
        if (StringUtils.isBlank(memberId)) {
            return CepPointRecordListPageResponse.Empty(cepPointBehaviorRecordDto.getPage(), cepPointBehaviorRecordDto.getPageSize());
        }
        cepPointBehaviorRecordDto.setCustomerNo(memberId);
        log.info("查询会员id: {}", memberId);
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("select count(1) total from ");
        stringBuffer.append(ModelConstants.POINT_BEHAVIOR_RECORD);
        stringBuffer.append(" where memberId = '");
        stringBuffer.append(cepPointBehaviorRecordDto.getCustomerNo());
        if (StringUtils.isNoneBlank(cepPointBehaviorRecordDto.getStartTime()) &&
                StringUtils.isNoneBlank(cepPointBehaviorRecordDto.getEndTime())) {
            stringBuffer.append("' and modified between '");
            stringBuffer.append(DateHelper.getZone(cepPointBehaviorRecordDto.getStartTime()));
            stringBuffer.append("' and  '");
            stringBuffer.append(DateHelper.getZone(cepPointBehaviorRecordDto.getEndTime()));
        }
        stringBuffer.append("'");
        log.info("查询总条数SQL: {}", stringBuffer.toString());
        BaseResponse totalResponse = dataapiHttpSdk.execute(stringBuffer.toString(), Collections.emptyMap());
        if (CollectionUtils.isEmpty(totalResponse.getData())) {
            return CepPointRecordListPageResponse.Empty(cepPointBehaviorRecordDto.getPage(), cepPointBehaviorRecordDto.getPageSize());
        }
        List<Map<String, Object>> totalData = totalResponse.getData();
        int total = (Integer) totalData.get(0).get("total");
        CriteriaBuilder cbw = CriteriaBuilder.newBuilder();
        QueryDataRequest param = new QueryDataRequest();
        param.setFields("point,modified,recordType,channel");
        cbw.and(CriteriaBuilder.eq("memberId", cepPointBehaviorRecordDto.getCustomerNo()));
        if (StringUtils.isNoneBlank(cepPointBehaviorRecordDto.getStartTime()) &&
                StringUtils.isNoneBlank(cepPointBehaviorRecordDto.getEndTime())) {
            cbw.and(CriteriaBuilder.between("modified", DateHelper.getZone(cepPointBehaviorRecordDto.getStartTime()), DateHelper.getZone(cepPointBehaviorRecordDto.getEndTime())));
        }
        param.setFqn(ModelConstants.POINT_BEHAVIOR_RECORD);
        param.setFilter(cbw.build());
        param.setOffset((cepPointBehaviorRecordDto.getPage() - 1) * cepPointBehaviorRecordDto.getPageSize());
        param.setLimit(cepPointBehaviorRecordDto.getPageSize());
        PageQueryResponse pageQueryResponse = dataapiHttpSdk.queryObjects(param);
        List<Map<String, Object>> mapData = pageQueryResponse.getData();
        log.info("查询结果: {}", mapData);
        if (mapData == null || mapData.size() == 0) {
            return CepPointRecordListPageResponse.Empty(cepPointBehaviorRecordDto.getPage(), cepPointBehaviorRecordDto.getPageSize());
        }
        List<CepPointBehaviorRecordResponse> pointRecordListResponses = new ArrayList<>();
        mapData.forEach(map -> {
            PointRecordResponse pointRecordResponse = JSON.parseObject(JSON.toJSONString(map), PointRecordResponse.class);
            CepPointBehaviorRecordResponse cepPointBehaviorRecordResponse = new CepPointBehaviorRecordResponse();
            if ("SEND".equals(pointRecordResponse.getRecordType())) {
                cepPointBehaviorRecordResponse.setPoint(pointRecordResponse.getPoint());
            } else {
                cepPointBehaviorRecordResponse.setPoint(-pointRecordResponse.getPoint());
            }
            cepPointBehaviorRecordResponse.setChangeType(pointRecordResponse.getRecordType());
            cepPointBehaviorRecordResponse.setChannelType(pointRecordResponse.getChannel());
            cepPointBehaviorRecordResponse.setChangeTime(pointRecordResponse.getModified());
            pointRecordListResponses.add(cepPointBehaviorRecordResponse);
        });
        CepPointRecordListPageResponse<CepPointBehaviorRecordResponse> pageResponse = CepPointRecordListPageResponse.PagingList(cepPointBehaviorRecordDto.getPage(), cepPointBehaviorRecordDto.getPageSize(), pointRecordListResponses, total);

        return pageResponse;
    }


    @Override
    public ResponsesVo deleteMember(MemberCancelDto memberCancelDto) {

        try {
            deleteMemberRunnable runnable = new deleteMemberRunnable(memberCancelDto);
            new Thread(runnable, "deleteMember").start();
        } catch (Exception e) {
            log.error("线程执行失败:{}", JSON.toJSONString(memberCancelDto));
        }

        return new ResponsesVo(200, ResponseCodeEnum.SUCCESS.getMsg());
    }

    public class deleteMemberRunnable implements Runnable {
        MemberCancelDto memberCancelDto;

        private deleteMemberRunnable(MemberCancelDto memberCancelDto) {
            this.memberCancelDto = memberCancelDto;
        }

        @Override
        public void run() {
            try {
                deleteMemberTable(memberCancelDto);
            } catch (Exception e) {
                log.error("自定义子线程执行失败:{}", JSON.toJSONString(memberCancelDto));
            }
        }
    }

    /**
     * 会员注销异步处理
     */
    public void deleteMemberTable(MemberCancelDto memberCancelDto) {

        for (String s : memberCancelDto.getMemberId()) {
            //查询会员是否存在
            MemberDto memberDto = null;
            String customerNo = null;
            String channelType = null;
            PointDto pointInfo = null;
            GradeDto gradeInfo = null;
            String id = UUID.randomUUID().toString().replaceAll("-", "");
            List<Map<String, String>> customerNoList = null;
            try {
                memberDto = memberClient.query(s, null, null, ModelNameEnum.MEMBER_TYPE.getValue(), null, false);
                if (StringUtils.isBlank(memberDto.getMemberId())) {
                    memberCancel(id, memberCancelDto, memberDto.getMemberId(), memberDto.getMobile(), "N", "CRM系统：会员不存在");
                    continue;
                    //return new ResponsesVo(300, "会员不存在");
                } //查询会员是否存在
                log.info("注销memberDto:{}", JSON.toJSONString(memberDto));
                List<MemberCancelDto> memberCancel = memberCancelRepository.getMemberCancelState(s);
                if (CollectionUtils.isNotEmpty(memberCancel)) {
                    memberCancel(id, memberCancelDto, memberDto.getMemberId(), memberDto.getMobile(), "N", "CRM系统：会员已注销");
                    continue;
                    //return new ResponsesVo(300, "会员已存在黑名单");
                }
                //查询会员CustomerNo
                customerNoList = memberRepository.queryCustomerNoList(s);
                log.info("注销查询会员CustomerNo:{}", JSON.toJSONString(customerNoList));
                //查询会员积分
                pointInfo = memberClient.getPointInfo(s, null, ModelNameEnum.MEMBER_TYPE.getValue(), null, null);
                //查询会员等级
                gradeInfo = memberClient.getGradeInfo(s, null, ModelNameEnum.MEMBER_TYPE.getValue(), null, null);
                log.info("查询会员积分:{},查询会员等级:{},customerNo:{}", JSON.toJSONString(pointInfo), JSON.toJSONString(gradeInfo));
            } catch (Exception e) {
                log.error("注销查询会员失败:{}", e.getMessage());
                memberCancel(id, memberCancelDto, memberDto.getMemberId(), memberDto.getMobile(), "N", "CRM系统：openapi服务接口异常");
                continue;
            }
            Boolean statusType = true;

            // 根据 channelType 去重
            List<Map<String, String>> uniqueList = customerNoList.stream()
                    .collect(Collectors.groupingBy(map -> map.get("channelType")))
                    .values().stream()
                    .map(list -> list.get(0)) // 取每个 channelType 的第一个元素
                    .collect(Collectors.toList());

            for (Map<String, String> map : uniqueList) {
                if ("KO_MP".equals(map.get("channelType"))) {
                    log.info("注销的渠道:{}, memberId:{}, customerNo:{}", map.get("channelType"), memberDto.getMemberId(), map.get("customerNo"));
                    // 尝试同步更新联蔚
                    try {
                        JSONObject object = cancelWechatWithdrawConsumer(memberDto.getMemberId(), 4);
                        if (!"200".equals(object.get("code"))) {
                            memberCancel(id, memberCancelDto, memberDto.getMemberId(), memberDto.getMobile(), "N", "调用CT注销失败：" + object.get("message").toString());
                            statusType = false;
                            continue;
                        }
                    } catch (Exception e) {
                        log.info("同步联蔚接口异常:{}, 会员:{}", e.getMessage(), memberDto.getMemberId());
                        memberCancel(id, memberCancelDto, memberDto.getMemberId(), memberDto.getMobile(), "N", "调用CT注销失败：同步CT接口异常");
                        statusType = false;
                        continue;
                    }
                    // 更新会员绑定信息
                    memberRepository.updateMemberBinding(s, map.get("customerNo"), map.get("channelType"));
                }
                if ("SWIRE_MP".equals(map.get("channelType")) && statusType) {
                    log.info("注销的渠道:{},memberId:{},customerNo:{}", map.get("channelType"), memberDto.getMemberId(), map.get("customerNo"));
                    //cepMemberCouponServiceImpl.deleteMember(ConfigurationCenterUtil.MEMBER_CEP_CANCELLATION, map.get("customerNo"), "DELETE_MEMBERSHIP");
                    memberRepository.updateMemberBinding(s, map.get("customerNo"), map.get("channelType"));
                }
                if ("KO_Ali".equals(map.get("channelType")) && statusType) {
                    log.info("注销的渠道:{},memberId:{},customerNo:{}", map.get("channelType"), memberDto.getMemberId(), map.get("customerNo"));
                    //cepMemberCouponServiceImpl.deleteMember(ConfigurationCenterUtil.MEMBER_CEP_CANCELLATION, map.get("customerNo"), "DELETE_MEMBERSHIP");
                    memberRepository.updateMemberBinding(s, map.get("customerNo"), map.get("channelType"));
                }
                if ("CBL_MP".equals(map.get("channelType")) && statusType) {
                    log.info("注销的渠道:{},memberId:{},customerNo:{}", map.get("channelType"), memberDto.getMemberId(), map.get("customerNo"));
                    //cepMemberCouponServiceImpl.deleteMember(ConfigurationCenterUtil.MEMBER_CEP_CANCELLATION, map.get("customerNo"), "DELETE_MEMBERSHIP");
                    memberRepository.updateMemberBinding(s, map.get("customerNo"), map.get("channelType"));
                }
            }
            if (statusType) {

            //扣减会员积分至0
            String occId = null;
            try {
                    if (pointInfo.getPoint() > 0) {
                        PointRequestDto pointRequestDto = new PointRequestDto();
                        pointRequestDto.setPoint(pointInfo.getPoint());
                        pointRequestDto.setMemberId(s);
                        pointRequestDto.setChangeType("DEDUCT");
                        pointRequestDto.setDescription("会员注销扣减");
                        pointRequestDto.setKZZD2("商家调整");
                        pointRequestDto.setChannelType("loyalty");
                        pointRequestDto.setMemberType(memberDto.getMemberType());
                        memberClient.updatePoint(pointRequestDto, UUID.randomUUID().toString().replaceAll("-", ""));
                    }

                    if (null == gradeInfo.getName()) {
                        gradeInfo.setName("青铜");
                    }
                    String gradeInfoName = gradeInfo.getName();
                    if (!"青铜".equals(gradeInfoName)) {
                        log.info("注销会员等级:{}", gradeInfoName);
                        //等级降至最低
                        GradeRequestDto gradeRequestDto = new GradeRequestDto();
                        gradeRequestDto.setGradeId(ConfigurationCenterUtil.MEMBER_GRADE_DEFINITIONID);
                        gradeRequestDto.setMemberId(s);
                        gradeRequestDto.setRecordSourceDetail("会员注销变更");
                        gradeRequestDto.setChannelType("loyalty");
                        gradeRequestDto.setMemberType(memberDto.getMemberType());
                        memberClient.updateGrade(gradeRequestDto, UUID.randomUUID().toString().replaceAll("-", ""));
                    }
                    //查询所以勋章
                    JSONArray jsonArray = loyaltyFacade.medalQuery(ConfigurationCenterUtil.medalHierarchyId, ConfigurationCenterUtil.planId, s, 0, 500);
                    log.info("查询所以勋章:{}",JSON.toJSONString(jsonArray));
                    if (ObjectUtil.isNotEmpty(jsonArray)) {
                        for (int i = 0; i < jsonArray.size(); i++) {
                            //回收勋章
                            MedalObtainRequest request = new MedalObtainRequest();
                            request.setMemberId(s);
                            request.setChannelType("loyalty");
                            request.setMedalDefinitionId(jsonArray.getJSONObject(i).getInteger("medalDefinitionId"));
                            request.setMedalHierarchyId(jsonArray.getJSONObject(i).getInteger("medalHierarchyId"));
                            request.setDescription("会员注销变更");
                            log.info("回收勋章:{}",JSON.toJSONString(request));
                            //回收勋章
                            JSONObject object = loyaltyFacade.medalRecycle(request);
                            log.info("object回收勋章:{}",JSON.toJSONString(object));
                        }
                    }
                    //查询会员occId
                    occId = memberRepository.getOmniChannelCustomerByOccId(s);
                } catch (Exception e) {
                    log.error("CRM系统：openapi服务接口异常1:{}", e.getMessage());
                    memberCancel(id, memberCancelDto, memberDto.getMemberId(), memberDto.getMobile(), "N", "CRM系统：openapi服务接口异常1");
                    continue;
                }
                ChecklistDto checkedOccid = new ChecklistDto();
                checkedOccid.setChecklistType("BLACK");
                checkedOccid.setFqn("data.prctvmkt.KO.Member");
                checkedOccid.setCustomer(occId);
                log.info("注销分组:{}", ConfigurationCenterUtil.MEMBER_CANCEL_GROUPID);
                checkedOccid.setGroupId(ConfigurationCenterUtil.MEMBER_CANCEL_GROUPID);
                checkedOccid.setRemark("会员注销");
                //会员memberId记录黑名单
                ChecklistDto checklistDto = new ChecklistDto();
                checklistDto.setChecklistType("BLACK");
                checklistDto.setFqn("data.prctvmkt.KO.Member");
                checklistDto.setCustomer(s);
                checklistDto.setGroupId(ConfigurationCenterUtil.MEMBER_CANCEL_GROUPID);
                checklistDto.setRemark("会员注销");

                try {
                    //删除MemberIdentify
                    memberRepository.updateMembrIdtify(s);
                    log.info("黑名单实体:{}", checkedOccid);
                    memberChecklistClient.memberChenklist(checkedOccid);
                    memberChecklistClient.memberChenklist(checklistDto);
                } catch (Exception e) {
                    memberCancel(id, memberCancelDto, memberDto.getMemberId(), memberDto.getMobile(), "N", "CRM系统：risk-control服务接口异常");
                    continue;
                    //return new ResponsesVo(500, "risk-control服务接口异常");
                }
                //更新会员mobile字段
                Map<String, Object> parameters = new HashMap<>();
                parameters.put("mobile", null);
                parameters.put("mobileCancel", memberDto.getMobile());
                parameters.put("isCancel", true);
                parameters.put("unionId", null);
                parameters.put("blacklist", null);
                parameters.put("rsvField4", null);
                parameters.put("rsvField5", null);
                try {
                    memberRepository.updateMemberDto(s, parameters);
                } catch (Exception e) {
                    memberCancel(id, memberCancelDto, memberDto.getMemberId(), memberDto.getMobile(), "N", "更新会员mobile字段服务接口异常");
                    continue;
                }
                //更新会员注销记录表 crm注销成功
                memberCancel(id, memberCancelDto, memberDto.getMemberId(), memberDto.getMobile(), "Y", "成功");
           }

        }
    }

    private JSONObject cancelWechatWithdrawConsumer(String memberId, int retryNum) throws Exception {
        int index = 1;
        while (index <= retryNum || retryNum == -1) {
            try {
                // 创建请求对象
                LwWechatCustomerRemove customerRemove = new LwWechatCustomerRemove();
                customerRemove.setType("CRM_ID");
                customerRemove.setUid(memberId);
                log.info("同步注销至联蔚:{}", JSON.toJSONString(customerRemove));

                // 调用接口
                JSONObject object = wechatService.lwWechatWithdrawConsumer(customerRemove);
                log.info("同步注销至联蔚返回结果:{}", JSON.toJSONString(object));

                // 检查接口返回结果
                if ("200".equals(object.get("code"))) {
                    log.info("注销成功 memberId:{}", memberId);
                    return object; // 重试成功，返回结果
                } else if ("EXCEED_LIMIT".equals(object.get("code"))) {
                    log.info("注销上限 memberId:{}", memberId);
                    return object; // 上限，返回结果
                } else {
                    log.info("第{}次重试, memberId:{}", index, memberId);
                    index++;
                }
            } catch (Exception e) {
                log.info("同步联蔚接口异常:{}, 会员:{}", e.getMessage(), memberId);
                index++;
            }

            // 重试间隔
            try {
                // 设置重试间隔为2分钟（120000毫秒）
                Thread.sleep(120000);
            } catch (InterruptedException interruptedException) {
                log.error("会员注销InterruptedException:{}", interruptedException);
                Thread.currentThread().interrupt();
                break; // 如果线程被中断，退出重试循环
            }
        }
        throw new Exception("重试失败，达到最大重试次数");
    }


    @Override
    public CepMemberPointItemDto getMemberPointItems(String customerNo, String startTime, String endTime, Integer page, Integer pageSize, List<String> scene, String channelType) {
        //查询会员
        CepMemberPointItemDto itemDto = new CepMemberPointItemDto();
        String memberId = memberRepository.queryMemberId(customerNo);
        if (StringUtils.isBlank(memberId)) {
            itemDto.setMsgs("会员不存在");
            itemDto.setCode(ResponseCodeEnum.MEMBER_FAILED.getCode());
            return itemDto;
        }
        List<PointBehaviorRecordDto> pointlist = null;
        if (CollectionUtils.isEmpty(scene)) {
            //查询总页数
            /*String count = pointBehaviorRecordRepository.getMemberPointCount(memberId, startTime, endTime);
            itemDto.setTotalCount(count);*/
            //查询会员积分明细
            List<PointBehaviorRecordDto> pointBehaviorRecordDtos = pointBehaviorRecordRepository.selectMemberPointItemlist(memberId, startTime, endTime, page, pageSize);
            pointlist = JSON.parseArray(JSON.toJSONString(pointBehaviorRecordDtos), PointBehaviorRecordDto.class);
        } else {
            //根据场景值查询积分明细
/*            List<String> list = new ArrayList<>();
            for (String s : scene) {
                InteractiveAndCampaignPointsRuleDto campaign = interactiveCampaignPointRuleRepository.getCampaign(s + channelType);
                log.info("根据场景值查询积分明细:{}",JSON.toJSONString(campaign));
                if(null == campaign){
                    list.add(s);
                }else {
                    list.add(s + "#" + campaign.getName());
                }
            }*/
            //查询总页数
            /*String count = pointBehaviorRecordRepository.getPointCount(memberId, scene, channelType);
            itemDto.setTotalCount(count);*/
            log.info("根据场景值查询:{}", JSON.toJSONString(scene));
            List<PointBehaviorRecordDto> pointBehaviorRecordDtos = pointBehaviorRecordRepository.selectMemberPointScene(memberId, scene, channelType, startTime, endTime, page, pageSize);
            pointlist = JSON.parseArray(JSON.toJSONString(pointBehaviorRecordDtos), PointBehaviorRecordDto.class);
        }

        ArrayList<PointBehaviorRecordDto> behaviorRecordDtos = new ArrayList<>();
        log.info("会员customerNo:{},积分明细:{}",customerNo, JSON.toJSONString(pointlist));
        if (CollectionUtils.isNotEmpty(pointlist)) {
            for (PointBehaviorRecordDto recordDto : pointlist) {
                if (!"FREEZE".equals(recordDto.getChangeType()) && !"UNFREEZE".equals(recordDto.getChangeType()) && !"SPECIAL_FREEZE".equals(recordDto.getChangeType()) && !"SPECIAL_UNFREEZE".equals(recordDto.getChangeType()) && !"OPEN_UNFREEZE".equals(recordDto.getChangeType()) && !"OPEN_FREEZE".equals(recordDto.getChangeType())) {
                    PointBehaviorRecordDto po = new PointBehaviorRecordDto();
                    po.setChannelType(recordDto.getChannelType());
                    po.setChangeSource(recordDto.getChangeSource());
                    po.setChangeSourceDetail(recordDto.getChangeSourceDetail());
                    if(!"SEND".equals(recordDto.getChangeType()) && !"REVERSE_SEND".equals(recordDto.getChangeType()) && !"DELAY_SEND".equals(recordDto.getChangeType()) && !"TIMER".equals(recordDto.getChangeType()) ){
                        String s = String.valueOf(recordDto.getPoint()).replace(".0", "");
                        po.setDisplayPoint("-" + s);
                    }else {
                        String s = String.valueOf(recordDto.getPoint()).replace(".0", "");
                        po.setDisplayPoint("+" + s);
                    }

                    if ("loyalty".equals(recordDto.getChannelType())) {
                        po.setChannelType("CRM系统");
                    }
                    if ("FANS_DAY".equals(recordDto.getChannelType())) {
                        po.setChannelType("粉丝节");
                    }
                    /*if (!"SWIRE_MP".equals(recordDto.getChannelType())) {
                        po.setChannelType("KO_MP");
                    }*/
                    //MA积分
                    if ("YXZX".equals(recordDto.getChannelType())) {
                        po.setChangeSource("MA");
                        po.setChangeSourceDetail(recordDto.getActionId() + "_" + recordDto.getActionNodeId());
                    }
                    //手动变更积分
                    if ("MANUAL".equals(recordDto.getChangeMode())) {
                        String state = recordDto.getChangeType();
                        switch (state) {
                            case "SPECIAL_FREEZE":
                                po.setChangeSource("商家冻结");
                                break;
                            case "SPECIAL_UNFREEZE":
                                po.setChangeSource("商家解冻");
                                break;
                            case "EXPIRE":
                                po.setChangeSource("过期扣减");
                                break;
                  /*  case "SEND":
                        po.setChangeSource("商家调整");
                        break;
                    case "MANUAL_ABOLISH":
                        po.setChangeSource("商家调整");
                        break;
                    case "DEDUCT":
                        po.setChangeSource("商家调整");
                        break;*/
                            default:
                                po.setChangeSource("商家调整");
                        }
                    }
                    //定时生效的
               /* if ("TIMER".equals(recordDto.getChangeType()) && !"MANUAL".equals(recordDto.getChangeMode())) {
                    po.setChangeSource("消费");
                }*/
                    if ("EXPIRE".equals(recordDto.getChangeType())) {
                        po.setChangeSource("过期扣减");
                    }
                    if ("参与活动".equals(po.getChangeSource()) || "参与互动".equals(po.getChangeSource())){
                        log.info("po.getDesc() :{}",recordDto.getDesc());
                        String [] parts  = recordDto.getDesc().split("#");
                        if (parts.length > 1) {
                            po.setDisplayDesc(parts[1]);
                        } else {
                            po.setDisplayDesc(recordDto.getDesc());
                        }
                    } else if ("消费".equals(po.getChangeSource()) || "MA".equals(po.getChangeSource())){
                        po.setDisplayDesc(recordDto.getDesc());
                    }else {
                        po.setDisplayDesc(po.getChangeSource());
                    }
                    po.setDesc(recordDto.getDesc());
                    po.setPoint(recordDto.getPoint());
                    po.setInvalidPoint(recordDto.getInvalidPoint());
                    po.setFrozenPoint(recordDto.getFrozenPoint());
                    po.setChangeType(recordDto.getChangeType());
                    po.setChangeTime(getStartTimeDate(recordDto.getChangeTime()));
                    behaviorRecordDtos.add(po);
                }
            }
        }
        itemDto.setCurrentPage(page);
        itemDto.setPageSize(pageSize);
        itemDto.setData(behaviorRecordDtos);
        itemDto.setCode(ResponseCodeEnum.SUCCESS.getCode());
        itemDto.setMsgs(ResponseCodeEnum.SUCCESS.getMsg());
        return itemDto;
    }

    @Override
    public ResponseResult<Map<String, Object>> getMemberPoint(String channelType, String customerNo, String memberType) {
        //查询会员id
        //String memberId = memberRepository.queryMemberId(customerNo);
        List<MemberBingDto> memberBingDtos = openApiFeignClient.queryCustomer(customerNo, channelType, memberType);
        log.info("查询会员记录customerNo:{},memberBingDtos:{}", customerNo, JSON.toJSONString(memberBingDtos));
        if (CollectionUtils.isEmpty(memberBingDtos)){
            return new ResponseResult(ResponseCodeEnum.MEMBER_FAILED);
        }
        String memberId = memberBingDtos.get(0).getMemberId();
        String mobile =memberBingDtos.get(0).getMobile();
        //查询会员积分
        //MemberDto memberDto = memberClient.query(memberId, null, null, ModelNameEnum.MEMBER_TYPE.getValue(), null, false);
        //自定义查询的衍生字段
        /*ArrayList<String> optionalFields = new ArrayList<>();
        optionalFields.add("pointOfValid");
        optionalFields.add("experiencePoints");
        optionalFields.add("frozenPoint");
        optionalFields.add("delayPoint");*/
        PointDto ko = memberClient.getPointInfo(memberId, null, ModelNameEnum.MEMBER_TYPE.getValue(), null, null);
        log.info("查询会员积分接口customerNo:{},可用积分ko:{}", customerNo,JSON.toJSONString(ko));
        //查询会员
        //MemberCustomDto memberDto = openApiFeignClient.queryMember(ModelConstants.MEMBER_TYPE, memberId, optionalFields);
        Map<String,Object> memberPoint = memberRepository.getMemberPoint(mobile);
        log.info("查询会员扩展字段数据customerNo:{},memberPoint:{}", customerNo,JSON.toJSONString(memberPoint));
        HashMap<String, Object> map = new HashMap<>();
        /*if (StringUtils.isNotBlank(memberDto.getCustomizedProperties().get("pointOfValid").toString())) {
            map.put("point", Double.parseDouble(memberDto.getCustomizedProperties().get("pointOfValid").toString()));
        }*/
        if (null == ko.getPoint()) {
            map.put("point", 0.0);
        } else {
            map.put("point", ko.getPoint());
        }
        map.put("experiencePoints", memberPoint.get("experiencePoints"));
        map.put("frozenPoint", memberPoint.get("frozenPoint"));
        map.put("delayPoint", 0.0);
        return new ResponseResult(ResponseCodeEnum.SUCCESS, map);
    }


    @Override
    public ResponsesVo getCommonUser(String appid, String user_id) {
        String str = ConfigurationCenterUtil.MEMBER_SIDEBAR_GETMEMBERPATH + "?appid=" + appid + "&user_id=" + user_id;
        String sign = SHA256(str);
        try {
            String utls = ConfigurationCenterUtil.MEMBER_SIDEBAR_URL + ConfigurationCenterUtil.MEMBER_SIDEBAR_GETMEMBERPATH + "?appid=" + appid + "&user_id=" + user_id;
            RestTemplate restTemplate = new RestTemplate(new BufferingClientHttpRequestFactory(new SimpleClientHttpRequestFactory()));
            HttpHeaders header = new HttpHeaders();
            header.add("Content-Type", "application/json");
            header.add("X-EACH-VENDOR-ID", ConfigurationCenterUtil.MEMBER_SIDEBAR_VENDOR_ID);
            header.add("X-EACH-APP-ID", ConfigurationCenterUtil.MEMBER_SIDEBAR_APPID);
            header.add("X-EACH-SIGNATURE", sign);
            HttpEntity<String> requestEntity = new HttpEntity<>(null, header);
            String url = String.format(utls, 1, 2);
            List<ClientHttpRequestInterceptor> interceptors = new ArrayList<>();
            interceptors.add(new LoggingRequestInterceptor());
            restTemplate.setInterceptors(interceptors);
            ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class);
            log.info("用户授权返回结果:{}", JSON.toJSONString(responseEntity));
            ResponseResult results = JSON.parseObject(responseEntity.getBody(), ResponseResult.class);
            return new ResponsesVo(Integer.valueOf(results.getCode()).intValue(), results.getMsg(), (List<Map>) results.getData());
        } catch (RestClientException e) {
            log.error("用户授权返回结果:{}", e.getMessage());
            return new ResponsesVo(Integer.valueOf(ResponseCodeEnum.OPENAPI_FAILED.getCode()).intValue(), e.getMessage());
        }
    }

    @Override
    public Map<String, Object> queryMemberuat(OcpMemberBindingDto ocpMemberBindingDto) {

        //指定要查询的字段(含衍生/扩展)
        List<String> optionalFields = Arrays.asList("memberSource", "memberSourceDetail");

        //查询会员渠道信息
        List<MemberBingDto> memberBingDtos = openApiFeignClient.queryListChannels(ocpMemberBindingDto.getMember().get("crm_member_id").toString(), "KO", null, optionalFields);
        log.info("cdp查询会员渠道信息:{}",JSON.toJSONString(memberBingDtos));
        OcpMemberDto memberDto = new OcpMemberDto();
        memberDto.setIs_ocp_member(ocpMemberBindingDto.getIs_ocp_member());
        memberDto.setCreated_at(CepCouponSchedulng.getStartTimeDate(ocpMemberBindingDto.getCreated_at()));
        memberDto.setUpdated_at(CepCouponSchedulng.getStartTimeDate(ocpMemberBindingDto.getUpdated_at()));

        if (memberBingDtos.stream().anyMatch(m -> "SWIRE_MP".equals(m.getChannelType()))) {
            memberDto.setConsumer_source("MTYL");
            memberDto.setOrganization_name("Swire");
            memberDto.setOrganization_code("SCCL");
        }
        if (memberBingDtos.stream().anyMatch(m -> "CBL_MP".equals(m.getChannelType()))) {
            memberDto.setConsumer_source("YXH");
            memberDto.setOrganization_name("COFCO");
            memberDto.setOrganization_code("CBL");
        }
        if (memberBingDtos.stream().anyMatch(m -> "KO_Ali".equals(m.getChannelType()))) {
            memberDto.setConsumer_source("Ali MP");
            memberDto.setOrganization_name(ocpMemberBindingDto.getOrganization_name());
            memberDto.setOrganization_code(ocpMemberBindingDto.getOrganization_code());
        }
        if (memberBingDtos.stream().anyMatch(m -> "KO_MP".equals(m.getChannelType()))) {
            memberDto.setConsumer_source(ocpMemberBindingDto.getConsumer_source());
            memberDto.setOrganization_name(ocpMemberBindingDto.getOrganization_name());
            memberDto.setOrganization_code(ocpMemberBindingDto.getOrganization_code());
        }

        HashMap<String, Object> campaignInfo = new HashMap<>();
        campaignInfo.put("info_authorized", ocpMemberBindingDto.getCampaign_info().get("info_authorized"));
        campaignInfo.put("campaign_id", ocpMemberBindingDto.getCampaign_info().get("campaign_id"));
        campaignInfo.put("lbs_authorized", ocpMemberBindingDto.getCampaign_info().get("lbs_authorized"));
        memberDto.setCampaign_info(campaignInfo);
        ArrayList<Map> arrayList = new ArrayList<>();

        ArrayList<Object> platformBind = new ArrayList<>();
        for (MemberBingDto bingDto : memberBingDtos) {
            HashMap<String, Object> bind = new HashMap<>();
            bind.put("channel", bingDto.getChannelType());
            bind.put("timestamp", getStartTimeDate(bingDto.getCreateTime()));
            bind.put("source", bingDto.getOptionalFieldData().get("memberSource"));
            if (StringUtils.isNotBlank(bingDto.getOptionalFieldData().get("memberSourceDetail"))) {
                bind.put("source_detail", bingDto.getOptionalFieldData().get("memberSourceDetail"));
            }
            bind.put("platform", "wechat");
            HashMap<String, String> map = new HashMap<>();
            map.put("app_id", bingDto.getAppId());
            if ("SWIRE_MP".equals(bingDto.getChannelType())) {
                map.put("app_name", "每天有乐");
                //添加openId
                if (StringUtils.isNotBlank(bingDto.getOpenId())) {
                    HashMap<String, Object> wechatMap = new HashMap<>();
                    wechatMap.put("type", "wechat_open_id");
                    wechatMap.put("value", bingDto.getOpenId());
                    wechatMap.put("platform", "wechat");
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_name", "每天有乐");
                    hashMap.put("app_id", bingDto.getAppId());
                    hashMap.put("app_group_name", "太古可口可乐");
                    wechatMap.put("attribute", hashMap);
                    arrayList.add(wechatMap);
                }
                //添加unionId
                if (StringUtils.isNotBlank(bingDto.getUnionId())) {
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_group_name", "太古可口可乐");
                    HashMap<String, Object> tmallMap = new HashMap<>();
                    tmallMap.put("platform", "wechat");
                    tmallMap.put("type", "wechat_union_id");
                    tmallMap.put("value", bingDto.getUnionId());
                    tmallMap.put("attribute", hashMap);
                    arrayList.add(tmallMap);
                }
            }
            if ("CBL_MP".equals(bingDto.getChannelType())) {
                map.put("app_name", "悦喜荟");
                //添加openId
                if (StringUtils.isNotBlank(bingDto.getOpenId())) {
                    HashMap<String, Object> wechatMap = new HashMap<>();
                    wechatMap.put("type", "wechat_open_id");
                    wechatMap.put("value", bingDto.getOpenId());
                    wechatMap.put("platform", "wechat");
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_name", "悦喜荟");
                    hashMap.put("app_id", bingDto.getAppId());
                    hashMap.put("app_group_name", "中粮可口可乐");
                    wechatMap.put("attribute", hashMap);
                    arrayList.add(wechatMap);
                }
                //添加unionId
                if (StringUtils.isNotBlank(bingDto.getUnionId())) {
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_group_name", "中粮可口可乐");
                    HashMap<String, Object> tmallMap = new HashMap<>();
                    tmallMap.put("platform", "wechat");
                    tmallMap.put("type", "wechat_union_id");
                    tmallMap.put("value", bingDto.getUnionId());
                    tmallMap.put("attribute", hashMap);
                    arrayList.add(tmallMap);
                }
            }
            if ("KO_Ali".equals(bingDto.getChannelType())) {
                map.put("app_name", "支付宝可口可乐吧");
                bind.put("platform", "alibaba");
                if (StringUtils.isNotBlank(bingDto.getOpenId())) {
                    HashMap<String, Object> wechatMap = new HashMap<>();
                    wechatMap.put("type", "alipay");
                    wechatMap.put("value", bingDto.getOpenId());
                    wechatMap.put("platform", "alibaba");
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_name", "支付宝可口可乐吧");
                    hashMap.put("app_id", bingDto.getAppId());
                    wechatMap.put("attribute", hashMap);
                    arrayList.add(wechatMap);
                }
            }
            if ("KO_MP".equals(bingDto.getChannelType())) {
                map.put("app_name", "可口可乐吧");
                //添加openId
                if (StringUtils.isNotBlank(bingDto.getOpenId())) {
                    HashMap<String, Object> wechatMap = new HashMap<>();
                    wechatMap.put("type", "wechat_open_id");
                    wechatMap.put("value", bingDto.getOpenId());
                    wechatMap.put("platform", "wechat");
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("is_koplus_openid", ocpMemberBindingDto.getAttribute().get("is_koplus_openid"));
                    hashMap.put("app_group_name", "可口可乐");
                    wechatMap.put("attribute", hashMap);
                    arrayList.add(wechatMap);
                }
                //添加unionId
                if (StringUtils.isNotBlank(bingDto.getUnionId())) {
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_group_name", "可口可乐");
                    HashMap<String, Object> tmallMap = new HashMap<>();
                    tmallMap.put("platform", "wechat");
                    tmallMap.put("type", "wechat_union_id");
                    tmallMap.put("value", bingDto.getUnionId());
                    tmallMap.put("attribute", hashMap);
                    arrayList.add(tmallMap);
                }
            }

            bind.put("attribute", map);
            platformBind.add(bind);
        }

        //添加memberId
        HashMap<String, Object> memberCrm = new HashMap<>();
        memberCrm.put("platform", ocpMemberBindingDto.getCrm_member().get("platform"));
        memberCrm.put("type", ocpMemberBindingDto.getCrm_member().get("type"));
        memberCrm.put("value", ocpMemberBindingDto.getCrm_member().get("value"));
        //memberCrm.put("attribute", Collections.EMPTY_MAP);
        arrayList.add(memberCrm);

        //添加手机号
        HashMap<String, Object> memberMobile = new HashMap<>();
        memberMobile.put("platform", "phone");
        memberMobile.put("type", "phone_number");
        memberMobile.put("value", ocpMemberBindingDto.getMember().get("mobile"));
        memberMobile.put("attribute", Collections.EMPTY_MAP);
        arrayList.add(memberMobile);
        memberDto.setPlatform_info(arrayList);
        memberDto.setDemographic(ocpMemberBindingDto.getDemographic());

        HashMap<String, Object> equity = new HashMap<>();
        if (null != ocpMemberBindingDto.getMember().get("first_bottle_time")) {
            equity.put("first_bottle_time", copStartTimeDate(JSON.toJSONString(ocpMemberBindingDto.getMember().get("first_bottle_time"))));
            equity.put("available_bottle", ocpMemberBindingDto.getMember().get("available_bottle"));
            equity.put("total_bottle", ocpMemberBindingDto.getMember().get("total_bottle"));
            equity.put("used_bottle", ocpMemberBindingDto.getMember().get("used_bottle"));
            equity.put("expired_bottle", ocpMemberBindingDto.getMember().get("expired_bottle"));
        }
        HashMap<String, Object> member = new HashMap<>();
        member.put("member_level", ocpMemberBindingDto.getMember().get("member_level"));
        member.put("register_source_detail", ocpMemberBindingDto.getMember().get("register_source_detail"));
        if (null != ocpMemberBindingDto.getMember().get("logoff_time")) {
            member.put("logoff_time", copStartTimeDate(JSON.toJSONString(ocpMemberBindingDto.getMember().get("logoff_time"))));
        }
        if (null != ocpMemberBindingDto.getMember().get("register_time")) {
            member.put("register_time", copStartTimeDate(JSON.toJSONString(ocpMemberBindingDto.getMember().get("register_time"))));
        }
        member.put("is_invite_register", ocpMemberBindingDto.getMember().get("is_invite_register"));
        member.put("is_logoff", ocpMemberBindingDto.getMember().get("is_logoff"));
        member.put("experience", ocpMemberBindingDto.getMember().get("experience"));
        member.put("member_type", ocpMemberBindingDto.getMember().get("member_type"));
        member.put("register_channel", ocpMemberBindingDto.getMember().get("register_channel"));
        member.put("crm_member_id", ocpMemberBindingDto.getMember().get("crm_member_id"));
        member.put("register_address", ocpMemberBindingDto.getMember().get("register_address"));
        member.put("register_source", ocpMemberBindingDto.getMember().get("register_source"));
        member.put("member_name", ocpMemberBindingDto.getMember().get("member_name"));
        if (null != equity) {
            member.put("equity", equity);
        }

        member.put("platform_bind", platformBind);
        memberDto.setMember(member);

        log.info("封装的值:{}", JSON.toJSONString(memberDto));

        try {
            HashMap<String, String> headers = new HashMap<>();
            headers.put("CampaignId", "40000000");
            headers.put("Content-Type", "application/json");
            headers.put("Ocp-Apim-Subscription-Key", ConfigurationCenterUtil.MIDDLEGROUND_KEY);
            String s = HttpClientHelper.doPostJSON(ConfigurationCenterUtil.OCP_MEMBER_UPSERT, memberDto, headers);
            log.info(s);
        } catch (Exception e) {
            return responseType(ocpMemberBindingDto.getCrm_member().get("value").toString(), false, e.toString());
        }
        return responseType(ocpMemberBindingDto.getCrm_member().get("value").toString(), true, "同步成功");
    }

    @Override
    public Map<String, Object> queryCustomizedPropertiest(Map request) {
        String memberId = request.get("data_memberId").toString();
        HashMap<String, Object> map = new HashMap<>();
        //查询会员
        MemberDto memberDto = memberClient.query(memberId, null, null, ModelNameEnum.MEMBER_TYPE.getValue(), null, false);
        if (StringUtils.isBlank(memberDto.getMobile())) {
            map.put("data_memberId", "1");
            return map;
        }
        Map<String,Object> memberPoint = memberRepository.getMemberPoint(memberDto.getMobile());
        log.info("扩展字段:{}",JSON.toJSONString(memberPoint));
        //查询可以积分
        PointDto ko = memberClient.getPointInfo(memberId, null, ModelNameEnum.MEMBER_TYPE.getValue(), null, null);

        if (null == ko.getPoint()) {
            map.put("available_bottle", "0");
        } else {
            map.put("available_bottle", ko.getPoint().toString());
        }
        map.put("used_bottle", memberPoint.get("usedPoint").toString());
        map.put("total_bottle", memberPoint.get("totalPoint").toString());
        map.put("expired_bottle", memberPoint.get("experiencePoints").toString());
        map.put("data_memberId", memberDto.getMemberId());
        map.put("data_channelType", memberPoint.get("firstRegisterChannelType"));
        log.info("查询会员字段:{}", JSON.toJSONString(map));
        return map;
    }

    @Override
    public Map<String, String> deleteConsumerInfo(ConsumerInfoDto consumerInfoDto) {
        HashMap<String, String> result = new HashMap<>();
        try {
            if ("save".equals(consumerInfoDto.getState())) {
                //consumer 传过来的数组
                AttributeDto attribute = consumerInfoDto.getAttribute();
                List<Map<String, Object>> info = attribute.getInfo();
                log.info("info日志:{}", JSON.toJSONString(info));
                ArrayList<String> list = new ArrayList<>();
                String openId = "1";
                String phone = "1";

                for (Map<String, Object> infoMap : info) {
                    Object o = infoMap.get("attribute");
                    Map<String, Object> map = JSONObject.parseObject(JSON.toJSONString(o));
                    if ("wechat".equals(infoMap.get("platform")) && "wechat_open_id".equals(infoMap.get("type")) && "true".equals(JSON.toJSONString(map.get("is_koplus_openid")))) {
                        openId = "0";
                    }
                    if ("phone_number".equals(infoMap.get("type"))) {
                        phone = "0";
                    }
                    list.add(consumerInfoDto.getKoId() + infoMap.get("platform") + infoMap.get("type") + infoMap.get("value"));
                }
                //查询info对象不存的
                List<ConsumerPlatInfoDto> infoList = consumerPlatInfoRepository.getConsumerPlatInfo(consumerInfoDto.getKoId(), list);
                List<ConsumerPlatInfoDto> consumerInfoList = JSON.parseArray(JSON.toJSONString(infoList), ConsumerPlatInfoDto.class);

                log.info("consumerInfoList对象:{}", JSON.toJSONString(consumerInfoList));
                if (CollectionUtils.isNotEmpty(list)) {
                    //删除ConsumerPlatInfo
                    for (ConsumerPlatInfoDto consumerPlatInfoDto : consumerInfoList) {
                        consumerPlatInfoRepository.deleteById(ModelConstants.COSMOS_CONSUMERPLASTINFO, consumerPlatInfoDto.getId());
                        //consumerPlatInfoRepository.deleteConsumerPlatInfo(consumerPlatInfoDto.getId());
                    }
                }
                //清除consumer模型字段
                if ("1".equals(openId)) {
                    consumerPlatInfoRepository.updateOpenId(consumerInfoDto.getKoId());
                }
                if ("1".equals(phone)) {
                    consumerPlatInfoRepository.updatePhone(consumerInfoDto.getKoId());
                }
            }
        } catch (Exception e) {
            log.error("info删除失败:{}", e.getMessage());
        }

        try {
            if ("delete".equals(consumerInfoDto.getState())) {
                //删除ConsumerPlatInfo数据
                Map<String, Object> map = new HashMap<>();
                map.put("KoId", consumerInfoDto.getKoId());
                consumerPlatInfoRepository.deleteByFilter(ModelConstants.COSMOS_CONSUMERPLASTINFO, map);
                //清除consumer模型字段
                consumerPlatInfoRepository.updateOpenId(consumerInfoDto.getKoId());
                consumerPlatInfoRepository.updatePhone(consumerInfoDto.getKoId());
            }
        } catch (Exception e) {
            log.error("info为空删除失败:{}", e.getMessage());
        }
        result.put("koId", consumerInfoDto.getKoId());
        return result;
    }

    @Override
    public ResponseResult saveManualPoint(Map<String, String> pointRequestDto) {
        PointRequestDto requestDto = new PointRequestDto();
        //查询会员
        String memberId = memberRepository.queryMemberId(pointRequestDto.get("customerNo"));
        if (StringUtils.isBlank(memberId)) {
            return new ResponseResult(ResponseCodeEnum.MEMBER_FAILED);
        }
        //成本中心
        requestDto.setShopCode(pointRequestDto.get("costCenter"));
        requestDto.setMemberId(memberId);
        //变更的积分
        requestDto.setPoint(Double.valueOf(pointRequestDto.get("point")));
        Date date = new Date();
        try {
            date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(pointRequestDto.get("effectTime"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        requestDto.setEffectTime(date);
        requestDto.setExpiredTime(DateHelper.getLastDayOfMonth());
        requestDto.setDescription(pointRequestDto.get("desc"));
        requestDto.setChannelType(pointRequestDto.get("channelType"));
        requestDto.setMemberType(pointRequestDto.get("memberType"));
        requestDto.setKZZD1(pointRequestDto.get("changeSourceDetail"));
        requestDto.setKZZD2(pointRequestDto.get("changeSource"));
        requestDto.setChangeType("SEND");
        try {
            log.info("封装pointRequestDto:{}", JSON.toJSONString(requestDto));
            memberClient.updatePoint(requestDto, pointRequestDto.get("businessId"));
        } catch (Exception e) {
            return new ResponseResult("500", e.getMessage());
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getMsg());
    }

    @Override
    public ResponseResult changeMemberScenePoint(CepLbsMemberPointRecordDto cepMemberPointRecordDto) {

        MemberPointModifyRequest memberPointDto = new MemberPointModifyRequest();
        //查询会员
        String memberId = memberRepository.queryMemberId(cepMemberPointRecordDto.getCustomerNo());
        if (StringUtils.isBlank(memberId)) {
            return new ResponseResult(ResponseCodeEnum.MEMBER_FAILED);
        }
        //查询超额限制
        Double  beyondPoint =  memberRepository.queryBeyondPoint(cepMemberPointRecordDto.getChannelType(),cepMemberPointRecordDto.getChangeType());
        log.info("查询大额限制积分值:{},businessId:{}", beyondPoint,cepMemberPointRecordDto.getBusinessId());

        if (null != beyondPoint) {
            int comparison1 = Double.compare(beyondPoint, cepMemberPointRecordDto.getPoint());
            if (comparison1 < 0){
                //记录大额积分值
                Map<String, Object> map = new HashMap<>();
                map.put("memberId", memberId);
                map.put("customerNo", cepMemberPointRecordDto.getCustomerNo());
                map.put("point", cepMemberPointRecordDto.getPoint());
                map.put("changeType", cepMemberPointRecordDto.getChangeType());
                map.put("lastSync", ZonedDateTime.now());
                map.put("businessId", cepMemberPointRecordDto.getBusinessId());
                map.put("channelType", cepMemberPointRecordDto.getChannelType());
                DMLResponse resultResponse = DataApiUtil.upsertIsData(ModelConstants.MEMBER_POINT_DOBEYONDPOINTLOG, UUID.randomUUID().toString().replaceAll("-", ""), map);
                if (!resultResponse.getIsSuccess()) {
                    log.error("记录大额积分值创建失败traceId:{},msg:{}", cepMemberPointRecordDto.getBusinessId(), resultResponse.getOperation());
                }
                return new ResponseResult(ResponseCodeEnum.MEMBER_POINT_OVER_THRESHOLD);
            }
        }
        //查询该笔积分是否已发送
        /*if (!"扣减取消".equals(cepMemberPointRecordDto.getChangeSource())) {
            Double changePoints = memberRepository.queryMemberTraceId(cepMemberPointRecordDto.getBusinessId());
            if (0.0 != changePoints) {
                log.info("查询该笔积分是否已发送:{}",cepMemberPointRecordDto.getBusinessId());
                return new ResponseResult(ResponseCodeEnum.SUCCESS);
            }
        }*/
        //查询该笔积分是否已发送
        if (!"扣减取消".equals(cepMemberPointRecordDto.getChangeSource())) {
            String key = REDIS_BNSID + cepMemberPointRecordDto.getBusinessId();
            Object reValue = redisCache.get(key);
            if (null != reValue) {
                log.info("redis查询该笔积分是否已发送key:{}，reValue：{}",key,reValue);
                return new ResponseResult(ResponseCodeEnum.SUCCESS);
            }
        }
        log.info("changeMemberScenePoint...查询会员id: {}", memberId);
        memberPointDto.setShopId(cepMemberPointRecordDto.getCostCenter());
        if (!StringUtils.equalsAny(cepMemberPointRecordDto.getChangeType(), "DEDUCT", "SEND")) {
            return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, "积分变更类型不对： SEND: 立即发放，DEDUCT: 扣减");
        }
        String costCenter = cepCostCenterRepository.getCostCenter(cepMemberPointRecordDto.getCostCenter());
        //判断成本中心是否存在
        if (StringUtils.isBlank(costCenter)) {
            return new ResponseResult(ResponseCodeEnum.MEMBER_COStCENTER);
        }
        //封装实体
        memberPointDto.setKZZD1(cepMemberPointRecordDto.getChangeSourceDetail());
        memberPointDto.setKZZD2(cepMemberPointRecordDto.getChangeSource());
        memberPointDto.setKZZD3(cepMemberPointRecordDto.getCostTracing());
        // memberPointDto.setActionName(cepMemberPointRecordDto.);
        memberPointDto.setChangeMode("INTERFACE");
        memberPointDto.setChannelType(cepMemberPointRecordDto.getChannelType());
        memberPointDto.setDesc(cepMemberPointRecordDto.getDesc());
        memberPointDto.setMemberId(memberId);
        memberPointDto.setPointAccountId(Integer.valueOf(ConfigurationCenterUtil.ACCOUNT_POINT));
        //接收太古历史积分经验值
        if (null != cepMemberPointRecordDto.getExperience()) {
            Map<String, Object> map = new HashMap<>();
            map.put("differenceValue", cepMemberPointRecordDto.getDifferenceValue());
            map.put("experience", cepMemberPointRecordDto.getExperience());
            map.put("created", ZonedDateTime.now());
            HashMap<Object, Object> memberIds = new HashMap<>();
            memberIds.put("id", memberId);
            map.put("member", memberIds);
            DMLResponse resultResponse = DataApiUtil.upsertIsData(ModelConstants.SWIRE_MEMBER_EXPERIENEC, memberId, map);
            if (!resultResponse.getIsSuccess()) {
                log.error("记录经验值积分创建失败traceId:{},msg:{}", cepMemberPointRecordDto.getBusinessId(), resultResponse.getOperation());
            }
        }
        try {
            if ("DEDUCT".equals(cepMemberPointRecordDto.getChangeType())) {
                //查询会员积分
                memberPointDto.setPoint(cepMemberPointRecordDto.getPoint());
                memberPointDto.setRecordType(cepMemberPointRecordDto.getChangeType());
                memberPointDto.setTriggerId(cepMemberPointRecordDto.getBusinessId());
                memberPointDto.setIdempotentMode(1);
                log.info("changeMemberScenePoint...封装的实体: {}", memberPointDto);
                memberPointModifyClient.memberModifyPoint(memberPointDto);
                log.info("changeMemberScenePoint...请求成功变更会员积分(瓶子)memberId:{},businessId:{}", memberId, cepMemberPointRecordDto.getBusinessId());
                //pushPointRecordComsom(cepMemberPointRecordDto);
                return new ResponseResult(ResponseCodeEnum.SUCCESS);
            } else if ("SEND".equals(cepMemberPointRecordDto.getChangeType())) {
                String changeSource = cepMemberPointRecordDto.getChangeSource();
                if (!CHANGE_SOURCE_DEDUCT_CANCEL.equals(changeSource)) {
                    /*BaseResponse response = pointBehaviorRecordRepository.execute("SELECT lbsPointLimited FROM " + ModelConstants.CONFIG_LIST + " WHERE id = 'threshold'", Collections.EMPTY_MAP);
                    BigDecimal lbsPointLimited = (BigDecimal) ((Map<String, Object>) response.getData().get(0)).get("lbsPointLimited");
                    if (lbsPointLimited.doubleValue() < cepMemberPointRecordDto.getPoint()) {
                        return new ResponseResult(ResponseCodeEnum.MEMBER_POINT_OVER_THRESHOLD);
                    }*/
                    PointRequestDto requestDto = new PointRequestDto();
                    requestDto.setShopCode(costCenter);
                    requestDto.setMemberId(memberId);
                    requestDto.setPoint(cepMemberPointRecordDto.getPoint());

                    // 计算生效与失效时间
                    LocalDateTime effectTime = LocalDateTime.now();
                    LocalDateTime expiredTime = effectTime.plusYears(1).with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX).withNano(0);
                    if (StringUtils.isBlank(cepMemberPointRecordDto.getEffectTime())) {
                        requestDto.setEffectTime(DateHelper.toDate(effectTime));
                    } else {
                        requestDto.setEffectTime(DateHelper.transferString2Date(cepMemberPointRecordDto.getEffectTime()));
                    }
                    if (StringUtils.isBlank(cepMemberPointRecordDto.getExpiredTime())) {
                        requestDto.setExpiredTime(DateHelper.toDate(expiredTime));
                    } else {
                        requestDto.setExpiredTime(DateHelper.transferString2Date(cepMemberPointRecordDto.getExpiredTime()));
                    }

                    requestDto.setChangeType("SEND");
                    requestDto.setDescription(cepMemberPointRecordDto.getDesc());
                    requestDto.setChannelType(cepMemberPointRecordDto.getChannelType());
                    requestDto.setMemberType(cepMemberPointRecordDto.getMemberType());
                    requestDto.setKZZD1(cepMemberPointRecordDto.getChangeSourceDetail());
                    requestDto.setKZZD2(cepMemberPointRecordDto.getChangeSource());
                    requestDto.setKZZD3(cepMemberPointRecordDto.getCostTracing());
                    log.info("积分发放会员对象封装:{}", JSON.toJSONString(requestDto));
                    memberClient.updatePoint(requestDto, cepMemberPointRecordDto.getBusinessId());
                    redisCache.put(REDIS_BNSID+cepMemberPointRecordDto.getBusinessId(), requestDto.getPoint(), 1, TimeUnit.DAYS);
                    log.info("changeMemberScenePoint...请求成功变更会员积分(瓶子)memberId:{},businessId:{}", memberId, cepMemberPointRecordDto.getBusinessId());
                    //接收太古超额扣减记录
                    if (StringUtils.isNotBlank(cepMemberPointRecordDto.getExcessPoints())) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("memberId", requestDto.getMemberId());
                        map.put("excessPoints", cepMemberPointRecordDto.getExcessPoints());
                        map.put("created", ZonedDateTime.now());
                        //记录超额积分
                        DMLResponse resultResponse = DataApiUtil.upsertIsData(ModelConstants.MEMBER_EXCESSPOINTSLOG, cepMemberPointRecordDto.getBusinessId(), map);
                        if (!resultResponse.getIsSuccess()) {
                            log.error("记录超额积分创建失败traceId:{},msg:{}", cepMemberPointRecordDto.getBusinessId(), resultResponse.getOperation());
                        }
                    }
                }
                if (CHANGE_SOURCE_DEDUCT_CANCEL.equals(changeSource)) {
                    memberPointDto.setRecordType("DEDUCT");
                    memberPointDto.setTradeId(cepMemberPointRecordDto.getBusinessId());
                    memberPointDto.setTriggerId(IdWorker.get32UUID());
                    memberPointDto.setIdempotentMode(1);
                    log.info("changeMemberScenePoint...封装的实体: {}", memberPointDto);
                    CustomizeException map = memberPointModifyClient.memberReversePoint(memberPointDto);
                    log.info("changeMemberScenePoint...请求成功忠诚度返回值: {}", JSON.toJSONString(map));
                    if (!Objects.isNull(map)) {
                        return new ResponseResult(map.getError_code(), map.getMsg());
                    }
                }
            }
            //pushPointRecordComsom(cepMemberPointRecordDto);
        } catch (Exception e) {
            if ("changeMemberScenePoint..此X-Business-Token已执行".equals(e.getMessage())) {
                return new ResponseResult(ResponseCodeEnum.SUCCESS_IDEMPOTENT);
            }
            if (e.getMessage().contains("重复请求")) {
                Double changePoints = memberRepository.queryMemberTraceId(cepMemberPointRecordDto.getBusinessId());
                if (0.0 != changePoints) {
                    redisCache.put(REDIS_BNSID+cepMemberPointRecordDto.getBusinessId(), changePoints, 1, TimeUnit.DAYS);
                    log.info("查询该笔积分是否已发送:{},vo:{}",cepMemberPointRecordDto.getBusinessId(),changePoints);
                    return new ResponseResult(ResponseCodeEnum.SUCCESS_IDEMPOTENT);
                }
            }
            //log.error("changeMemberScenePoint...变更会员积分(瓶子)--memberId:{},businessId:{},e:{}", memberId, cepMemberPointRecordDto.getBusinessId(), e.getMessage());
            if ("changeMemberScenePoint...未找到正向操作".equals(e.getMessage())) {
                return new ResponseResult(ResponseCodeEnum.MEMBER_POINT_ERROR);
            }
            if ("changeMemberScenePoint...正向积分已经被使用".equals(e.getMessage())) {
                return new ResponseResult(ResponseCodeEnum.MEMBER_POINT_USED);
            }
            log.warn("changeMemberScenePoint...变更会员积分(瓶子)--memberId:{},businessId:{},e:{}", memberId, cepMemberPointRecordDto.getBusinessId(), e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED.getCode(), e.getMessage());
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }

    @Override
    public ResponseResult getCheckMobile(String mobile) {
        HashMap<String, String> map = new HashMap<>();
        map.put("phoneNumber", mobile);
        Map checkMobilen = epassportFeignClient.adminCheckMobilen(map);
        log.info("校验手机号:{}", JSON.toJSONString(checkMobilen));
        return null;
    }

    @Override
    public CepMemberPointItemDto getPointRecords(String customerNo, String startTime, String endTime, Integer page, Integer pageSize, String channelType) {
        //查询会员
        CepMemberPointItemDto itemDto = new CepMemberPointItemDto();
        String memberId = memberRepository.queryMemberId(customerNo);
        if (StringUtils.isBlank(memberId)) {
            itemDto.setMsgs("会员不存在");
            itemDto.setCode(ResponseCodeEnum.MEMBER_FAILED.getCode());
            return itemDto;
        }

        //查询总页数
        String count = pointBehaviorRecordRepository.getSwirePointCount(memberId, startTime, endTime);
        itemDto.setTotalCount(count);
        //查询会员积分明细
        List<PointBehaviorRecordDto> pointBehaviorRecordDtos = pointBehaviorRecordRepository.swirePointItemlist(memberId, startTime, endTime, page, pageSize);
        List<PointBehaviorRecordDto> pointlist = JSON.parseArray(JSON.toJSONString(pointBehaviorRecordDtos), PointBehaviorRecordDto.class);
        ArrayList<PointBehaviorRecordDto> behaviorRecordDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pointlist)) {
            for (PointBehaviorRecordDto recordDto : pointlist) {
                if (!"FREEZE".equals(recordDto.getChangeType()) && !"UNFREEZE".equals(recordDto.getChangeType()) && !"SPECIAL_FREEZE".equals(recordDto.getChangeType()) && !"SPECIAL_UNFREEZE".equals(recordDto.getChangeType()) && !"OPEN_UNFREEZE".equals(recordDto.getChangeType()) && !"OPEN_FREEZE".equals(recordDto.getChangeType())) {
                    PointBehaviorRecordDto po = new PointBehaviorRecordDto();
                    po.setChannelType(recordDto.getChannelType());
                    po.setChangeSource(recordDto.getChangeSource());
                    po.setDesc(recordDto.getChangeSource());
                    po.setChangeSourceDetail(recordDto.getChangeSourceDetail());
                    if ("loyalty".equals(recordDto.getChannelType())) {
                        po.setChannelType("CRM系统");
                    }
                    //MA积分
                    if ("YXZX".equals(recordDto.getChannelType())) {
                        po.setChangeSource("MA");
                        po.setChangeSourceDetail(recordDto.getActionId() + "_" + recordDto.getActionNodeId());
                    }
                    //手动变更积分
                    if ("MANUAL".equals(recordDto.getChangeMode())) {
                        String state = recordDto.getChangeType();
                        switch (state) {
                            case "SPECIAL_FREEZE":
                                po.setChangeSource("商家冻结");
                                break;
                            case "SPECIAL_UNFREEZE":
                                po.setChangeSource("商家解冻");
                                break;
                            case "EXPIRE":
                                po.setChangeSource("过期扣减");
                                break;
                            default:
                                po.setChangeSource("商家调整");
                        }
                    }
                    if ("EXPIRE".equals(recordDto.getChangeType())) {
                        po.setChangeSource("过期扣减");
                    }
                    po.setId(recordDto.getId());
                    //成本中心转换
                    String convertCostCenter = pointBehaviorRecordRepository.getConvertCostCenter(recordDto.getShopId());
                    po.setCostCenter(convertCostCenter);
                    if (StringUtils.isNotBlank(recordDto.getDesc())){
                        po.setDesc(recordDto.getDesc());
                    }
                    po.setPoint(recordDto.getPoint());
                    po.setInvalidPoint(recordDto.getInvalidPoint());
                    po.setFrozenPoint(recordDto.getFrozenPoint());
                    po.setChangeType(recordDto.getChangeType());
                    if(StringUtils.isBlank(recordDto.getChangeTime())){
                        po.setChangeTime(getStartTimeDate(recordDto.getModified()));
                    }else {
                        po.setChangeTime(getStartTimeDate(recordDto.getChangeTime()));
                    }
                    if ("CBL_MP".equals(recordDto.getChannelType())) {
                        po.setChangeSource("其他");
                        po.setDesc("其他");
                    }
                    behaviorRecordDtos.add(po);
                }
            }
        }
        itemDto.setCurrentPage(page);
        itemDto.setPageSize(pageSize);
        itemDto.setData(behaviorRecordDtos);
        itemDto.setCode(ResponseCodeEnum.SUCCESS.getCode());
        itemDto.setMsgs(ResponseCodeEnum.SUCCESS.getMsg());
        return itemDto;
    }


    private void pushPointRecordComsom(CepLbsMemberPointRecordDto cepMemberPointRecordDto) {

        //log.info("太古渠道参与活动积分记录推送到中台:{}",JSON.toJSONString(cepMemberPointRecordDto));
        getExecutorService().execute(new Runnable() {
            @Override
            public void run() {
                sendPointRecordComsom(cepMemberPointRecordDto);
            }
        });
    }

    /**
     * 单例线程池
     *
     * @return
     */
    private static ExecutorService getExecutorService() {
        if (null == executorService) {
            synchronized (AbstractInteractionSceneResolver.class) {
                if (null == executorService) {
                    executorService = DataApiUtil.newBlockingThreadPool(10, ConfigurationCenterUtil.THREAD_CORE_SUM, ConfigurationCenterUtil.THREAD_QUEUE_SUM);
                }
            }
        }
        return executorService;
    }

    protected void sendPointRecordComsom(CepLbsMemberPointRecordDto cepMemberPointRecordDto) {

        //推送中台参与活动积分记录
        int index = cepMemberPointRecordDto.getCustomerNo().indexOf("_");
        String openId = cepMemberPointRecordDto.getCustomerNo().substring(index + 1);
        //String openId = cepMemberDeductionDto.getCustomerNo().split("_")[1];
        OcpInteractiveRecordDto interactiveRecordDto = new OcpInteractiveRecordDto();
        interactiveRecordDto.setDate(DateHelper.getDateToDay());
        interactiveRecordDto.setConsumer_id(openId);
        interactiveRecordDto.setCampaign_id("40000000");
        interactiveRecordDto.setOrganization_code("Swire");
        interactiveRecordDto.setOrganization_name("SCCL");
        interactiveRecordDto.setActivity_source("SWIRE_MP");
        if ("参与互动".equals(cepMemberPointRecordDto.getChangeSource())) {
            interactiveRecordDto.setActivity_type("INTERACTIVE");
        } else {
            interactiveRecordDto.setActivity_type("CAMPAIGN");
        }
        interactiveRecordDto.setCep_campaign_id(cepMemberPointRecordDto.getChangeSourceDetail().split("_")[0]);
        Map activity_info = new HashMap<String, Object>();
        activity_info.put("activity_id", "CRM#" + cepMemberPointRecordDto.getChannelType() + "#" + cepMemberPointRecordDto.getChangeSource());
        activity_info.put("activity_name", "CRM#" + cepMemberPointRecordDto.getChannelType() + "#" + cepMemberPointRecordDto.getChangeSource());
        activity_info.put("timestamp", DateHelper.getDateMilliseconds());
        Map activity_attribute = new HashMap<String, String>();
        activity_attribute.put("app_id", cepMemberPointRecordDto.getCustomerNo().split("_")[0]);
        activity_attribute.put("record_id", cepMemberPointRecordDto.getChangeSourceDetail());
        activity_attribute.put("points_type", "member_bottle");
        activity_attribute.put("points_status", "immediate effect");
        activity_attribute.put("points_channel", "SWIRE_MP");
        activity_attribute.put("platform", "wechat");
        HashMap<Object, Object> maps = new HashMap<>();
        if (StringUtils.isNotBlank(cepMemberPointRecordDto.getChangeSourceDetail())) {
            maps.put("change_source_detail", cepMemberPointRecordDto.getChangeSourceDetail());
            activity_attribute.put("remark", JSON.toJSONString(maps));
        }
        if ("DEDUCT".equals(cepMemberPointRecordDto.getChangeType())) {
            activity_attribute.put("consume_point", cepMemberPointRecordDto.getPoint());
        } else {
            activity_attribute.put("get_point", cepMemberPointRecordDto.getPoint());
        }
        activity_attribute.put("interactive_name", cepMemberPointRecordDto.getChangeSource());
        activity_attribute.put("interactive_id", cepMemberPointRecordDto.getChangeSource());
        activity_attribute.put("interactive_type", cepMemberPointRecordDto.getChangeSource());
        activity_info.put("activity_attribute", activity_attribute);
        interactiveRecordDto.setActivity_info(activity_info);
        log.info("太古渠道参与活动积分记录推送到中台:{}", JSON.toJSONString(interactiveRecordDto));
        try {
            HashMap<String, String> header = new HashMap<>();
            header.put("CampaignId", "40000000");
            header.put("Content-Type", "application/json");
            header.put("Ocp-Apim-Subscription-Key", ConfigurationCenterUtil.MIDDLEGROUND_KEY);
            String body = HttpClientHelper.doPostJSON(ConfigurationCenterUtil.OCP_INTERACTIVE_RECORD, interactiveRecordDto, header);
            log.info(body);
        } catch (Exception e) {
            HashMap<String, Object> map = new HashMap<>();
            map.put("code", 500);
            map.put("message", e.getMessage());
            map.put("retryCount", 1);
            map.put("newRequest", JsonUtils.toJson(interactiveRecordDto));
            map.put("lastSync", ZonedDateTime.now());
            map.put("state", false);
            DataApiUtil.upsertIsData(ModelConstants.POINT_CDP_INTERFACLOG, UUID.randomUUID().toString().replaceAll("-", ""), map);
            log.error("太古渠道参与活动积分记录推送到中台接口异常:{},interactiveRecordDto:{}", e.getMessage(), JSON.toJSONString(interactiveRecordDto));
        }

    }


    @Override
    public ResponsesVo consumerCancel(ConsumerCancelDto consumerCancelDto, String memberId, String operator) {

        try {
            consumerCancelRunnable runnable = new consumerCancelRunnable(consumerCancelDto, memberId, operator);
            new Thread(runnable, "consumerCancel").start();
        } catch (Exception e) {
            log.error("线程执行失败:{}", JSON.toJSONString(consumerCancelDto));
        }
        return new ResponsesVo(200, ResponseCodeEnum.SUCCESS.getMsg());
    }


    public class consumerCancelRunnable implements Runnable {
        ConsumerCancelDto consumerCancelDto;
        String memberId;
        String operator;

        private consumerCancelRunnable(ConsumerCancelDto consumerCancelDto, String memberId, String operator) {
            this.consumerCancelDto = consumerCancelDto;
            this.memberId = memberId;
            this.memberId = operator;
        }

        @Override
        public void run() {
            try {
                consumerCancelTable(consumerCancelDto, memberId, operator);
            } catch (Exception e) {
                log.error("自定义子线程执行失败:{}", JSON.toJSONString(consumerCancelDto));
            }
        }
    }

    /**
     * 异步调用用户注销
     *
     * @param consumerCancelDto
     */
    public void consumerCancelTable(ConsumerCancelDto consumerCancelDto, String memberId, String operator) {
        for (String koid : consumerCancelDto.getKoid()) {
            log.info("异步调用用户注销koid:{}", koid);
            Map consumerMap = null;
            try {
                consumerMap = memberRepository.queryOpneIdByKoid(koid);
            } catch (Exception e) {
                log.info("异步调用用户注销:{}", e.getMessage());
                if (StringUtils.isNotBlank(memberId)) {
                    consumerCancelDto.setOperator(operator);
                }
                upsertConsumerCancel(consumerCancelDto, koid, "N", "CRM系统: 用户openid不存在");
                continue;
            }
            //同步更新cep-用户注销 cep已下线
/*            String s;
            log.info("用户注销openid:{}", JSON.toJSONString(consumerMap));
            try {
                s = cepMemberCouponServiceImpl.deleteConsumer(ConfigurationCenterUtil.MEMBER_CEP_CANCELLATION, consumerMap.get("openId").toString());
            } catch (Exception e) {
                log.info("同步cep接口异常:{},koid:{}", e.getMessage(), koid);
                if (StringUtils.isNotBlank(memberId)) {
                    consumerCancelDto.setOperator(operator);
                }
                upsertConsumerCancel(consumerCancelDto, koid, "N", "CEP系统：同步cep接口异常");
                continue;
            }
            JSONObject error = null;
            try {
                if (StringUtils.isNotBlank(s)) {
                    JSONObject jsonObject = JSONObject.parseObject(s);
                    error = JSONObject.parseObject(jsonObject.get("error").toString());
                }
            } catch (Exception e) {
                if (StringUtils.isNotBlank(memberId)) {
                    consumerCancelDto.setOperator(operator);
                }
                upsertConsumerCancel(consumerCancelDto, koid, "N", "CEP系统：CEP返回错误结果封装异常");
                continue;
            }
            if (error != null && memberRepository.getConsumerCancel(koid) && "USER_NOT_FOUND".equals(error.get("code").toString())) {
                if (StringUtils.isNotBlank(memberId)) {
                    consumerCancelDto.setOperator(operator);
                }
                upsertConsumerCancel(consumerCancelDto, koid, "N", "CEP系统： 用户已注销");
                continue;
            } else if (error != null) {
                if (StringUtils.isNotBlank(memberId)) {
                    consumerCancelDto.setOperator(operator);
                }
                upsertConsumerCancel(consumerCancelDto, koid, "N", "CEP_ERROR_CODE:" + error.get("code").toString());
                continue;
            }
            log.info("用户注销ceperror:{}", JSON.toJSONString(error));*/
            //解绑存在会员用户
            String customerNo = ConfigurationCenterUtil.MEMBER_CANCEL_WEIXIN_APPID + "_" + consumerMap.get("openId").toString();
            if (memberRepository.queryCustomerNoOrMobile(customerNo, null, "KO_MP",null)) {
                UnbindMemberDto unbindMemberDto = memberRepository.queryCustomerByCustomerNo(customerNo);
                unbindMemberDto.setUnbindTime(new Date());
                try {
                    //查询会员id
                    String id = memberRepository.queryMemberId(customerNo);
                    log.info("unbindMemberDto:{}", JsonUtils.toJson(unbindMemberDto));
                    memberClient.unbindMember(unbindMemberDto);
                    Map<String, Object> parameters = new HashMap<>();
                    parameters.put("rsvField4", null);
                    parameters.put("rsvField5", null);
                    if(StringUtils.isNotBlank(id)){
                        memberRepository.updateMemberDto(id, parameters);
                    }
                } catch (Exception e) {
                    if (StringUtils.isNotBlank(memberId)) {
                        consumerCancelDto.setOperator(operator);
                    }
                    upsertConsumerCancel(consumerCancelDto, koid, "N", "同步openapi接口异常");
                    continue;
                }
            }
            upsertConsumerCancel(consumerCancelDto, koid, "Y", "成功");
            //同步更新至数据中台 走kafka同步
           /* OcpMemberDeleteDto deleteDto = new OcpMemberDeleteDto();
            deleteDto.setLogoff_time(DateHelper.getDateMilliseconds());
            ArrayList<Map> platform = new ArrayList<>();
            HashMap<String, String> ocpUser = new HashMap<>();
            ocpUser.put("value", consumerMap.get("openId").toString());
            ocpUser.put("platform", "wechat");
            platform.add(ocpUser);
            deleteDto.setPlatform_info(platform);
            log.info("同步中台数据:{}", JSON.toJSONString(deleteDto));
            try {
                HashMap<String, String> header = new HashMap<>();
                header.put("CampaignId", "40000000");
                header.put("Content-Type", "application/json");
                header.put("Ocp-Apim-Subscription-Key", ConfigurationCenterUtil.MIDDLEGROUND_KEY);
                String body = HttpClientHelper.doPostJSON(ConfigurationCenterUtil.OCP_MEMBER_DELETEUAT, deleteDto, header);
                log.info(body);
            } catch (Exception e) {
                log.info("同步中台接口异常:{},koid:{}", e.getMessage(), koid);
            }*/
        }
    }

    public void memberCancel(String id, MemberCancelDto memberCancelDto, String memberId, String mobile, String state, String msg) {
        //插入会员注销记录表
        String memberOpneId = memberRepository.queryMemberOpneId(memberId);
        memberCancelDto.setStatus(state);
        memberCancelDto.setFailReason(msg);
        String uId = DigestUtils.md5Hex(id + memberId).toLowerCase();
        if ("Y".equals(state)) {
            List<MemberCancelDto> memberCancel = memberCancelRepository.getMemberCancelState(memberId);
            if (CollectionUtils.isNotEmpty(memberCancel)) {
                memberCancelDto.setStatus("N");
                memberCancelDto.setFailReason("会员已注销");
            }
        }
        memberCancelDto.setOperator(memberCancelDto.getOperator());
        ArrayList<String> list = new ArrayList<>();
        list.add(memberId);
        memberCancelDto.setMemberId(list);
        HashMap<String, String> map = new HashMap<>();
        map.put("id", memberId);
        memberCancelDto.setMember(map);
        memberCancelDto.setMemberIdStr(memberId);
        memberCancelDto.setPhone(mobile);
        memberCancelDto.setOpenId(memberOpneId);
        memberCancelDto.setLastSync(DateHelper.getNowZone());
        memberCancelDto.setCancelTime(DateHelper.getNowZone());
        memberCancelDto.setCreated(DateHelper.getNowZone());
        memberCancelRepository.upsert(ModelConstants.MEMBER_CANCEL, uId, JSONObject.parseObject(JSON.toJSONString(memberCancelDto)));
    }

    public void upsertConsumerCancel(ConsumerCancelDto consumerCancelDto, String koid, String state, String msg) {
        HashMap<String, Object> map = new HashMap<>();
        ArrayList<String> consumerCodeList = new ArrayList<>();
        consumerCodeList.add(koid);
        HashMap<String, Object> consumerDataMap = new HashMap<>();
        consumerDataMap.put("id", koid);
        map.put("consumer", consumerDataMap);
        map.put("koId", koid);
        map.put("cancelTime", StringUtils.isNotBlank(consumerCancelDto.getCancelTime()) ? consumerCancelDto.getCancelTime() : DateHelper.getNowZone());
        map.put("reason", consumerCancelDto.getReason());
        map.put("operator", consumerCancelDto.getOperator());
        map.put("status", state);
        map.put("failReason", msg);
        map.put("created", DateHelper.getNowZone());
        map.put("lastSync", DateHelper.getNowZone());
        memberCancelRepository.upsert(ModelConstants.CONSUMER_CANCEL, UUID.randomUUID().toString().replaceAll("-", ""), JSONObject.parseObject(JSON.toJSONString(map)));
    }

    /**
     * 返回数据封装
     */
    private Map<String, Object> responseType(String memberId, Boolean state, String msg) {
        Map<String, Object> res = new HashMap<>();
        res.put("memberId", memberId);
        res.put("state", state);
        res.put("msg", msg);
        return res;
    }

    public static String getStartTimeDate(String time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeStrart = time.substring(0, 19).replace("T", " ");
        String timeDate = "";
        try {
            Date dt = sdf.parse(timeStrart);
            Calendar rightNow = Calendar.getInstance();
            rightNow.setTime(dt);
            rightNow.add(Calendar.HOUR, 8);
            Date nowTime = rightNow.getTime();
            timeDate = sdf.format(nowTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return timeDate;
    }

    public static String copStartTimeDate(String time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeDate = time.substring(1, 20).replace("T", " ");

        return timeDate;
    }
}
