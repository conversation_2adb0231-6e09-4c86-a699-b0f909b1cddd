package com.shuyun.kylin.customized.cbl.resource;

import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.cbl.dto.CblMemberGradeDto;
import com.shuyun.kylin.customized.cbl.dto.CblMemberUpdateDto;
import com.shuyun.kylin.customized.cbl.service.CblDataSyncService;
import com.shuyun.kylin.customized.swire.dto.SwireMemberRecordsDto;
import com.shuyun.kylin.customized.swire.service.ISwireDataSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@Validated
@RestController
@RequestMapping("/cbl/data/sync")
public class CblDataSyncResource {

    @Autowired
    private CblDataSyncService cblDataSyncService;

    @PostMapping("/member")
    public Map<String, String> dataSyncMember(@RequestBody Map<String, String> params) {
        log.info("同步悦喜荟会员: {}", params);
        return cblDataSyncService.dataSyncMember(params);
    }

    @PostMapping("/grade")
    public Map<String, String> dataSyncGrade(@RequestBody Map<String, String> params) {
        log.info("同步悦喜荟会员等级: {}", params);
        return cblDataSyncService.dataSyncGrade(params);
    }

}
