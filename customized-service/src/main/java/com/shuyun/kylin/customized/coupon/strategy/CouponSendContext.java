package com.shuyun.kylin.customized.coupon.strategy;

import com.shuyun.kylin.customized.base.context.SpringApp;
import com.shuyun.kylin.customized.coupon.dto.CustomerCouponDto;
import com.shuyun.kylin.customized.coupon.dto.CustomerDto;
import com.shuyun.kylin.customized.coupon.enums.CouponSendStrategyEnum;
import com.shuyun.pip.component.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;


import java.util.List;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @Description
 * @date 2021/3/19
 */
@Slf4j
public class CouponSendContext {

    private CouponSendContext(){

    }

    public static CouponSendContext newInstance(){
        return new CouponSendContext();
    }

    public List<CustomerDto> sendCoupon(CustomerCouponDto customerCouponDto, CouponSendStrategyEnum couponSendStrategyEnum){
        CouponSendStrategy sendStrategy = SpringApp.getBean(couponSendStrategyEnum.getStrategyName());
        return this.process(customerCouponDto, sendStrategy::sendCoupon);
    }

    private List<CustomerDto> process(CustomerCouponDto customerCouponDto, Function<CustomerCouponDto,List<CustomerDto>> function){
        try {
            log.info("执行发送，入参：{}", JsonUtils.toJson(customerCouponDto));
            return function.apply(customerCouponDto);
        } catch (Exception e) {
            log.error("发送优惠券出现异常",e);
            return null;
        }
    }
}
