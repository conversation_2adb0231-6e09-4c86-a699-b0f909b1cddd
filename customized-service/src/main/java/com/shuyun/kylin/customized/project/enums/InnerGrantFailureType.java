package com.shuyun.kylin.customized.project.enums;

import lombok.Getter;

/**
 * 内部发券失败类型：1-扣积分失败；2-发券失败
 * @author: Jingwei
 * @date: 2024-12-20
 */
@Getter
public enum InnerGrantFailureType {

    POINT_FAILURE(1, "扣积分失败"),

    COUPON_GRANT_FAILURE(2, "发券失败"),

    IQIYI_FAILURE(3, "爱奇艺核销失败"),

    ;

    private Integer code;

    private String desc;

    InnerGrantFailureType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
