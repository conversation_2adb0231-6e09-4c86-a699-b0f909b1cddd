package com.shuyun.kylin.customized.behavior.resource;

import com.shuyun.kylin.customized.base.util.RedisKeyUtil;
import com.shuyun.kylin.customized.behavior.resource.po.CheckPO;
import com.shuyun.kylin.customized.behavior.resource.vo.CheckResponse;
import com.shuyun.kylin.customized.behavior.resource.vo.CommonApiResponse;
import com.shuyun.kylin.customized.behavior.resource.vo.InteractiveResponseVO;
import com.shuyun.kylin.customized.behavior.service.InteractionBehaviorServiceImpl;
import com.shuyun.kylin.customized.behavior.util.DateRange;
import com.shuyun.kylin.customized.behavior.util.DateUtils;
import com.shuyun.kylin.starter.redis.ICache;
import com.shuyun.pip.component.json.JsonUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.concurrent.locks.Lock;

import static com.shuyun.kylin.customized.behavior.resource.vo.InteractiveResponseVO.InteractiveDateRangeCheckCode.*;

/**
 * behavior目录下进行互动行为开发
 *
 * @since 2021/12/11
 */
@Slf4j
@Tag(name = "互动行为接口")
@RestController
@RequestMapping("/interaction")
public class InteractionBehaviorResource {

    @Autowired
    InteractionBehaviorServiceImpl interactionBehaviorService;
    @Autowired
    @Qualifier("redisCache")
    private ICache redisCache;

    @GetMapping("/behavior/valid")
    @Operation(summary = "检查时间范围有效性-3587")
    @ApiResponses(value = {
            @ApiResponse(
                    description = "检查时间范围有效性返回结果",
                    content = {
                            @Content(
                                    mediaType = MediaType.APPLICATION_JSON_VALUE,
                                    schema = @Schema(implementation = InteractiveResponseVO.class)
                            )
                    })
    })
    public InteractiveResponseVO isValidDateRange(
            @Parameter(description = "活动ID（新增时不传）") @RequestParam(required = false) String id,
            @Parameter(description = "scene 场景值", required = true) @RequestParam String scene,
            @Parameter(description = "channelType 互动渠道类型", required = true) @RequestParam String channelType,
            @Parameter(description = "start 开始时间", required = true) @RequestParam String start,
            @Parameter(description = "end 结束时间", required = true) @RequestParam String end
    ) {
        Date startDate = DateUtils.parse(start, DateUtils.YYYY_MM_DD_HH_MM_SS);
        Date endDate = DateUtils.parse(end, DateUtils.YYYY_MM_DD_HH_MM_SS);

        log.info("检查时间范围有效性，请求入参：Id：{}，scene：{}，时间范围：{}->{}（原始参数）", id, scene, start, end);
        if (start == null || end == null) {
            log.info("无效范围，原因：{}", DATE_IS_NULL.getMessage());
            return InteractiveResponseVO.buildOf(DATE_IS_NULL);
        }

        //如果结束日期早于或等于开始日期，也报错,如果结束日期小于当前时间，也报错
        if (!DateRange.isValid(startDate, endDate)) {
            log.info("无效范围，原因：{}", DATE_RANGE_INVALID.getMessage());
            return InteractiveResponseVO.buildOf(DATE_RANGE_INVALID);
        }

        if(endDate.before(new Date())){
            log.info("无效范围，原因：{}", DATE_RANGE_INVALID_ENDDATE_BEFORE.getMessage());
            return InteractiveResponseVO.buildOf(DATE_RANGE_INVALID_ENDDATE_BEFORE);
        }

        InteractiveResponseVO validDateRange = interactionBehaviorService.isValidDateRange(id, scene, channelType, startDate, endDate);
        log.info("检查时间范围有效性，请求入参：Id：{}，scene：{}，时间范围：{}->{}（原始参数），结果：{}", id, scene, start, end, JsonUtils.toJson(validDateRange));
        return validDateRange;
    }

    @PostMapping("/check")
    @Operation(summary = "互动规则检查")
    @ApiResponses(value = {
            @ApiResponse(
                    description = "互动规则检查",
                    content = {
                            @Content(
                                    mediaType = MediaType.APPLICATION_JSON_VALUE,
                                    schema = @Schema(implementation = InteractiveResponseVO.class)
                            )
                    })
    })
    public CommonApiResponse<CheckResponse> check(@RequestBody CheckPO checkPO) {
        log.info("互动规则检查，请求入参：{}", JsonUtils.toJson(checkPO));
        Assert.isTrue(checkPO.getMembershipId() != null, "会员ID不可为空");
        Assert.isTrue(StringUtils.isNoneBlank(checkPO.getChannelType()), "互动渠道类型不可为空");

        String lockKey = RedisKeyUtil.joinString("InteractionBehaviorResource.check::", checkPO.getMembershipId(), checkPO.getScene(), checkPO.getChannelType());
        Lock lock = redisCache.getLock(lockKey);

        //只尝试一次，失败就算了
        if (lock.tryLock()) {
            try {
                return interactionBehaviorService.check(checkPO);
            } finally {
                lock.unlock();
            }
        } else {
            log.info("互动抢锁失败：{}", checkPO);
            return CommonApiResponse.buildOf(NO_VALID_ACTIVITY);
        }
    }

    @GetMapping("/repair")
    @Operation(summary = "主数据与成本模型关联修复")
    @ApiResponses(value = {
            @ApiResponse(
                    description = "主数据与成本模型关联修复",
                    content = {
                            @Content(
                                    mediaType = MediaType.APPLICATION_JSON_VALUE,
                                    schema = @Schema(implementation = InteractiveResponseVO.class)
                            )
                    })
    })
    public InteractiveResponseVO repairJoinRelation() {
        return interactionBehaviorService.repairJoinRelation();
    }
}
