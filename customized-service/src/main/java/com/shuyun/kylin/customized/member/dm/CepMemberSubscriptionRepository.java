package com.shuyun.kylin.customized.member.dm;


import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.member.dto.CampaignPointsRuleDto;
import com.shuyun.kylin.customized.member.dto.CepMemberSubscriptionDto;
import com.shuyun.kylin.customized.member.dto.CepPointRecordDto;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class CepMemberSubscriptionRepository extends BaseDsRepository<CepMemberSubscriptionDto> {

    public CepMemberSubscriptionDto selectSubscription(String subscribeId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("subscribeId", subscribeId);
        return queryByFilter(ModelConstants.MEMBER_SUBSCRIPTIONS, null, queryMap);
    }
}
