package com.shuyun.kylin.customized.project.strategy;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.common.Constants;
import com.shuyun.kylin.customized.base.feign.dao.MemberBingDto;
import com.shuyun.kylin.customized.base.feign.dao.OfferProjectDetailClientResponse;
import com.shuyun.kylin.customized.base.util.DateHelper;
import com.shuyun.kylin.customized.project.dto.SendCouponResultDto;
import com.shuyun.kylin.customized.project.request.GrantCouponRequest;
import com.shuyun.kylin.customized.rpc.dto.IQiYiRequestDto;
import com.shuyun.kylin.customized.rpc.entity.IQiYiConfigEntity;
import com.shuyun.motor.common.cons.PropsUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 爱奇艺券发放
 */
@Slf4j
public class IQiYiSendCouponStrategy extends SendCouponStrategy {

    static final IQiYiConfigEntity iQiYiConfig =JSON.parseObject(PropsUtil.getSysOrEnv("iqiyi.api.config"), IQiYiConfigEntity.class);

    /**
     * 爱奇艺三方调用放在核销流程中
     *
     * @param templateId 模板id，暂时没用，主要在接口外部根据此值判断使用哪种券测率
     * @param projectDetail 券项目详情
     * @param grantCouponRequest 券发放原始请求入参
     * @param memberBindInfo
     * @return
     */
    @Override
    public SendCouponResultDto sendCoupon(String templateId, OfferProjectDetailClientResponse projectDetail, GrantCouponRequest grantCouponRequest, MemberBingDto memberBindInfo) {
        log.info("爱奇艺券发放开始");
        return new SendCouponResultDto(Boolean.TRUE, null, null, null);
    }

    /**
     * 组装请求参数
     * @param externalProjectId 三方活动id/商品id
     * @param sporderId 我方传给爱奇艺的sporderId，规则：券项目id_crm自己的券码
     * @return
     */
    public static IQiYiRequestDto assembleRequestParams(String externalProjectId, String mobile, String sporderId) {
        IQiYiRequestDto iQiYiRequestDto = new IQiYiRequestDto();
        iQiYiRequestDto.setUserId(iQiYiConfig.getUserId());
        iQiYiRequestDto.setUserPws(MD5.create().digestHex(iQiYiConfig.getUserPwd()).toLowerCase());
        iQiYiRequestDto.setCardId(externalProjectId);
        iQiYiRequestDto.setCardNum(Constants.ONE_STR);
        iQiYiRequestDto.setSporderId(sporderId);
        iQiYiRequestDto.setSporderTime(DateUtil.format(DateUtil.date(), DateHelper.DATE_TIME_FORMAT_COMPACT));
        iQiYiRequestDto.setGameUserId(mobile);
        iQiYiRequestDto.setGameUserpsw(CharSequenceUtil.EMPTY);
        iQiYiRequestDto.setGameArea(CharSequenceUtil.EMPTY);
        iQiYiRequestDto.setGameSrv(CharSequenceUtil.EMPTY);
        iQiYiRequestDto.setMd5Str(getSign(iQiYiRequestDto));
        iQiYiRequestDto.setPhoneno(CharSequenceUtil.EMPTY);
        iQiYiRequestDto.setRetUrl(iQiYiConfig.getRetUrl());
        iQiYiRequestDto.setVersion(iQiYiConfig.getVersion());
        iQiYiRequestDto.setUserIp(CharSequenceUtil.EMPTY);
        return iQiYiRequestDto;
    }

    /**
     * 签名规则：userid+userpws+cardid+cardnum+sporder_id+sporder_time+ game_userid+ game_area+ game_srv
     * 以上字段只取其值，再加上密钥
     * @param iQiYiRequestDto
     * @return
     */
    public static String getSign(IQiYiRequestDto iQiYiRequestDto) {
        StringBuilder sb = new StringBuilder();
        sb.append(iQiYiRequestDto.getUserId())
                .append(iQiYiRequestDto.getUserPws())
                .append(iQiYiRequestDto.getCardId())
                .append(iQiYiRequestDto.getCardNum())
                .append(iQiYiRequestDto.getSporderId())
                .append(iQiYiRequestDto.getSporderTime())
                .append(iQiYiRequestDto.getGameUserId())
                .append(iQiYiRequestDto.getGameArea())
                .append(iQiYiRequestDto.getGameSrv())
                .append(iQiYiConfig.getSignKey());
        log.info("签名原始参数，sb={}", sb);
        String sign = MD5.create().digestHex(sb.toString()).toUpperCase();
        log.info("签名结果，sign={}", sign);
        return sign;
    }
}
