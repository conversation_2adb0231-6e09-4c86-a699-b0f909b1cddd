package com.shuyun.kylin.customized.base.util;

import org.apache.commons.lang3.StringUtils;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.Base64;

public class Base64Util {

    public static String decode(String info) {
        if (StringUtils.isEmpty(info)) {
            return info;
        }
        try {
            info = info.replace("zero_x","0x");
            byte[] decode = Base64.getDecoder().decode(info);
            return URLDecoder.decode(new String(decode), "UTF-8");
        } catch (Exception e) {
            return info;
        }
    }

    public static String encode(String info) {
        if (StringUtils.isEmpty(info)) {
            return info;
        }
        try {
            byte[] decode = Base64.getEncoder().encode(URLEncoder.encode(info,"UTF-8").getBytes());
            return new String(decode).replace("0x","zero_x");
        } catch (Exception e) {
            return info;
        }
    }
}