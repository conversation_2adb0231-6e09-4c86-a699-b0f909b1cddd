package com.shuyun.kylin.customized.base.stream.kafka.consumer.coupon;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.stream.kafka.messaging.KafkaSink;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.member.service.TreadProcess.ThreadProcessService;
import com.shuyun.kylin.customized.swire.dto.SwireMemberDto;
import com.shuyun.kylin.customized.swire.dto.SwireMemberRecordsDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class CustomizedSwireMemberConsumer {

    @Autowired
    private ThreadProcessService threadProcessService;

    @StreamListener(KafkaSink.KAFKA_SEND_SWIRE_MEMBER_INPUT)
    public void syncSendMemberInput(SwireMemberRecordsDto swireMemberRecordsDto) {
        try {
            //SwireMemberDto swireMemberDto = JSON.parseObject(swireMemberDto, SwireMemberDto.class);
            log.info("接收到同步会员信息注册至太古信息:{}", JSON.toJSONString(swireMemberRecordsDto));
            String url = ConfigurationCenterUtil.HTTP_SWIRE_MEMBER_REGISTER;
            threadProcessService.sendSwireMember(swireMemberRecordsDto,url,"1");
        } catch (Exception e) {
            log.info("接收KAFKA到同步会员信息注册至太古信息失败:{}", JSON.toJSONString(swireMemberRecordsDto),e.getMessage());
        }

    }

    @StreamListener(KafkaSink.KAFKA_UPDATE_SWIRE_MEMBER_INPUT)
    public void syncUpdateMemberInput(SwireMemberRecordsDto swireMemberRecordsDto) {
        try {
            //SwireMemberDto swireMemberDto = JSON.parseObject(swireMemberDto, SwireMemberDto.class);
            log.info("接收到同步会员信息更新至太古信息:{}", JSON.toJSONString(swireMemberRecordsDto));
            String url = ConfigurationCenterUtil.HTTP_SWIRE_MEMBER_UPDATALNFO;
            threadProcessService.updateSwireMember(swireMemberRecordsDto,url,"0");
        } catch (Exception e) {
            log.info("接收KAFKA到同步会员信息注册至太古信息失败:{},msg:{}", JSON.toJSONString(swireMemberRecordsDto),e.getMessage());
        }
    }
}
