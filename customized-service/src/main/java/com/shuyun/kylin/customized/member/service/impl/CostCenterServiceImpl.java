package com.shuyun.kylin.customized.member.service.impl;


import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.common.ResponsesVo;
import com.shuyun.kylin.customized.member.dm.CostCenterSaveRepository;
import com.shuyun.kylin.customized.member.dto.CostCenterSaveDto;
import com.shuyun.kylin.customized.member.dto.CostCenterSaveModel;
import com.shuyun.kylin.customized.member.service.CostCenterService;
import com.shuyun.kylin.starter.redis.RedisCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@Slf4j
@Service
public class CostCenterServiceImpl implements CostCenterService {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private CostCenterSaveRepository costCenterSaveRepository;

    @Override
    public ResponsesVo upsetCostCenter(CostCenterSaveDto costCenterDto) {
        try {
            if (StringUtils.isNotBlank(costCenterDto.getCostCenterCode())){
                Map<String, Object> filter = new HashMap<>();
                filter.put("id", costCenterDto.getCostCenterCode());
                CostCenterSaveModel costCenterDto1 = costCenterSaveRepository.queryByFilter(ModelConstants.COSTCENTER, null, filter);
                //如果是修改则需要将整个父类的名称和id也更新
                if (null != costCenterDto1) {
                    log.info("修改数据原数据为：{}", costCenterDto1);
                    Map<String, Object> queryMap = new HashMap<>();
                    queryMap.put("id", costCenterDto.getCostCenterCode());
                    Map<String, Object> params = new HashMap<>();
                    params.put("costCenterCode", costCenterDto.getCostCenterCode());
                    params.put("costCenterName", costCenterDto.getCostCenterName());
                    params.put("isSettlement", costCenterDto.getIsSettlement());
                    if (null!=costCenterDto.getParentCostCenter()
                            && costCenterDto.getParentCostCenter().size()>0){
                        log.info("父类id不等于空查询父类名称");
                        params.put("parentCostCenterCode", costCenterDto.getParentCostCenter().get(0));
                        params.put("parentCostCode", costCenterDto.getParentCostCenter());
                        //params.put("parentCostCenterCode", costCenterDto.getParentCostCenterCode());
                        Map<String, Object> pfilter = new HashMap<>();
                        pfilter.put("id", costCenterDto.getParentCostCenter().get(0));
                        CostCenterSaveModel costCenterSaveDto = costCenterSaveRepository.queryByFilter(ModelConstants.COSTCENTER, null, pfilter);
                        if (null!=costCenterSaveDto) {
                            params.put("parentCostCenterName", costCenterSaveDto.getCostCenterName());
                        }
                    }else {
                        params.put("parentCostCenterCode", null);
                        params.put("parentCostCode", costCenterDto.getParentCostCenter());
                        params.put("parentCostCenterName", null);
                    }
                    params.put("lastSync", new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX").format(new Date()));
                    costCenterSaveRepository.update(ModelConstants.COSTCENTER, queryMap, params);
                    //更新父类parent
                    if (StringUtils.isNotBlank(costCenterDto.getCostCenterCode())) {
                        log.info("更新父节点：{}", costCenterDto.getCostCenterCode());
                        Map<String, Object> parentQueryMap = new HashMap<>();
                        parentQueryMap.put("parentCostCenterCode",costCenterDto.getCostCenterCode());
                        Map<String, Object> parentParams = new HashMap<>();
                        //父类是成本中心的编码和code都需要改
                        parentParams.put("parentCostCenterName", costCenterDto.getCostCenterName());
                        costCenterSaveRepository.update(ModelConstants.COSTCENTER, parentQueryMap, parentParams);
                    }
                    return new ResponsesVo(200, costCenterDto.getCostCenterCode());
                }
            }
            String dateMark = new SimpleDateFormat("yyyyMMdd").format(new Date());
            String redisKey = "kl_cost_center_" + dateMark;
            String redisCostCenter = redisCache.get(redisKey);
            if (StringUtils.isBlank(redisCostCenter)) {
                redisCostCenter = "000";
            }
            int i = Integer.parseInt(redisCostCenter) + 1;
            if (i>1000){
                return new ResponsesVo(300, "今日添加的成本中心组织已满999个，无法继续添加");
            }
            redisCache.put(redisKey, "" + i, 2, TimeUnit.DAYS);
            if (redisCostCenter.length() < 3) {
                String[] arr = {"", "0", "00", "000"};
                redisCostCenter = arr[3 - redisCostCenter.length()] + redisCostCenter;
            }
            String redisCostCenterNo = String.format("%s%s%s", "C", dateMark, redisCostCenter);
            log.info("成本中心缓存打印：{}", redisCostCenterNo);
            //新增保存
            costCenterDto.setId(redisCostCenterNo);
           // if (StringUtils.isNotBlank(costCenterDto.getParentCostCenterCode())){
            if (null!=costCenterDto.getParentCostCenter()
                    && costCenterDto.getParentCostCenter().size()>0){
                costCenterDto.setParentCostCenterCode(costCenterDto.getParentCostCenter().get(0));
                log.info("父类id不等于空查询父类名称");
                Map<String, Object> pfilter = new HashMap<>();
                pfilter.put("id", costCenterDto.getParentCostCenterCode());
                CostCenterSaveModel costCenterSaveDto = costCenterSaveRepository.queryByFilter(ModelConstants.COSTCENTER, null, pfilter);
                if (null!=costCenterSaveDto) {
                    costCenterDto.setParentCostCenterName(costCenterSaveDto.getCostCenterName());
                }
            }
            costCenterDto.setCostCenterCode(redisCostCenterNo);
            costCenterDto.setCreated(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX").format(new Date()));
            costCenterDto.setLastSync(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX").format(new Date()));
            CostCenterSaveModel costCenterSaveModel=new CostCenterSaveModel();
            BeanUtils.copyProperties(costCenterDto, costCenterSaveModel);
            costCenterSaveModel.setParentCostCode(costCenterDto.getParentCostCenter());
            costCenterSaveRepository.insert(ModelConstants.COSTCENTER, costCenterSaveModel);
            return new ResponsesVo(200, costCenterSaveModel.getCostCenterCode());
        } catch (Exception e) {
            log.error("成本中心保存失败：{}", e.getMessage());
            return new ResponsesVo(500, "未知异常");
        }
    }
}
