package com.shuyun.kylin.customized.project.strategy;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.enums.ResponseCodeEnum;
import com.shuyun.kylin.customized.base.feign.dao.MemberBingDto;
import com.shuyun.kylin.customized.base.feign.dao.OfferProjectDetailClientResponse;
import com.shuyun.kylin.customized.project.dto.SendCouponResultDto;
import com.shuyun.kylin.customized.project.request.GrantCouponRequest;
import com.shuyun.kylin.customized.rpc.dto.ZerXCouponRequestDto;
import com.shuyun.kylin.customized.rpc.dto.ZerXCouponResponseDto;
import com.shuyun.kylin.customized.rpc.entity.ZerXConfigEntity;
import com.shuyun.kylin.customized.rpc.template.ZerXRequestTemplate;
import com.shuyun.motor.common.cons.PropsUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.UUID;

/**
 * 知而行券发放
 */
@Slf4j
public class ZerXSendCouponStrategy extends SendCouponStrategy {

    static final ZerXConfigEntity zerXConfig = JSON.parseObject(PropsUtil.getSysOrEnv("zerx.api.config"), ZerXConfigEntity.class);

    private static final String ZERX_MSY = "调用第三方接口失败";

    @Override
    public SendCouponResultDto sendCoupon(String templateId,
                                          OfferProjectDetailClientResponse projectDetail,
                                          GrantCouponRequest grantCouponRequest,
                                          MemberBingDto memberBindInfo) {
        log.info("知而行券发放开始");
        OfferProjectDetailClientResponse.RspData data = projectDetail.getData();
        if(ObjectUtil.isNull(data)) {
            log.error("知而行内部接口返回失败，data is empty!");
            return new SendCouponResultDto(Boolean.FALSE, null, null,null, ResponseCodeEnum.GRANT_COUPON_FAILED.getCode(),ZERX_MSY);
        }
        OfferProjectDetailClientResponse.RspData.ExtData extData = data.getExtData();
        if(ObjectUtil.isNull(extData)) {
            log.error("知而行内部接口返回失败，extData is empty!");
            return new SendCouponResultDto(Boolean.FALSE, null, null,null,ResponseCodeEnum.GRANT_COUPON_FAILED.getCode(),ZERX_MSY);
        }
        String externalProjectId = extData.getExternalProjectId();
        if(ObjectUtil.isNull(externalProjectId)) {
            log.error("知而行内部接口返回失败，externalProjectId is empty!");
            return new SendCouponResultDto(Boolean.FALSE, null, null,null,ResponseCodeEnum.GRANT_COUPON_FAILED.getCode(),ZERX_MSY);
        }
        ZerXCouponRequestDto zerXCouponRequestDto = assembleRequestParam(externalProjectId, memberBindInfo.getAppId(), memberBindInfo.getOpenId(), grantCouponRequest.getCampaignId());
        log.info("请求参数：zerXCouponRequestDto={}", zerXCouponRequestDto);
        ZerXCouponResponseDto zerXCouponResponseDto = ZerXRequestTemplate.sendCoupon(zerXCouponRequestDto, grantCouponRequest.getTransactionId());
        if(ObjectUtil.isNull(zerXCouponResponseDto) || ObjectUtil.notEqual(zerXCouponResponseDto.getCode(), HttpStatus.HTTP_OK)) {
            log.error("知而行券发放失败，zerXCouponResponseDto={}", zerXCouponResponseDto);
            HashMap<String, String> map = errorCodeMapper(zerXCouponResponseDto.getCode());
            return new SendCouponResultDto(Boolean.FALSE, null, null,null,map.get("code"),map.get("msg"));
        }
        ZerXCouponResponseDto.RspData zerXResponseData = zerXCouponResponseDto.getData();
        return new SendCouponResultDto(Boolean.TRUE, null, zerXResponseData.getCoupon(), null);
    }

    private static HashMap<String, String> errorCodeMapper (Integer code){
        HashMap<String, String> map = new HashMap<>();
        if (code == null) {
            map.put("code", ResponseCodeEnum.GRANT_COUPON_FAILED.getCode());
            map.put("msg", ZERX_MSY);
        }
        switch (code) {
            case 4004:
            case 4015:
            case 10004:
            case 60001:
                map.put("code", "30001");
                map.put("msg", "活动已结束");
                break;
            case 4014:
                map.put("code", "30001");
                map.put("msg", "活动未开始");
                break;
            case 4006:
                map.put("code", "30001");
                map.put("msg", "用户已达最大领取次数");
                break;
            default:
                map.put("code", ResponseCodeEnum.GRANT_COUPON_FAILED.getCode());
                map.put("msg", ZERX_MSY);
        }
        return map;
    }

    /**
     *
     * @param externalProjectId 外部活动id/商品id
     * @param appId
     * @param openId
     * @return
     */
    private static ZerXCouponRequestDto assembleRequestParam(String externalProjectId, String appId, String openId, String campaignId) {
        ZerXCouponRequestDto zerXCouponRequestDto = new ZerXCouponRequestDto();
        zerXCouponRequestDto.setAppid(appId);
        zerXCouponRequestDto.setOpenId(openId);
        zerXCouponRequestDto.setId(Integer.valueOf(externalProjectId));
        zerXCouponRequestDto.setScene(zerXConfig.getScene());
        // 取值小程序utm_source，若无则取值活动编码（campaign code）
        zerXCouponRequestDto.setQrcodeScene(campaignId);
        zerXCouponRequestDto.setTransactionId(UUID.randomUUID().toString().replace("-", ""));
        // 获取不到经纬度，暂时先不要
//        zerXCouponRequestDto.setLng(null);
//        zerXCouponRequestDto.setLat(null);
        zerXCouponRequestDto.setTimestamp(String.valueOf(System.currentTimeMillis()));
        String sign = getSign(zerXCouponRequestDto);
        zerXCouponRequestDto.setSign(sign);
        return zerXCouponRequestDto;
    }

    /**
     * 签名规则：
     * 将所有参与签名的参数名按照字典排序（每一个参数名按照a-z的顺序排序），
     * 若遇到相同的首字母，则看第二个字母，以此类推。
     * 排序完成后，再把数组所有参数按照“参数=参数值用”的模式用“&”字符拼接成字符串signStr
     * @param zerxCouponRequestDto
     * @return
     */
    public static String getSign(ZerXCouponRequestDto zerxCouponRequestDto) {
        StringBuilder sb = new StringBuilder();
        Map<String, Object> requestMap = BeanUtil.beanToMap(zerxCouponRequestDto, false, true);
        TreeMap<Object, Object> sortMap = new TreeMap<>(requestMap);
        for (Map.Entry<Object, Object> entry : sortMap.entrySet()) {
            if("sign".equals(entry.getKey())) {
                continue;
            }
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        // 去除sb最后一个&符号
        sb.deleteCharAt(sb.length() - 1);
        sb.append(zerXConfig.getSignKey());
        log.info("签名原始参数，sb={}", sb);
        String sign = MD5.create().digestHex(sb.toString());
        log.info("签名，sign={}", sign);
        return sign;
    }
}
