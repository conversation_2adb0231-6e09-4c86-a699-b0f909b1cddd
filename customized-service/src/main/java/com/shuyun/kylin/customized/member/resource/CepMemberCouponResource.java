package com.shuyun.kylin.customized.member.resource;


import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.stream.kafka.dao.CepResponseResult;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.member.dto.CepAppletSubscribeDto;
import com.shuyun.kylin.customized.member.dto.CepCustomizationCouponDto;
import com.shuyun.kylin.customized.member.dto.CepMemberPointRecordDto;
import com.shuyun.kylin.customized.member.service.CepMemberCouponService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Validated
@RestController
@RequestMapping("/customization")
public class CepMemberCouponResource {

    @Autowired
    private CepMemberCouponService cepMemberCouponService;

    /**
     * 定制优惠券发放 =>cep
     *
     * @param cepCustomizationCouponDto
     * @return
     */
    @PostMapping("/coupon/save")
    public CepResponseResult saveMemberCoupon(@RequestBody CepCustomizationCouponDto cepCustomizationCouponDto) {
        log.info("定制优惠券发放 =>cep请求入参: {}", cepCustomizationCouponDto);
        return cepMemberCouponService.saveMemberCoupon(ConfigurationCenterUtil.MEMBER_CEP_COUPON, cepCustomizationCouponDto);
    }

    /**
     * 定制小程序消息订阅 =>cep
     *
     * @param
     * @return
     */
    /*@GetMapping("/applet/subscription")
    public void saveAppletSubscription(@RequestParam String taskId,@RequestParam String tenant,@RequestParam String id,@RequestParam String processId,@RequestParam String nodeId) {
        log.info("定制小程序消息订阅 =>cep请求入参: {}", taskId);

        cepMemberCouponService.saveAppletSubscriptionTask(id,processId,nodeId,taskId, tenant);

        // return cepMemberCouponService.saveAppletSubscription(ConfigurationCenterUtil.MEMBER_CEP_APPLET,cepAppletSubscribeDto);
    }*/

}
