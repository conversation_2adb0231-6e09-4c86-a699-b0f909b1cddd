package com.shuyun.kylin.customized.spi.resource;

import com.shuyun.kylin.crm.openapi.message.dto.EventDto;
import com.shuyun.kylin.crm.openapi.message.dto.EventResponse;
import com.shuyun.pip.component.json.JsonUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "对外透出事件spi实现", description = "用于处理对外透出的事件")
@RestController
@RequestMapping("/openapi")
@ConditionalOnExpression("${customized-service.openapi.spi.enabled}")
public class CustomizedMessageSpiResource {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @PostMapping("/message/spi/member/event")
    public EventResponse memberEvent(@RequestBody EventDto request){
        logger.info("接收到来自于openapi的会员对外事件请求，参数:{}", JsonUtils.toJson(request));
        return new EventResponse();
    }
    @PostMapping("/message/spi/order/event")
    public EventResponse  orderEvent(@RequestBody EventDto request){
        logger.info("接收到来自于openapi的订单对外事件请求，参数:{}", JsonUtils.toJson(request));
        return new EventResponse();
    }

    @PostMapping("/message/spi/loyalty/event")
    public EventResponse  loyaltyEvent(@RequestBody EventDto request) {
        logger.info("接收到来自于openapi的忠诚度对外事件请求，参数:{}", JsonUtils.toJson(request));
        return new EventResponse();
    }
}
