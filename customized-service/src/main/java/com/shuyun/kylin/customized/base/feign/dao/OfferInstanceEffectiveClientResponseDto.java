package com.shuyun.kylin.customized.base.feign.dao;

import lombok.Data;

import java.util.List;

/**
 * 卡劵启用feign response dto
 */
@Data
public class OfferInstanceEffectiveClientResponseDto {

    /**
     * 卡券处理结果状态(成功:SUCCESS)(待处理:WAITING)(处理中:HANDLING)(失败:FAIL)(重复:DUPLICATED)
     */
    private String status;

    /**
     * 消息
     */
    private String message;

    private List<SuccessRtn> success;

    @Data
    public static class SuccessRtn {
        /**
         * 卡券id
         */
        private String id;

        /**
         * 卡券code
         */
        private String couponCode;

        /**
         * 卡券项目id
         */
        private String projectId;
    }

    private List<ErrorsRtn> errors;

    @Data
    public static class ErrorsRtn {
        private String id;

        /**
         * 卡券code
         */
        private String couponCode;

        /**
         * 卡券项目id
         */
        private String projectId;

        /**
         * 错误编码
         */
        private String code;

        /**
         * 错误消息
         */
        private String message;
    }
}
