package com.shuyun.kylin.customized.rpc.enums;

import lombok.Getter;

/**
 * 券实例状态:
 * - New [新创建]
 * - Distributed [已分发]
 * - Sold [已售出]
 * - Shared [已分享]
 * - Redeemed [已核销]
 * - Void [已失效]
 */
@Getter
public enum UbrCoupoonCreatedStatusEnum {
    NEW("New", "新创建"),
    DISTRIBUTED("Distributed", "已分发"),
    SOLD("Sold", "已售出"),
    SHARED("Shared", "已分享"),
    REDEEMED("Redeemed", "已核销"),
    VOID("Void", "已失效");

    private String code;
    private String desc;

    UbrCoupoonCreatedStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
   }
}
