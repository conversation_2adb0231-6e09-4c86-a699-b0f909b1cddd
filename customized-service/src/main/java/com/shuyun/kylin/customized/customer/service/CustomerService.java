package com.shuyun.kylin.customized.customer.service;

import com.shuyun.cdp.tags.response.OriginTagsResponse;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.customer.dto.CommonTagSingleResponse;
import com.shuyun.kylin.customized.customer.dto.CustomerTagDto;

/**
 * <AUTHOR>
 * @date 2022/2/28 14:32
 * @description
 */
public interface CustomerService {
    /**
     * CDP标签校验
     * @param customerTagDto
     * @return
     */
    ResponseResult customerTag(CustomerTagDto customerTagDto);

    /**
     * CDP标签生成任务
     * @return
     */
    CommonTagSingleResponse submitCdpTagTask();
}
