package com.shuyun.kylin.customized.member.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

@Data
public class InteractiveRecordsDto {
    //会员渠道编号，appid_openId
    @NotBlank
    private String customerNo;
    @NotBlank(message = "参与时间")
    private String joinTime;

    private String refuseReason;
    @NotBlank
    private String interactiveType;
    /*TAOBAO：天猫
    KO_MP：KO小程序
    EC_SHOPPING：EC购物商城
    EC_POINT：EC积分商城*/
    @NotBlank
    private String channelType;

    private String interactiveRecordId;
    @NotBlank
    private String interactiveId;
    @NotBlank
    private String interactiveName;
    /*发放状态，
    枚举值：
     0，发放权益成功
    1，发放权益失败
    2，拒绝发放*/
    private String grantStatus;
    //本次互动发放的积分数
    private Number memberIntegral;
    //是否得到奖励
    @NotNull
    private Boolean isGetRewards;
    //本次互动发放的券
    private String coupon;

    private Number consumePoint;

    private String rewardsCode;

    private String rewardsName;

    private String rewardsCost;

    private String rewardsType;

    private String invitee;//被邀请者


    private Number signTime;

    private String mobile;



    private String ruleDesc;

    private String memberIDStr;

    private String lastSync;

    private String created;

    private Boolean isLuckyDraw;
}
