package com.shuyun.kylin.customized.member.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class EcSyncShopDto {

    @NotBlank(message = "店铺code不能为空")
    private String shopCode;
    @NotBlank(message = "店铺名称不能为空")
    private String shopName;
    private String provinceCode;
    private String cityCode;
    private String districtCode;
    private String introduction;

    private String memberType;
    private String lastSync;
    private String relatedBg;
    private String channelType;
}
