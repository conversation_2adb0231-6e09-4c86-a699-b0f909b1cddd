package com.shuyun.kylin.customized.member.dto;

import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Data
@ToString
/**
 * CepPointRecordListPageResponse
 * caoliyuan
 * 20211227
 */
public class CepPointRecordListPageResponse<T> {
    private Integer currentPage;//当前页
    private Integer pageSize;//分页大小
    private Integer totalCount;//总记录数
    //private Integer totalPage;//总页数
    private List<T> data;

    private CepPointRecordListPageResponse(Integer currentPage, Integer pageSize, List<T> data,Integer totalCount){
        this.currentPage = currentPage;
        this.pageSize = pageSize;
        this.data = data;
        this.totalCount = totalCount;
    }

    private CepPointRecordListPageResponse(Integer currentPage,Integer pageSize){
        this.currentPage = currentPage;
        this.pageSize = pageSize;
        this.totalCount = 0;
        //this.totalPage = 0;
        this.data = new ArrayList<>();
    }

    /**
     * 分页列表
     * @param currentPage 当前页
     * @param pageSize 页大小
     * @param list 分页数据
     * @param totalCount 总数据量
     * @param <T>
     * @return
     */
    private static <T> CepPointRecordListPageResponse<T> pagingList(Integer currentPage, Integer pageSize, List<T> list, Integer totalCount){
        CepPointRecordListPageResponse<T> pageResponse = new CepPointRecordListPageResponse<>(currentPage, pageSize, list,totalCount);
        /*if( totalCount%pageSize == 0 ){
            pageResponse.setTotalPage(totalCount/pageSize);
        }else{
            pageResponse.setTotalPage(totalCount/pageSize+1);
        }*/
        return pageResponse;
    }

    public static <T> CepPointRecordListPageResponse<T> PagingList(Integer currentPage, Integer pageSize, List<T> list, Integer totalCount){
        return pagingList(currentPage,pageSize,list,totalCount);
    }

    /**
     * @Description:  返回无数据对象
     * @Author: Z.Wei-m
     * @Date: 2021/4/6 16:07
     */
    private static <T> CepPointRecordListPageResponse<T> empty(Integer currentPage,Integer pageSize){
        return new CepPointRecordListPageResponse(currentPage,pageSize);
    }

    public static <T> CepPointRecordListPageResponse<T> Empty(Integer currentPage,Integer pageSize){
        return empty(currentPage,pageSize);
    }
}
