package com.shuyun.kylin.customized.rpc.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <pre>
 * {
 *     "ubr.api_version": "10",
 *     "ubr.client_id": "p0028",
 *     "ubr.client_secret": "cmroDhogV6dTBhjStuuFwCMpIBldCF6U",
 *     "ubr.grant_type": "client_credentials",
 *     "ubr.send_coupon_url": "https://apiq.eubrmb.com/eco/qa/v1.0/coupons/%s/instance",
 *     "ubr.sign_method": "sha256",
 *     "ubr.sign_secret": "1234567890",
 *     "ubr.token_url": "https://apiq.eubrmb.com/getToken"
 * }
 * </pre>
 */
@Data
public class UbrConfigEntity {
    @JSONField(name = "ubr.api_version")
    private String apiVersion;
    @JSONField(name = "ubr.client_id")
    private String clientId;
    @JSONField(name = "ubr.client_secret")
    private String clientSecret;
    @JSONField(name = "ubr.grant_type")
    private String grantType;
    @JSONField(name = "ubr.send_coupon_url")
    private String sendCouponUrl;
    @JSONField(name = "ubr.sign_method")
    private String signMethod;
    @JSONField(name = "ubr.sign_secret")
    private String signSecret;
    @JSONField(name = "ubr.token_url")
    private String tokenUrl;

}
