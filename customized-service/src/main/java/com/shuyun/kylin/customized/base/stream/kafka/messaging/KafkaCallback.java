package com.shuyun.kylin.customized.base.stream.kafka.messaging;

import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnExpression("${system.kafka.enabled:true}")
public interface KafkaCallback {
    String KAFKA_OUTPUT = "kafka_callback";
    @Output(KAFKA_OUTPUT)
    MessageChannel output();
}
