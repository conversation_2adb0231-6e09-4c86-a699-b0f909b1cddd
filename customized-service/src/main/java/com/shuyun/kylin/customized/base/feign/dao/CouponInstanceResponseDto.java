package com.shuyun.kylin.customized.base.feign.dao;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 卡券项目列表feign调用返回对象
 */
@Data
public class CouponInstanceResponseDto {
    @JSONField(name = "error_code")
    private String errorCode;

    private String msg;

    private RspData data;

    @Data
    public static class RspData {
        private Integer page;
        private Integer pageSize;
        private Integer total;

        private List<CardList> cardList;

        @Data
        public static class CardList {
            /**
             * 卡券实例ID
             */
            private String id;

            /**
             * 券项目id
             */
            private String projectId;

            /**
             * 券发放状态
             */
            private String status;

            /**
             * 券码
             */
            private String couponCode;

            /**
             * 三方券码
             */
            private String externalCode;

            /**
             * 生效时间
             */
            private String effectiveAt;

            /**
             * 失效时间
             */
            private String expiredAt;

            /**
             * 发放时间
             */
            private String grantAt;

            /**
             * 使用时间
             */
            private String useAt;

            /**
             * 扩展字段
             */
            private Extension extension;

            @Data
            public static class Extension {

                private String externalCode;

                private String campaignId;

                /**
                 * 来源活动名称
                 */
                private String campaignName;
                /**
                 * 使用方式（兑换类型）
                 */
                private String redeemType;
                /**
                 * 跳转参数
                 */
                private String jumpParameter;
            }

            private Project project;

            @Data
            public static class Project {
                private String type;
                /**
                 * 项目名称
                 */
                private String title;

                /**
                 * 来源活动名称
                 */
//                private String campaignTitle;

                private ExtensionProject extData;

                public static class ExtensionProject {
                    @Getter
                    @Setter
                    private String projectBusiness;
                    @Getter
                    @Setter(onMethod_ = @JsonProperty("VirtualType"))
                    private String VirtualType;
                }
            }
        }
    }
}
