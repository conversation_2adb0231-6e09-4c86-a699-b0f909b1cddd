package com.shuyun.kylin.customized.cbl.service.Impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.base.util.DataApiUtil;
import com.shuyun.kylin.customized.base.util.HttpClientHelper;
import com.shuyun.kylin.customized.cbl.dto.CblMemberGradeDto;
import com.shuyun.kylin.customized.cbl.dto.CblMemberUpdateDto;
import com.shuyun.kylin.customized.cbl.dto.CblReturnResultsDto;
import com.shuyun.kylin.customized.cbl.service.CblDataSyncService;
import com.shuyun.kylin.customized.cbl.utils.MD5Util;
import com.shuyun.kylin.customized.swire.dto.ReturnResultsDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

import static com.shuyun.lite.client.SignConvertUtil.DTF;

@Slf4j
@Service
public class CblDataSyncServiceImpl implements CblDataSyncService {


    @Override
    public Map<String, String> dataSyncMember(Map<String, String> params) {
        Map<String, String> map = new HashMap<>();
        Map<String, Object> resultMap = new HashMap<>();

        String timestamp = String.valueOf(System.currentTimeMillis());
        Map<String, String> head = new HashMap<>();
        head.put("app_key", ConfigurationCenterUtil.CBL_APPKEY);
        head.put("timestamp", timestamp);
        head.put("sign", getCblSign(params,timestamp));
        log.info("CBL会员信息同步sign:{},timestamp:{},json:{}",head.get("sign"),timestamp,JSON.toJSON(params));
        String body = null;
        try {
            body = HttpClientHelper.doPostJSON(ConfigurationCenterUtil.CBL_UPDATEMEMBER, params, head);
        } catch (Exception e) {
            resultMap.put("success",false);
            resultMap.put("data",false);
            resultMap.put("error","同步接口CBL接口异常");
            resultMap.put("mobile",params.get("mobile"));
            log.info("CBL会员信息同步sign:{},error:{}",head.get("sign"),e.getMessage());
        }
        CblReturnResultsDto result = JSONObject.parseObject(body, CblReturnResultsDto.class);
        if (null != result){
            if (null != result.getSuccess()){
                resultMap.put("success",result.getSuccess());
            }
            if (null != result.getData()){
                resultMap.put("data",result.getData());
            }
            if (StringUtils.isNotBlank(result.getError())){
                resultMap.put("error",result.getError());
            }
        }
        resultMap.put("lastSync", ZonedDateTime.now());
        DataApiUtil.upsertIsData(ModelConstants.CBL_MEMBERSYNC,params.get("mobile"), resultMap);
        log.info("CBL会员信息同步sign:{},返回值:{}", head.get("sign"),JSONObject.toJSONString(result));
        map.put("code","200");
        return map;
    }

    @Override
    public Map<String, String> dataSyncGrade(Map<String, String> params) {
        Map<String, String> map = new HashMap<>();
        Map<String, Object> resultMap = new HashMap<>();

        String timestamp = String.valueOf(System.currentTimeMillis());
        Map<String, String> head = new HashMap<>();
        head.put("app_key", ConfigurationCenterUtil.CBL_APPKEY);
        head.put("timestamp", timestamp);
        head.put("sign", getCblSign(params,timestamp));
        log.info("CBL等级同步sign:{},json:{}",head.get("sign"),JSON.toJSON(params));
        String body = null;
        try {
            body = HttpClientHelper.doPostJSON(ConfigurationCenterUtil.CBL_MEMBERGRADE, params, head);
        } catch (Exception e) {
            resultMap.put("success",false);
            resultMap.put("data",false);
            resultMap.put("error","同步接口CBL接口异常");
            resultMap.put("mobile",params.get("mobile"));
            log.info("CBL等级同步异常sign:{},error:{}",head.get("sign"),e.getMessage());
        }
        CblReturnResultsDto result = JSONObject.parseObject(body, CblReturnResultsDto.class);
        if (null != result){
            if (null != result.getSuccess()){
                resultMap.put("success",result.getSuccess());
            }
            if (null != result.getData()){
                resultMap.put("data",result.getData());
            }
            if (StringUtils.isNotBlank(result.getError())){
                resultMap.put("error",result.getError());
            }
        }
        resultMap.put("lastSync", ZonedDateTime.now());
        DataApiUtil.upsertIsData(ModelConstants.CBL_GRADESYNC,params.get("mobile"), resultMap);
        log.info("CBL等级同步sign:{},返回值:{}", head.get("sign"),JSONObject.toJSONString(result));
        map.put("code","200");
        return map;
    }

    private static String getCblSign(Map<String, String> params,String timestamp){
        final TreeMap<String, String> treeMap = new TreeMap<>();
        treeMap.putAll(params);
        treeMap.put("timestamp", timestamp);
        treeMap.put("app_key",ConfigurationCenterUtil.CBL_APPKEY);
        StringBuilder sb = new StringBuilder(ConfigurationCenterUtil.CBL_APPSECRET);
        treeMap.forEach((key,value)->{
            if(!StringUtils.isAnyBlank(key,value) ){
                sb.append(key).append(value);
            }
        });
        sb.append(ConfigurationCenterUtil.CBL_APPSECRET);
        final String encrypt = MD5Util.encrypt(sb.toString());
        return encrypt;
    }

}
