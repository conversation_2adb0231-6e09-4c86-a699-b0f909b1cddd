package com.shuyun.kylin.customized.coupon.handler;

import com.google.common.collect.Maps;
import com.shuyun.kylin.customized.base.context.SpringApp;
import java.util.concurrent.ConcurrentMap;

public class CouponSendHandlerFactory {

    private static final ConcurrentMap<Object, Object> handlerMap = Maps.newConcurrentMap();

    public static <T> CouponSendHandler getHandler(String sendType){
        return (CouponSendHandler)handlerMap.computeIfAbsent(sendType,   k -> createHandler(sendType)  );
    }

    private static <T> Object createHandler(String sendType) {
        return SpringApp.getBean(sendType);
    }

}
