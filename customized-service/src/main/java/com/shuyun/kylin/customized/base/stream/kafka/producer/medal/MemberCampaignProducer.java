package com.shuyun.kylin.customized.base.stream.kafka.producer.medal;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.shuyun.kylin.customized.base.stream.kafka.messaging.KafkaBehavior;
import com.shuyun.kylin.customized.base.stream.kafka.messaging.KafkaNotification;
import com.shuyun.kylin.customized.base.stream.kafka.messaging.KafkaSource;
import com.shuyun.kylin.customized.medal.request.CampaignRecordRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@EnableBinding({KafkaBehavior.class, KafkaNotification.class})
@ConditionalOnExpression("${system.kafka.enabled:true}")
public class MemberCampaignProducer {

    @Autowired
    private KafkaSource kafkaSource;

    //异步发送会员参与活动数据
    public void sendMemberCampaignData(CampaignRecordRequest campaignRecordRequest) {
        log.info("【 参与活动memberCampaign Topic： KAFKA_MEMBER_CAMPAIGN_OUTPUT  开始");
        kafkaSource.memberCampaignOutput().send(MessageBuilder.withPayload(campaignRecordRequest).build());
        log.info("【 参与活动memberCampaign Topic：KAFKA_MEMBER_CAMPAIGN_OUTPUT 结束=======> 送出参数: {} 】", JSON.toJSONString(campaignRecordRequest , SerializerFeature.WriteMapNullValue));
    }


}
