package com.shuyun.kylin.customized.base.exception;

import com.shuyun.kylin.starter.exception.model.IErrorType;
import com.shuyun.kylin.starter.exception.model.RequestException;

/**
 * 条件校验器
 * <AUTHOR>
 * @create 2020/1/3
 */
public final class Checker {

    /**
     * 校验
     * @param condition    校验通过的条件
     * @param iErrorType   错误码
     * @param args         国际化消息中可变参数
     */
    public static void requiresThrowRequestException(boolean condition, IErrorType iErrorType,Object... args) {
        if (!condition) {
            throw new RequestException(iErrorType,args);
        }
    }

    public static void requiresThrowRequestException(boolean condition,IErrorType iErrorType,String format, Object... args) {
        if (!condition) {
            throw new RequestException(iErrorType, String.format(format, args));
        }
    }




}
