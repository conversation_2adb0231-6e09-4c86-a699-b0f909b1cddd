package com.shuyun.kylin.customized.project.enums;

import lombok.Getter;

/**
 * @author: <PERSON><PERSON>
 * @date: 2024-12-14
 */
@Getter
public enum CouponInstanceUseState {
    CREATED("CREATED", "成功"),
    ACTIVATED("ACTIVATED", "待处理"),
    DISCARDED("DISCARDED", "处理中"),
    EFFECTED("EFFECTED", "失败"),
    USED("USED", "重复");

    private String code;
    private String desc;

    CouponInstanceUseState(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
