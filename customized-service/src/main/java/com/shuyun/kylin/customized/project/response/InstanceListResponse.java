package com.shuyun.kylin.customized.project.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class InstanceListResponse extends CocoPageResult {

    private List<Instance> data;

    @Data
    public static class Instance {

        /**
         * 卡券实例ID
         */
        private String id;
        /**
         * 券项目id
         */
        private String projectId;
        /**
         * CREATED:已发放,ACTIVATED:已启用,DISCARDED:已作废,EFFECTED:已生效,EXPIRED:已失效,LOCKED:已锁定,USED:已使用
         */
        private String status;
        /**
         * 券码
         */
        private String couponCode;
        /**
         * 生效时间
         */
        private String effectiveAt;
        /**
         * 失效时间
         */
        private String expiredAt;
        /**
         * 三方券码
         */
        private String externalCode;
        /**
         * 扩展数据
         */
        private ExtData extData;

        @Data
        public static class ExtData {
            /**
             * 权益类型
             */
            private String type;
            /**
             * 虚拟权益类型
             */
            private String virtualType;
            /**
             * 券商
             */
            private String projectBusiness;

            /**
             * 项目名称
             */
            private String title;
            /**
             * 来源活动Id
             */
            private String campaignId;
            /**
             * 来源活动名称
             */
            private String campaignName;
            /**
             * 发放时间
             */
            private String grantAt;
            /**
             * 使用时间
             */
            private String useAt;

            /**
             * 使用方式（兑换类型）
             */
            private String redeemType;
            /**
             * 跳转参数
             */
            private String jumpParameter;
        }
    }
}
