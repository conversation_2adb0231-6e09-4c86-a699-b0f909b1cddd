package com.shuyun.kylin.customized.behavior.dm;

import com.google.common.base.Joiner;
import lombok.Getter;
import lombok.Setter;

import java.util.function.Function;

@Setter
@Getter
public class DmQueryBuilder {
    private String fields;
    private String fqn;
    private String where;

    public DmQueryBuilder setFields(String fields) {
        this.fields = fields;
        return this;
    }

    public DmQueryBuilder setFqn(String fqn) {
        this.fqn = fqn;
        return this;
    }

    public DmQueryBuilder setWhere(String where) {
        this.where = where;
        return this;
    }

    public String build(Function<String, String> sqlFunction) {
        if (where == null) {
            where = "";
        }

        String sql = Joiner.on(" ").join("SELECT", fields, "FROM", fqn, where);
        if (sqlFunction != null) {
            sql = sqlFunction.apply(sql);
        }

        return sql;
    }

    public String build() {
        return build(null);
    }

}
