package com.shuyun.kylin.customized.api.resource;

import com.shuyun.kylin.customized.coupon.dto.CouponDto;
import com.shuyun.pip.component.json.JsonUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @Description
 * @date 2020/4/13
 */
@Tag(name = "demo 接口")
@RestController
@RequestMapping("/api/demo")
public class DemoResource {

    @GetMapping(value = "/info", produces = MediaType.APPLICATION_JSON_VALUE)
    public String getMessage(@RequestBody CouponDto dto) {
        return JsonUtils.toJson(dto);
    }


}
