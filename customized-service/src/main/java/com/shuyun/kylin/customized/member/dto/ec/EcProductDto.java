package com.shuyun.kylin.customized.member.dto.ec;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.ZonedDateTime;

@Data
public class EcProductDto {

    @NotBlank(message = "商品编码不能为空")
    @Size(max =32, message = "参数长度不能超过32位")
    private String skuCode;
    private String prodCode;
    private String prodName;
    private String brandIds;
    private String brandName;
    private String erpCode;
    private String divCode;
    private String divName;
    private String subDivCode;
    private String subDivName;
    private String kzzd1;
    private String kzzd1Name;
    private String kzzd2;
    private String kzzd2Name;
    private Double marketPrice;
    private String internationalCode;
    private String code99;
    private String lastSync;
    private String created;


}
