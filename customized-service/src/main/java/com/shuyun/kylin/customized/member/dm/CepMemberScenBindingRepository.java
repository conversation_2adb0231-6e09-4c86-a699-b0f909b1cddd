package com.shuyun.kylin.customized.member.dm;

import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.base.util.DateHelper;
import com.shuyun.kylin.customized.member.dto.CepMemberDeductionDto;
import com.shuyun.kylin.customized.member.dto.CepMemberSceneBindingLogDto;
import com.shuyun.kylin.customized.member.dto.CepRegulationPointDto;
import com.shuyun.kylin.customized.member.dto.CepRulePointLogDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class CepMemberScenBindingRepository extends BaseDsRepository<CepMemberSceneBindingLogDto> {


    /**
     * 查询会员每天上限次数
     * @param memberId
     * @param scene
     * @return
     */
    public String getDayTime(String memberId, String scene) {
        //获取当天时间
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        queryMap.put("scene", scene);
        queryMap.put("lastSync", DateHelper.getDateDay());
        String queryMemberSql = " select count(1) as count from " + ModelConstants.CAMPAIGN_SCENE_BINDINGLOG + " where  memberId = :memberId and  scene = :scene  and lastSync = :lastSync ";
        List<Map<String,Object>> list = execute(queryMemberSql, queryMap).getData();
        String count = "0";
        if (!CollectionUtils.isEmpty(list)){
            for (Map map : list) {
                count = map.get("count") +"";
            }
        }
        return count;
    }

    /**
     * 查询会员每月上线次数
     * @param memberId
     * @param scene
     * @return
     */
    public String getMonthTime(String memberId, String scene) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        queryMap.put("scene", scene);
        queryMap.put("startTime", DateHelper.getThisMonthDay());
        queryMap.put("endTime", DateHelper.getDateDay());
        //String queryMemberSql = " select count(1) as count from " + ModelConstants.CAMPAIGN_SCENE_BINDINGLOG + " where  customerNo = :customerNo and  scene = :scene  and DATE_FORMAT(lastSync,'%Y%m') = DATE_FORMAT(CURDATE(),'%Y%m') ";
        String queryMemberSql = " select count(1) as count from " + ModelConstants.CAMPAIGN_SCENE_BINDINGLOG + " where  memberId = :memberId and  scene = :scene  and lastSync >= :startTime  and  lastSync <= :endTime  ";
        List<Map<String,Object>> list = execute(queryMemberSql, queryMap).getData();
        String count = "0";
        if (!CollectionUtils.isEmpty(list)){
            for (Map map : list) {
                count = map.get("count") +"";
            }
        }
        return count;
    }

    /**
     * 查询会员上限次数
     * @param memberId
     * @param scene
     * @return
     */
    public String getMaximumTime(String memberId, String scene) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        queryMap.put("scene", scene);
        String queryMemberSql = " select count(1) as count from " + ModelConstants.CAMPAIGN_SCENE_BINDINGLOG + " where  memberId = :memberId and  scene = :scene ";
        List<Map<String,Object>> list = execute(queryMemberSql, queryMap).getData();
        String count = "0";
        if (!CollectionUtils.isEmpty(list)){
            for (Map map : list) {
                count = map.get("count") +"";
            }
        }
        return count;
    }

    /**
     * 删除活动
     * @param id
     */
    public void deleteActivity(String id) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("id", id);
        String queryMemberSql = " DELETE FROM " + ModelConstants.CAMPAIGN_SCENE_BINDINGLOG + " where id = :id ";
        execute(queryMemberSql, queryMap);
    }

    /**
     * 修改KZZD3
     * @param cepRulePointLogDto
     */
    public void updateMemberPointLog(CepRulePointLogDto cepRulePointLogDto) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("traceId", cepRulePointLogDto.getBusinessId());
        queryMap.put("KZZD3", cepRulePointLogDto.getCostTracing());
        queryMap.put("created", ZonedDateTime.now().plusSeconds(1));
        String queryMemberSql = " UPDATE " + ConfigurationCenterUtil.MEMBER_POINTRECORD + " SET KZZD3=:KZZD3,created =:created where traceId = :traceId ";
        log.info("更新sql:{}",queryMemberSql);
        execute(queryMemberSql, queryMap);
    }

    /**
     * 修改KZZD3
     * @param cepRulePointLogDto
     */
/*    public void updatePointBehaviorRecord(CepRulePointLogDto cepRulePointLogDto) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("traceId", cepRulePointLogDto.getBusinessId()+"_RETURN");
        queryMap.put("trace", cepRulePointLogDto.getBusinessId());
        queryMap.put("KZZD3", cepRulePointLogDto.getCostTracing());
        String queryMemberSql = " UPDATE " + ModelConstants.POINT_BEHAVIOR_RECORD + " SET shopId=:KZZD3 where traceId = :traceId or traceId =:trace";
        log.info("更新sql:{}",queryMemberSql);
        execute(queryMemberSql, queryMap);
    }*/

    /**
     * 修改KZZD3
     * @param cepRulePointLogDto
     */
    /*public void updatePointRecord(CepRulePointLogDto cepRulePointLogDto) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("traceId", cepRulePointLogDto.getBusinessId());
        queryMap.put("KZZD3", cepRulePointLogDto.getCostTracing());
        String queryMemberSql = " UPDATE " + ModelConstants.POINT_BEHAVIOR + " SET KZZD3=:KZZD3 where traceId = :traceId ";
        log.info("更新sql:{}",queryMemberSql);
        execute(queryMemberSql, queryMap);
    }*/


}
