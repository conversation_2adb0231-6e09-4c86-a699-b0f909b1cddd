package com.shuyun.kylin.customized.project.strategy;

import com.shuyun.kylin.customized.base.feign.dao.MemberBingDto;
import com.shuyun.kylin.customized.base.feign.dao.OfferProjectDetailClientResponse;
import com.shuyun.kylin.customized.project.dto.SendCouponResultDto;
import com.shuyun.kylin.customized.project.request.GrantCouponRequest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ConventionSendCouponStrategy extends SendCouponStrategy {
    @Override
    public SendCouponResultDto sendCoupon(String templateId, OfferProjectDetailClientResponse projectDetail, GrantCouponRequest grantCouponRequest, MemberBingDto memberBindInfo) {
        log.info("策略ConventionSendCouponStrategy.sendCoupon");
        return new SendCouponResultDto(Boolean.TRUE, null, null,null);
    }
}
