package com.shuyun.kylin.customized.member.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

@Data
public class CepUpdateMemberDto {
    //@NotBlank(message = "客户编号不能为空，格式：appid_openid")
    private String customerNo;
    @NotBlank
    private String memberType;
    @NotBlank
    private String channelType;
    private String memberName;
    private String gender;
    private String birthDay;
    private String birthYear;
    private String nickName;
    private String headImgUrl;
    private String lbsIdentifying;
    private String provinceName;
    private String cityName;
    private String districtName;
    private String income;
    private String job;
    private String agreementVersion;
    private String memberId;
    private String originLbsProvince;
    private String originLbsCity;
    private String originLbsDistrict;
    private String createTime;


    public static boolean hasValues(CepUpdateMemberDto cepUpdateMemberDto) {
        return cepUpdateMemberDto != null
                && (cepUpdateMemberDto.getMemberName() != null || cepUpdateMemberDto.getGender() != null
                || cepUpdateMemberDto.getBirthDay() != null || cepUpdateMemberDto.getBirthYear() != null
                || cepUpdateMemberDto.getNickName() != null || cepUpdateMemberDto.getHeadImgUrl() != null
                || cepUpdateMemberDto.getLbsIdentifying() != null || cepUpdateMemberDto.getProvinceName() != null
                || cepUpdateMemberDto.getCityName() != null || cepUpdateMemberDto.getDistrictName() != null
                || cepUpdateMemberDto.getIncome() != null || cepUpdateMemberDto.getJob() != null) ||
                cepUpdateMemberDto.getAgreementVersion() != null;

    }
}
