package com.shuyun.kylin.customized.member.dto;

import lombok.Data;
import org.jetbrains.annotations.Nullable;

@Data
public class InteractiveDto {
    private String interactiveId;

    private String interactiveName;
    @Nullable
    private String startTime;
    @Nullable
    private String endTime;

    private Integer inviteLimit;

    private String remarks;

    private String couponList;

    private Boolean boolEnableState;

    private String interactiveRuleDesc;

    private String interactiveCostCenter;
    //	Enum
    private String enableState;
    //是否奖励积分
    private Boolean isRewardPoint;
    //是否奖励优惠券
    private Boolean isRewardCoupon;
    //奖励积分数量
    private Integer pointNum;
    //任务类型
    private String taskType;
    //场景值
    private String scene;
    //	Enum奖励方式
    private String awardType;
    //是否连续签到额外奖励
    private Boolean isExtraAward;

    private String lastSync;

    private String created;

}
