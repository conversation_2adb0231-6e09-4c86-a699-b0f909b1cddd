package com.shuyun.kylin.customized.cdp.resource;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.cdp.service.CdpMemberPointService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/cdp/sync")
public class CdpMemberPointRecource {

    @Autowired
    private CdpMemberPointService cdpMemberPointService;

    /**
     *  数据流 => 重试积分同步失败重推cdp
     * @param request
     * @return
     */
    @PostMapping("/retry/point")
    public Map<String, Object> retrySynchronousPoint(@RequestBody Map<String, String> request){
        log.info("重试积分同步失败重推cdp请求入参: {}", JSON.toJSONString(request));
        return cdpMemberPointService.retrySynchronousPoint(request);
    }
}
