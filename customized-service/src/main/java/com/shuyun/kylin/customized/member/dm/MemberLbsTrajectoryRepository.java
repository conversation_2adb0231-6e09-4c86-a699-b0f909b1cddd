package com.shuyun.kylin.customized.member.dm;

import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.member.dto.CepAppletTemplateDto;
import com.shuyun.kylin.customized.member.dto.MemberLbsTrajectoryDto;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class MemberLbsTrajectoryRepository extends BaseDsRepository<MemberLbsTrajectoryDto> {

    public MemberLbsTrajectoryDto queryByMemberId(String memberId, String originLbsCity) {
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("memberId",memberId);
            queryMap.put("originLbsCity",originLbsCity);
            MemberLbsTrajectoryDto query = queryByFilter(ModelConstants.MEMBER_LBS_TRAJECTORY, null,queryMap);
            return query;
    }
}
