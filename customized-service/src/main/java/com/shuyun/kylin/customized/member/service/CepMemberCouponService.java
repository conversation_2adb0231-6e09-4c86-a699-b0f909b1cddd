package com.shuyun.kylin.customized.member.service;


import com.shuyun.kylin.customized.base.stream.kafka.dao.CepResponseResult;
import com.shuyun.kylin.customized.member.dto.CepAppletSubscribeDto;
import com.shuyun.kylin.customized.member.dto.CepCustomizationCouponDto;


public interface CepMemberCouponService {

    CepResponseResult saveMemberCoupon(String url, CepCustomizationCouponDto cepCustomizationCouponDto);

    CepResponseResult saveAppletSubscription(String url, CepAppletSubscribeDto cepAppletSubscribeDto);

    //void saveAppletSubscriptionTask(String id, String processId, String nodeId, String taskId, String tenant);
}
