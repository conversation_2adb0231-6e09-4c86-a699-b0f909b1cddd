package com.shuyun.kylin.customized.medal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.dm.api.response.DMLResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.enums.ResponseCodeEnum;
import com.shuyun.kylin.customized.base.feign.client.LoyaltyFacade;
import com.shuyun.kylin.customized.base.stream.kafka.producer.medal.MemberCampaignProducer;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.base.util.DataApiUtil;
import com.shuyun.kylin.customized.base.util.DataapiSdkUtil;
import com.shuyun.kylin.customized.base.util.DateHelper;
import com.shuyun.kylin.customized.medal.enums.MedalObtainTypeEnum;
import com.shuyun.kylin.customized.medal.request.CampaignRecordRequest;
import com.shuyun.kylin.customized.medal.request.MedalObtainRequest;
import com.shuyun.kylin.customized.medal.response.*;
import com.shuyun.kylin.customized.medal.service.MedalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class MedalServiceImpl implements MedalService {

    final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();

    final static String ACTIVITY_2505SSUTC = "2505SSUTC";

    final static String ACTIVITY_2505COSTAUTC = "2505COSTAUTC";

    //勋章体系id
//    final static  Integer planId = Integer.valueOf(PropsUtil.getSysOrEnv("medal.planId", "60001"));

    //勋章体系id
//    final static  Integer medalHierarchyId = Integer.valueOf(PropsUtil.getSysOrEnv("medal.medalHierarchyId", "60001"));

    //注册勋章id
//    final static  Integer medalRegisterId = Integer.valueOf(PropsUtil.getSysOrEnv("medal.medalRegisterId", "60002"));

    //勋章等级体系id
//    final static  Integer medalGradeId = Integer.valueOf(PropsUtil.getSysOrEnv("medal.gradeId", "60807"));

    @Autowired
    private MemberCampaignProducer memberCampaignProducer;
    /**
     * 忠诚度接口
     */
    @Autowired
    private LoyaltyFacade loyaltyFacade;

    //2.48.查询勋章主数据列表
    @Override
    public ResponseResult<List<MedalListResponse>> medal() {
        //查询勋章主数据
        String getMedalSql = "select medalDefinitionId,medalDefinitionName from "+ModelConstants.MEDAL_RULE+" where typeData='medalRule' " ;
        log.info("medal...getMedalSql:{}",getMedalSql);
        BaseResponse execute = dataapiHttpSdk.execute(getMedalSql, Collections.emptyMap());
        if (ObjectUtil.isEmpty(execute.getData())) {
            return ResponseResult.responseError("400", "勋章主数据暂未维护", null);
        }
        List<Map<String, Object>> dataList = execute.getData();
        if (dataList.size()> 0 && dataList != null){
            ArrayList<MedalListResponse> list = new ArrayList<>();
            for (int i = 0; i < dataList.size(); i++){
                MedalListResponse medalListResponse = new MedalListResponse();
                medalListResponse.setMedalDefinitionId(Integer.valueOf(dataList.get(i).get("medalDefinitionId").toString()));
                medalListResponse.setMedalDefinitionName(dataList.get(i).get("medalDefinitionName").toString());
                list.add(medalListResponse);
            }
            return ResponseResult.responseSuccess(list);
        }
        return ResponseResult.responseError("400", "获取勋章列表失败", null);
    }


    //参与活动记录保存
    @Override
    public ResponseResult<String> campaignRecord(CampaignRecordRequest campaignRecord) {
        try {
            //限制campaignId：2505SSUTC超过5次不落库
            if (ACTIVITY_2505SSUTC.equals(campaignRecord.getCampaignId())){
                Map<String, Object> queryMap = new HashMap<>();
                queryMap.put("memberId", campaignRecord.getMemberId());
                queryMap.put("campaignId", campaignRecord.getCampaignId());
                queryMap.put("activityId", campaignRecord.getActivityId());
                String querySql = " select id from " + ModelConstants.MEMBER_CAMPAIGN + " where memberId =:memberId and campaignId =:campaignId and activityId =:activityId limit 5 ";
                List<Map<String, String>> resultList = DataApiUtil.queryAnalysis(querySql, queryMap);
                if (CollectionUtils.isNotEmpty(resultList)) {
                    log.info("限制campaignId：2505SSUTC...resultList:{}", JSONObject.toJSONString(resultList.size()));
                    if (resultList.size() == 5){
                        return ResponseResult.responseSuccess(null);
                    }
                }
            }
            //限制campaignId：2505COSTAUTC超过3次不落库
            if (ACTIVITY_2505COSTAUTC.equals(campaignRecord.getCampaignId())){
                Map<String, Object> queryMap = new HashMap<>();
                queryMap.put("memberId", campaignRecord.getMemberId());
                queryMap.put("campaignId", campaignRecord.getCampaignId());
                String querySql = " select id from " + ModelConstants.MEMBER_CAMPAIGN + " where memberId =:memberId and campaignId =:campaignId limit 3";
                List<Map<String, String>> resultList = DataApiUtil.queryAnalysis(querySql, queryMap);
                if (CollectionUtils.isNotEmpty(resultList)) {
                    log.info("限制campaignId：2505COSTAUTC...resultList:{}", JSONObject.toJSONString(resultList.size()));
                    if (resultList.size() == 3){
                        return ResponseResult.responseSuccess(null);
                    }
                }
            }
            //保存参与活动记录
            Map<String, Object> map = BeanUtil.beanToMap(campaignRecord);
            Map memberMap = new HashMap();
            memberMap.put("id", campaignRecord.getMemberId());
            map.put("member", memberMap);
            Date date = DateHelper.parseDateGracefully(campaignRecord.getCreateTime());
            map.put("createTime", DateHelper.formatZonedDateTime(DateHelper.addHours(date,-8) ,null));
            map.put("lastSync", ZonedDateTime.now());
            log.info("写入模型campaignRecordMap:{}", JSON.toJSONString(map));
            DMLResponse upsert = dataapiHttpSdk.upsert(ModelConstants.MEMBER_CAMPAIGN, campaignRecord.getId(), map, false);
            log.info("medalRecord...upsert:{}", JSON.toJSONString(campaignRecord));
            memberCampaignProducer.sendMemberCampaignData(campaignRecord);
            return ResponseResult.responseSuccess(null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    //2.49.查询会员已获取勋章列表
    @Override
    public ResponseResult<List<MemberMedalListResponse>> getMemberMadelList(String memberId) {
        try {
            //loyalty-facade/v1/medal/query/ 分页勋章查询
            JSONArray jsonArray = loyaltyFacade.medalQuery(ConfigurationCenterUtil.medalHierarchyId, ConfigurationCenterUtil.planId, memberId, 0, 500);
            if (ObjectUtil.isEmpty(jsonArray)) {
                return ResponseResult.responseError("400", "未获取到会员获取勋章列表数据", null);
            }
            JSONArray json = new JSONArray();
            for (int i = 0; i < jsonArray.size(); i++) {
                MemberMedalListResponse memberMedalListResponse = new MemberMedalListResponse();
                String outputDateTime= DateHelper.transferString2Date1(jsonArray.getJSONObject(i).getString("effectDate"));
                memberMedalListResponse.setEffectDate(outputDateTime);
                memberMedalListResponse.setMedalDefinitionId(jsonArray.getJSONObject(i).getInteger("medalDefinitionId"));
                memberMedalListResponse.setMedalDefinitionName(jsonArray.getJSONObject(i).getString("medalDefinitionName"));
                json.add(memberMedalListResponse);
            }
            return ResponseResult.responseSuccess(json);
        } catch (Exception e) {
            log.error("查询会员已获取勋章列表异常:{}", e);
            return new ResponseResult<>(ResponseCodeEnum.OPENAPI_FAILED.getCode(), ResponseCodeEnum.OPENAPI_FAILED.getMsg());
        }
    }


    //2.51.查询勋章详情
    @Override
    public ResponseResult<MedalDetailsResponse> getMedalDetails(Integer medalDefinitionId,String memberId) throws ParseException {
        // 1. 查询主数据表
        StopWatch sw = new StopWatch("getMedalDetails");
        String getMedalByIdSql = String.format( "select  medalDefinitionName, medalCounts as counts from  %s   where  medalDefinitionId = '%s' ",ModelConstants.MEDAL_RULE,medalDefinitionId);
        try {
            sw.start();
            BaseResponse execute = dataapiHttpSdk.execute(getMedalByIdSql, Collections.emptyMap());
            if (CollectionUtils.isEmpty(execute.getData())) {
                return ResponseResult.responseError("400", "查询勋章详情失败", null);
            }
            List<Map<String, Object>> dataList = execute.getData();
            MedalDetailsResponse medalDetailsResponse = new MedalDetailsResponse();
            medalDetailsResponse.setMedalDefinitionName(String.valueOf(dataList.get(0).get("medalDefinitionName")));
            medalDetailsResponse.setMedalDefinitionId(medalDefinitionId);
            int total  =  (Integer) dataList.get(0).get("counts");
            medalDetailsResponse.setMedalTotal(total);
            // 查询这个会员对应的勋章发放时间
            String getMedalByMemberIdSql = String.format("select memberId, medalHierarchyId, effectDate from %s  where medalDefinitionId = '%s' and memberId ='%s'  ",ModelConstants.MEMBER_MEDAL,medalDefinitionId,memberId);
            BaseResponse memberMedalResponse = dataapiHttpSdk.execute(getMedalByMemberIdSql, Collections.emptyMap());
            List<Map<String, Object>> memberMedalData = memberMedalResponse.getData();
            if (CollectionUtils.isNotEmpty(memberMedalData)) {
                String effectDate = DateHelper.formatDateStr(memberMedalData.get(0).get("effectDate").toString(), DateHelper.DS_DATE_FORMAT);
                medalDetailsResponse.setEffectDate(effectDate);
            }
            sw.stop();
            log.info("getMedalDetail...sw: {}", sw.prettyPrint(TimeUnit.MILLISECONDS));
            return ResponseResult.responseSuccess(medalDetailsResponse);
        } catch (Exception e) {
            log.error("查询勋章详情时发生异常", e);
            return ResponseResult.responseError("500", "系统错误", null);
        }
    }


    //2.52.查询勋章等级
    @Override
    public ResponseResult<MedalGradeResponse> grade(String memberId) {
        JSONArray gradeJsonArray;
        try {
            log.info("grade...memberId:{},medalGradeId:{}", memberId, ConfigurationCenterUtil.medalGradeId);
            gradeJsonArray = loyaltyFacade.grade(memberId,  ConfigurationCenterUtil.medalGradeId);
            log.info("grade...gradeJsonArray:{}", gradeJsonArray);
        } catch (Exception e) {
            log.error("调用忠诚度查询会员等级异常：{}", e);
            return new ResponseResult<>(ResponseCodeEnum.OPENAPI_FAILED.getCode(), ResponseCodeEnum.OPENAPI_FAILED.getMsg());
        }
        MedalGradeResponse medalGradeResponse = new MedalGradeResponse();
        Integer medalCurrentGradeDefinitionId = 0;
        //当前勋章等级Id
        if (CollectionUtils.isNotEmpty(gradeJsonArray)) {
            //当前勋章等级Id
            medalCurrentGradeDefinitionId = gradeJsonArray.getJSONObject(0).getInteger("currentGradeDefinitionId");
            medalGradeResponse.setMedalCurrentGradeDefinitionId(medalCurrentGradeDefinitionId);
            //当前勋章等级名称
            medalGradeResponse.setMedalCurrentGradeName(gradeJsonArray.getJSONObject(0).getString("gradeDefinitionName"));
            //勋章等级生效时间
            String effectDate = gradeJsonArray.getJSONObject(0).getString("effectDate");
            String outputDateTime= DateHelper.transferString2Date1(effectDate);
            medalGradeResponse.setEffectDate(outputDateTime);
        }
        //已获得勋章总数 需要调用产品的勋章列表接口
        JSONArray jsonArray;
        try {
            log.info("grade...medalHierarchyId:{},planId:{},memberId:{}", ConfigurationCenterUtil.medalHierarchyId, ConfigurationCenterUtil.planId, memberId);
            jsonArray = loyaltyFacade.medalQuery(ConfigurationCenterUtil.medalHierarchyId, ConfigurationCenterUtil.planId, memberId, 0, 500);
            log.info("grade...jsonArray:{}", jsonArray);
        } catch (Exception e) {
            log.error("调用忠诚度分页查询会员勋章异常：{}", e);
            return new ResponseResult<>(ResponseCodeEnum.OPENAPI_FAILED.getCode(), ResponseCodeEnum.OPENAPI_FAILED.getMsg());
        }
        if (ObjectUtil.isEmpty(jsonArray)) {
            medalGradeResponse.setMedalTotal(0);
        }
        //已获取勋章总数
        int medalTotal = jsonArray.size();
        medalGradeResponse.setMedalTotal(medalTotal);
        //查询勋章等级主数据模型
        String getMedalGradeSql = "select id,medalDefinitionName,medalDefinitionId,countStart,medalSort from " + ModelConstants.MEDAL_RULE + " where typeData = 'medalGradeRule'";
        log.info("grade...getMedalGradeSql:{}", getMedalGradeSql);
        BaseResponse execute = dataapiHttpSdk.execute(getMedalGradeSql, Collections.emptyMap());
        if (CollectionUtils.isNotEmpty(execute.getData())) {
            log.info("grade...execute.getData:{}", JSONObject.toJSONString(execute.getData()));
            ArrayList<MedalGradeResponse.MedalLevelThreshold> list = new ArrayList<>();
            List<Map<String, Object>> executeList = execute.getData();
            for (int i = 0; i < executeList.size(); i++) {
                MedalGradeResponse.MedalLevelThreshold medalLevelThreshold = new MedalGradeResponse.MedalLevelThreshold();
                Map<String, Object> map = executeList.get(i);
                int currentGradeDefinitionId = Integer.parseInt(map.get("medalDefinitionId").toString());
                medalLevelThreshold.setMedalGradeDefinitionId(currentGradeDefinitionId);
                medalLevelThreshold.setMedalCurrentGradeName(map.get("medalDefinitionName").toString());
                medalLevelThreshold.setMedalLevelThresholdValue(Integer.valueOf(map.get("countStart").toString()));
                list.add(medalLevelThreshold);
                medalGradeResponse.setMedalLevelThreshold(list);
            }
            //根据会员当前等级查询下一等级门槛数据
            //距离下勋章等级需要勋章数 用自建模型规则的等级门槛countStart字段-当前勋章等级已获得勋章总数
            String getNextGradeSql;
            if (ObjectUtil.isNotEmpty(gradeJsonArray)) {
                getNextGradeSql = String.format("select countStart from " + ModelConstants.MEDAL_RULE + " where medalDefinitionId =%s and typeData ='medalGradeRule' ", medalCurrentGradeDefinitionId + 1);
            } else {
                getNextGradeSql = String.format("select countStart from " + ModelConstants.MEDAL_RULE + " where medalSort ='1' and typeData ='medalGradeRule' ");
            }
            log.info("grade...getNextGradeSql:{}", getNextGradeSql);
            BaseResponse nextGradeResponse = dataapiHttpSdk.execute(getNextGradeSql, Collections.emptyMap());
            log.info("grade...nextGradeResponse:{}", JSONObject.toJSONString(nextGradeResponse));
            if (CollectionUtils.isNotEmpty(nextGradeResponse.getData())) {
                List<Map<String, Object>> nextGradeData = nextGradeResponse.getData();
                int countStart = Integer.parseInt(nextGradeData.get(0).get("countStart").toString());
                medalGradeResponse.setMedalRequired(countStart - medalTotal);
            } else {
                medalGradeResponse.setMedalRequired(0);
            }
        }
        return ResponseResult.responseSuccess(medalGradeResponse);
    }

    //2.50.查询当前任务进度
    @Override
    public ResponseResult<List<MedalProgressResponse>> progress(String memberId) {
        String getProgressSql = "select  memberId,medalDefinitionId,medalDefinitionName,taskProgress,taskThresholdValue  from " + ModelConstants.MEDAL_TASK + " where isFinished='N'  and   memberId = '" + memberId + "' ";
        log.info("progress...getProgressSql:{}", getProgressSql);
        BaseResponse progressResponse = dataapiHttpSdk.execute(getProgressSql, Collections.emptyMap());
        if (CollectionUtils.isEmpty(progressResponse.getData())) {
            return ResponseResult.responseError("400", "未查询到勋章进度", null);
        }
        List<Map<String, Object>> progressData = progressResponse.getData();
        ArrayList<Object> list = new ArrayList<>();
        for (Map map : progressData) {
            MedalProgressResponse medalProgressResponse = new MedalProgressResponse();
            medalProgressResponse.setMedalDefinitionId(Integer.valueOf(map.get("medalDefinitionId").toString()));
            medalProgressResponse.setMedalDefinitionName(map.get("medalDefinitionName").toString());
            medalProgressResponse.setTaskProgress(Integer.valueOf(map.get("taskProgress").toString()));
            medalProgressResponse.setTaskThresholdValue(Integer.valueOf(map.get("taskThresholdValue").toString()));
            list.add(medalProgressResponse);
        }
        return ResponseResult.responseSuccess(list);
    }


    //2.53.勋章发放
    @Override
    public ResponseResult<String> obtain(MedalObtainRequest medalObtainRequest) {
        Integer medalDefinitionId = 0;
        String medalType = medalObtainRequest.getMedalType();
        if (MedalObtainTypeEnum.REGISTER_TYPE.getCode().equals(medalType)) {
            medalDefinitionId = ConfigurationCenterUtil.medalRegisterId;
        } else if (MedalObtainTypeEnum.CNY_TYPE.getCode().equals(medalType)) {
            medalDefinitionId = ConfigurationCenterUtil.medalCNYId;
        } else {
            medalDefinitionId=Integer.valueOf(medalType);
        }
        String memberId = medalObtainRequest.getMemberId();
        //查询会员是否存在
        String getMemberSql = String.format("select id from  " + ModelConstants.Member + " where memberId = '%s' ", memberId);
        log.info("obtain...getMemberSql:{}", getMemberSql);
        BaseResponse memberExe = dataapiHttpSdk.execute(getMemberSql, Collections.emptyMap());
        if (ObjectUtil.isEmpty(memberExe.getData())) {
            return ResponseResult.responseError("400", "会员不存在", memberId);
        }
        //查询该会员是否已存在注册勋章
        String getMemberRegisterMedalSql = String.format("select id from  " + ModelConstants.MEMBER_MEDAL + " where memberId = '%s' and medalDefinitionId='%s' and disabled=false ", memberId, medalDefinitionId);
        log.info("obtain...getMemberRegisterMedalSql:{}", getMemberRegisterMedalSql);
        BaseResponse execute = dataapiHttpSdk.execute(getMemberRegisterMedalSql, Collections.emptyMap());
        if (ObjectUtil.isNotEmpty(execute.getData())) {
            return ResponseResult.responseError("400", "该勋章已存在", memberId);
        }
        //调用内部发放勋章接口
        try {
            medalObtainRequest.setMedalHierarchyId(ConfigurationCenterUtil.medalHierarchyId);
            medalObtainRequest.setMedalDefinitionId(medalDefinitionId);
            medalObtainRequest.setChannelType("KO_MP");
            log.info("请求忠诚度接口发放勋章入参medalObtainRequest:{}", JSONObject.toJSONString(medalObtainRequest));
            JSONObject jsonObject = loyaltyFacade.medalObtain(medalObtainRequest);
            if (ObjectUtil.isNotEmpty(jsonObject)) {
                log.info("请求忠诚度接口发放勋章返回:{}", jsonObject);
                return ResponseResult.responseError("500", "发放勋章失败", null);
            }
            return ResponseResult.responseSuccess(null);
        } catch (Exception e) {
            log.error("请求忠诚度接口发放勋章异常:{}", e);
            return new ResponseResult<>(ResponseCodeEnum.OPENAPI_FAILED.getCode(), ResponseCodeEnum.OPENAPI_FAILED.getMsg());
        }
    }




    //根据参与活动计算勋章进度
//    public void medalProgressByCampaignConsumer(MedalProgressRequest medalProgressRequest){
//        log.info("medalProgressByCampaignConsumer...medalProgressRequest:{}", JSONObject.toJSONString(medalProgressRequest));
//        String memberId = medalProgressRequest.getMemberId();
//        Integer getMedalDefinitionId = medalProgressRequest.getMedalDefinitionId();
//        String campaignId = medalProgressRequest.getCampaignId();
//        BaseResponse medalRuleResponse = getBaseResponse(getMedalDefinitionId, memberId);
//        if (medalRuleResponse == null) return;
//        List<Map<String, Object>> medalRuleData = medalRuleResponse.getData();
//        //勋章id对应的门槛
//        int countStart = Integer.parseInt(medalRuleData.get(0).get("countStart").toString());
//        Integer medalDefinitionId = Integer.valueOf(medalRuleData.get(0).get("medalDefinitionId").toString());
//        String medalDefinitionName = medalRuleData.get(0).get("medalDefinitionName").toString();
//        //查询勋章规则类型 counts=累计参与次数  weeks=连续签到周 months=累计参与月数
//        String ruleType = medalRuleData.get(0).get("ruleType").toString();
//
//        //当前进度
//        int taskProgress =0;
//        //根据勋章规则进行不同sql查询
//        if ("counts".equals(ruleType)){
//            log.info("开始计算会员参与活动次数数据");
//            //查询会员参与活动次数
//            String getMemberCampaignByCountSql = String.format("select campaignId , count(1)  as count  from " + ModelConstants.MEMBER_CAMPAIGN + " where    memberId = '%s' and campaignId = '%s' " ,memberId,campaignId);
//            log.info("medalProgressByCampaignConsumer...getMemberCampaignByCountSql:{}", getMemberCampaignByCountSql);
//            BaseResponse getMemberCampaignByCountResponse = dataapiHttpSdk.execute(getMemberCampaignByCountSql, Collections.emptyMap());
//            if (CollectionUtils.isNotEmpty(getMemberCampaignByCountResponse.getData())) {
//                List<Map<String, Object>> getMemberCampaignByCountData = getMemberCampaignByCountResponse.getData();
//                taskProgress = Integer.parseInt(getMemberCampaignByCountData.get(0).get("count").toString());
//            }
//        }
//
//        if ("weeks".equals(ruleType)){
//            log.info("开始计算连续4周会员日勋章数据");
//            // 查询会员连续签到周数
//            DateTime endTime = DateUtil.offsetHour(DateUtil.endOfWeek(new Date()), -8);
//            String endTimes = DateUtil.format(endTime, DateHelper.DATE_ZONE);  // 格式化为指定格式
//            log.info("endTimes:{}", endTimes);
//
//            DateTime oneTime = DateUtil.offsetWeek(endTime, -1);  // 向前推4周
//            String oneTimes = DateUtil.format(oneTime, DateHelper.DATE_ZONE);
//            log.info("oneTimes:{}", oneTimes);
//
//            DateTime twoTime = DateUtil.offsetWeek(endTime, -2);  // 向前推4周
//            String twoTimes = DateUtil.format(twoTime, DateHelper.DATE_ZONE);
//            log.info("twoTimes:{}", twoTimes);
//
//            DateTime threeTime = DateUtil.offsetWeek(endTime, -3);  // 向前推4周
//            String threeTimes = DateUtil.format(threeTime, DateHelper.DATE_ZONE);
//            log.info("threeTimes:{}", threeTimes);
//
//            DateTime fourTime = DateUtil.offsetWeek(endTime, -4);  // 向前推4周
//            String fourTimes = DateUtil.format(fourTime, DateHelper.DATE_ZONE);
//            log.info("fourTimes:{}", fourTimes);
//
//            // 查询第一周数据
//            String getMemberCampaignByWeekOneSql ="select id from " + ModelConstants.MEMBER_CAMPAIGN + " where    memberId = '"+memberId+"' and campaignId = '"+campaignId+"'  and createTime  BETWEEN  '"+oneTimes+"' and '"+endTimes+"' ";
//            log.info("medalProgressByCampaignConsumer...getMemberCampaignByWeekOneSql:{}", getMemberCampaignByWeekOneSql);
//            BaseResponse getMemberCampaignByWeekOneResponse = dataapiHttpSdk.execute(getMemberCampaignByWeekOneSql, Collections.emptyMap());
//            if (CollectionUtils.isNotEmpty(getMemberCampaignByWeekOneResponse.getData())) {
//                List<CampaignRecordResponse> getMemberCampaignByWeekOneData = getMemberCampaignByWeekOneResponse.getData();
//                log.info("getMemberCampaignByWeekOneData:{}", JSONObject.toJSONString(getMemberCampaignByWeekOneData));
//                taskProgress = 1;
//                // 查询第二周数据
//                String getMemberCampaignByWeekTwoSql ="select id from " + ModelConstants.MEMBER_CAMPAIGN + " where    memberId = '"+memberId+"' and campaignId = '"+campaignId+"'  and createTime  BETWEEN  '"+twoTimes+"' and '"+oneTimes+"' ";
//                log.info("medalProgressByCampaignConsumer...getMemberCampaignByWeekTwoSql:{}", getMemberCampaignByWeekTwoSql);
//                BaseResponse getMemberCampaignByWeekTwoResponse = dataapiHttpSdk.execute(getMemberCampaignByWeekTwoSql, Collections.emptyMap());
//                if (CollectionUtils.isNotEmpty(getMemberCampaignByWeekTwoResponse.getData())) {
//                    List<CampaignRecordResponse> getMemberCampaignByWeekTwoData = getMemberCampaignByWeekTwoResponse.getData();
//                    log.info("getMemberCampaignByWeekTwoData:{}", JSONObject.toJSONString(getMemberCampaignByWeekTwoData));
//                    taskProgress = 2;
//                    // 查询第三周数据
//                    String getMemberCampaignByWeekThreeSql ="select id from " + ModelConstants.MEMBER_CAMPAIGN + " where    memberId = '"+memberId+"' and campaignId = '"+campaignId+"'  and createTime  BETWEEN  '"+threeTimes+"' and '"+twoTimes+"' ";
//                    log.info("medalProgressByCampaignConsumer...getMemberCampaignByWeekThreeSql:{}", getMemberCampaignByWeekThreeSql);
//                    BaseResponse getMemberCampaignByWeekThreeResponse = dataapiHttpSdk.execute(getMemberCampaignByWeekThreeSql, Collections.emptyMap());
//                    if (CollectionUtils.isNotEmpty(getMemberCampaignByWeekThreeResponse.getData())) {
//                        List<CampaignRecordResponse> getMemberCampaignByWeekThreeData = getMemberCampaignByWeekThreeResponse.getData();
//                        log.info("getMemberCampaignByWeekThreeData:{}", JSONObject.toJSONString(getMemberCampaignByWeekThreeData));
//                        taskProgress = 3;
//                        // 查询第四周数据
//                        String getMemberCampaignByWeekFourSql ="select id from " + ModelConstants.MEMBER_CAMPAIGN + " where    memberId = '"+memberId+"' and campaignId = '"+campaignId+"'  and createTime  BETWEEN  '"+fourTimes+"' and '"+threeTimes+"' ";
//                        log.info("medalProgressByCampaignConsumer...getMemberCampaignByWeekFourSql:{}", getMemberCampaignByWeekFourSql);
//                        BaseResponse getMemberCampaignByWeekFourResponse = dataapiHttpSdk.execute(getMemberCampaignByWeekFourSql, Collections.emptyMap());
//                        if (CollectionUtils.isNotEmpty(getMemberCampaignByWeekFourResponse.getData())) {
//                            List<CampaignRecordResponse> getMemberCampaignByWeekFourData = getMemberCampaignByWeekFourResponse.getData();
//                            log.info("getMemberCampaignByWeekFourData:{}", JSONObject.toJSONString(getMemberCampaignByWeekFourData));
//                            taskProgress = 4;
//                        }
//                    }
//                }
//            }
//        }
//
//        if("months".equals(ruleType)){
//            //查询会员累计签到月数
//            String  getMemberCampaignByMonthSql="select DATE_FORMAT(add_hours(createTime, 8),'%Y-%m') yearAndMonth ,  count(1)   count  from " + ModelConstants.MEMBER_CAMPAIGN + " where    memberId = '"+memberId+"' and campaignId = '"+campaignId+"'  group by  DATE_FORMAT(add_hours(createTime, 8),'%Y-%m') " ;
//            log.info("medalProgressByCampaignConsumer...getMemberCampaignByMonthSql:{}", getMemberCampaignByMonthSql);
//            BaseResponse getMemberCampaignByMonthResponse = dataapiHttpSdk.execute(getMemberCampaignByMonthSql, Collections.emptyMap());
//            if (CollectionUtils.isNotEmpty(getMemberCampaignByMonthResponse.getData())) {
//
//            List<Map<String, Object>> getMemberCampaignByMonthData = getMemberCampaignByMonthResponse.getData();
//            taskProgress = getMemberCampaignByMonthData.size();
//        }
//        }
//        upsertMedalTask(memberId, medalDefinitionId, medalDefinitionName, taskProgress, countStart);
//    }


//
//
//    /**
//     * 根据消耗积分计算勋章进度
//     * @param medalProgressRequest
//     */
//    @Override
//    public void medalProgressByPointRecordConsumer(MedalProgressRequest medalProgressRequest) {
//        log.info("medalProgressByPointRecordConsumer...medalProgressRequest:{}", JSONObject.toJSONString(medalProgressRequest));
//        String memberId = medalProgressRequest.getMemberId();
//        Integer medalDefinitionId = medalProgressRequest.getMedalDefinitionId();
//        BaseResponse medalRuleResponse = getBaseResponse(medalDefinitionId, memberId);
//        if (medalRuleResponse == null) return;
//        List<Map<String, Object>> medalRuleData = medalRuleResponse.getData();
//        //勋章id对应的门槛
//        int countStart = Integer.parseInt(medalRuleData.get(0).get("countStart").toString());
//        String medalDefinitionName = medalRuleData.get(0).get("medalDefinitionName").toString();
//
//        //查询积分
//        int  pointRecordChangePoint =0;
//        String getPointRecordSql = String.format("select sum(changePoint) as changePoint from "+ModelConstants.POINT_RECORD+" where changePoint < 0  and created >= '%s' and memberId = '%S' and status not in ('FROZEN','EXPIRE')  and recordType  not in ('REVERSE_DEDUCT','RECALCULATE')  " ,medalProgressRequest.getCreated(),medalProgressRequest.getMemberId());
//        log.info("medalProgressByPointRecordConsumer...getPointRecordSql:{}",getPointRecordSql);
//        BaseResponse pointRecordExe = dataapiHttpSdk.execute(getPointRecordSql, Collections.emptyMap());
//        if (CollectionUtils.isEmpty(pointRecordExe.getData())) {
//            log.error("medalProgress...memberId:{},medalDefinitionId:{}", memberId,medalDefinitionId);
//        }else {
//            List<Map> data = pointRecordExe.getData();
//            if (ObjectUtil.isNotEmpty(data.get(0).get("changePoint"))){
//                String points = data.get(0).get("changePoint").toString();
//                pointRecordChangePoint = (int) Double.parseDouble(points);
//            }
//        }
//        log.info("medalProgressByPointRecordConsumer...pointRecordChangePoint:{}",pointRecordChangePoint);
//        int  pointRecordReverseSendPoint =0;
//        String  getPointRecordReverseSendSql =  String.format("select sum(changePoint) as changePoint from "+ModelConstants.POINT_RECORD+" where changePoint >  0  and created >= '%s' and memberId = '%S' and   recordType ='REVERSE_SEND'  " ,medalProgressRequest.getCreated(),medalProgressRequest.getMemberId());
//        log.info("medalProgressByPointRecordConsumer...getPointRecordReverseSendSql:{}",getPointRecordReverseSendSql);
//        BaseResponse pointRecordReverseSendExe = dataapiHttpSdk.execute(getPointRecordReverseSendSql, Collections.emptyMap());
//        if (CollectionUtils.isEmpty(pointRecordReverseSendExe.getData())) {
//            log.error("medalProgress...memberId:{},medalDefinitionId:{}", memberId,medalDefinitionId);
//        }else {
//            List<Map> data = pointRecordReverseSendExe.getData();
//            if(ObjectUtil.isNotEmpty(data.get(0).get("changePoint"))){
//                String points = data.get(0).get("changePoint").toString();
//                pointRecordReverseSendPoint = (int) Double.parseDouble(points);
//            }
//        }
//        log.info("medalProgressByPointRecordConsumer...pointRecordReverseSendPoint:{}",pointRecordReverseSendPoint);
//        //当前进度
//        int taskProgress = Math.abs(pointRecordChangePoint+pointRecordReverseSendPoint);
//        upsertMedalTask(memberId,medalDefinitionId,medalDefinitionName,taskProgress,countStart);
//    }
//
//
//    private static BaseResponse getBaseResponse(Integer getMedalDefinitionId, String memberId) {
//        //根据入参medalDefinitionId查询对应规则主数据
//        String getMedalRuleSql = String.format("select medalDefinitionId, medalDefinitionName, countStart,ruleType from " + ModelConstants.MEDAL_RULE + " where medalDefinitionId =%s and typeData ='medalRule' ", getMedalDefinitionId);
//        log.info("medalProgress...getMedalRuleSql:{}", getMedalRuleSql);
//        BaseResponse medalRuleResponse = dataapiHttpSdk.execute(getMedalRuleSql, Collections.emptyMap());
//        if (CollectionUtils.isEmpty(medalRuleResponse.getData())) {
//            log.error("medalProgress...memberId:{},medalDefinitionId:{}", memberId, getMedalDefinitionId);
//            return null;
//        }
//        log.info("getBaseResponse...medalRuleResponse:{}", JSONObject.toJSONString(medalRuleResponse.getData()));
//        return medalRuleResponse;
//    }
//
//
//    private static void upsertMedalTask(String memberId, Integer medalDefinitionId, String medalDefinitionName, int taskProgress, int countStart) {
//        log.info("开始写入勋章进度模型...memberId:{},medalDefinitionId:{},medalDefinitionName:{},taskProgress:{},countStart:{}",memberId,medalDefinitionId,medalDefinitionName,taskProgress,countStart);
//        //写入任务进度模型
//        JSONObject jsonObject = new JSONObject();
//        String id = MD5Util.encrypt(memberId + medalDefinitionId);
//        jsonObject.put("id", id);
//        jsonObject.put("memberId", memberId);
//        jsonObject.put("medalDefinitionId", medalDefinitionId);
//        jsonObject.put("medalDefinitionName", medalDefinitionName);
//        jsonObject.put("taskProgress", taskProgress);
//        jsonObject.put("taskThresholdValue", countStart);
//        jsonObject.put("lastSync", ZonedDateTime.now());
//        log.info("medalProgress...upsert:{}", jsonObject);
//        dataapiHttpSdk.upsert(ModelConstants.MEDAL_TASK, id, jsonObject, false);
//    }
}
