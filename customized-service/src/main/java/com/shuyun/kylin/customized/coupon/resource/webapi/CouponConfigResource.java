package com.shuyun.kylin.customized.coupon.resource.webapi;

import com.shuyun.kylin.customized.coupon.dto.CouponConfigDto;
import com.shuyun.kylin.customized.coupon.service.ICouponService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@Tag(name = "第三方优惠券配置")
@RestController
@RequestMapping("/admin/coupon")
public class CouponConfigResource {

    @Autowired
    private ICouponService couponService;

    @PostMapping("config")
    @ResponseBody
    public void initCouponSendConfig(@Valid @RequestBody CouponConfigDto couponConfigDto) {
        couponService.initCouponConfig(couponConfigDto);
    }

}
