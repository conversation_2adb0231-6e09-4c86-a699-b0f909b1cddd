package com.shuyun.kylin.customized.base.util;

public class RedisKeyUtil {

    private static final String MARK = "#";

    public static String joinString(String ... params) {
        StringBuilder key = new StringBuilder();

        for (int i = 0; i < params.length; i++) {
            key.append(params[i]);

            if (i < params.length - 1) {
                key.append(RedisKeyUtil.MARK);
            }
        }

        return key.toString();
    }

}
