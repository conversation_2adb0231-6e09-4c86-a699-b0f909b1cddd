package com.shuyun.kylin.customized.customer.service.impl;

import com.shuyun.calc.service.online.job.task.JobTaskSupport;
import com.shuyun.calc.service.online.job.task.OnlineJobTaskCalculator;
import com.shuyun.calc.service.online.job.task.OnlineJobTaskCalculatorHelper;
import com.shuyun.cdp.tagExport.enums.TaskType;
import com.shuyun.cdp.tagExport.request.ExportTask;
import com.shuyun.cdp.tagExport.vo.AzureExportAddress;
import com.shuyun.cdp.tagExport.vo.DefaultExportTags;
import com.shuyun.cdp.tags.response.OriginTagsResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.enums.ResponseCodeEnum;
import com.shuyun.kylin.customized.base.feign.client.MemberCdpClient;
import com.shuyun.kylin.customized.base.repository.BaseDsRepositoryImpl;
import com.shuyun.kylin.customized.base.util.DataapiSdkUtil;
import com.shuyun.kylin.customized.base.util.DateHelper;
import com.shuyun.kylin.customized.customer.dm.CustomerRepository;
import com.shuyun.kylin.customized.customer.dto.CommonTagSingleResponse;
import com.shuyun.kylin.customized.customer.dto.CustomerTagDto;
import com.shuyun.kylin.customized.customer.service.CustomerService;
import com.shuyun.pip.component.json.JsonUtils;
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/2/28 14:33
 * @description
 */
@Service
@Slf4j
public class CustomerServiceImpl implements CustomerService {
    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    protected BaseDsRepositoryImpl baseDsRepositoryImpl;

    @Autowired
    private SubTagServiceImpl subTagServiceImpl;
    @Autowired
    MemberCdpClient memberCdpClient;

//    final static DataapiHttpSdk sdk = DataapiSdkUtil.getDataapiHttpSdk();

    @Override
    public ResponseResult customerTag(CustomerTagDto customerTagDto) {
        long startTime = System.currentTimeMillis();
        String customerId = "";
        String channelType = "";
        try {
            Map<String, Object> data = new HashMap<>();
            if (!StringUtils.isEmpty(customerTagDto.getMobile())) {
                // 1 手机号不为空时，通过手机号查询消费者用户信息
                data = customerRepository.queryCustomer(customerTagDto.getMobile());
            } else {
                // 2 手机号为空时，通过openId + uniouId 查询消费者用户信息
                data = customerRepository.queryCustomer(customerTagDto.getOpenId(), customerTagDto.getUnionId());
            }
            if (!data.isEmpty()){
                customerId = data.get("firstCustomerNo").toString();
                channelType = data.get("firstChannelType").toString();
            }
            Map map = new HashMap<>();
            boolean flag = false;
            if (!StringUtils.isEmpty(customerId)) {
                log.info("消费者渠道为:{},消费者原始id为:{}", channelType, customerId);
                String occId = subTagServiceImpl.getOccIdByChannelId(customerTagDto.getMemberType(), channelType, customerId);
                log.info("消费者occId:{}", occId);
                if (StringUtils.isEmpty(occId)) {
                    log.warn("找不到occId,校验可能存在问题");
                }
                DataapiHttpSdk sdk = DataapiSdkUtil.getDataapiHttpSdk();
                OnlineJobTaskCalculator calc = OnlineJobTaskCalculatorHelper.onlineSegmentTaskCalculator((sql, parameters) -> {
                    try (DataapiWebSocketSdk ws = sdk.asDataapiWebSocketSdk()) {
                        UserContextThreadSafe.getInstance();
//                        UserContextThreadSafe.setOlapModeContext(false);
                        if (JobTaskSupport.Context.olapForce()) {
                            UserContextThreadSafe.getInstance().setOlapMode(true);
                            UserContextThreadSafe.getInstance().setOlapForce(true);
                        }
                        log.info("sql:{}", sql);
                        log.info("parameters:{}", parameters);
                        ws.open();
                        return ws.execute(sql, parameters).getData();
                    } finally {
                        UserContextThreadSafe.getInstance();
                        UserContextThreadSafe.setOlapModeContext(false);
                    }
                });
                log.info("标签校验cdp-mgmt耗时P1：{}", System.currentTimeMillis() - startTime);
                startTime = System.currentTimeMillis();
                flag = subTagServiceImpl.subVerify(customerTagDto.getTagId(), customerId, channelType, customerTagDto.getMemberType(), occId, calc);
//                OriginTagRequest originTagRequest = new OriginTagRequest();
//                originTagRequest.setOrigin("FRONT");
//                originTagRequest.setMemberType(customerTagDto.getMemberType());
//                originTagRequest.setChannelType(ModelConstants.COSMOS_CHANNEL);
//                originTagRequest.setOriginIds(Lists.newArrayList(koId));
//                originTagRequest.setSelectTags(Lists.newArrayList(customerTagDto.getTagId()));
//                // 调用CDP产品接口
//                Long brandId = Long.valueOf(ModelConstants.BRAND);
//                log.info("cdp标签接口入参:{}", JsonUtils.toJson(originTagRequest));
//                OriginTagsResponse originTagsResponse = CdpRestClient.INSTANCE.getTagsByOriginId(brandId, originTagRequest);
//                log.info("cdp标签接口出参:{}", JsonUtils.toJson(originTagsResponse));
//                if (originTagsResponse.getData().isEmpty()) {
//                    map.put("flag", false);
//                }
//                map.put("flag", true);
            }
//            else {
//                map.put("flag", false);
//            }
            map.put("flag", flag);
            log.info("标签校验cdp-mgmt耗时P2：{}", System.currentTimeMillis() - startTime);
            return new ResponseResult(ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getMsg(), map);
        } catch (Exception e) {
            log.error("校验用户标签异常:{}", e);
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED.getCode(), ResponseCodeEnum.OPENAPI_FAILED.getMsg());
        }
    }

    @Override
    public CommonTagSingleResponse submitCdpTagTask() {
        ExportTask exportTask = new ExportTask();
        exportTask.setTaskId(DateHelper.localDate(LocalDate.now()));
        exportTask.setTaskType(TaskType.valueOf(ModelConstants.TAG_EXPORT_TASK_TYPE));
        exportTask.setDomainCode(ModelConstants.MEMBER_TYPE);
        exportTask.setChannelType(ModelConstants.COSMOS_CHANNEL);
        DefaultExportTags exportTags = new DefaultExportTags();
        exportTags.setType(ModelConstants.TAG_EXPORT_EXPORT_TAGS_TYPE);
        AzureExportAddress exportAddress = new AzureExportAddress();
        exportAddress.setType(ModelConstants.TAG_EXPORT_ADDRESS_TYPE);
        exportAddress.setConnection(ModelConstants.TAG_EXPORT_ADDRESS_CONNECTION);
        exportAddress.setContainName(ModelConstants.TAG_EXPORT_ADDRESS_CONTAIN_NAME);
        exportAddress.setBaseDir(ModelConstants.TAG_EXPORT_ADDRESS_BASE_DIR);
        exportTask.setExportTags(exportTags);
        exportTask.setExportAddress(exportAddress);
        log.info("CDP标签人群包生成任务请求参数:{}", JsonUtils.toJson(exportTask));
        CommonTagSingleResponse response = memberCdpClient.tagExportTask(exportTask);
        log.info("CDP标签人群包生成任务返回结果:{}", JsonUtils.toJson(response));
        return response;
    }

}
