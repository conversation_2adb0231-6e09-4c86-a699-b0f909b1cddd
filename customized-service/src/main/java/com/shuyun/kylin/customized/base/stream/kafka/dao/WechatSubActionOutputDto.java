package com.shuyun.kylin.customized.base.stream.kafka.dao;

import lombok.Data;

@Data
public class WechatSubActionOutputDto {
    private String id;


    /**
     * 触达身份标识
     */
    private String objectId;
    /**
     * 活动id
     */
    private String campaignId;

    /**
     * 	任务ID
     */
    private String taskId;

    private String occId;
    private String nodeId;
    private String nodeName;
    private String campaignName;
    private String testRun;

    //选择客户主题
    private String customerTheme;

    //选择订阅场景（活动类)
    private String sceneCampaign;

    //选择订阅场景（通用类）
    private String sceneCommen;

    //选择消息模板
    private String templateId;

    //选择订阅场景类型 PLATFORM=平台通用 ;  CAMPAIGN=活动
    private String type;

    //选择落地页方式 COMMON = 常规落地页 ; MANUAL= 自定义落地页
    private String choiceType;

    //选择落地页
    private String page;

    private String thing1;
    private String thing2;
    private String thing3;
    private String thing4;
    private String thing5;
    private String thing6;
    private String thing7;

    //接收者openId
    private String toUser ;


}
