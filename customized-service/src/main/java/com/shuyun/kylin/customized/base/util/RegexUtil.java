package com.shuyun.kylin.customized.base.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.regex.Pattern;

/**
 * 正则校验 工具类
 * <AUTHOR>
 * @create 2020/1/8
 */
@Slf4j
public final class RegexUtil {
    /**
     * 正则校验
     * @param pattern   正则表达式
     * @param content   校验文本
     * @return
     */
    public static boolean matches(String pattern, String content) {
        content = StringUtils.trimToNull(content);
        return StringUtils.isNotBlank(pattern) && StringUtils.isNotBlank(content) ? Pattern.matches(pattern, content) : false;
    }


    /**
     * 校验日期格式(yyyy-MM-dd)
     * @param dateStr
     * @return
     */
    public static boolean isValidDate(String dateStr){
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String regex = "[0-9]{4}-[0-9]{2}-[0-9]{2}";
        Pattern pattern = Pattern.compile(regex);
        if(pattern.matcher(dateStr).matches()) {
            dateFormat.setLenient(false);
            try {
                dateFormat.parse(dateStr);
                return true;
            } catch (Exception e) {
                log.error("字符串->日期转换失败:",e);
            }
        }
        return false;

    }

    /**
     * 是否有效的datetime
     *    默认格式: yyyy-MM-dd HH:mm:ss
     * @param dateStr
     * @return
     */
    public static boolean isValidDateTime(String dateStr){
        DateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        simpleDateFormat.setLenient(false);
        try {
            simpleDateFormat.parse(dateStr);
            return true;
        } catch (Exception e) {
            log.error("字符串->日期时间转换失败:",e);
        }
        return false;
    }

}
