package com.shuyun.kylin.customized.context;

import lombok.Getter;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;

/**
 * @author: Jingwei
 * @date: 2024-12-18
 */
public class CustomizedSpringContextUtil {

    private CustomizedSpringContextUtil() {}

    @Getter
    private static ApplicationContext applicationContext;

    public static void setApplicationContext(ConfigurableApplicationContext context) {
        CustomizedSpringContextUtil.applicationContext = context;
    }

}
