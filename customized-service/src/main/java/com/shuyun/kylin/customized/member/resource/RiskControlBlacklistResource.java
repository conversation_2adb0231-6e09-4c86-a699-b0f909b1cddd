package com.shuyun.kylin.customized.member.resource;



import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.common.ResponsesVo;
import com.shuyun.kylin.customized.member.dto.BlacListRequestDto;
import com.shuyun.kylin.customized.member.dto.PointFreezeDto;
import com.shuyun.kylin.customized.member.dto.ThresholdDto;
import com.shuyun.kylin.customized.member.service.IRiskControlBlacklistService;
import com.shuyun.pip.component.json.JsonUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Tag(name = "黑名单接口")
@RestController
@RequestMapping("/control/black")
public class RiskControlBlacklistResource {
    @Autowired
    IRiskControlBlacklistService riskControlBlacklistService;
    @PostMapping("/freezePoint")
    @Operation(summary = "冻结积分", tags = "冻结积分")
    @ApiResponses(value =  {@ApiResponse(description = "冻结积分",content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,schema = @Schema(implementation = ResponsesVo.class))})})
    public ResponsesVo freezePoint(@RequestBody PointFreezeDto request) {
        log.info("冻结积分入参:{}", JsonUtils.toJson(request));
        return riskControlBlacklistService.freezePoint(request);
    }

    @PostMapping("/unfreezePoint")
    @Operation(summary = "解冻积分", tags = "解冻积分")
    @ApiResponses(value =  {@ApiResponse(description = "解冻积分",content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,schema = @Schema(implementation = ResponsesVo.class))})})
    public ResponsesVo wechatReg(@RequestBody PointFreezeDto request) {
        log.info("解冻积分入参:{}", JsonUtils.toJson(request));
        return riskControlBlacklistService.unfreezePoint(request);
    }

    @PostMapping("/add/checkList")
    @Operation(summary = "加入黑名单", tags = "加入黑名单")
    @ApiResponses(value =  {@ApiResponse(description = "加入黑名单",content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,schema = @Schema(implementation = ResponsesVo.class))})})
    public ResponsesVo addCheckList(@RequestBody BlacListRequestDto request) {
        log.info("加入黑名单入参:{}", JsonUtils.toJson(request));

        return riskControlBlacklistService.addCheckList2(request);
    }

    @PostMapping("/freezeMember")
    @Operation(summary = "冻结账户", tags = "冻结账户")
    @ApiResponses(value =  {@ApiResponse(description = "冻结账户",content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,schema = @Schema(implementation = ResponsesVo.class))})})
    public ResponsesVo freezeMember(@RequestBody BlacListRequestDto request) {
        log.info("冻结账户入参:{}", JsonUtils.toJson(request));
        return riskControlBlacklistService.freezeMember(request);
    }

    @PostMapping("/unfreezeMember")
    @Operation(summary = "解冻账户", tags = "解冻账户")
    @ApiResponses(value =  {@ApiResponse(description = "解冻账户",content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,schema = @Schema(implementation = ResponsesVo.class))})})
    public ResponsesVo unfreezeMember(@RequestBody BlacListRequestDto request) {
        log.info("解冻账户入参:{}", JsonUtils.toJson(request));
        return riskControlBlacklistService.unfreezeMember(request);
    }

    @PostMapping("/deleteMember")
    @Operation(summary = "删除黑名单", tags = "删除黑名单")
    @ApiResponses(value =  {@ApiResponse(description = "删除黑名单",content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,schema = @Schema(implementation = ResponsesVo.class))})})
    public ResponsesVo deleteMember(@RequestBody BlacListRequestDto request) {
        log.info("删除黑名单入参:{}", JsonUtils.toJson(request));
        return riskControlBlacklistService.deleteMember(request.getMemberId());
    }
    @PostMapping("/setThreshold")
    @Operation(summary = "设置阈值", tags = "设置阈值")
    @ApiResponses(value =  {@ApiResponse(description = "设置阈值",content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,schema = @Schema(implementation = ResponsesVo.class))})})
    public ResponsesVo setThreshold(@RequestBody ThresholdDto request) {
        log.info("设置法制入参:{}", JsonUtils.toJson(request));
        return riskControlBlacklistService.setThreshold(request);
    }

    @PostMapping("/checkWarning")
    @Operation(summary = "校验是否加入预警名单", tags = "校验是否加入预警名单")
    @ApiResponses(value =  {@ApiResponse(description = "校验是否加入预警名单",content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,schema = @Schema(implementation = ResponsesVo.class))})})
    public ResponsesVo checkWarning(@RequestBody BlacListRequestDto request) {
        log.info("校验是否加入预警名单入参:{}", JsonUtils.toJson(request));
        return riskControlBlacklistService.queryWaringMember(request.getMemberId());
    }
}
