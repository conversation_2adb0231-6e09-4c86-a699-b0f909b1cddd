package com.shuyun.kylin.customized.swire.resource;

import com.alibaba.fastjson.JSONObject;
import com.shuyun.kylin.crm.openapi.core.dto.common.SaveDataSingleRequestDto;
import com.shuyun.kylin.crm.openapi.core.dto.spi.DataTransferValidationResultDto;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.feign.client.OpenApiFeignClient;
import com.shuyun.kylin.customized.base.util.ActionResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
@Slf4j
@RestController
@RequestMapping(("/data/sync/"))
public class StoreResource {
    @Resource
    private OpenApiFeignClient openApiFeignClient;
    @PostMapping("store")
    public ResponseResult store(@RequestBody SaveDataSingleRequestDto saveDataSingleRequestDto) {
        log.info("太古数据保存参数: {}", JSONObject.toJSONString(saveDataSingleRequestDto));
        ActionResult<DataTransferValidationResultDto> actionResult = openApiFeignClient.store(saveDataSingleRequestDto);
        if (actionResult.getCode() == 200) {
            return new ResponseResult("200", "推送成功");
        }
        return new ResponseResult("000", "推送失败");
    }
}
