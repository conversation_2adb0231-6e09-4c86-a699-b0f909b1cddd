package com.shuyun.kylin.customized.rpc.template.base;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.util.DateHelper;
import com.shuyun.kylin.customized.context.CustomizedSpringContextUtil;
import com.shuyun.kylin.customized.rpc.entity.KoRequestLog;
import com.shuyun.kylin.customized.rpc.enums.RpcSourceEnum;
import com.shuyun.kylin.customized.rpc.listener.RpcLogListenerEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;

import java.util.Optional;

/**
 * Ko请求模板基类
 *
 * @author: Jingwei
 * @date: 2024-12-19
 */
@Slf4j
public class KoRequestTemplate {

    /**
     * 发送请求日志
     *
     * @param requestUrl 请求路径
     * @param requestTDto 请求参数
     * @param responseBody 请求体
     * @param transactionId 反参
     * @param channelType 日志来源渠道 {@link com.shuyun.kylin.customized.project.strategy.router.SendCouponEnum }
     * @param <T>
     */
    public static <T> void pushKoRpcRequestLog(String requestUrl, T requestTDto, String responseBody, String transactionId, String channelType) {
        try {
            ApplicationContext applicationContext = CustomizedSpringContextUtil.getApplicationContext();
            log.info("获取当前上下文，applicationContext={}", applicationContext);
            if(ObjectUtil.isNull(applicationContext)) {
                return;
            }
            KoRequestLog koRequestLog = new KoRequestLog();
            koRequestLog.setRequestUrl(requestUrl);
            Optional.ofNullable(requestTDto).ifPresent(o -> koRequestLog.setRequestParams(JSON.toJSONString(o)));
            koRequestLog.setResponseResult(responseBody);
            koRequestLog.setCreatedTime(DateHelper.formatZonedDateTime(DateUtil.date(), DateHelper.DATE_FORMAT_T));
            koRequestLog.setBusinessCode(transactionId);
            koRequestLog.setChannelType(channelType);
            RpcLogListenerEvent<KoRequestLog> logListenerEvent = new RpcLogListenerEvent<>(RpcSourceEnum.KO, koRequestLog);
            log.info("发券请求日志事件发布，logListenerEvent={}", JSON.toJSONString(logListenerEvent));
            applicationContext.publishEvent(logListenerEvent);
        } catch (Exception e) {
            log.error("KoRequestTemplate.pushKoRpcRequestLog error, requestUrl={}, requestTDto={}, responseBody={}, transactionId={}, channelType={}", requestUrl, requestTDto, responseBody, transactionId, channelType, e);
        }
    }
}
