package com.shuyun.kylin.customized.member.dm;

import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.member.dto.UnionMemberChannelDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class UnionMemberChannelRepository extends BaseDsRepository<UnionMemberChannelDto> {

    /**
     * 查询联合会员渠道信息
     */
    public List<Map<String, String>> queryUnionMemberChannel(String memberId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        String queryMemberSql = " select id,memberId,channelType, channelName from " + ModelConstants.UNION_MEMBER_CHANNEL + " where  memberId = :memberId  ";
        return execute(queryMemberSql, queryMap).getData();
    }
}
