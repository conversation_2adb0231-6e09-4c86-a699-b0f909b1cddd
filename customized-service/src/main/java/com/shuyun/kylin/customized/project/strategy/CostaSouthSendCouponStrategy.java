package com.shuyun.kylin.customized.project.strategy;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.kylin.customized.base.feign.client.OpenApiFeignClient;
import com.shuyun.kylin.customized.base.feign.dao.MemberBingDto;
import com.shuyun.kylin.customized.base.feign.dao.OfferProjectDetailClientResponse;
import com.shuyun.kylin.customized.base.util.DataapiSdkUtil;
import com.shuyun.kylin.customized.base.util.SpringBeanFetcherUtil;
import com.shuyun.kylin.customized.project.dto.SendCouponResultDto;
import com.shuyun.kylin.customized.project.request.GrantCouponRequest;
import com.shuyun.kylin.customized.rpc.dto.CostaSendCouponRequestDto;
import com.shuyun.kylin.customized.rpc.dto.CostaSendCouponResponseDto;
import com.shuyun.kylin.customized.rpc.template.CostaRequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * costa发券
 */
@Slf4j
public class CostaSouthSendCouponStrategy extends SendCouponStrategy {

    static final DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();

    @Override
    public SendCouponResultDto sendCoupon(String templateId, OfferProjectDetailClientResponse projectDetail, GrantCouponRequest grantCouponRequest, MemberBingDto memberBindInfo) {
        log.info("请求costa发券策略入参，templateId={}，projectDetail={}，grantCouponRequest={}", templateId, JSON.toJSONString(projectDetail), JSON.toJSONString(grantCouponRequest));
        // 参数组装
        OfferProjectDetailClientResponse.RspData projectDetailData = projectDetail.getData();
        if(ObjectUtil.isNull(projectDetailData)) {
            log.error("内部接口返回失败，data is empty!");
            return new SendCouponResultDto(Boolean.FALSE, null, null,null);
        }
        OfferProjectDetailClientResponse.RspData.ExtData extData = projectDetailData.getExtData();
        if(ObjectUtil.isNull(extData)) {
            log.error("内部接口返回失败，extData is empty!");
            return new SendCouponResultDto(Boolean.FALSE, null, null,null);
        }
        String externalProjectId = extData.getExternalProjectId();
        if(ObjectUtil.isNull(externalProjectId)) {
            log.error("内部接口返回失败，externalProjectId is empty!");
            return new SendCouponResultDto(Boolean.FALSE, null, null,null);
        }
        CostaSendCouponRequestDto costaSendCouponRequestDto = assembleRequestData(grantCouponRequest,externalProjectId);
        // 发送costa请求
        CostaSendCouponResponseDto costaSendCouponResponseDto = CostaRequestTemplate.sendCoupon(costaSendCouponRequestDto, grantCouponRequest.getTransactionId());
        if(ObjectUtil.notEqual(costaSendCouponResponseDto.getStatus(), 0)) {
            log.error("costa发券失败，costaSendCouponResponseDto={}", JSON.toJSONString(costaSendCouponResponseDto));
            return new SendCouponResultDto(Boolean.FALSE, null, null,null);
        }
        CostaSendCouponResponseDto.RspData data = costaSendCouponResponseDto.getData();
        return new SendCouponResultDto(Boolean.TRUE, null, data.getCouponCode(),null);
    }

    private CostaSendCouponRequestDto assembleRequestData(GrantCouponRequest grantCouponRequest, String externalProjectId) {
        String memberGrantIdentify = grantCouponRequest.getMemberGrantIdentify();
        OpenApiFeignClient openApiFeignClient = SpringBeanFetcherUtil.getBean(OpenApiFeignClient.class);
        List<MemberBingDto> memberBingDtos = openApiFeignClient.queryListChannels(memberGrantIdentify, "KO", grantCouponRequest.getChannelType(), null);
        if(CollectionUtils.isEmpty(memberBingDtos)) {
            log.error("costa发券失败，会员信息不存在，memberGrantIdentify={}", memberGrantIdentify);
            throw new RuntimeException("costa发券失败，会员信息不存在");
        }
        MemberBingDto memberBingDto = memberBingDtos.get(0);
        CostaSendCouponRequestDto costaSendCouponRequestDto = new CostaSendCouponRequestDto();
        Optional.ofNullable(memberBingDto.getMobile()).ifPresent(costaSendCouponRequestDto::setMixPhone);
        Optional.ofNullable(memberBingDto.getUnionId()).ifPresent(costaSendCouponRequestDto::setTpAccountId);
        costaSendCouponRequestDto.setSource(*********);
        costaSendCouponRequestDto.setCampaignItemCode(externalProjectId);
        Optional.ofNullable(memberBingDto.getNick()).ifPresent(costaSendCouponRequestDto::setNickName);
        Optional.ofNullable(memberBingDto.getCreateTime()).ifPresent(registerTime ->
                costaSendCouponRequestDto.setRegisterTime(DateUtil.parse(registerTime)));
        costaSendCouponRequestDto.setRandNum(UUID.randomUUID().toString().replace("-",""));
        return costaSendCouponRequestDto;
    }

    private CostaSendCouponRequestDto assembleRequestDataOld(GrantCouponRequest grantCouponRequest, String externalProjectId) {
        String memberGrantIdentify = grantCouponRequest.getMemberGrantIdentify();
        String sql = String.format("select id,memberId,mobile,unionId,wechatNick,registerTime from data.prctvmkt.KO.Member where memberId='%s'", memberGrantIdentify);
        log.info("costa发券-会员信息原始sql={}", sql);
        BaseResponse<Map> execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
        log.info("costa发券-查询会员信息sql原始结果，data={}", JSON.toJSONString(execute));
        List<Map> data = execute.getData();
        if(CollectionUtils.isEmpty(data)) {
            log.error("costa发券失败，会员信息不存在，memberGrantIdentify={}", memberGrantIdentify);
            throw new RuntimeException("costa发券失败，会员信息不存在");
        }
        Map map = data.get(0);
        CostaSendCouponRequestDto costaSendCouponRequestDto = new CostaSendCouponRequestDto();
        Optional.ofNullable(map.get("mobile")).ifPresent(mobile -> costaSendCouponRequestDto.setMixPhone(mobile.toString()));
        Optional.ofNullable(map.get("unionId")).ifPresent(unionId -> costaSendCouponRequestDto.setTpAccountId(unionId.toString()));
        costaSendCouponRequestDto.setSource(*********);
        costaSendCouponRequestDto.setCampaignItemCode(externalProjectId);
        Optional.ofNullable(map.get("wechatNick")).ifPresent(wechatNick -> costaSendCouponRequestDto.setNickName(wechatNick.toString()));
        Optional.ofNullable(map.get("registerTime")).ifPresent(registerTime ->
                costaSendCouponRequestDto.setRegisterTime(DateUtil.parse(registerTime.toString())));
        costaSendCouponRequestDto.setRandNum(UUID.randomUUID().toString().replace("-",""));
        return costaSendCouponRequestDto;
    }
}
