package com.shuyun.kylin.customized.base.feign.client;


import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Map;

@FeignClient(name = "epassportFeignClient")
@RequestMapping("epassport/v1/")
public interface EpassportFeignClient {


    @PostMapping("admin/util/check/mobile")
    Map adminCheckMobilen(@RequestBody Map<String,String> map);

}
