package com.shuyun.kylin.customized.project.strategy;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.feign.dao.MemberBingDto;
import com.shuyun.kylin.customized.base.feign.dao.OfferProjectDetailClientResponse;
import com.shuyun.kylin.customized.project.dto.SendCouponResultDto;
import com.shuyun.kylin.customized.project.request.GrantCouponRequest;
import com.shuyun.kylin.customized.rpc.dto.BKResponseDto;
import com.shuyun.kylin.customized.rpc.template.BKRequestTemplate;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.Optional;

@Slf4j
public class BKCouponStrategy extends SendCouponStrategy {
//    static final BKConfigEntity bkConfig = JSON.parseObject(PropsUtil.getSysOrEnv("ko.bk.api.config"), BKConfigEntity.class);


    @Override
    public SendCouponResultDto sendCoupon(String templateId, OfferProjectDetailClientResponse projectDetail, GrantCouponRequest grantCouponRequest, MemberBingDto memberBindInfo) {
        log.info("汉堡王券发放开始transactionId:{}",grantCouponRequest.getTransactionId());
        // 发送汉堡王请求
        BKResponseDto bkResponseDto = BKRequestTemplate.sendCoupon(grantCouponRequest,projectDetail, memberBindInfo);
        if(Objects.isNull(bkResponseDto)) {
            log.error("汉堡王发券失败transactionId:{},grantCouponRequest={}",grantCouponRequest.getTransactionId(), JSON.toJSONString(grantCouponRequest));
            return new SendCouponResultDto(Boolean.FALSE, null, null,null);
        }
        String ticketCode = Optional.ofNullable(bkResponseDto.getCodes()).map(item -> item.get(0).getTicketCode()).orElse(null);
         log.info("汉堡王券发放成功transactionId:{}，ticketCode={}",grantCouponRequest.getTransactionId(), ticketCode);
        return new SendCouponResultDto(Boolean.TRUE, null, ticketCode,null);
    }
}
