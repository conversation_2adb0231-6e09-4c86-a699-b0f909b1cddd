package com.shuyun.kylin.customized.rpc.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class IQiYiRequestDto {
    /**
     * SP编码如（A00001），
     * 可由SP自动注册生成,在登陆系统时得到以A开头的编号 注册地址:http://apilogin.ofpay.com/register
     */
    @JSONField(name = "userid")
    private String userId;
    /**
     * SP接入密码(为账户密码的MD5值，
     * 如登陆密码为111111,此时这个值为md5(“111111”) (32位小写)
     * SP接入密码(为账户密码的MD5值，如登陆密码为111111,此时这个值为md5(“111111”) (32位小写)
     */
    @JSONField(name = "userpws")
    private String userPws;
    /**
     * 商品编号
     */
    @JSONField(name = "cardid")
    private String cardId;
    /**
     * 任意充传实际金额,固定面值传1
     */
    @JSONField(name = "cardnum")
    private String cardNum;
    /**
     * 商家传给欧飞的唯一订单号
     */
    @JSONField(name = "sporder_id")
    private String sporderId;
    /**
     * 订单时间 （yyyyMMddHHmmss 如：20070323140214）
     */
    @JSONField(name = "sporder_time")
    private String sporderTime;
    /**
     * 待充值账号
     */
    @JSONField(name = "game_userid")
    private String gameUserId;
    /**
     * 充值账号的密码
     */
    @JSONField(name = "game_userpsw")
    private String gameUserpsw;
    /**
     * 游戏所在区（用URLEncode编码指定字符集GBK
     */
    @JSONField(name = "game_area")
    private String gameArea;
    /**
     * 游戏所在服务器组（如果有服则必须填）（用URLEncode编码指定字符集GBK）
     */
    @JSONField(name = "game_srv")
    private String gameSrv;
    /**
     * 签名，规则在接口说明中体现
     */
    @JSONField(name = "md5Str")
    private String md5Str;
    /**
     * 手机号码(Q币充值可传)
     */
    @JSONField(name = "phoneno")
    private String phoneno;
    /**
     * 充值结果回调地址
     */
    @JSONField(name = "ret_url")
    private String retUrl;
    /**
     * 固定值
     */
    @JSONField(name = "version")
    private String version;
    /**
     * 用户ip(Q币充值可传)
     */
    @JSONField(name = "userip")
    private String userIp;
}
