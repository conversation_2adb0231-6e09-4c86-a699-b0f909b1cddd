package com.shuyun.kylin.customized.coupon.handler;

import com.shuyun.kylin.customized.base.stream.kafka.producer.coupon.CouponProducer;
import com.shuyun.kylin.customized.coupon.dto.CustomerCouponDto;
import com.shuyun.kylin.customized.coupon.dto.CustomerDto;
import com.shuyun.kylin.customized.coupon.dto.ExtCouponRuleDto;
import com.shuyun.kylin.customized.coupon.enums.CouponSendStrategyEnum;
import com.shuyun.kylin.customized.coupon.enums.StatusEnum;
import com.shuyun.kylin.customized.coupon.service.ICouponService;
import com.shuyun.kylin.customized.coupon.service.impl.ModelService;
import com.shuyun.kylin.customized.coupon.strategy.CouponSendContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component("outPutCustomHandler")
public class OutPutCustomHandler implements CouponSendHandler {

    @Autowired
    private ICouponService iCouponService;

    @Autowired
    private ModelService modelService;

    @Autowired
    private CouponProducer couponProducer;

    @Override
    public void dealCoupon(CustomerCouponDto customerCouponDto) {
        log.info("进入同步活动人群 接收报文:{}", customerCouponDto);
        if (StringUtils.isEmpty(customerCouponDto.getProjectId())) {
            log.warn("权益规则id为空，不执行发券逻辑！");
            return;
        }
        //todo 按照batchId判断优惠券品牌和渠道，如果是酷客多走酷客多方法，其他走第三方
        // TODO -- 需要根据需要,来选择发送策略

        //通过券规则id获取权益模型对象
        ExtCouponRuleDto extCouponRuleDto = modelService.getExtCouponRuleByBatchId(customerCouponDto.getProjectId());
        if(extCouponRuleDto == null){
            log.info("无法查到券规则{}，请检查参数！",customerCouponDto.getProjectId());
            return;
        }else if(StringUtils.isEmpty(extCouponRuleDto.getMemberType())
                || StringUtils.isEmpty(extCouponRuleDto.getChannelType())){
            log.info("权益规则品牌或渠道为空，请检查券规则！");
            return;
        }
        customerCouponDto.setMemberType(extCouponRuleDto.getMemberType());
        CouponSendStrategyEnum sendEnum;
        try{
            sendEnum = CouponSendStrategyEnum.valueOf(extCouponRuleDto.getChannelType());
        }catch (Exception e){
            log.error("当前券规则的渠道没有找到对应的发放策略");
            return;
        }
        //发放策略执行
        List<CustomerDto> customerDtoList = CouponSendContext.newInstance().sendCoupon(customerCouponDto,sendEnum);//
        if(null == customerDtoList || customerDtoList.isEmpty()){
            log.info("没有券执行发送成功");
            customerCouponDto.getData().forEach(
                    t-> t.setStatus(StatusEnum.GRANT_FAILED.getValue())
            );
            couponProducer.sendMsg(customerCouponDto);
            return;
        }
        customerCouponDto.setData(customerDtoList);
        log.info("消息回推kafka");
        couponProducer.sendMsg(customerCouponDto);
        modelService.saveCouponDetail(customerCouponDto,extCouponRuleDto);
        log.info("执行发送批次:{} 的优惠券成功",customerCouponDto.getBatchId());
    }

}
