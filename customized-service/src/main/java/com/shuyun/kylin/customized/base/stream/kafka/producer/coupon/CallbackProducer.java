package com.shuyun.kylin.customized.base.stream.kafka.producer.coupon;

import com.shuyun.kylin.customized.base.stream.kafka.dao.CallbackDto;
import com.shuyun.kylin.customized.base.stream.kafka.messaging.KafkaCallback;
import com.shuyun.kylin.customized.base.stream.kafka.messaging.KafkaNotification;


import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@EnableBinding({KafkaCallback.class, KafkaNotification.class})
@ConditionalOnExpression("${system.kafka.enabled:true}")
public class CallbackProducer {

    @Autowired
    private KafkaCallback kafkaCallback;

    public void sendMsg(CallbackDto callbackDto){
        log.info("【 发送到主动营销 Topic：marketing-action-task-notification-callback  开始=======> 送出参数: {} 】",callbackDto);
        kafkaCallback.output().send(MessageBuilder.withPayload(callbackDto).build());
        log.info("【 发送到主动营销 Topic：marketing-action-task-notification-callback 结束=======> 送出参数: {} 】", callbackDto);
    }
}
