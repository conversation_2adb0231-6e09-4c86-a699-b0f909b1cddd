package com.shuyun.kylin.customized.base.util;


import com.shuyun.motor.common.cons.PropsUtil;

public class ConfigurationCenterUtil {

    /**
     * 忠诚度积分账号类型id
     */
    public static final String ACCOUNT_POINT = PropsUtil.getSys("loyalty.account.point");

    /**
     * cep密钥
     */
    public static final String CEP_SECRED = PropsUtil.getSys("cep.secrec.key");

    /**
     * cep域名
     */
    public static final String CEP_URL = PropsUtil.getSys("cep.domain.url");

    /**
     * cepTOKEN
     */
    public static final String CEP_TOCKEN = PropsUtil.getSys("cep.domain.token");

    /**
     * cep用户名
     */
    public static final String CEP_USERNAME = PropsUtil.getSys("cep.userName");
    /**
     * cep黑名单同步URL
     *
     * <AUTHOR>
     * @Date 2022/2/16 17:17
     */
    public static final String CEP_RISKRECORD_URL = PropsUtil.getSys("cep.risk.record.url");
    /**
     * cep黑名单删除URL
     *
     * <AUTHOR>
     * @Date 2022/2/16 17:17
     */
    public static final String CEP_RISKRELEASE_URL = PropsUtil.getSys("cep.risk.release.url");
    /**
     * cep密码
     */
    public static final String CEP_PASSWOED = PropsUtil.getSys("cep.password");

    /**
     * 自定义触达-发放权益券-活动记录
     */
    public static final String MEMBER_COUPON = PropsUtil.getSys("custom.made.coupon.model");

    /**
     * 新-自定义触达-发放权益券-活动记录
     */
    public static final String MEMBER_COUPON_NEW = PropsUtil.getSys("custom.made.coupon.model.new");

    /**
     * 自定义优惠券名称
     */
    public static final String CUSTOM_COUPONNAME = PropsUtil.getSys("custom.coupon.name");

    /**
     * 自定义触达-发送小程序订阅消息-活动记录
     */
    public static final String MEMBER_SUBSCRIBE = PropsUtil.getSys("custom.made.applet.model");

    /**
     * 自定义小程序订阅消息 名称
     */
    public static final String CUSTOM_APPLETSUBSCRIBE = PropsUtil.getSys("custom.applet.subscribe.name");


    /**
     * 中台 Ocp-Apim-Subscription-Key
     */
    public static final String MIDDLEGROUND_KEY = PropsUtil.getSys("middleground.Subscription.key");

    /**
     * 中台 会员注销url
     */
    public static final String OCP_MEMBER_DELETEUAT = PropsUtil.getSys("ocp.member.deleteuat");

    /**
     * 中台 会员同步url
     */
    public static final String OCP_MEMBER_UPSERT = PropsUtil.getSys("ocp.member.upsert");
    /**
     * 中台 互动记录同步url
     */
    public static final String OCP_INTERACTIVE_RECORD = PropsUtil.getSys("ocp.InteractiveRecord.url");
    /**
     * 延迟推送中台数据时间
     */
    public static final String SEND_CMOSOM_SLEEP = PropsUtil.getSys("send.cosmos.sleep");


    /**
     * 超额记录表
     */
    public static final String BEYOND_POINTRECORD = PropsUtil.getSys("member.beyond.point.record");
    /**
     * 会员等级表
     */
    public static final String MEMBER_GRADE = PropsUtil.getSys("member.grade");
    /**
     * 会员等级明细表
     */
    public static final String MEMBER_GRADERECORD = PropsUtil.getSys("member.grade.record");
    /**
     * 会员积分表
     */
    public static final String MEMBER_POINTCOUNT = PropsUtil.getSys("member.point");
    /**
     * 会员积分明细表
     */
    public static final String MEMBER_POINTRECORD = PropsUtil.getSys("member.point.record");

    /**
     * 会员等级code
     */
    public static final Long MEMBER_GRADE_DEFINITIONID = Long.valueOf(PropsUtil.getSys("member.grade.currentGradeDefinitionId"));

    /**
     * 会员注销分组id
     */
    public static final String MEMBER_CANCEL_GROUPID = PropsUtil.getSys("member.cancel.groupId");

    /**
     * 黑名单分组id
     */
    public static final String MEMBER_BLACK_GROUPID = PropsUtil.getSys("member.black.groupId");

    /**
     * 账户冻结分组id
     */
    public static final String MEMBER_FROZEN_GROUPID = PropsUtil.getSys("member.frozen.groupId");

    /**
     * cep 优惠券请求地址
     */
    public static final String MEMBER_CEP_COUPON = PropsUtil.getSys("cep.coupon.url");
    /**
     * cep 小程序请求地址
     */
    public static final String MEMBER_CEP_APPLET = PropsUtil.getSys("cep.applet.url");

    /**
     * cep 会员注销请求地址
     */
    public static final String MEMBER_CEP_CANCELLATION = PropsUtil.getSys("cep.cancellation.url");

    /**
     * 侧边栏 域名
     */
    public static final String MEMBER_SIDEBAR_URL = PropsUtil.getSys("sidebar.url");

    /**
     * 侧边栏 查询会员路径
     */
    public static final String MEMBER_SIDEBAR_GETMEMBERPATH = PropsUtil.getSys("sidebar.getMemberPath");

    /**
     * 侧边栏 X-EACH-VENDOR-ID
     */
    public static final String MEMBER_SIDEBAR_VENDOR_ID = PropsUtil.getSys("sidebar.vendorId");

    /**
     * 侧边栏 key
     */
    public static final String MEMBER_SIDEBAR_KEY = PropsUtil.getSys("sidebar.key");

    /**
     * 侧边栏 X-EACH-APP-ID
     */
    public static final String MEMBER_SIDEBAR_APPID = PropsUtil.getSys("sidebar.appId");

    /**
     * 历史积分错误日志
     */
    public static final String HISTORY_POINT_ERROR = PropsUtil.getSys("data.prctvmkt.KO.HistoryMemberPointLog");
    /**
     * 历史会员错误日志
     */
    public static final String HISTORY_MEMBER_ERROR = PropsUtil.getSys("data.prctvmkt.KO.HistoryMemberLog");

    /**
     * 历史减 积分明细表
     */
    public static final String HISTORY_UPDATE_POINTLOG = PropsUtil.getSys("data.prctvmkt.KO.pointHistory");

    /**
     * 历史会员表
     */
    public static final String HISTORY_MEMBER = PropsUtil.getSys("data.prctvmkt.KO.MemberHistory");

    /**
     * 核心线程数
     */
    public static final int THREAD_CORE_SUM = Integer.parseInt(PropsUtil.getSys("thread.core.sum"));

    /**
     * 队列数
     */
    public static final int THREAD_QUEUE_SUM = Integer.parseInt(PropsUtil.getSys("thread.queue.sum"));


    /**
     * 小程序订阅 核心线程数
     */
    public static final int THREAD__SUBSCRIBED_CORES = Integer.parseInt(PropsUtil.getSys("thread.subscribed.cores"));

    /**
     * 小程序订阅 队列数
     */
    public static final int THREAD_SUBSCRIBED_QUEUE = Integer.parseInt(PropsUtil.getSys("thread.subscribed.queue"));

    /**
     * 小程序订阅 分页数
     */
    public static final int THREAD_SUBSCRIBED_PAGESIZE = Integer.parseInt(PropsUtil.getSys("thread.subscribed.pageSize"));

    /**
     * 优惠券 核心线程数
     */
    public static final int THREAD__COUPONS_CORES = Integer.parseInt(PropsUtil.getSys("thread.coupon.cores"));

    /**
     * 优惠券 队列数
     */
    public static final int THREAD_COUPONS_QUEUE = Integer.parseInt(PropsUtil.getSys("thread.coupon.queue"));

    /**
     * 优惠券 分页数
     */
    public static final int THREAD_COUPONS_PAGESIZE = Integer.parseInt(PropsUtil.getSys("thread.coupon.pageSize"));

    /**
     * 请求cep发订阅消息重试次数(-1:无限重试)
     */
    public static final int SUBSCRIPTION_RETRY_NUM = PropsUtil.getIntSys("subscription.retry.num", 3);

    /**
     * 请求cep发订阅消息重试时间间隔
     */
    public static final String SEND_SUBSCRIPTION_SLEEP = PropsUtil.getSysOrEnv("send.subscription.sleep", "1000");

    /**
     * httpClient最大连接数
     */
    public static final int HTTPCLIENT_MAX_TOTAL = PropsUtil.getIntSys("httpclient.max.total", 200);

    /**
     * httpClient单链接最大连接数
     */
    public static final int HTTPCLIENT_MAX_TOTAL_ROUTE = PropsUtil.getIntSys("httpclient.max.total.route", 50);

    /**
     * httpClient ConnectTimeout连接超时
     */
    public static final int HTTPCLIENT_CONNECT_TIME_OUT = PropsUtil.getIntSys("httpclient.connect.time.out", 10000);


    /**
     * 跳转小程序类型：developer为开发
     * 版；trial为体验版；formal为正式版；
     * 默认为正式版
     */
    public static final String CEP_MINIPROGRAM_STATE = PropsUtil.getSys("cep.miniProgram.state");

    /**
     * 会员微信渠道appId
     */
    public static final String MEMBER_CANCEL_WEIXIN_APPID = PropsUtil.getSys("member.cancel.weixin.appId");


    /**
     * 每天有乐CRM会员域名
     */
    public static final String HTTP_SWIRE_MEMBER_HOST = PropsUtil.getSys("http.swire.member.query.host");
    /**
     * 每天有乐CRM会员注册
     */
    public static final String HTTP_SWIRE_MEMBER_REGISTER = PropsUtil.getSys("http.swire.member.register.url");

    /**
     * 每天有乐CRM会员更新
     */
    public static final String HTTP_SWIRE_MEMBER_UPDATALNFO = PropsUtil.getSys("http.swire.member.updateInfo.url");

    /**
     * 每天有乐CRM会员积分同步
     */
    public static final String HTTP_SWIRE_MEMBER_POINT = PropsUtil.getSys("http.swire.member.point.url");

    /**
     * 每天有乐CRM会员历史积分同步
     */
    public static final String HTTP_SWIRE_HISTORY_MEMBER_POINT = PropsUtil.getSys("http.swire.Historymember.point.url");

    /**
     * 每天有乐CRMkey
     */
    public static final String HTTP_SWIRE_MEMBER_SIGN_KEY= PropsUtil.getSys("http.swire.member.sign.key");
    /**
     * 每天有乐CRM sid
     */
    public static final String HTTP_SWIRE_MEMBER_SID= PropsUtil.getSys("http.swire.member.sign.sid");


    /**
     * cbl-app_key
     */
    public static final String CBL_APPKEY= PropsUtil.getSys("cbl_appKey");
    /**
     * cbl-app_secret
     */
    public static final String CBL_APPSECRET= PropsUtil.getSys("cbl_appSecret");

    /**
     * cbl-更新会员等级路径
     */
    public static final String CBL_MEMBERGRADE= PropsUtil.getSys("cbl_memberGrade");
    /**
     * cbl-创建或更新会员信息路径
     */
    public static final String CBL_UPDATEMEMBER= PropsUtil.getSys("cbl_updateMember");

    /**
     *会员参与活动信息
     */
    public static final String MEMBER_CAMPAIGN = PropsUtil.getSys("data.prctvmkt.KO.MemberCampaign");


    //KO等级与积分体系id
    public  final static  Integer planId = Integer.valueOf(PropsUtil.getSysOrEnv("medal.planId", "60001"));

    //勋章体系id
    public  final static  Integer medalHierarchyId = Integer.valueOf(PropsUtil.getSysOrEnv("medal.medalHierarchyId", "60001"));

    //注册勋章id
    public final static  Integer medalRegisterId = Integer.valueOf(PropsUtil.getSysOrEnv("medal.medalRegisterId", "60002"));

    //25CNY
    public final static  Integer medalCNYId = Integer.valueOf(PropsUtil.getSysOrEnv("medal.medalCNYId", "60255"));

    //勋章等级体系id
    public  final static  Integer medalGradeId = Integer.valueOf(PropsUtil.getSysOrEnv("medal.gradeId", "60807"));


    /**
     * 自定义触达-发送小程序订阅消息（新）-活动记录
     */
    public static final String MEMBER_CAMPAIGN_ACTION_NEW = PropsUtil.getSys("custom.made.wechat.sub.model.new");



    //联蔚小程序url
    public static final String wechatLwUrl = PropsUtil.getSys("lw.url");

    //联蔚小程序clientId
    public static final String wechatLwClientId = PropsUtil.getSys("lw.clientId");

    //联蔚小程序secret
    public static final String wechatLwSecret = PropsUtil.getSys("lw.secret");

    //联蔚小程序buCode
    public static final String wechatLwBuCode = PropsUtil.getSys("lw.buCode");

}
