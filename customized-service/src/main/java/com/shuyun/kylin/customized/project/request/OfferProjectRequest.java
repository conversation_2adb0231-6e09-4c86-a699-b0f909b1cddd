package com.shuyun.kylin.customized.project.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class OfferProjectRequest {

//    @NotBlank(message = "品牌不能为空")
    private String brand;

//    @NotBlank(message = "渠道不能为空")
    private String channelType;

    private String identify;

    private String campaignId;

    private Integer pageNum;

    private Integer pageSize;
    /**
     * 是否展示字段，默认不展示
     */
    private Boolean showProject;

}
