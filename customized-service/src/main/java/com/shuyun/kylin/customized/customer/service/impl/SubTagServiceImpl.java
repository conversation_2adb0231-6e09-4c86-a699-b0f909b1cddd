package com.shuyun.kylin.customized.customer.service.impl;

import com.shuyun.calc.service.base.rule.expression.CommonExpression;
import com.shuyun.calc.service.online.job.task.OnlineJobTaskCalculator;
import com.shuyun.cdp.tags.request.OriginTagRequest;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.feign.client.MemberCdpClient;
import com.shuyun.kylin.customized.customer.dto.CommonTagSingleResponse;
import com.shuyun.kylin.customized.customer.dto.TagDto;
import com.shuyun.kylin.starter.exception.enumerate.DefaultErrorTypes;
import com.shuyun.kylin.starter.exception.model.RequestException;
import com.shuyun.pip.component.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/1 17:31
 * @description
 */
@Service
@Slf4j
public class SubTagServiceImpl {
    @Autowired
    MemberCdpClient memberCdpClient;

    public Boolean subVerify(String tagId, String customerId, String channelType, String memberType, String occId, OnlineJobTaskCalculator calc) {
        boolean check = false;
        TagDto tagDto = null;
        try {
            tagDto = memberCdpClient.getTagInfo(tagId).getData();
            tagDto.getType();
        } catch (Exception e) {
            log.error("标签参数错误：{}", tagId);
            throw new RequestException(DefaultErrorTypes.OUT_OF_RANGE, "tagIdList");
        }
        long startTime = System.currentTimeMillis();
        if (ModelConstants.TAG_TYPE_MANUAL.equals(tagDto.getType())) {
            OriginTagRequest originTagRequest = new OriginTagRequest();
            List<String> memberIdList = new ArrayList<>();
            List<String> tagIdIdList = new ArrayList<>();
            memberIdList.add(customerId);
            tagIdIdList.add(tagId);
            originTagRequest.setOriginIds(memberIdList);
            originTagRequest.setMemberType(memberType);
            originTagRequest.setChannelType(channelType);
            originTagRequest.setSelectTags(tagIdIdList);
            CommonTagSingleResponse<Map> customerTag = memberCdpClient.checkCustomerTag(ModelConstants.BRAND, originTagRequest);
            log.info("消费者标签:{}", JsonUtils.toJson(customerTag));
            if (customerTag.getData() == null || customerTag.getData().get(customerId) == null) {
                return false;
            }
            List<Map> tagList = (List<Map>) customerTag.getData().get(customerId);
            for (Map tagMap : tagList) {
                if (tagMap.get("id").equals(tagId)) {
                    check = true;
                }
            }
            log.info("手工标签校验:{},耗时:{}", tagId, System.currentTimeMillis() - startTime);
        } else if (ModelConstants.TAG_TYPE_WITHIN_RULE.equals(tagDto.getType())) {
            if (!StringUtils.isEmpty(occId)) {
                CommonTagSingleResponse<CommonExpression> tagRuleResponse = memberCdpClient.getTagRuleInfo(tagId);
                log.info("规则标签校验-获取规则:{},耗时:{}", tagId, System.currentTimeMillis() - startTime);
                startTime = System.currentTimeMillis();
                check = calc.match(tagRuleResponse.getData(), occId);
                log.info("规则标签校验-计算规则:{},耗时:{}", tagId, System.currentTimeMillis() - startTime);
                startTime = System.currentTimeMillis();
                log.info("标签:{},会员:{},校验结果:{}", tagId, occId, check);
            }
        }
        log.info("标签校验-总:{},耗时:{}", tagId, System.currentTimeMillis() - startTime);
        return check;
    }

    public String getOccIdByChannelId(String memberType, String channelType, String originId) {
        OriginTagRequest originTagRequest = new OriginTagRequest();
        List<String> memberIdList = new ArrayList<>();
        memberIdList.add(originId);
        originTagRequest.setOriginIds(memberIdList);
        originTagRequest.setChannelType(channelType);
        originTagRequest.setMemberType(memberType);
        CommonTagSingleResponse<List<String>> commonTagSingleResponse = memberCdpClient.getOccIdByChannelId(originTagRequest);
        if (!CollectionUtils.isEmpty(commonTagSingleResponse.getData())) {
            return commonTagSingleResponse.getData().get(0);
        } else {
            return null;
        }
    }
}
