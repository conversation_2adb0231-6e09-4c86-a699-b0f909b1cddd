package com.shuyun.kylin.customized.rpc.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.kylin.customized.base.util.DataapiSdkUtil;
import com.shuyun.kylin.customized.rpc.entity.KoRequestLog;
import com.shuyun.kylin.customized.rpc.enums.RpcSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @author: Jingwei
 * @date: 2024-12-17
 */
@Slf4j
@Component
public class RpcLogListener {

    static final DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil. getDataapiHttpSdk();

    @Async("rpcLogThreadPool")
    @EventListener
    public void onApplicationEvent(RpcLogListenerEvent event) {
        log.info("rpc日志事件监听 receive event: {}", event);
        if(ObjectUtil.equal(event.getSource(), RpcSourceEnum.KO)) {
            log.info("===> KO RPC日志存储开始");
            KoRequestLog koRequestLog = (KoRequestLog) event.getData();
            if(ObjectUtil.isNull(koRequestLog)) {
                return;
            }
            log.info("KO rpc日志，koRequestLog: {}", koRequestLog);
            try {
                Map<String, Object> insertMap = BeanUtil.beanToMap(koRequestLog, false, true);
                dataapiHttpSdk.insert("data.prctvmkt.KO.ThirdPartCreateCouponLog", insertMap, false, true);
            } catch (Exception e) {
                log.error("KO RPC日志存储失败，koRequestLog: {} ", JSON.toJSONString(koRequestLog), e);
            }

        }
    }
}
