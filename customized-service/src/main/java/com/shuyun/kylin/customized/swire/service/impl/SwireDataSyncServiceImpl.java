package com.shuyun.kylin.customized.swire.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.dm.api.response.DMLResponse;
import com.shuyun.kylin.crm.openapi.core.dto.wechat.WechatRegisterRequest;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.common.ResponsesVo;
import com.shuyun.kylin.customized.base.dm.SwireMemberRepository;
import com.shuyun.kylin.customized.base.enums.ResponseCodeEnum;
import com.shuyun.kylin.customized.base.feign.client.OpenApiFeignClient;
import com.shuyun.kylin.customized.base.feign.dao.MemberCustomDto;
import com.shuyun.kylin.customized.base.stream.kafka.producer.coupon.BehaviorProducer;
import com.shuyun.kylin.customized.base.stream.kafka.producer.coupon.DataSyncMember;
import com.shuyun.kylin.customized.base.util.*;
import com.shuyun.kylin.customized.swire.dto.*;
import com.shuyun.kylin.customized.swire.repository.DailyLimitedPointsRuleRepository;
import com.shuyun.kylin.customized.swire.repository.InteractiveCampaignPointRuleRepository;
import com.shuyun.kylin.customized.swire.service.ISwireDataSyncService;
import com.shuyun.kylin.customized.swire.utils.Sha256Utils;
import com.shuyun.kylin.customized.swire.utils.SortByWeightsUtils;
import com.shuyun.kylin.starter.redis.ICache;

import com.shuyun.pip.component.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.util.*;


@Slf4j
@Service
public class SwireDataSyncServiceImpl implements ISwireDataSyncService {


    @Autowired
    @Qualifier("redisCache")
    private ICache iCache;

    @Autowired
    private DataSyncMember dataSyncMember;

    @Resource
    private SwireMemberRepository swireMemberRepository;

    @Autowired
    private OpenApiFeignClient openApiFeignClient;

    @Autowired
    private InteractiveCampaignPointRuleRepository interactiveCampaignPointRuleRepository;

    @Autowired
    private DailyLimitedPointsRuleRepository dailyLimitedPointsRuleRepository;


    @Override
    public ResponsesVo sendPointRule(PointRuleDto pointRuleDto) {

        boolean ruleIs = ruleIsDate(pointRuleDto.getStartTime(), pointRuleDto.getEndTime());
        if (ruleIs) {
            return new ResponsesVo(500, ResponseCodeEnum.RULE_POINT_RULE.getMsg());
        }
        Integer ruleId;

        if (null != pointRuleDto.getRuleCode()) {
            ruleId = Integer.valueOf(pointRuleDto.getRuleCode());
        } else {
           // ruleId = iCache.get(PointLimitEnum.RULE_POINT_KEY.getKey());
            ruleId = dailyLimitedPointsRuleRepository.getMaxRuleId();
            log.info("获取ruleId:{}", ruleId);
            if (0 == ruleId) {
                //生成规则id
                ruleId = 1;
                //iCache.put(PointLimitEnum.RULE_POINT_KEY.getKey(), ruleId);
            } else {
                ruleId++;
            }
        }
        List<String> id = pointRuleDto.getId();
        ArrayList<String> list = new ArrayList<>();
        for (String s : id) {
            //查询活动信息
            InteractiveAndCampaignPointsRuleDto campaign = interactiveCampaignPointRuleRepository.getCampaign(s);
            log.info("查询活动信息:{}", JSON.toJSONString(campaign));
            //校验时间活动 范围是否合理
           /* boolean effectiveDate = isEffectiveDate(campaign.getStartTime(), pointRuleDto.getEndTime());
            if (effectiveDate) {
                return new ResponsesVo(500, s + ResponseCodeEnum.STARTIME_POINT_RULE.getMsg());
            }
            boolean endTimeDate = isEffectiveDate(campaign.getEndTime(), pointRuleDto.getStartTime());
            if (!endTimeDate) {
                return new ResponsesVo(500, s + ResponseCodeEnum.ENDTIME_POINT_RULE.getMsg());
            }*/
            //记录规则
            HashMap<String, Object> map = new HashMap<>();
            map.put("ruleCode", ruleId);
            map.put("ruleName", pointRuleDto.getRuleName());
            map.put("name", campaign.getName());
            map.put("scene", campaign.getScene());
            map.put("type", campaign.getType());
            map.put("channelType", campaign.getChannelType());
            map.put("pointsThreshold", pointRuleDto.getPointsThreshold());
            map.put("lastSync", DateHelper.getNowZone());
            map.put("created", DateHelper.getNowZone());
            map.put("startTime", pointRuleDto.getStartTime());
            map.put("endTime", pointRuleDto.getEndTime());
            map.put("isDailyLimited", pointRuleDto.getIsDailyLimited());
            map.put("sceneArray", id);
            map.put("channelFrom",pointRuleDto.getChannelFrom());
            map.put("isOpenQuery",pointRuleDto.getIsOpenQuery());
            try {
                log.info("积分规则对象:{}", JSON.toJSONString(map));
                list.add(ruleId + s);
                DMLResponse response = DataApiUtil.upsertIsData(ModelConstants.KAILY_LIMITED_POINTSRULE, ruleId + s, map);
                if (!response.getIsSuccess()) {
                    log.error("场景积分规则创建失败:{}", response.getOperation());
                    return new ResponsesVo(500, response.getOperation());
                }
            } catch (Exception e) {
                log.error("场景积分规则创建失败1:{}", e.getMessage());
                return new ResponsesVo(500, "场景积分规则创建失败");
            }
        }

        //删除不包含场景值
        interactiveCampaignPointRuleRepository.deleteByScene(ruleId, list);

        return new ResponsesVo(200, ResponseCodeEnum.SUCCESS.getMsg());
    }

    @Override
    public Map<String, String> dataSyncMemberPoint(Map<String, String> request) {
        Map<String, String> result = new HashMap<>();
        Map<String, Object> map = new HashMap<>();
        map.put("sid", ConfigurationCenterUtil.HTTP_SWIRE_MEMBER_SID);
        map.put("type", request.get("type"));
        map.put("costCenter",request.get("costCenter"));
        map.put("bottleType",request.get("bottleType"));
        map.put("bottle", request.get("bottle"));

        map.put("availBottle", request.get("availBottle"));
        map.put("mobile", request.get("mobile"));
        map.put("orderId", request.get("orderId"));
        map.put("insertTime", request.get("effectiveDate"));
        map.put("expireTime", request.get("overdueDate"));
        map.put("bottleFrom", request.get("bottleFrom"));
        map.put("bottleDesc", request.get("bottleDesc"));
        map.put("afterBottle", request.get("availBottle"));
        //查询冻结积分
        ArrayList<String> optionalFields = new ArrayList<>();
        optionalFields.add("frozenPoint");
        optionalFields.add("experiencePoints");
        MemberCustomDto memberDto = openApiFeignClient.queryMember(ModelConstants.MEMBER_TYPE, request.get("memberId"), optionalFields);
        log.info("swire查询经验值:{}",JSON.toJSONString(memberDto));
        if (null == memberDto.getOptionalFieldData().get("frozenPoint")) {
            map.put("freezeBottle", "0.0");
        } else {
            double point = Math.abs(Double.parseDouble(memberDto.getOptionalFieldData().get("frozenPoint").toString()));
            double afterBottle = addPoint(point, Double.parseDouble(request.get("availBottle")));
            map.put("freezeBottle", String.valueOf(point));
            map.put("afterBottle", String.valueOf(afterBottle));
        }
        if (null == memberDto.getOptionalFieldData().get("experiencePoints")) {
            map.put("growBottle", "0.0");
        } else {
            map.put("growBottle", memberDto.getOptionalFieldData().get("experiencePoints").toString());
        }
        ReturnResultsDto body = null;
        try {
            /*
            body = swirePostBody(ConfigurationCenterUtil.HTTP_SWIRE_MEMBER_POINT, map);
             */
            body = swirePostBodyKA(ConfigurationCenterUtil.HTTP_SWIRE_MEMBER_POINT, map);
        } catch (Exception e) {
            log.error("同步太古积分记录失败orderId:{},msg:{}",request.get("orderId") ,e.getMessage());
            map.put("code", 500);
            map.put("msg", "同步太古积分记录接口报错");
            //记录失败的积分记录
            map.put("lastSync", ZonedDateTime.now());
            map.put("failuresNumber", request.get("failuresNumber"));
            map.put("memberId",request.get("memberId"));
            //记录失败
            DataApiUtil.upsertIsData(ModelConstants.SWIRE_MEMBERPOINT_SYNC_LOG,request.get("orderId"), map);
        }
        if (null == body){
            map.put("code", 500);
            map.put("msg", "同步太古积分记录接口报错");
            //记录失败的积分记录
            map.put("lastSync", ZonedDateTime.now());
            map.put("failuresNumber", request.get("failuresNumber"));
        }else {
            map.put("code",body.getCode());
            map.put("msg",body.getMsg());
            map.put("status",body.getStatus());
            map.put("lastSync", ZonedDateTime.now());
            map.put("failuresNumber", request.get("failuresNumber"));
        }
        map.put("memberId",request.get("memberId"));
        DataApiUtil.upsertIsData(ModelConstants.SWIRE_MEMBERPOINT_SYNC_LOG,request.get("orderId"), map);
        return result;
    }

    @Override
    public Map<String, String> dataSyncHistoryPoint(Map<String, String> request) {

        Map<String, String> result = new HashMap<>();
        Map<String, Object> map = new HashMap<>();
        map.put("sid", ConfigurationCenterUtil.HTTP_SWIRE_MEMBER_SID);
        map.put("type", request.get("type"));
        map.put("costCenter",request.get("costCenter"));
        map.put("bottleType",request.get("bottleType"));
        map.put("bottle", request.get("bottle"));

        map.put("availBottle", request.get("availBottle"));
        map.put("mobile", request.get("mobile"));
        map.put("orderId", request.get("orderId"));
        map.put("insertTime", request.get("effectiveDate"));
        map.put("bottleFrom", request.get("bottleFrom"));
        map.put("bottleDesc", request.get("bottleDesc"));
        map.put("afterBottle", request.get("availBottle"));
        map.put("expireTime", request.get("overdueDate"));
        //查询冻结积分
        ArrayList<String> optionalFields = new ArrayList<>();
        optionalFields.add("frozenPoint");
        optionalFields.add("experiencePoints");
        MemberCustomDto memberDto = openApiFeignClient.queryMember(ModelConstants.MEMBER_TYPE, request.get("memberId"), optionalFields);
        log.info("swire查询经验值:{}",JSON.toJSONString(memberDto));
        if (null == memberDto.getOptionalFieldData().get("frozenPoint")) {
            map.put("freezeBottle", "0.0");
        } else {
            double point = Math.abs(Double.parseDouble(memberDto.getOptionalFieldData().get("frozenPoint").toString()));
            double afterBottle = addPoint(point, Double.parseDouble(request.get("bottle")));
            map.put("freezeBottle", String.valueOf(point));
            map.put("afterBottle", String.valueOf(afterBottle));
        }
        if (null == memberDto.getOptionalFieldData().get("experiencePoints")) {
            map.put("growBottle", "0.0");
        } else {
            map.put("growBottle", memberDto.getOptionalFieldData().get("experiencePoints").toString());
        }
        ReturnResultsDto body = null;
        try {
            /*
            body = swirePostBody(ConfigurationCenterUtil.HTTP_SWIRE_HISTORY_MEMBER_POINT, map);
             */
            body = swirePostBodyKA(ConfigurationCenterUtil.HTTP_SWIRE_HISTORY_MEMBER_POINT, map);
        } catch (Exception e) {
            log.error("同步历史太古积分记录失败orderId:{},msg:{}",request.get("orderId") ,e.getMessage());
            map.put("code", 500);
            map.put("msg", "同步历史太古积分记录接口报错");
            //记录失败的积分记录
            map.put("lastSync", ZonedDateTime.now());
            map.put("failuresNumber", request.get("failuresNumber"));
            map.put("memberId",request.get("memberId"));
            //记录失败
            DataApiUtil.upsertIsData(ModelConstants.SWIRE_HIATORYPOINT_SYNC_LOG,request.get("orderId"), map);
        }
        if (null == body){
            map.put("code", 500);
            map.put("msg", "同步历史太古积分记录接口报错");
            //记录失败的积分记录
            map.put("lastSync", ZonedDateTime.now());
            map.put("failuresNumber", request.get("failuresNumber"));
        }else {
            map.put("code",body.getCode());
            map.put("msg",body.getMsg());
            map.put("status",body.getStatus());
            map.put("lastSync", ZonedDateTime.now());
            map.put("failuresNumber", request.get("failuresNumber"));
        }
        map.put("memberId",request.get("memberId"));
        DataApiUtil.upsertIsData(ModelConstants.SWIRE_HIATORYPOINT_SYNC_LOG,request.get("orderId"), map);
        return result;
    }

/*    @Override
    public void dataSyncMember(WechatRegisterRequest request) {
        SwireMemberDto swireMemberDto = new SwireMemberDto();
        try {
            swireMemberDto.setMobile(request.getMobile());
            swireMemberDto.setOpenId(request.getOpenId());
            swireMemberDto.setAppId(request.getAppId());
            swireMemberDto.setRegisterTime(request.getRegisterTime());
            swireMemberDto.setRegisterCity(request.getCity());
            swireMemberDto.setRegisterArea(request.getProvince());
            swireMemberDto.setUnionId(request.getUnionId());
            swireMemberDto.setNickName(request.getNickname());
            swireMemberDto.setBirthDay(request.getBirthDay());
            swireMemberDto.setBirthYear(request.getBirthYear());
            swireMemberDto.setSource(request.getBindingExtProperties().get("memberSource").toString());
            swireMemberDto.setChannel(request.getChannelType());
            swireMemberDto.setAvatarUrl(request.getHeadImgUrl());
            swireMemberDto.setMemberName(request.getMemberName());
            String gender = "F".equals(request.getGender()) ? "2" : "M".equals(request.getGender()) ? "1" : "0";
            swireMemberDto.setGender(gender);
            if (StringUtils.isEmpty(request.getRegisterTime())) {
                request.setRegisterTime(DateUtil.getDateTime());
            }
            //放入kaFka队列
            dataSyncMember.sendMemberData(swireMemberDto);
        } catch (Exception e) {
            log.error("注册同步推送kafka失败:{},msg:{}", JSON.toJSONString(swireMemberDto), e.getMessage());
        }

    }

    @Override
    public void dataSyncUpdateMember(SwireMemberDto swireMemberDto) {
        try {
            dataSyncMember.updateMemberData(swireMemberDto);
        } catch (Exception e) {
            log.error("更新同步推送kafka失败:{},msg:{}", JSON.toJSONString(swireMemberDto), e.getMessage());
        }
    }*/

    public static double addPoint(double v1, double v2) {
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return b1.add(b2).doubleValue();
    }
    private boolean ruleIsDate(String startTime, String endTime) {
        String timeStrart = startTime.substring(0, 19).replace("T", " ");
        String endTimes = endTime.substring(0, 19).replace("T", " ");
        Date startTimeDate = getStartTime(timeStrart);
        Date nowTimes = getStartTime(endTimes);


        //当前时间
        //Date nowTime = new Date();
        Calendar date = Calendar.getInstance();
        date.setTime(nowTimes);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTimeDate);

        //log.info("开始时间:{},规则时间:{}",startTimeDate,nowTimes);
        //比较
        //before():指定时间是否早于当前时间
        //after()指定时间晚于当前时间
        if (begin.after(date)) {
            return true;
        } else {
            return false;
        }
    }


    /**
     * 同步太古会员
     *
     * @param swireMemberRecordsDto
     * @return
     */
    @Override
    public ResponseResult registerSyncMember(SwireMemberRecordsDto swireMemberRecordsDto,String url,String status) {

        SwireMemberDto swireMemberDto = JsonUtils.parseJavaType(JsonUtils.toJson(swireMemberRecordsDto), SwireMemberDto.class);

        swireMemberDto.setSid(ConfigurationCenterUtil.HTTP_SWIRE_MEMBER_SID);
        log.info("dataSyncMember入参:{}", JSONObject.toJSONString(swireMemberDto));
        SwireMemberDto registerMember = null;
        try {
            /*
            registerMember = httpPostBody(url, swireMemberDto);
             */
            registerMember = httpPostBodyKA(url, swireMemberDto);
            registerMember.setMobile(swireMemberDto.getMobile());
            registerMember.setLastSync(DateUtil.getGMT8Str(new Date()));
            registerMember.setOpenId(swireMemberRecordsDto.getOpenId());
            registerMember.setAppId(swireMemberRecordsDto.getAppId());
        } catch (Exception e) {
            registerMember.setMobile(swireMemberDto.getMobile());
            registerMember.setLastSync(DateUtil.getGMT8Str(new Date()));
            registerMember.setCode(500);
            registerMember.setMsg(e.getMessage());
            registerMember.setOpenId(swireMemberRecordsDto.getOpenId());
            registerMember.setAppId(swireMemberRecordsDto.getAppId());
            if(status == "1"){
                swireMemberRepository.upsert(ModelConstants.SWIRE_MEMBER_SYNC_LOG, swireMemberRecordsDto.getMemberId(), JSONObject.parseObject(JSONObject.toJSONString(registerMember), HashMap.class));
            }else {
                swireMemberRepository.upsert(ModelConstants.SWIRE_UPDATEMEMBEBER_SYNC_LOG, swireMemberRecordsDto.getMemberId(), JSONObject.parseObject(JSONObject.toJSONString(registerMember), HashMap.class));
            }
            log.error("注册更新会员到太古失败:{},url:{},msg:{},status:{}",JSON.toJSONString(swireMemberDto),url,e.getMessage(),status);
        }
        if(status == "1"){
            swireMemberRepository.upsert(ModelConstants.SWIRE_MEMBER_SYNC_LOG, swireMemberRecordsDto.getMemberId(), JSONObject.parseObject(JSONObject.toJSONString(registerMember), HashMap.class));
        }else {
            swireMemberRepository.upsert(ModelConstants.SWIRE_UPDATEMEMBEBER_SYNC_LOG, swireMemberRecordsDto.getMemberId(), JSONObject.parseObject(JSONObject.toJSONString(registerMember), HashMap.class));
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS.getCode(), JSONObject.toJSONString(registerMember));
    }

    private ReturnResultsDto swirePostBody(String url, Map<String, Object> map) {
        Map<String, String> head = new HashMap<>();
        head.put("sign", getSignMap(map, url, ConfigurationCenterUtil.HTTP_SWIRE_MEMBER_SIGN_KEY));
        log.info("sign:{},orderId:{}",head.get("sign"));
        log.info("太古积分同步封装orderId:{},封装对象:{}",map.get("orderId").toString(),JSONObject.toJSONString(map));
        String body = HttpClientHelper.doPostJSON(ConfigurationCenterUtil.HTTP_SWIRE_MEMBER_HOST + url, map, head);
        ReturnResultsDto result = JSONObject.parseObject(body, ReturnResultsDto.class);
        log.info("太古积分同步orderId:{},返回值:{}", map.get("orderId").toString(),JSONObject.toJSONString(result));
        return result;
    }

    private SwireMemberDto httpPostBody(String url, SwireMemberDto swireMemberDto) {
        Map<String, String> head = new HashMap<>();
        head.put("sign", getSign(swireMemberDto, url, ConfigurationCenterUtil.HTTP_SWIRE_MEMBER_SIGN_KEY));
        log.info("注册更新的mobile:{},sign:{}",swireMemberDto.getMobile(),head.get("sign"));
        String body = HttpClientHelper.doPostJSON(ConfigurationCenterUtil.HTTP_SWIRE_MEMBER_HOST + url, swireMemberDto, head);
        SwireMemberDto result = JSONObject.parseObject(body, SwireMemberDto.class);
        log.info("太古返回值:{}", JSONObject.toJSONString(result));
        return result;
    }

    private ReturnResultsDto swirePostBodyKA(String url, Map<String, Object> map) {
        Map<String, String> head = new HashMap<>();
        head.put("sign", getSignMap(map, url, ConfigurationCenterUtil.HTTP_SWIRE_MEMBER_SIGN_KEY));
        log.info("sign:{},orderId:{}",head.get("sign"));
        log.info("KA太古积分同步封装orderId:{},封装对象:{}",map.get("orderId").toString(),JSONObject.toJSONString(map));
        String body = HttpClientHelper.doPostJSONKA(ConfigurationCenterUtil.HTTP_SWIRE_MEMBER_HOST + url, map, head);
        ReturnResultsDto result = JSONObject.parseObject(body, ReturnResultsDto.class);
        log.info("KA太古积分同步orderId:{},返回值:{}", map.get("orderId").toString(),JSONObject.toJSONString(result));
        return result;
    }

    private SwireMemberDto httpPostBodyKA(String url, SwireMemberDto swireMemberDto) {
        Map<String, String> head = new HashMap<>();
        head.put("sign", getSign(swireMemberDto, url, ConfigurationCenterUtil.HTTP_SWIRE_MEMBER_SIGN_KEY));
        log.info("KA注册更新的mobile:{},sign:{}",swireMemberDto.getMobile(),head.get("sign"));
        String body = HttpClientHelper.doPostJSONKA(ConfigurationCenterUtil.HTTP_SWIRE_MEMBER_HOST + url, swireMemberDto, head);
        SwireMemberDto result = JSONObject.parseObject(body, SwireMemberDto.class);
        log.info("KA太古返回值:{}", JSONObject.toJSONString(result));
        return result;
    }

    private static String getSignMap(Map<String, Object> map, String url, String key) {
        JSONObject paramObj = JSONObject.parseObject(JSONObject.toJSONString(map));
        String[] paramArr = paramObj.keySet().toArray(new String[paramObj.keySet().size()]);
        paramArr = SortByWeightsUtils.sortByWeights(paramArr);
        String b = "";
        for (String param : paramArr) {
            if (StringUtils.isEmpty(paramObj.getString(param))) {
                continue;
            }
            b = b + "&" + param + "=" + paramObj.get(param);
        }
        String signString = url + b + "&" + key;
        return Sha256Utils.getSHA256Str(signString);

    }

    private static String getSign(SwireMemberDto swireMemberDto, String url, String key) {
        swireMemberDto.setResultNull();
        JSONObject paramObj = JSONObject.parseObject(JSONObject.toJSONString(swireMemberDto));
        String[] paramArr = paramObj.keySet().toArray(new String[paramObj.keySet().size()]);
        paramArr = SortByWeightsUtils.sortByWeights(paramArr);
        String b = "";
        for (String param : paramArr) {
            if (StringUtils.isEmpty(paramObj.getString(param))) {
                continue;
            }
            b = b + "&" + param + "=" + paramObj.get(param);
        }
        String signString = url + b + "&" + key;
        log.info("会员同步签名:{}",b);
        return Sha256Utils.getSHA256Str(signString);

    }


    public static boolean isEffectiveDate(String startTime, String nowTime) {
        String timeStrart = startTime.substring(0, 19).replace("T", " ");
        Date startTimeDate = getStartTime(timeStrart);
        Date nowTimes = DateHelper.transferString2Date(nowTime);

        //当前时间
        //Date nowTime = new Date();
        Calendar date = Calendar.getInstance();
        date.setTime(nowTimes);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTimeDate);

        //log.info("开始时间:{},规则时间:{}",startTimeDate,nowTimes);
        //比较
        //before():指定时间是否早于当前时间
        //after()指定时间晚于当前时间
        if (begin.after(date)) {
            return true;
        } else {
            return false;
        }
    }

    public static Date getStartTime(String time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date timeDate = null;
        try {
            Date dt = sdf.parse(time);
            Calendar rightNow = Calendar.getInstance();
            rightNow.setTime(dt);
            rightNow.add(Calendar.HOUR, 8);
            timeDate = rightNow.getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return timeDate;
    }

    public static void main(String[] args) {
        String param = "{\n" +
                "    \"appId\": \"wxad63eb2ce3f0d7d5\",\n" +
                "    \"avatarUrl\": \"https://iuat.icoke.cn/public/member-resource/350115376571420672/2022-11-17T15:27:00.508/avatar.jpeg\",\n" +
                "    \"gender\": \"0\",\n" +
                "    \"mobile\": \"18565647923\",\n" +
                "    \"nickName\": \"龙小\",\n" +
                "    \"registerCity\": \"\",\n" +
                "    \"registerTime\": \"2023-05-25 10:19:29\",\n" +
                "    \"sid\": \"ad4c75625fd34009b52678e306f6cb03\",\n" +
                "    \"unionId\": \"oAzxt07aDQ2uldj8H8nt8Gh7S_FY\",\n" +
                "    \"userId\": \"ox3-v4siw2AATMnhQqRRbRvl87yk\"\n" +
                "}";
        String url = "/surface/addSYCrmMember";
        SwireMemberDto swireMemberDto = JSONObject.parseObject(param, SwireMemberDto.class);
        String sign = getSign(swireMemberDto, url, "9750c7dc2b654ccf895eb0a5da0d6fc3");
        System.out.println(sign);
        System.out.println(JSONObject.toJSONString(swireMemberDto));
    }

}
