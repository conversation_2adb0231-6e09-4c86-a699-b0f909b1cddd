package com.shuyun.kylin.customized.member.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CepMemberGetableDto {

    private String customerNo;
    private String scene;
    private String channelType;
    private Integer ruleCode;
    private String channelFrom;
    @NotBlank(message = "会员类型不能为空,固定值KO")
    private String memberType;
    @NotBlank(message = "memberId不能为空")
    private String memberId;

}
