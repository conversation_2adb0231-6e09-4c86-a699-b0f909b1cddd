package com.shuyun.kylin.customized.spi.resource;

import com.shuyun.kylin.data.dto.FilterResponse;
import com.shuyun.kylin.data.dto.request.*;
import com.shuyun.kylin.data.dto.taobao.request.TaobaoOrderEventDto;
import com.shuyun.kylin.data.dto.taobao.response.TaobaoFilterResponse;
import com.shuyun.pip.component.json.JsonUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

@Tag(name = "数据流方案包SPI实现", description = "用于自定义数据流的过滤、调整入参等行为")
@RestController
@RequestMapping("/data/docking/spi")
@ConditionalOnExpression("${customized-service.data.docking.spi.enabled:false}")
public class CustomizedDataDockingSpiResource {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @PostMapping("/taobao/order/filter")
    @Operation(summary = "淘宝订单过滤SPI", description = "应用场景: 淘宝订单自定义过滤")
    public TaobaoFilterResponse filterTaobaoOrder(@Valid @RequestBody TaobaoOrderEventDto taobaoOrderEventDto,
                                                  @RequestParam("memberType") String memberType,
                                                  @RequestParam("filterResult")Boolean filterResult) {
        TaobaoFilterResponse filterResponse = new TaobaoFilterResponse();
        filterResponse.setResult(true);
        logger.info("接到data-docking淘宝订单过滤SPI请求,{}",JsonUtils.toJson(taobaoOrderEventDto));
        return filterResponse;
    }

    @PostMapping("/taobao/refund/filter")
    @Operation(summary = "淘宝退单过滤SPI", description = "应用场景: 淘宝退单自定义过滤")
    public TaobaoFilterResponse filterTaobaoRefund(@Valid @RequestBody TaobaoRefundEventDto taobaoRefundEventDto,
                                                   @RequestParam("memberType") String memberType,
                                                   @RequestParam("filterResult")Boolean filterResult) {
        TaobaoFilterResponse filterResponse = new TaobaoFilterResponse();
        filterResponse.setResult(true);
        logger.info("接到data-docking淘宝退单过滤SPI请求,{}",JsonUtils.toJson(taobaoRefundEventDto));
        return filterResponse;
    }

    @PostMapping("/jd/order/filter")
    @Operation(summary = "京东订单过滤SPI", description = "应用场景: 京东订单自定义过滤")
    public FilterResponse filterJdOrder(@Valid @RequestBody JdOrderEventDto jdOrderEventDto,
                                        @RequestParam("memberType") String memberType,
                                        @RequestParam("filterResult")Boolean filterResult) {
        FilterResponse filterResponse = new FilterResponse();
        filterResponse.setResult(true);
        logger.info("接到data-docking京东订单过滤SPI请求,{}",JsonUtils.toJson(jdOrderEventDto));
        return filterResponse;
    }

    @PostMapping("/jd/refund/filter")
    @Operation(summary = "京东退单过滤SPI", description = "应用场景: 京东退单自定义过滤")
    public FilterResponse filterJdRefund(@Valid @RequestBody JdRefundEventDto jdRefundEventDto,
                                         @RequestParam("memberType") String memberType,
                                         @RequestParam("filterResult")Boolean filterResult) {
        FilterResponse filterResponse = new FilterResponse();
        filterResponse.setResult(true);
        logger.info("接到data-docking京东退单过滤SPI请求,{}",JsonUtils.toJson(jdRefundEventDto));
        return filterResponse;
    }

    @PostMapping("/youzan/order/filter")
    @Operation(summary = "有赞订单过滤SPI", description = "应用场景: 有赞订单自定义过滤")
    public FilterResponse filterYzOrder(@Valid @RequestBody YouzanOrderEventDto youzanOrderEventDto,
                                        @RequestParam("memberType") String memberType,
                                        @RequestParam("filterResult")Boolean filterResult) {
        FilterResponse filterResponse = new FilterResponse();
        filterResponse.setResult(true);
        logger.info("接到data-docking有赞订单过滤SPI请求,{}",JsonUtils.toJson(youzanOrderEventDto));
        return filterResponse;
    }

    @PostMapping("/youzan/refund/filter")
    @Operation(summary = "有赞退单过滤SPI", description = "应用场景: 有赞退单自定义过滤")
    public FilterResponse filterYzRefund(@Valid @RequestBody YouzanRefundEventDto youzanRefundEventDto,
                                         @RequestParam("memberType") String memberType,
                                         @RequestParam("filterResult")Boolean filterResult) {
        FilterResponse filterResponse = new FilterResponse();
        filterResponse.setResult(true);
        logger.info("接到data-docking有赞退单过滤SPI请求,{}",JsonUtils.toJson(youzanRefundEventDto));
        return filterResponse;
    }

    @PostMapping("/douyin/order/filter")
    @Operation(summary = "抖音订单过滤SPI", description = "应用场景: 抖音订单自定义过滤")
    public FilterResponse filterDouyinOrder(@Valid @RequestBody DouyinOrderEventDto douyinOrderEventDto,
                                        @RequestParam("memberType") String memberType,
                                        @RequestParam("filterResult")Boolean filterResult) {
        FilterResponse filterResponse = new FilterResponse();
        filterResponse.setResult(true);
        logger.info("接到data-docking抖音订单过滤SPI请求,{}",JsonUtils.toJson(douyinOrderEventDto));
        return filterResponse;
    }

    @PostMapping("/douyin/refund/filter")
    @Operation(summary = "抖音退单过滤SPI", description = "应用场景: 抖音退单自定义过滤")
    public FilterResponse filterDouyinRefund(@Valid @RequestBody DouyinRefundEventDto douyinRefundEventDto,
                                         @RequestParam("memberType") String memberType,
                                         @RequestParam("filterResult")Boolean filterResult) {
        FilterResponse filterResponse = new FilterResponse();
        filterResponse.setResult(true);
        logger.info("接到data-docking抖音退单过滤SPI请求,{}",JsonUtils.toJson(douyinRefundEventDto));
        return filterResponse;
    }

    @PostMapping("/loyalty/order/filter")
    @Operation(summary = "忠诚度订单过滤SPI", description = "应用场景: 忠诚度订单自定义过滤")
    public FilterResponse filterLoyaltyOrder(@Valid @RequestBody OmniLoyaltyOrderDto omniLoyaltyOrderDto,
                                             @RequestParam("memberType") String memberType,
                                             @RequestParam("filterResult")Boolean filterResult) {
        TaobaoFilterResponse filterResponse = new TaobaoFilterResponse();
        filterResponse.setResult(true);
        logger.info("接到data-docking忠诚度订单过滤SPI请求,{}",JsonUtils.toJson(omniLoyaltyOrderDto));
        return filterResponse;
    }

    @PostMapping("/loyalty/refund/filter")
    @Operation(summary = "忠诚度退单过滤SPI", description = "应用场景: 忠诚度退单自定义过滤")
    public FilterResponse filterLoyaltyRefund(@Valid @RequestBody OmniLoyaltyRefundDto omniLoyaltyRefundDto,
                                              @RequestParam("memberType") String memberType,
                                              @RequestParam("filterResult")Boolean filterResult) {
        FilterResponse filterResponse = new FilterResponse();
        filterResponse.setResult(true);
        logger.info("接到data-docking忠诚度退单过滤SPI请求,{}",JsonUtils.toJson(omniLoyaltyRefundDto));
        return filterResponse;
    }

    @PostMapping("/loyalty/event/rebuild")
    @Operation(summary = "忠诚度事件重构SPI", description = "应用场景: 忠诚度事件重构")
    public Map<String,Object> rebuildLoyaltyEvent(@Valid @RequestBody Map<String,Object> request ,
                                              @RequestParam("memberType") String memberType) {
        logger.info("接到data-docking忠诚度事件重构SPI请求,{}",JsonUtils.toJson(request));
        return request;
    }


    @PostMapping("/omni/order/rebuild")
    @Operation(summary = "全渠道订单重构SPI", description = "应用场景: 全渠道订单重构")
    public OrderDto rebuildOmniOrder(@Valid @RequestBody OrderDto orderDto,
                                     @RequestParam("memberType") String memberType) {
        logger.info("接到data-docking全渠道订单重构SPI请求,{}",JsonUtils.toJson(orderDto));
        return orderDto;
    }

    @PostMapping("/omni/refund/rebuild")
    @Operation(summary = "全渠道退单重构SPI", description = "应用场景: 全渠道退单重构")
    public RefundOrderDto rebuildOmniRefund(@Valid @RequestBody RefundOrderDto refundOrderDto,
                                            @RequestParam("memberType") String memberType) {
        logger.info("接到data-docking全渠道退单重构SPI请求,{}",JsonUtils.toJson(refundOrderDto));
        return refundOrderDto;
    }

}
