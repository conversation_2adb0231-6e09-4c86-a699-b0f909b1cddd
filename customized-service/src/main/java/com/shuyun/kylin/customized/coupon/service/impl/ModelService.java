package com.shuyun.kylin.customized.coupon.service.impl;

import com.google.common.collect.Lists;
import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.kylin.customized.base.common.KukeduoConstant;
import com.shuyun.kylin.customized.base.dm.CouponDetailRepository;
import com.shuyun.kylin.customized.base.dm.CouponRuleRepository;
import com.shuyun.kylin.customized.base.util.DataapiSdkUtil;
import com.shuyun.kylin.customized.coupon.dto.CustomerCouponDto;
import com.shuyun.kylin.customized.coupon.dto.CustomerDto;
import com.shuyun.kylin.customized.coupon.dto.ExtCouponDetailDto;
import com.shuyun.kylin.customized.coupon.dto.ExtCouponRuleDto;
import com.shuyun.kylin.customized.coupon.enums.StatusEnum;
import com.shuyun.motor.common.cons.PropsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ModelService {

    @Autowired
    private CouponRuleRepository couponRuleRepository;

    @Autowired
    private CouponDetailRepository couponDetailRepository;

    final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();

    /**
     * 根据batchId获取权益memberType
     * @param batchId
     * @return
     */
    public ExtCouponRuleDto getExtCouponRuleByBatchId(String batchId){
//        String sql = "select memberType,channelType from " + KukeduoConstant.COUPONRULE_FQN + "" +
//                " where couponRuleId = '" + batchId + "'";
//        log.info("执行sql：{}",sql);

        Map<String,Object> filter = new HashMap<>();
        filter.put("couponRuleId",batchId);
        //        BaseResponse<Map<String,String>> baeResponse = dataapiHttpSdk.execute(sql, Collections.emptyMap());
//        if(baeResponse.getIsSuccess() && !baeResponse.getData().isEmpty()){
//            return baeResponse.getData().get(0);
//        }
        return couponRuleRepository.queryByFilter(KukeduoConstant.COUPONRULE_FQN,null,filter);
    }

    /**
     * 根据memberId转化成酷客多渠道customerNo
     * @param fqn
     * @param customerCouponDto
     * @return
     */
    public List<Map<String,String>> getKkdCustomerNos(String fqn , CustomerCouponDto customerCouponDto){
        List<Map<String,String>> resultMapList = Lists.newArrayList();
        int pageIndex = Integer.parseInt(PropsUtil.getSysOrEnv("customized.customer.transfer.size", "500"));
        int totalSize = customerCouponDto.getData().size();
        int pageNo = 0;
        log.info("MemberBinding本次查询总数：{}",totalSize);
        while(true){
            List<String> memberIdList = new ArrayList<>();
            customerCouponDto.getData().stream().skip((long) pageNo *pageIndex).limit(pageIndex).collect(Collectors.toList()).forEach(
                  t ->  memberIdList.add(t.getCustomerNo())
            );
            ++pageNo;
            if(memberIdList.isEmpty()){
                log.info("MemberBinding查询分页查询结束，跳出循环");
                break;
            }
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("memberIdList",memberIdList);
            String sql = "select customerNo,memberId from "+fqn+" where channelType = '" + KukeduoConstant.VALUE_KKD + "' and memberId in (:memberIdList)";
            log.info("执行第{}次循环,本次提交{}条。", pageNo , memberIdList.size());
            BaseResponse<Map<String,String>> baseResponse = dataapiHttpSdk.execute(sql,paramMap);
            resultMapList.addAll(baseResponse.getData());
        }
        return resultMapList;
    }

    @Async
    public void saveCouponDetail(CustomerCouponDto customerCouponDto, ExtCouponRuleDto extCouponRuleDto){
        long begin = System.currentTimeMillis();
        Date nowTime = new Date();
        SimpleDateFormat formater = new SimpleDateFormat(
                "yyyy-MM-dd'T'HH:mm:ss");
        formater.setTimeZone(TimeZone.getTimeZone("GMT"));
        String nowTimeStr = formater.format(nowTime);
        List<ExtCouponDetailDto> couponDetailList = Lists.newArrayList();
        for(CustomerDto customerDto : customerCouponDto.getData()){
            if(customerDto.getStatus().equalsIgnoreCase(StatusEnum.GRANT_SUCCESS.getValue())) {
                ExtCouponDetailDto extCouponDetailDto = new ExtCouponDetailDto();
                extCouponDetailDto.setId(DigestUtils.md5Hex(customerCouponDto.getMemberType()+customerDto.getCoupon()).toLowerCase());
                extCouponDetailDto.setMemberType(customerCouponDto.getMemberType());
                extCouponDetailDto.setChannelType(extCouponRuleDto.getChannelType());
                extCouponDetailDto.setChannelTypeName(extCouponRuleDto.getChannelTypeName());
                extCouponDetailDto.setMemberId(customerDto.getCustomerNo());
                extCouponDetailDto.setCouponRuleId(extCouponRuleDto.getCouponRuleId());
                extCouponDetailDto.setCouponNo(customerDto.getCoupon());
                extCouponDetailDto.setCouponName(extCouponRuleDto.getCouponName());
                extCouponDetailDto.setCouponType(extCouponRuleDto.getCouponType());
                extCouponDetailDto.setCouponStatus("0");
                extCouponDetailDto.setCreateTime(nowTimeStr);
                extCouponDetailDto.setLastSync(nowTimeStr);
                extCouponDetailDto.setGrantTime(nowTimeStr);
                couponDetailList.add(extCouponDetailDto);
            }
        }
        int insertCount = couponDetailRepository.batchInsert(KukeduoConstant.COUPON_DETAIL_FQN,couponDetailList);
        log.info("保存优惠券人券关系模型结束，保存条数：{}，耗时：{} ms",insertCount,(System.currentTimeMillis() - begin));
    }

}
