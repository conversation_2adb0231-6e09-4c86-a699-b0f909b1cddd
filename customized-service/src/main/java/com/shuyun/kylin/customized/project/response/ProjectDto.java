package com.shuyun.kylin.customized.project.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

@Data
public class ProjectDto {

    /**
     * 发放总量
     */
    private Integer maxQuantity;

    /**
     * 权益类型
     * VIRTUAL：虚拟权益
     * PHYSICAL	：实物权益
     */
    private String type;

    /**
     * 虚拟权益类型
     * COUPON：优惠券
     * WALLPAPER：海报
     * OTHER：其他
     */
    private String virtualType;

    /**
     * 券商
     * COKESTORE：可口可乐商店
     * UBR：UBR
     * MEITUAN	美团
     * COSTANORTH：COSTA北区
     * COSTASOUTH：COSTA南区
     * EXTERNALCODE：券码导入
     */
    private String projectBusiness;

    /**
     * 劵类型 劵类型 1P：一方券 2P：二方券 3P：三方
     */
    private String couponType;

    /**
     * 发放形式
     * DIRECT：直冲
     * INDIRECT：非直冲
     */
    private String projectGrantType;

    //发放渠道
    private String grantPlatforms;

    //权益成本
    private String cost;

    //权益规则说明
    private String description;

    //项目Id
    private String projectId;

    //三方项目id
    private String externalProjectId;

    // 所需积分
    private Integer point;

    // 是否积分兑换权益
    private String isPointExchange;

    // 红包金额
    private Double amount;
}
