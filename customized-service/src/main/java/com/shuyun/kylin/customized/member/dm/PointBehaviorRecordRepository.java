package com.shuyun.kylin.customized.member.dm;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.dm.BaseDsRepository;

import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.base.util.DateHelper;
import com.shuyun.kylin.customized.member.dto.PointBehaviorRecordDto;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class PointBehaviorRecordRepository extends BaseDsRepository<PointBehaviorRecordDto> {

    /**
     * 查询总数
     * @param memberId
     */
    public String getMemberPointCount(String memberId,String startTime,String endTime) {
        String str = "";
      /*  if (io.micrometer.core.instrument.util.StringUtils.isNotBlank(startTime)){
            str = " and modified >= '" + DateHelper.getZone(startTime) + "' ";
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotBlank(endTime)){
            str = " and modified <= '" +  DateHelper.getZone(endTime) + "' ";
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotBlank(startTime)&& StringUtils.isNotBlank(endTime)){
            str = " and modified BETWEEN '" + DateHelper.getZone(startTime) + "' and  '" +  DateHelper.getZone(endTime)  + "' ";
        }*/
        //SELECT * FROM  d_l_m_a_PointRecord60003_283 q left JOIN (SELECT id FROM d_l_m_a_PointRecord60003_283  WHERE memberId = '61bf6c4610ff423384de897ffeabe8ef' AND recordType ='FREEZE' or recordType ='UNFREEZE' or recordType = 'SPECIAL_FREEZE' or recordType = 'SPECIAL_UNFREEZE' or recordType = 'OPEN_UNFREEZE'or recordType = 'OPEN_FREEZE') a ON q.id=a.id  WHERE q.memberId = '61bf6c4610ff423384de897ffeabe8ef'  AND a.id is null

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        String queryMemberSql = " select count(1) as count from " + ConfigurationCenterUtil.MEMBER_POINTRECORD + " q left JOIN " +
                " (SELECT id from " + ConfigurationCenterUtil.MEMBER_POINTRECORD + " WHERE memberId = :memberId AND recordType ='FREEZE' or recordType ='UNFREEZE' or recordType = 'SPECIAL_FREEZE' or recordType = 'SPECIAL_UNFREEZE' or recordType = 'OPEN_UNFREEZE' or recordType = 'OPEN_FREEZE' ) a " +
                " ON q.id=a.id where q.memberId = :memberId AND a.id is null GROUP BY IFNULL(`key`,q.id),`desc` ";
        log.info("查询总数sql:{}",queryMemberSql);
        List<Map<String,Object>> list = execute(queryMemberSql, queryMap).getData();
        String count = "0";
        if (!CollectionUtils.isEmpty(list)){
           /* for (Map map : list) {
                count = map.get("count").toString();
            }*/
            count = String.valueOf(list.size());
        }
        return count;
    }

    public String getSwirePointCount(String memberId,String startTime,String endTime) {
        String str = "";
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId", memberId);
        String queryMemberSql = "SELECT count(1) as count from " + ConfigurationCenterUtil.MEMBER_POINTRECORD + " WHERE memberId = :memberId  ";
        log.info("Swire查询总数sql:{}",queryMemberSql);
        List<Map<String,Object>> list = execute(queryMemberSql, queryMap).getData();
        String count = "0";
        if (!CollectionUtils.isEmpty(list)){
            count = String.valueOf(list.size());
        }
        return count;
    }


    /**
     * 查询会员积分记录
     * @return
     */
    public List<PointBehaviorRecordDto> selectMemberPointItemlist(String memberId, String startTime, String endTime, Integer page, Integer pageSize) {
        String str = "";
        if (io.micrometer.core.instrument.util.StringUtils.isNotBlank(startTime)){
            str = " and modified >= '" + DateHelper.getZone(startTime) + "' ";
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotBlank(endTime)){
            str = " and modified <= '" +  DateHelper.getZone(endTime) + "' ";
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotBlank(startTime)&& StringUtils.isNotBlank(endTime)){
            str = " and modified BETWEEN '" + DateHelper.getZone(startTime) + "' and  '" +  DateHelper.getZone(endTime)  + "' ";
        }
        String change = " " + str + " GROUP BY IFNULL(`key`,id),`desc` order by a.modified desc LIMIT  " + pageSize + " OFFSET " + (page - 1) * pageSize + " ";
        //String change = " " + str + " GROUP BY IFNULL(`key`,id),`desc` order by a.modified desc limit  " + page + "," + pageSize + " ";
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId",memberId);


        //select sum(point) as point from d_l_m_a_PointRecord60003_283 where memberId = 'a702b15baa834ab49b88b2bd790b6886' GROUP BY `key` order by modified desc LIMIT 20 OFFSET 0

        String querySql = " SELECT sum(point) as point,a.desc,a.modified as changeTime,a.recordType as changeType,a.channel as channelType,a.KZZD2 as changeSource,a.KZZD1 as changeSourceDetail,actionId,actionNodeId,changeMode" +
                "  FROM  " + ConfigurationCenterUtil.MEMBER_POINTRECORD + " a where memberId = :memberId and recordType != 'DELAY_SEND' " + change + "";

        log.info("会员积分记录sql:{}",querySql);
        List<PointBehaviorRecordDto> couponList = executeSQL(querySql, queryMap);
        return couponList;
    }

    public List<PointBehaviorRecordDto> swirePointItemlist(String memberId, String startTime, String endTime, Integer page, Integer pageSize) {
        String str = "";
        if (io.micrometer.core.instrument.util.StringUtils.isNotBlank(startTime)){
            str = " and modified >= '" + DateHelper.getZone(startTime) + "' ";
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotBlank(endTime)){
            str = " and modified <= '" +  DateHelper.getZone(endTime) + "' ";
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotBlank(startTime)&& StringUtils.isNotBlank(endTime)){
            str = " and modified BETWEEN '" + DateHelper.getZone(startTime) + "' and  '" +  DateHelper.getZone(endTime)  + "' ";
        }
        String change = " " + str + " order by a.modified desc LIMIT  " + pageSize + " OFFSET " + (page - 1) * pageSize + " ";
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId",memberId);

        String querySql = " SELECT id,shopId, point,a.desc,a.effectiveDate as changeTime,a.modified,a.recordType as changeType,a.channel as channelType,a.KZZD2 as changeSource,a.KZZD1 as changeSourceDetail,actionId,actionNodeId,changeMode" +
                "  FROM  " + ConfigurationCenterUtil.MEMBER_POINTRECORD + " a where memberId = :memberId and recordType != 'DELAY_SEND' " + change + "";

        log.info("swire会员积分记录sql:{}",querySql);
        List<PointBehaviorRecordDto> couponList = executeSQL(querySql, queryMap);
        return couponList;
    }


    /**
     * 查询会员积分记录
     * @return
     */
    public List<PointBehaviorRecordDto> selectMemberPointIteml(String memberId, String startTime, String endTime, Integer page, Integer pageSize) {
        String str = "";
        if (io.micrometer.core.instrument.util.StringUtils.isNotBlank(startTime)){
            str = " and modified >= '" + DateHelper.getZone(startTime) + "' ";
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotBlank(endTime)){
            str = " and modified <= '" +  DateHelper.getZone(endTime) + "' ";
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotBlank(startTime)&& StringUtils.isNotBlank(endTime)){
            str = " and modified BETWEEN '" + DateHelper.getZone(startTime) + "' and  '" +  DateHelper.getZone(endTime)  + "' ";
        }
        String change = " " + str + " GROUP BY `key` order by a.modified desc LIMIT  " + pageSize + " OFFSET " + (page - 1) * pageSize + " ";
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId",memberId);


        //select sum(point) as point from d_l_m_a_PointRecord60003_283 where memberId = 'a702b15baa834ab49b88b2bd790b6886' GROUP BY `key` order by modified desc LIMIT 20 OFFSET 0

        String querySql = " SELECT sum(point) as point,a.desc,a.modified as changeTime,a.recordType as changeType,a.channel as channelType,a.KZZD2 as changeSource,a.KZZD1 as changeSourceDetail" +
                "  FROM  " + ConfigurationCenterUtil.MEMBER_POINTRECORD + " a where memberId = :memberId " + change + "";

        log.info("会员积分记录sql:{}",querySql);
        List<PointBehaviorRecordDto> couponList = executeSQL(querySql, queryMap);
        return couponList;
    }

    public List<PointBehaviorRecordDto> selectMemberPointScene(String memberId, List<String> scene,String channelType,String startTime, String endTime, Integer page,Integer pageSize) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId",memberId);
        queryMap.put("channel",channelType);
        queryMap.put("scene",scene);
        String str = "";
        if (io.micrometer.core.instrument.util.StringUtils.isNotBlank(startTime)){
            str = " and modified >= '" + DateHelper.getZone(startTime) + "' ";
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotBlank(endTime)){
            str = " and modified <= '" +  DateHelper.getZone(endTime) + "' ";
        }
        if (io.micrometer.core.instrument.util.StringUtils.isNotBlank(startTime)&& StringUtils.isNotBlank(endTime)){
            str = " and modified BETWEEN '" + DateHelper.getZone(startTime) + "' and  '" +  DateHelper.getZone(endTime)  + "' ";
        }

        String change = " " + str + " order by a.modified desc LIMIT  " + pageSize + " OFFSET " + (page - 1) * pageSize + " ";

        String querySql = " SELECT  point,a.desc,a.modified as changeTime,a.recordType as changeType,a.channel as channelType,a.KZZD2 as changeSource,a.KZZD1 as changeSourceDetail" +
                "  FROM  " + ConfigurationCenterUtil.MEMBER_POINTRECORD + " a where memberId = :memberId and channel =:channel and `desc` in (:scene) " + change + "";
        List<PointBehaviorRecordDto> couponList = executeSQL(querySql, queryMap);
        log.info("根据场景值查询到数据:{}", JSON.toJSONString(couponList));
        return couponList;
    }

    public String getPointCount(String memberId, List<String> scene, String channelType) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("memberId",memberId);
        queryMap.put("channel",channelType);
        queryMap.put("scene",scene);
        String querySql = " SELECT count(1) as count FROM  " + ConfigurationCenterUtil.MEMBER_POINTRECORD + " a where memberId = :memberId and channel =:channel and `desc` in (:scene)";
        List<Map<String,Object>> list = execute(querySql, queryMap).getData();
        String count = "0";
        if (!CollectionUtils.isEmpty(list)){
            for (Map map : list) {
                count = map.get("count").toString();
            }
        }
        return count;
    }

    public String getConvertCostCenter(String shopId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("id",shopId);
        String querySql = " SELECT convertName FROM  " + ModelConstants.CONVERT_COSTCOENTER + " a where id = :id ";
        List<Map<String,Object>> list = execute(querySql, queryMap).getData();
        String count = null;
        if (!CollectionUtils.isEmpty(list)){
            for (Map map : list) {
                count = map.get("convertName").toString();
            }
        }
        return count;
    }
}
