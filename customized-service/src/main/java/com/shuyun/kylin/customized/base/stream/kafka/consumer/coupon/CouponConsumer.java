package com.shuyun.kylin.customized.base.stream.kafka.consumer.coupon;

import com.google.common.collect.Lists;
import com.shuyun.kylin.customized.base.exception.ServiceException;
import com.shuyun.kylin.customized.base.stream.kafka.messaging.KafkaSink;
import com.shuyun.kylin.customized.base.stream.kafka.messaging.KafkaSource;
import com.shuyun.kylin.customized.coupon.dto.CustomerCouponDto;
import com.shuyun.kylin.customized.coupon.dto.CustomerDto;
import com.shuyun.kylin.customized.coupon.enums.CouponHandlerEnum;
import com.shuyun.kylin.customized.coupon.enums.CouponTypeEnum;
import com.shuyun.kylin.customized.coupon.handler.CouponSendHandlerFactory;
import com.shuyun.kylin.customized.coupon.service.ICouponService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Slf4j
@Component
@EnableBinding({KafkaSink.class, KafkaSource.class})
@ConditionalOnExpression("${system.kafka.enabled:true}")
public class CouponConsumer {
    @Autowired
    private ICouponService couponService;

    @StreamListener(KafkaSink.KAFKA_INPUT)
    public void processUser(CustomerCouponDto customerCouponDto){
        log.info("消费Kafka Topic：KafkaTopic:COUPON_NODE_OUTPUT_DATA =======> 入参:{}", customerCouponDto);
        try{
            //1. 入库 : 做测试使用。默认入库，可以通过开关来控制是否入库. 建议不要入库，直接转发
            //2. 客户提供人、劵绑定关系接口，直接调用客户人劵绑定关系接口转发，不需入库

            /**
             * 20210316
             * 修改了消息接收，按照不同的发放类型，使用不用的Handler处理
             * 如果需要调第三方接口发券，修改OutPutCustomHandler类
             */
            List<CustomerDto> customerDtoList = Optional.ofNullable(customerCouponDto.getData()).orElse(Lists.newArrayList());
            if(CollectionUtils.isEmpty(customerDtoList)){
                log.info("待发放人数为空，跳过执行发放逻辑");
                return;
            }
            String type = customerCouponDto.getType();
            if (StringUtils.isBlank(type)) {
                log.error("解析的type不能为空");
                return;
            }
            CouponTypeEnum couponTypeEnum = CouponTypeEnum.getEnumByValue(type);
            if(couponTypeEnum != null) {
                CouponSendHandlerFactory.getHandler(CouponHandlerEnum.valueOf(couponTypeEnum.getValue()).getHandlerName()).dealCoupon(customerCouponDto);
            }
        }catch (Exception ex){
            log.error("消费失败,Topic：KafkaTopic:COUPON_NODE_OUTPUT_DATA, msg:{}", ex);
            throw new ServiceException("消费Kafka Topic：KafkaTopic:COUPON_NODE_OUTPUT_DATA消息 消费失败", ex);
        }
    }
}
