package com.shuyun.kylin.customized.member.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

@Data
public class CepPointBehaviorRecordDto {
    @NotBlank(message = "客户编号不能为空，格式：appid_openid")
    private String customerNo;
    //  变更开始时间 格式: yyyy-MM-dd HH:mm:ss
    private String startTime;
    //变更结束时间 格式: yyyy-MM-dd HH:mm:ss
    private String endTime;

    @Min(value = 1,message = "页码非法")
    private Integer page;

    @Max(value = 100,message = "每页数量上限100")
    private Integer pageSize;

    public Integer getPageSize(){
        return null!=pageSize?pageSize:10;
    }
    public Integer getPage(){
        return null!=page?page:1;
    }
}
