package com.shuyun.kylin.customized.coupon.resource.openapi;


import com.shuyun.kylin.customized.coupon.dto.*;
import com.shuyun.kylin.customized.coupon.service.ICouponService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@Tag(name = "第三方优惠券相关apiDEMO")
@RestController
@RequestMapping("/coupon")
public class CouponResource {

    @Autowired
    ICouponService couponService;

    @GetMapping("/generateCode")
    @Operation(summary = "实时生成券码", tags = {})
    public CouponCodeResponseDto generateCode(CouponCodeRequestDto couponCodeRequestDto) {
        log.info("实时生成券码 =======> 入参: {}", couponCodeRequestDto);
        return couponService.generateCode(couponCodeRequestDto);
    }

    @PostMapping("/preCoupnModel")
    @Operation(summary = "预置卡券模型", tags = {})
    public BaseResponseDto preCoupnModel(@RequestBody @Valid PreCouponModelDto preCouponModelDto) {
        log.info("预置卡券模型 =======> 入参: {}", preCouponModelDto);
        return couponService.preCoupnModel(preCouponModelDto);
    }

    @PostMapping("/preEquityProject")
    @Operation(summary = "预置权益项目", tags = {})
    public BaseResponseDto preProject(@RequestBody @Valid PreCouponProjectModelDto preCouponProjectModelDto) {
        log.info("预置权益项目 =======> 入参: {}", preCouponProjectModelDto);
        return couponService.preEquityProject(preCouponProjectModelDto);
    }
}
