package com.shuyun.kylin.customized.base.job;

import com.shuyun.kylin.customized.customer.service.CustomerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/4/18 18:37
 * @description
 */
@Slf4j
@Component
@EnableScheduling
public class CdpTagScheduled {
    @Autowired
    CustomerService customerService;
    // 每天凌晨3点执行
    @Scheduled(cron = "0 0 3 * * ? ")
    /**
     * 提交CDP标签人群包生成任务
     */
    public void submitCdpTagTask() {
        customerService.submitCdpTagTask();
    }
}
