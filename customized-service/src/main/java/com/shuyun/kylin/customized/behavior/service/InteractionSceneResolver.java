package com.shuyun.kylin.customized.behavior.service;

import com.shuyun.kylin.customized.behavior.domain.Interactive;
import com.shuyun.kylin.customized.behavior.resource.po.CheckPO;
import com.shuyun.kylin.customized.behavior.resource.vo.CheckResponse;
import com.shuyun.kylin.customized.behavior.resource.vo.CommonApiResponse;

import java.util.HashMap;
import java.util.Map;

public interface InteractionSceneResolver {

    interface Constants {
        String MEMBER_TYPE = "KO";     //会员类型，固定为 KO
        String CHANGE_TYPE = "SEND";   //发放类型，固定为 SEND （发放）
        String CHANNEL_TYPE = "KO_MP";   //渠道类型，固定为 MO
        String KZZD2 = "参与互动";      //变更来源，固定为 ”互动行为“
        String DESCRIPTION = "";      //描述，留空吧
        String REWARD_NOTHING = "互动活动规则中，设置奖励积分为：0";
    }

    CheckPO.SceneType supportScene();

    CommonApiResponse<CheckResponse> resolver(CheckPO checkPO, Interactive interactive);

    class Manager {
        private static final Map<CheckPO.SceneType, InteractionSceneResolver> map = new HashMap<>();

        public static void register(CheckPO.SceneType type, InteractionSceneResolver resolver) {
            map.put(type, resolver);
        }

        public static InteractionSceneResolver get(CheckPO.SceneType type) {
            return map.get(type);
        }
    }
}
