package com.shuyun.kylin.customized.medal.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CampaignRecordRequest {
    //id
    @NotBlank(message = "主键id不能为空")
    private String id;

    //参与活动id
    @NotBlank(message = "参与活动id不能为空")
    private String campaignId;

    //参与活动名称
    @NotBlank(message = "参与活动名称不能为空")
    private String campaignName;

    //活动类型
    @NotBlank(message = "参与活动类型不能为空")
    private String campaignType;

    //会员id
    @NotBlank(message = "会员id不能为空")
    private String memberId;

    //活动参与时间
    @NotBlank(message = "活动参与时间不能为空")
    private String createTime;

    //行为id
    private String activityId;

    //行为名称
    private String activityName;
}
