package com.shuyun.kylin.customized.customer.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2022/2/28 14:26
 * @description
 */
@Data
public class CustomerTagDto {
    /**
     * openId
     */
    @NotBlank(message = "openId不能为空")
    private String openId;
    /**
     * unionId
     */
    @NotBlank(message = "unionId不能为空")
    private String unionId;
    /**
     * 用户手机号
     */
    private String mobile;
    /**
     * 会员类型
     */
    @NotBlank(message = "会员类型不能为空,固定值KO")
    private String memberType;
    /**
     *
     */
    private String tagId;
}
