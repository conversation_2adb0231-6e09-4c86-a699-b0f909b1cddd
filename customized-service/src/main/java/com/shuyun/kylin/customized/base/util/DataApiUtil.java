package com.shuyun.kylin.customized.base.util;

import com.alibaba.fastjson.JSON;
import com.shuyun.dm.api.response.DMLResponse;
import com.shuyun.dm.dataapi.sdk.DataapiSdkFactory;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk;
import com.shuyun.dm.sdk.Options;
import com.shuyun.motor.common.cons.PropsUtil;
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

@Slf4j
public class DataApiUtil {
    static final String dataApiService = PropsUtil.getSysOrEnv("dataapi/v1.ribbon.com.shuyun.motor.client.targetServer");

    //final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkFactory.INSTANCE.createDataapiHttpSdk();

    /**
     * dataapi ==> 分析库
     *
     * @param sql
     * @param paramsMap
     * @return
     */
   /* public static List<Map> queryFromOlap(String sql, Map<String, Object> paramsMap) {
        List<Map> list = new ArrayList<>();
        final DataapiHttpSdk dataapiHttpSdk;

        DataapiSdkFactory dataapiSdkFactory = DataapiSdkFactory.INSTANCE;
        dataapiSdkFactory.webSocketContainer().setDefaultMaxSessionIdleTimeout(600000);
        //分析库模式
        dataapiSdkFactory.userContextSupplier(() -> {
            UserContext.DefaultUserContext copy = new UserContext.DefaultUserContext();
            copy.getPayload();
            copy.olapForce(true).olapMode(true).setTenantId(ThreadLocalUserContext.currentUserContext().getTenantId());
            if (!Strings.isNullOrEmpty(ThreadLocalUserContext.currentUserContext().getTimeZone())) {
                copy.setTimeZone(ThreadLocalUserContext.currentUserContext().getTimeZone());
            }
            if (!Strings.isNullOrEmpty(ThreadLocalUserContext.currentUserContext().getUserId())) {
                copy.setUserId(ThreadLocalUserContext.currentUserContext().getUserId());
            }
            log.info("UserContext:{}", copy);
            return copy;
        });

        try {
            if (StringUtils.isEmpty(dataApiService)) {
                dataapiHttpSdk = dataapiSdkFactory.createDataapiHttpSdk();
            } else {
                Options options = Options.Companion.newBuilder().enableSign(true).caller(PropsUtil.getSysOrEnv("dataapi/v1.ribbon.com.shuyun.motor.client.name")).secret(PropsUtil.getSysOrEnv("dataapi/v1.ribbon.com.shuyun.motor.client.sign.secret")).version("v1").build();
                dataapiHttpSdk = dataapiSdkFactory.createDataapiHttpSdk(dataApiService, options, "customized-service");
            }
            list = dataapiHttpSdk.execute(sql, paramsMap).getData();
        } catch (Exception ex) {
            log.error("dataapiHttpSdk.execute ====> adb execute error !,msg:{}", ex.getMessage());
        }
        return list;
    }
*/

    public static List<Map<String, String>> queryAnalysis(String sql, Map<String, Object> paramsMap) {
        List<Map<String,String>> list = new ArrayList<>();
        DataapiHttpSdk sdk = DataapiSdkUtil.getDataapiHttpSdk();
        //UserContext.DefaultUserContext copy = new UserContext.DefaultUserContext();
        try (DataapiWebSocketSdk ws = sdk.asDataapiWebSocketSdk()) {
            UserContextThreadSafe.getInstance();
            UserContextThreadSafe.setOlapModeContext(true);
            UserContextThreadSafe.getInstance().setOlapMode(true);
            UserContextThreadSafe.getInstance().setOlapForce(true);
            log.info("sql:{}", sql);
            log.info("parameters:{}", paramsMap);
            log.info("UserContextThreadSafe.getInstance():{}", JSON.toJSONString(UserContextThreadSafe.getInstance()));
            ws.open();
            list = ws.execute(sql, paramsMap).getData();
        } finally {
            UserContextThreadSafe.getInstance().setOlapMode(false);
            UserContextThreadSafe.getInstance().setOlapForce(false);
            UserContextThreadSafe.setOlapModeContext(false);
        }
        return list;
    }

    public static DMLResponse upsertIsData(String fqnName, String id, Map<String, Object> paramsMap) {
        DMLResponse response = new DMLResponse();
        final DataapiHttpSdk dataapiHttpSdk;
        try {
            if (StringUtils.isEmpty(dataApiService)) {
                 dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();
            } else {
                Options options = Options.Companion.newBuilder().enableSign(true).caller(PropsUtil.getSysOrEnv("dataapi/v1.ribbon.com.shuyun.motor.client.name")).secret(PropsUtil.getSysOrEnv("dataapi/v1.ribbon.com.shuyun.motor.client.sign.secret")).version("v1").build();
                dataapiHttpSdk = DataapiSdkFactory.INSTANCE.createDataapiHttpSdk(dataApiService, options, "customized-service");
            }
            response = dataapiHttpSdk.upsert(fqnName, id, paramsMap, false);
        } catch (Exception ex) {
            //log.error("dataapiHttpSdk.upsert ====> upsert database error !,msg:{}", ex);
            response.setIsSuccess(false);
            response.setOperation(ex.getMessage());
        }
        log.info("数据服务访问" + fqnName + "返回影响行数:{}", response.getAffectedRows());
        return response;
    }

    public static DMLResponse update(String fqnName, String id, Map<String, Object> paramsMap) {
        DMLResponse response = new DMLResponse();
        final DataapiHttpSdk dataapiHttpSdk;
        try {
            if (StringUtils.isEmpty(dataApiService)) {
                dataapiHttpSdk = DataapiSdkFactory.INSTANCE.createDataapiHttpSdk();
            } else {
                Options options = Options.Companion.newBuilder().enableSign(true).caller(PropsUtil.getSysOrEnv("dataapi/v1.ribbon.com.shuyun.motor.client.name")).secret(PropsUtil.getSysOrEnv("dataapi/v1.ribbon.com.shuyun.motor.client.sign.secret")).version("v1").build();
                dataapiHttpSdk = DataapiSdkFactory.INSTANCE.createDataapiHttpSdk(dataApiService, options, "customized-service");
            }
            response = dataapiHttpSdk.update(fqnName, id, paramsMap, false);
        } catch (Exception ex) {
            response.setIsSuccess(false);
            log.error("dataapiHttpSdk.updata ====> updata database error !,msg:{}", ex);
            response.setOperation(ex.getMessage());
        }
        return response;
    }

    public static DMLResponse insert(String fqnName, Map<String, Object> paramsMap) {
        DMLResponse response = new DMLResponse();
        final DataapiHttpSdk dataapiHttpSdk;
        if (StringUtils.isEmpty(dataApiService)) {
            dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();
        } else {
            Options options = Options.Companion.newBuilder().enableSign(true).caller(PropsUtil.getSysOrEnv("dataapi/v1.ribbon.com.shuyun.motor.client.name")).secret(PropsUtil.getSysOrEnv("dataapi/v1.ribbon.com.shuyun.motor.client.sign.secret")).version("v1").build();
            dataapiHttpSdk = DataapiSdkFactory.INSTANCE.createDataapiHttpSdk(dataApiService, options, "customized-service");
        }
        try {
            response = dataapiHttpSdk.insert(fqnName, paramsMap, false, true);
        } catch (Exception ex) {
           /* log.error("dataapiHttpSdk.insert ====> insert database error !,msg:{}", ex);
            throw new ServiceException("dataapiHttpSdk.insert ====> insert database error !", ex);*/
            response.setIsSuccess(false);
            response.setOperation(ex.getMessage());
        }
        return response;
    }

    /**
     * @param poolSize  线程个数
     * @param queueSize 队列大小
     * @return
     */
    public static ExecutorService newBlockingThreadPool(int poolSize, int queueSize) {
        if (queueSize < poolSize) {
            queueSize = poolSize;
        }
        ExecutorService es = new ThreadPoolExecutor(poolSize, poolSize, 1L, TimeUnit.MINUTES,
                new LinkedBlockingQueue<Runnable>(queueSize), new ThreadPoolExecutor.CallerRunsPolicy());
        return es;
    }

    /**
     * @param corePoolSize    核心线程个数
     * @param maximumPoolSize 核心线程个数
     * @param queueSize       队列大小
     * @return
     */
    public static ExecutorService newBlockingThreadPool(int corePoolSize, int maximumPoolSize, int queueSize) {
        if (queueSize < maximumPoolSize) {
            queueSize = maximumPoolSize;
        }
        ExecutorService es = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 1L, TimeUnit.MINUTES,
                new LinkedBlockingQueue<Runnable>(queueSize), new ThreadPoolExecutor.CallerRunsPolicy());
        return es;
    }

}
