package com.shuyun.kylin.customized.base.feign.client;


import com.shuyun.kylin.customized.base.exception.CustomizeException;
import com.shuyun.kylin.customized.base.feign.dao.MemberPointModifyRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Map;

@FeignClient(name = "memberPointModifyClient")
@RequestMapping("/loyalty-manager/v1")
public interface MemberPointModifyClient {

    @PostMapping("/open/pointTransaction/member/modify")
    CustomizeException memberModifyPoint(@RequestBody MemberPointModifyRequest memberPointModifyRequest);

    @PostMapping("/open/pointTransaction/member/reverse")
    CustomizeException memberReversePoint(@RequestBody MemberPointModifyRequest memberPointModifyRequest);

}
