package com.shuyun.kylin.customized.base.exception;

import com.shuyun.kylin.starter.exception.model.IErrorType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据传输 自定义错误码
 * <AUTHOR>
 * @create 2020/1/6
 */
@AllArgsConstructor
@Getter
public enum CustomizedErrorTypes implements IErrorType {

    // request body为空
    MISSING_REQUEST_BODY("0420", "MISSING_REQUEST_BODY"),
    // 无效的 品牌类型或者数据类型
    INVALID_MEMBER_DATATYPE("0421","INVALID_MEMBER_DATATYPE"),
    // 参数不合法
    INVALID_PARAMTER("0422","INVALID_PARAMTER"),
    // 校验规则为空
    MISSING_VALIDATION_RULE("0423","MISSING_VALIDATION_RULE"),
    // 日期格式转化失败
    DATE_PARSER_ERROR("0424","DATE_PARSER_ERROR"),
    // 请求数据长度超过限制
    REQUEST_DATA_LENGTH_OVER_LIMIT("0425","REQUEST_DATA_LENGTH_OVER_LIMIT"),
    // FQN没有配置
    FQN_NOT_CONFIGE("0426","FQN_NOT_CONFIGE"),
    //对象不存在
    INTERNAL_SERVER_ERROR("0500","INTERNAL_SERVER_ERROR"),
    // 未配置
    NOT_CONFIGURED("0503","NOT_CONFIGURED"),
    // 对象不存在
    OBJECT_NOT_FOUND("0504","OBJECT_NOT_FOUND"),
    // 数据已变更
    DATA_CHANGED("0501","DATA_CHANGED");

    // 错误码
    private String errorCode;
    // 错误提示码
    private String messageCode;


}
