package com.shuyun.kylin.customized.behavior.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.shuyun.kylin.crm.openapi.core.dto.common.PointRequestDto;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.stream.kafka.dao.CepResponseResult;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.base.util.DataApiUtil;
import com.shuyun.kylin.customized.base.util.DateHelper;
import com.shuyun.kylin.customized.base.util.HttpClientHelper;
import com.shuyun.kylin.customized.behavior.domain.Interactive;
import com.shuyun.kylin.customized.behavior.domain.InteractiveRecord;
import com.shuyun.kylin.customized.behavior.enums.InteractiveRecordGrantStatusEnum;
import com.shuyun.kylin.customized.behavior.enums.RewardsScopeEnum;
import com.shuyun.kylin.customized.behavior.resource.po.CheckPO;
import com.shuyun.kylin.customized.behavior.resource.vo.CheckResponse;
import com.shuyun.kylin.customized.behavior.resource.vo.CommonApiResponse;
import com.shuyun.kylin.customized.behavior.resource.vo.InteractiveResponseVO;
import com.shuyun.kylin.customized.behavior.service.DMService;
import com.shuyun.kylin.customized.behavior.service.InteractionSceneResolver;
import com.shuyun.kylin.customized.behavior.util.StringUtils;
import com.shuyun.kylin.customized.member.dm.MemberRepository;
import com.shuyun.kylin.customized.member.dto.CepCustomizationCouponDto;
import com.shuyun.kylin.customized.member.dto.CepMemberInfoDto;
import com.shuyun.kylin.customized.member.dto.OcpInteractiveRecordDto;
import com.shuyun.kylin.customized.member.service.CepMemberCouponService;
import com.shuyun.kylin.customized.member.service.ICepMemberService;
import com.shuyun.pip.component.json.JsonUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;

import static com.shuyun.kylin.customized.behavior.service.InteractionSceneResolver.Constants.REWARD_NOTHING;

@Slf4j
public abstract class AbstractInteractionSceneResolver implements InteractionSceneResolver, InitializingBean {

    @Resource
    private ICepMemberService iCepMemberService;
    @Resource
    private CepMemberCouponService cepMemberCouponService;
    @Resource
    protected DMService dmService;
    @Resource
    private ICepMemberService cepMemberService;
    @Autowired
    private MemberRepository memberRepository;

    private static volatile ExecutorService executorService;



    @Override
    public void afterPropertiesSet() {
        InteractionSceneResolver.Manager.register(supportScene(), this);
    }

    @Override
    public CommonApiResponse<CheckResponse> resolver(CheckPO checkPO, Interactive interactive) {
        String customerNo = checkPO.getMembershipId();
        String channelType = checkPO.getChannelType();

        //查询互动渠道的用户信息，并添加校验
        CepMemberInfoDto memberDto = queryMemberByNoAndChannelType(customerNo, channelType);
        if (Objects.isNull(memberDto)) {
            return CommonApiResponse.buildOf(InteractiveResponseVO.InteractiveDateRangeCheckCode.MEMBER_NOT_EXISTS);
        }

        //更改成ID
        String memberId = memberDto.getMemberId();
        String mobile = memberDto.getMobile();

        //查询互动渠道的用户在本次场景下最后一条获奖记录
        InteractiveRecord lastRecord = getLastRecord(memberId, interactive.getScene(), channelType);
        InteractiveRecord nextRecord = new InteractiveRecord();
        List<String> couponList = interactive.fetchCouponList();
        nextRecord.setCouponList(couponList.toString());

        //如果规则中设置了，不给奖励，则直接返回
        RewardInfo rewardInfo;
        if (interactive.isRewardNothing()) {
            rewardInfo = RewardInfo.refuse(REWARD_NOTHING);
        } else {
            rewardInfo = fetchPointInfo(checkPO, memberId, mobile, interactive, lastRecord);
        }
        RewardContext rewardContext = RewardContext.buildOf(customerNo, memberId, interactive, rewardInfo);
        log.info("互动奖励参数：{}, 奖励信息：{}", checkPO, rewardInfo);

        //发放状态，0-发放权益成功，1-发放权益失败，2-互动规则拒绝
        int grantStatus = 0;
        if (rewardContext.getRewardInfo().isRefuse()) { //如果奖励信息为拒绝，置为：2-互动规则拒绝
            grantStatus = 2;
        }

        try {
            //如果互动规则拒绝了，就不发放权益
            if (!rewardContext.getRewardInfo().isRefuse()) {
                if (BooleanUtils.isTrue(interactive.getIsRewardPoint())) {
                    nextRecord.setInteractiveRecordId(doRewardPoint(rewardContext));
                    nextRecord.setRewardsType("发积分");
                }

                if (BooleanUtils.isTrue(interactive.getIsRewardCoupon())) {
                    rewardContext.setMembershipId(checkPO.getMembershipId());//发券得用第三方ID
                    List<String> list = doRewardCoupon(rewardContext);
                    nextRecord.setCouponDetailList(list.size() > 0 ? list.toString() : null);
                    nextRecord.setRewardsType("优惠券");
                }

                //都发放成功，置为：0-发放权益成功
                if (checkIsRewardSuccess(interactive.getIsRewardPoint(), interactive.getIsRewardCoupon(),
                        nextRecord.getInteractiveRecordId(), nextRecord.getCouponDetailList())) {
                    log.info("权益发放成功，记录ID：{}", nextRecord.getInteractiveRecordId());
                    grantStatus = 0;
                } else {
                    //如果任何一方发放失败，则置为：1-发放权益失败
                    log.info("权益发放失败，记录ID：{}", nextRecord.getInteractiveRecordId());
                    grantStatus = 1;
                }
            }

        } catch (Exception ex) {
            //接口调用可能失败
            ex.printStackTrace();
            throw new RuntimeException("发送积分失败，原因：" + ex.getMessage());
        } finally {
            //记录一条日志
            InteractiveRecord interactiveRecord = InteractiveRecord.initRecord(
                    customerNo,
                    memberId,
                    mobile,
                    channelType,
                    interactive,
                    rewardContext,
                    grantStatus,
                    lastRecord,
                    nextRecord,
                    rewardInfo.otherChannelCheckIn
            );

            dmService.save(InteractiveRecord.fqn, interactiveRecord, InteractiveRecord.Fields.getEditablePredicate());
            /*if (grantStatus == 0) {
                log.info("开始权益发放成功记录推送到中台(互动接口):{}",JSON.toJSONString(interactiveRecord));
                getExecutorService().execute(() -> sendInteractiveRecordComsom(interactiveRecord));
            }*/
        }

        // 如果是会员中心异常码则直接抛出
        if (rewardInfo.isRefuse()) {
            return CommonApiResponse.buildOf(StringUtils.isNotNullOrEmpty(rewardInfo.getErrorCode()) ? rewardInfo.errorCode : "400003", rewardInfo.getRefuseReason());
        }

        CheckResponse t = new CheckResponse();
        t.setTransactionPoint(rewardInfo.getPoint());
        t.setCouponList(interactive.fetchCouponList());

        return CommonApiResponse.success(t);
    }

    /**
     * 检查发放权益是否成功
     *
     * @param isRewardPoint    是否配置发积分
     * @param isRewardCoupon   是否配置发优惠券
     * @param interactiveRecordId    发积分结果
     * @param couponDetailList 发优惠券结果
     * @return 是否成功
     */
    private boolean checkIsRewardSuccess(Boolean isRewardPoint, Boolean isRewardCoupon, String interactiveRecordId, String couponDetailList) {
        log.info("检查发放权益是否成功，参数：isRewardPoint={}, isRewardCoupon={}, interactiveRecordId={}, couponDetailList={}", isRewardPoint, isRewardCoupon, interactiveRecordId, couponDetailList);
        if (interactiveRecordId != null && couponDetailList != null) {
            return true;
        }
        //只配置了发券
        if (BooleanUtils.isFalse(isRewardPoint) && BooleanUtils.isTrue(isRewardCoupon) && couponDetailList != null) {
            return true;
        }
        //只配置了发积分
        return BooleanUtils.isTrue(isRewardPoint) && BooleanUtils.isFalse(isRewardCoupon) && interactiveRecordId != null;
    }


    protected void sendInteractiveRecordComsom(InteractiveRecord interactiveRecord) {
        try {
            Thread.sleep(Long.valueOf(ConfigurationCenterUtil.SEND_CMOSOM_SLEEP));
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        //推送参与活动积分记录
        //String openId = interactiveRecord.getCustomerNo().split("_")[1];
        int index = interactiveRecord.getCustomerNo().indexOf("_");
        String openId =interactiveRecord.getCustomerNo().substring(index + 1);
        OcpInteractiveRecordDto interactiveRecordDto = new OcpInteractiveRecordDto();
        interactiveRecordDto.setDate(DateHelper.getDateToDay());
        interactiveRecordDto.setConsumer_id(openId);
        interactiveRecordDto.setCampaign_id("40000000");
        interactiveRecordDto.setOrganization_code("KO");
        interactiveRecordDto.setOrganization_name("Coca-Cola");
        Map activity_attribute = new HashMap<String, String>();
        activity_attribute.put("points_channel", interactiveRecord.getChannelType());
        activity_attribute.put("platform", "wechat");
        interactiveRecordDto.setActivity_source("OCPMP");
        if ("SWIRE_MP".equals(interactiveRecord.getChannelType())) {
            interactiveRecordDto.setOrganization_code("Swire");
            interactiveRecordDto.setOrganization_name("SCCL");
            activity_attribute.put("points_channel", "SWIRE_MP");
            interactiveRecordDto.setActivity_source("SWIRE_MP");
        }
        if ("KO_Ali".equals(interactiveRecord.getChannelType())) {
            activity_attribute.put("points_channel", "Ali MP");
            interactiveRecordDto.setActivity_source("Ali MP");
            activity_attribute.put("platform", "alibaba");
        }

        interactiveRecordDto.setActivity_type("INTERACTIVE");
        Map activity_info = new HashMap<String, Object>();
        activity_info.put("timestamp", DateHelper.getDateMilliseconds());
        String activity_id = null;
        String interactive_id = null;
        String interactive_name = null;
        String activity_name = null;
        // 注册成为会员/ 完善会员资料/ 签到/ 连续签到/ 邀请好友注册
        // 分别对应:  30，31，14，32，33；
        switch (interactiveRecord.getInteractiveType()) {
            case "REGISTER":
                activity_id = "30";
                interactive_id ="register";
                interactive_name ="注册成为会员";
                activity_name = "register";
                break;
            case "COMPLETE_INFO":
                activity_id = "31";
                interactive_id ="complete_info";
                interactive_name ="完善会员资料";
                activity_name = "complete_info";
                break;
            case "CHECK_IN":
                activity_id = "14";
                interactive_id ="check_in";
                interactive_name ="签到";
                activity_name = "signin";
                break;
            case "CONSECUTIVE_CHECK_IN":
                activity_id = "32";
                interactive_id ="consecutive_check_in";
                interactive_name ="连续签到";
                activity_name = "consecutive_check_in";
                break;
            case "INVITE_REGISTER":
                activity_id = "33";
                interactive_id ="invite_register";
                interactive_name ="邀请好友注册";
                activity_name = "invite_register";
        }
        activity_info.put("activity_id", activity_id);
        activity_info.put("activity_name", activity_name);
        activity_attribute.put("app_id", interactiveRecord.getCustomerNo().split("_")[0]);
        activity_attribute.put("record_id", interactiveRecord.getInteractiveRecordId());
        activity_attribute.put("interactive_id", interactive_id);
        activity_attribute.put("interactive_name", interactive_name);
        activity_attribute.put("points_type", "member_bottle");
        activity_attribute.put("points_status", "immediate effect");
        activity_attribute.put("invitee", interactiveRecord.getInvitee());
        activity_attribute.put("sign_time", interactiveRecord.getSignTime());
        activity_attribute.put("is_win", interactiveRecord.getIsGetRewards());
        activity_attribute.put("get_point", interactiveRecord.getMemberIntegral());
        activity_info.put("activity_attribute", activity_attribute);
        interactiveRecordDto.setActivity_info(activity_info);
        log.info("推送中台互动记录数据:{}", JSON.toJSONString(interactiveRecordDto));
        try {
            HashMap<String, String> header = new HashMap<>();
            header.put("CampaignId", "40000000");
            header.put("Content-Type", "application/json");
            header.put("Ocp-Apim-Subscription-Key", ConfigurationCenterUtil.MIDDLEGROUND_KEY);
            String body = HttpClientHelper.doPostJSON(ConfigurationCenterUtil.OCP_INTERACTIVE_RECORD, interactiveRecordDto, header);
            log.info(body);
        } catch (Exception e) {
            HashMap<String, Object> map = new HashMap<>();
            map.put("code", 500);
            map.put("message",e.getMessage());
            map.put("retryCount",1);
            map.put("newRequest",JsonUtils.toJson(interactiveRecordDto));
            map.put("lastSync", ZonedDateTime.now());
            map.put("state",false);
            DataApiUtil.upsertIsData(ModelConstants.POINT_CDP_INTERFACLOG,UUID.randomUUID().toString().replaceAll("-", ""), map);
            log.info("推送中台互动记录接口异常:{},interactiveRecordDto:{}", e.getMessage(), JSON.toJSONString(interactiveRecordDto));
        }


    }

    /**
     * 单例线程池
     * @return
     */
    private static ExecutorService getExecutorService() {
        if (null == executorService) {
            synchronized (AbstractInteractionSceneResolver.class){
                if (null == executorService) {
                    executorService = DataApiUtil.newBlockingThreadPool(10,ConfigurationCenterUtil.THREAD_CORE_SUM, ConfigurationCenterUtil.THREAD_QUEUE_SUM);
                }
            }
        }
        return executorService;
    }

    protected List<String> doRewardCoupon(RewardContext rewardContext) {
        Interactive interactive = rewardContext.getInteractive();
        List<String> couponList = interactive.fetchCouponList();
        log.info("要发送这些券！！！！！----> {}", couponList);

        List<String> couponDetailList = new ArrayList<>();

        if (couponList.size() == 0) {
            log.warn("发放优惠券失败，原因：选择了发券奖励，但却找不到券信息。");
            rewardContext.getRewardInfo().setRefuseReason("发券失败，原因：选择了发券奖励，但却找不到券信息。");
            return couponDetailList;
        }

        for (String couponId : couponList) {
            CepCustomizationCouponDto cepCustomizationCouponDto = new CepCustomizationCouponDto();
            cepCustomizationCouponDto.setActivityCode(interactive.getCouponSceneCode()); //活动Code
            cepCustomizationCouponDto.setUid(rewardContext.getMembershipId()); //用户ID不能为空
            cepCustomizationCouponDto.setResourceId(couponId);   //??资源ID
            cepCustomizationCouponDto.setAccountType("CRM_ID"); //用户类型固定为：OCP_ID
            cepCustomizationCouponDto.setSendCostCenterCode(interactive.getCostCenter()); //成本中心
            cepCustomizationCouponDto.setBindBenefit(true); //是否加入卡包

            CepResponseResult cepResponseResult = cepMemberCouponService.saveMemberCoupon(ConfigurationCenterUtil.MEMBER_CEP_COUPON, cepCustomizationCouponDto);
            String json = JSON.toJSONString(cepResponseResult);
            JSONObject jsonObject = (JSONObject) JSONObject.parse(json);
            try {
                String orderId = jsonObject.getJSONObject("data").getString("orderId");
                couponDetailList.add(orderId);
            } catch (Exception e) {
                log.error("发放优惠券失败，原因：{}", e.getMessage());
                String message = jsonObject.getJSONObject("error").getString("message");
                if (StringUtils.isNullOrEmpty(rewardContext.getRewardInfo().getRefuseReason())) {
                    rewardContext.getRewardInfo().setRefuseReason("发优惠券失败：" + message);
                } else {
                    String refuseReason = rewardContext.getRewardInfo().getRefuseReason();
                    rewardContext.getRewardInfo().setRefuseReason(refuseReason + ",发优惠券失败：" + message);
                }
            }
        }
        return couponDetailList;
    }

    protected abstract RewardInfo fetchPointInfo(CheckPO checkPO, String memberId, String mobile, Interactive interactive, InteractiveRecord lastRecord);

    protected CepMemberInfoDto queryMemberByNoAndChannelType(String customerNo, String channelType) {
        //查询列表
        ResponseResult responseResult = cepMemberService.queryMember(customerNo,null, Constants.MEMBER_TYPE, channelType);
        Object data = responseResult.getData();
        log.info("查询 CustomerNo[{}] --> 会员：[{}]", customerNo, JsonUtils.toJson(responseResult));
        if (data == null) {
            return null;
        }
        CepMemberInfoDto data1 = (CepMemberInfoDto) data;
        //没有会员id，就是空
        if (data1.getMemberId() == null) {
            return null;
        }

        return data1;
    }

    protected String doRewardPoint(RewardContext rewardContext) {
        PointRequestDto pointRequestDto = rewardContext.generatePointRequestDto();
        //如果不发放积分，则直接跳过
        RewardInfo rewardInfo = rewardContext.getRewardInfo();
        if (rewardInfo.isRefuse()) {
            log.info("本次互动不发放奖励（积分），原因：{}", rewardInfo.getRefuseReason());
            return pointRequestDto.getKZZD1();
        }

        String scene = rewardContext.getInteractive().getScene();//场景值
        String sceneDesc = rewardContext.getInteractive().getSceneDesc();//场景描述
        Integer extraDayNo = rewardContext.getInteractive().getExtraDayNo();//连续签到天数
        String desc = scene + "#" + sceneDesc;

        //连续需要将描述改为：CONSECUTIVE_CHECK_IN#签到(连续签到N天)
        if(Objects.equals(scene, CheckPO.SceneType.CHECK_IN.name()) && rewardInfo.isConsecutiveCheckin()) {
            CheckPO.SceneType consecutiveCheckIn = CheckPO.SceneType.CONSECUTIVE_CHECK_IN;
            desc = consecutiveCheckIn.name() + "#签到(连续签到" + extraDayNo + "天)";
        }

        pointRequestDto.setDescription(desc);

        Double point = pointRequestDto.getPoint();
        if (point == null || point <= 0) {
            log.info("本次互动不发放奖励（积分），原因：待发放的积分为 0");
            rewardInfo.setRefuseReason("发积分失败：待发放的积分为 0");
            return pointRequestDto.getKZZD1();
        }

        log.info("发放积分奖励，参数：{}", JsonUtils.toJsonPretty(pointRequestDto));
        ResponseResult responseResult = iCepMemberService.updatePoint(pointRequestDto, StringUtils.uuid(),scene,pointRequestDto.getChannelType());
        log.info("发放积分奖励，结果：{}", JsonUtils.toJsonPretty(responseResult));
        if (!org.apache.commons.lang.StringUtils.equals("200", responseResult.getCode())) {
            rewardInfo.setRefuseReason(responseResult.getMsg());
            rewardInfo.setPoint(0.0d);
            rewardInfo.setRefuse(true);
            rewardInfo.setInternalError(true);
            rewardInfo.setErrorCode(responseResult.getCode());
        }

        // 赋值实际发放积分
        if (Objects.nonNull(responseResult.getData())) {
            Map data = (HashMap) responseResult.getData();
            Optional.of(data.get("changePoint")).ifPresent(changePoint -> rewardInfo.setPoint((double) changePoint));
        }
        return pointRequestDto.getKZZD1();
    }

    @Setter
    @Getter
    public static class RewardInfo {
        /**
         * 发送多少积分
         */
        private double point;
        /**
         * 是否发送
         */
        private boolean refuse = false;
        private boolean internalError = false;
        /**
         * 拒绝原因
         */
        private String refuseReason;

        private InteractiveRecord interactiveRecord;
        /**
         * 是否连续签到
         */
        private boolean consecutiveCheckin = false;
        /**
         * 用于签到记录重置签到次数
         */
        private boolean otherChannelCheckIn = false;
        /**
         * 中心返回异常码
         */
        private String errorCode;

        public static RewardInfo refuse(String refuseReason) {
            RewardInfo rewardInfo = new RewardInfo();
            rewardInfo.setPoint(0);
            rewardInfo.setRefuse(true);
            rewardInfo.setRefuseReason(refuseReason);
            return rewardInfo;
        }

        public static RewardInfo point(double point) {
            RewardInfo rewardInfo = new RewardInfo();
            rewardInfo.setPoint(point);
            rewardInfo.setRefuse(false);
            return rewardInfo;
        }

        public void alreadyCheckIn() { this.otherChannelCheckIn = Boolean.TRUE; }

        @Override
        public String toString() {
            return JsonUtils.toJson(this);
        }
    }

    @Setter
    @Getter
    public static class RewardContext {
        /**
         * 会员账号
         */
        private String membershipId;
        /**
         * 会员渠道标识
         */
        private String customerNo;
        /**
         * 成本中心
         */
        private Interactive interactive;
        /**
         * 互动行为编号
         */
        private String interactiveId;
        /**
         * 奖励信息
         */
        private RewardInfo rewardInfo;

        public static RewardContext buildOf(String customerNo, String membershipId, Interactive interactive, RewardInfo rewardInfo) {
            RewardContext rewardContext = new RewardContext();
            rewardContext.setCustomerNo(customerNo);
            rewardContext.setMembershipId(membershipId);
            rewardContext.setInteractive(interactive);
            rewardContext.setRewardInfo(rewardInfo);
            return rewardContext;
        }

        public PointRequestDto generatePointRequestDto() {
            PointRequestDto requestDto = new PointRequestDto();
            requestDto.setMemberId(membershipId);

            //以下为固定值
            requestDto.setMemberType(Constants.MEMBER_TYPE);    //会员类型，固定为 KO
            requestDto.setChangeType(Constants.CHANGE_TYPE);    //发放类型，固定为 SEND （发放）
            requestDto.setChannelType(interactive.getChannelType());  //渠道类型，固定为 MO
            requestDto.setKZZD2(Constants.KZZD2);               //变更来源，固定为 ”互动行为“
            requestDto.setDescription(Constants.DESCRIPTION);   //描述，留空吧

            requestDto.setEffectTime(null);                       //生效时间，立即生效，不设置生效时间
            requestDto.setExpiredTime(DateHelper.getLastDayOfMonth());  //失效时间，默认： 一年后的今天

            //以下属性来源于活动定义
            String costCenter = interactive.getCostCenter();
            requestDto.setShopCode(costCenter); //成本中心
            requestDto.setKZZD1(interactive.getId() + "_" + StringUtils.uuid()); //互动行为编号（即ID）

            //积分是需要根据规则计算的
            requestDto.setPoint(rewardInfo.getPoint());         //积分数量

            requestDto.setDescription("参与互动行为发放积分");

            return requestDto;
        }
    }

    protected InteractiveRecord getLastRecord(String memberId, String scene, String channelType) {
        return queryRecord(Joiner.on(" And ").join(
                InteractiveRecord.Fields.interactiveScene.eq(scene),
                InteractiveRecord.Fields.channelType.eq(channelType),
                InteractiveRecord.Fields.memberIDStr.eq(memberId),
                // 发奖成功、部分成功
                InteractiveRecord.Fields.grantStatus.in(InteractiveRecordGrantStatusEnum.getSucceedOrPartObject())));
    }

    protected InteractiveRecord getLastRecordByExcludeChannelType(String memberId, String scene, String channelType) {
        return queryRecord(Joiner.on(" And ").join(
                InteractiveRecord.Fields.interactiveScene.eq(scene),
                InteractiveRecord.Fields.channelType.not(channelType),
                InteractiveRecord.Fields.memberIDStr.eq(memberId),
                // 发奖成功、部分成功
                InteractiveRecord.Fields.grantStatus.in(InteractiveRecordGrantStatusEnum.getSucceedOrPartObject())));
    }

    private InteractiveRecord queryRecord(String params) {
        return dmService.queryForObject(InteractiveRecord.genALlFieldQuery("Where " + params + " Order By joinTime Desc Limit 1"),
                InteractiveRecord.class,
                "查询最新一条积分获奖记录");
    }

    /**
     * 校验自然人本次互动类型是否存在其他渠道的获取积分记录
     * @param rewardsRecordScope 互动规则校验范围
     * @param checkPO 互动对象
     * @param memberId 会员ID （用户多渠道下会员是同一个）
     * @return
     */
    protected boolean isOtherChannelPointRecord(String rewardsRecordScope, CheckPO checkPO, String memberId) {
        // 开启全渠道校验，则查询是否存在积分获取记录
        if (RewardsScopeEnum.isAllChannel(rewardsRecordScope)) {
            String scene = checkPO.getScene();

            InteractiveRecord interactiveRecord = getLastRecordByExcludeChannelType(memberId, scene, checkPO.getChannelType());

            log.info("校验会员在本次场景下是否有其他渠道积分获取记录, 参数: MemberId={}, scene={}, interacticeRecord={}", memberId, scene, JSONObject.toJSONString(interactiveRecord));

            if (CheckPO.SceneType.isOnceOrConsecutiveCheckIn(scene)) {
                return Objects.nonNull(interactiveRecord) && interactiveRecord.isGetRewardsToday();
            } else {
                return Objects.nonNull(interactiveRecord);
            }
        }

        return false;
    }

}
