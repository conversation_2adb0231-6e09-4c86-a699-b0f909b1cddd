package com.shuyun.kylin.customized.history.service.impl;


import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.feign.client.LoyaltyFacade;
import com.shuyun.kylin.customized.base.feign.dao.LoyaltyFacadeDto;
import com.shuyun.kylin.customized.base.stream.kafka.dao.CampaignActionOutputDto;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.base.util.DataApiUtil;
import com.shuyun.kylin.customized.history.common.LoyaltyFacadeRepository;
import com.shuyun.kylin.customized.history.dao.LoyaltyPointDto;
import com.shuyun.kylin.customized.history.service.HistoryMemberPointService;
import com.shuyun.kylin.customized.history.service.HistoryPoint;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;


@Slf4j
@Service
public class HistoryMemberPointServiceImpl implements HistoryMemberPointService {

    @Autowired
    private LoyaltyFacade loyaltyFacade;

    @Autowired
    private HistoryPoint historyPoint;

    @Autowired
    private LoyaltyFacadeRepository loyaltyFacadeRepository;

    ExecutorService executorService = DataApiUtil.newBlockingThreadPool(ConfigurationCenterUtil.THREAD_CORE_SUM, ConfigurationCenterUtil.THREAD_QUEUE_SUM);


    /**
     * 新增修改积分
     * @param
     */
    @Override
    public void memberSendPoint() {

        try {
            Long start = System.currentTimeMillis();
            //分批处理
            final int pageSize = 2001;
            final int countNum = 2000;
            int index = 0;
            int completedNum = 0;

            while (true) {

                List<LoyaltyPointDto> loyaltyFacadeDtos;
                //规避批次查询超时异常
                try {
                    loyaltyFacadeDtos = loyaltyFacadeRepository.selectReducePoint(index, pageSize, completedNum);
                } catch (Exception e) {
                    log.error("批次查询超时：a.id > {} order by a.id limit {}; 【跳过循环不跳过批次】继续查询...", index,pageSize,e);
                    continue;
                }
                List<LoyaltyPointDto> pointRegister = JSON.parseArray(JSON.toJSONString(loyaltyFacadeDtos), LoyaltyPointDto.class);
                //记录该批次中最大id值
                index = pointRegister.get(pointRegister.size()-1).getId();
                completedNum += pointRegister.size();
                UpdataRunnable sendRunnable = new UpdataRunnable(index, completedNum, pointRegister);
                executorService.execute(sendRunnable);
                if (pointRegister.size() < countNum) {
                    break;
                }
            }

            log.info("历史会员积分变更任务分发完成total:{}+,分发耗时:{}ms,待子线程执行完毕...", completedNum, System.currentTimeMillis() - start);
        } catch (Exception e) {
            log.error("历史会员积分变更任务异常终止!", e);
        }
    }

    class UpdataRunnable implements Runnable {
        int index;
        int completedNum;
        List<LoyaltyPointDto> loyaltyPointDtos;

        private UpdataRunnable(int index, int completedNum, List<LoyaltyPointDto> loyaltyPointDtos) {
            this.index = index;
            this.completedNum = completedNum;
            this.loyaltyPointDtos = loyaltyPointDtos;
        }

        @Override
        public void run() {
            Long start = System.currentTimeMillis();
            updataMemberTable(loyaltyPointDtos);
            log.info("历史会员积分变更批次completedNum:{},index:{},耗时:{}ms", completedNum,index, System.currentTimeMillis() - start);
        }
    }

    public void updataMemberTable(List<LoyaltyPointDto> loyaltyPointDtos) {
        for (LoyaltyPointDto st : loyaltyPointDtos) {
            LoyaltyFacadeDto facadeDto = new LoyaltyFacadeDto();
            facadeDto.setMemberId(st.getMemberId());
            facadeDto.setChannelType(st.getChannelType());
            facadeDto.setPointAccountId(st.getPointAccountId());
            facadeDto.setShopId(st.getShopId());
            facadeDto.setBusinessId(st.getBusinessId());
            facadeDto.setDesc(st.getDesc());
            facadeDto.setChangeMode(st.getChangeMode());
            facadeDto.setPoint(st.getChangePoint());
            facadeDto.setKZZD1(st.getChangeSourceDetail());
            facadeDto.setKZZD2(st.getChangeSource());
            facadeDto.setKZZD3(st.getCostTracing());
            facadeDto.setIdempotentMode(1);
            try {
                if ("SEND".equals(st.getChangeType())){
                    //加积分
                    facadeDto.setEffectiveDate(st.getEffectiveDate());
                    facadeDto.setOverdueDate(st.getOverdueDate());
                    log.info("封装实体:{}",JSON.toJSONString(facadeDto));
                    loyaltyFacade.sendPoint(facadeDto);
                }
                if ("DEDUCT".equals(st.getChangeType())){
                    //减积分
                    loyaltyFacade.deductPoint(facadeDto);
                }
            } catch (Exception e) {
                historyPoint.updateHistoryPoint(st, e.getMessage(),st.getChangeType());
            }
        }
    }
}

