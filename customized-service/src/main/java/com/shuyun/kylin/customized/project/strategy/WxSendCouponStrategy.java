package com.shuyun.kylin.customized.project.strategy;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.enums.ResponseCodeEnum;
import com.shuyun.kylin.customized.base.feign.dao.MemberBingDto;
import com.shuyun.kylin.customized.base.feign.dao.OfferProjectDetailClientResponse;
import com.shuyun.kylin.customized.base.util.DateHelper;
import com.shuyun.kylin.customized.project.dto.SendCouponResultDto;
import com.shuyun.kylin.customized.project.request.GrantCouponRequest;
import com.shuyun.kylin.customized.rpc.dto.WxSendCouponRequestDto;
import com.shuyun.kylin.customized.rpc.dto.WxSendCouponResponseDto;
import com.shuyun.kylin.customized.rpc.template.WxRequestTemplate;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: Jingwei
 * @date: 2024-12-14
 */
@Slf4j
public class WxSendCouponStrategy extends SendCouponStrategy {

    private static final String ZERX_MSY = "调用第三方接口失败";

    @Override
    public SendCouponResultDto sendCoupon(String templateId, OfferProjectDetailClientResponse projectDetail, GrantCouponRequest grantCouponRequest, MemberBingDto memberBindInfo) {
        log.info("微信代金券发放开始，入参templateId={}, projectDetail={}，grantCouponRequest={}，memberBindInfo={}",
                templateId, JSON.toJSONString(projectDetail), JSON.toJSONString(grantCouponRequest), JSON.toJSONString(memberBindInfo));
        OfferProjectDetailClientResponse.RspData data = projectDetail.getData();
        if(ObjectUtil.isNull(data)) {
            log.error("代金券策略内部接口返回失败，data is empty!");
            return new SendCouponResultDto(Boolean.FALSE, null, null,null, ResponseCodeEnum.GRANT_COUPON_FAILED.getCode(),ZERX_MSY);
        }
        OfferProjectDetailClientResponse.RspData.ExtData extData = data.getExtData();
        if(ObjectUtil.isNull(extData)) {
            log.error("代金券策略内部接口返回失败，extData is empty!");
            return new SendCouponResultDto(Boolean.FALSE, null, null,null,ResponseCodeEnum.GRANT_COUPON_FAILED.getCode(),ZERX_MSY);
        }
        String externalProjectId = extData.getExternalProjectId();
        // 发券商户id
        String stockMchid = extData.getStockMchid();
        // 制券商户id
        String stockCreatorMchid = extData.getStockCreatorMchid();
        if(!ObjectUtil.isAllNotEmpty(externalProjectId, stockMchid, stockCreatorMchid)) {
            log.error("代金券策略内部接口返回失败，externalProjectId={}，stockMchid={}，stockCreatorMchid={}",
                    externalProjectId, stockMchid, stockCreatorMchid);
            return new SendCouponResultDto(Boolean.FALSE, null, null,null,ResponseCodeEnum.GRANT_COUPON_FAILED.getCode(),ZERX_MSY);
        }
        WxSendCouponRequestDto wxSendCouponRequestDto = assembleRequestParams(externalProjectId, stockMchid, stockCreatorMchid, grantCouponRequest.getTransactionId(), memberBindInfo);
        WxSendCouponResponseDto wxSendCouponResponseDto = WxRequestTemplate.sendCoupon(wxSendCouponRequestDto, memberBindInfo.getOpenId(), stockMchid, grantCouponRequest.getTransactionId());

        if(ObjectUtil.isEmpty(wxSendCouponResponseDto.getCouponId())) {
            log.error("微信发券返回结果code异常，code={}", JSON.toJSONString(wxSendCouponResponseDto));
            return new SendCouponResultDto(Boolean.FALSE, null, null,null,wxSendCouponResponseDto.getCode(),wxSendCouponResponseDto.getMessage());
        }
        return new SendCouponResultDto(Boolean.TRUE, null, wxSendCouponResponseDto.getCouponId(),null);
    }

    private static WxSendCouponRequestDto assembleRequestParams(String externalProjectId,
                                                                String stockMchid,
                                                                String stockCreatorMchid,
                                                                String transactionId,
                                                                MemberBingDto memberBingDto) {
        WxSendCouponRequestDto wxSendCouponRequestDto = new WxSendCouponRequestDto();
        wxSendCouponRequestDto.setStockId(externalProjectId);
        wxSendCouponRequestDto.setOutRequestNo(getOutRequestNo(transactionId, stockMchid));
        wxSendCouponRequestDto.setAppid(memberBingDto.getAppId());
        wxSendCouponRequestDto.setStockCreatorMchId(stockCreatorMchid);
        // 微信暂未开放以下两个字段，暂不设值
//        wxSendCouponRequestDto.setCouponValue(null);
//        wxSendCouponRequestDto.setCouponMinimum(null);
        return wxSendCouponRequestDto;
    }

    /**
     * 规则：商户id+日期+流水号
     * @return
     */
    private static String getOutRequestNo(String transactionId, String stockMchid) {
        return String.format("%s_%s_%s", stockMchid, DateUtil.format(DateUtil.date(), DateHelper.DATE_FORMAT_COMPACT), transactionId);
    }
}
