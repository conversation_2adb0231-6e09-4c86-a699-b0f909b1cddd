package com.shuyun.kylin.customized.base.common;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


@Data
@Schema(name = "ResponsesVo")
public class ResponsesVo implements Serializable {

	private int code;
	private String message;
	private List<Map> data;



	public ResponsesVo(int code, String message) {
		this.code = code;
		this.message = message;
	}

	public ResponsesVo(int code, String message, List<Map> data) {
		this.code = code;
		this.message = message;
		this.data = data;
	}
}
