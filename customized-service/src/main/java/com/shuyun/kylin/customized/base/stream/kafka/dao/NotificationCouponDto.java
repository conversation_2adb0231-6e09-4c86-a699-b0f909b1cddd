package com.shuyun.kylin.customized.base.stream.kafka.dao;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class NotificationCouponDto implements Serializable {
    private String Id;
    private String taskId;
    private String tenant;
    private String campaignId;
    private String campaignName;
    private String status;
    private String processId;
    private String nodeId;
    private String nodeName;
}
