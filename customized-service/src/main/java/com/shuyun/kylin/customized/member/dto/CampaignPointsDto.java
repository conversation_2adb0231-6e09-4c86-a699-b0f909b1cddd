package com.shuyun.kylin.customized.member.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * @Description:  活动积分
 * @Date: 2021/12/12
 * @Author: zeevi
 */
@Data
public final class CampaignPointsDto {

    //会员id
    private Map member;

    @NotBlank(message = "客户编号不能为空，格式：appid_openid")
    private String customerNo;
    //渠道
    @NotBlank(message = "渠道不能为空，eg. KO_MP:cep小程序  EC_SHOPPING：EC购物商城  EC_POINT：积分商城  H5：H5活动")
    private String channelType;
    //活动id
    private String campaignId;
    //活动名称
    private String campaignName;
    //活动规则描述
    private String campaignRuleDesc;
    //活动累计积分
    private String campaignCurrentPoint;
    //活动消耗积分
    private String campaignPointConsumption;
    //活动可用积分
    private String campaignPoint;

}
