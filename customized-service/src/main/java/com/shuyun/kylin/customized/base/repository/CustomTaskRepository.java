package com.shuyun.kylin.customized.base.repository;

import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.dataflow.dto.CustomizedTaskDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class CustomTaskRepository extends BaseDsRepository<CustomizedTaskDto> {


    public Boolean existCustomizedTaskExecuteStatus(String taskId, String... executeStatus) {
        String executeStatusStr = StringUtils.join(executeStatus, ",");
        log.info("existCustomizedTaskExecuteStatus taskId:{},executeStatus:{}", taskId, executeStatusStr);
        Map<String, Object> queryMap = new HashMap<>();
//        queryMap.put("taskId", taskId);
        String queryMemberSql = " select id from " + ModelConstants.CUSTOMIZEDTASK + "  where taskId = '" + taskId + "' and executeStatus in ('" + StringUtils.join(executeStatus, "','") + "') limit 1 ";
        log.info("existCustomizedTaskExecuteStatus sql:{}", queryMemberSql);
        List<CustomizedTaskDto> customizedTaskList = executeSQL(queryMemberSql, queryMap);
        if (!CollectionUtils.isEmpty(customizedTaskList)) {
            log.info("existCustomizedTaskExecuteStatus__存在状态为{}的记录，taskId:{}", executeStatusStr, taskId);
            return true;
        }
        log.info("existCustomizedTaskExecuteStatus__不存在状态为{}的记录，taskId:{}", executeStatusStr, taskId);
        return false;
    }
}