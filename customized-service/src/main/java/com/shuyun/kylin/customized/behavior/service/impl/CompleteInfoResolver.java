package com.shuyun.kylin.customized.behavior.service.impl;

import com.shuyun.kylin.customized.behavior.domain.Interactive;
import com.shuyun.kylin.customized.behavior.domain.InteractiveRecord;
import com.shuyun.kylin.customized.behavior.resource.po.CheckPO;
import org.springframework.stereotype.Service;

/**
 * 注册送礼
 */
@Service
public class CompleteInfoResolver extends AbstractInteractionSceneResolver {

    private final String USED_ERROR_INFO = "已领取过完善信息奖励";

    @Override
    public CheckPO.SceneType supportScene() {
        return CheckPO.SceneType.COMPLETE_INFO;
    }

    @Override
    protected RewardInfo fetchPointInfo(CheckPO checkPO, String memberId, String mobile, Interactive interactive, InteractiveRecord lastRecord) {
        if (lastRecord != null) {
            return RewardInfo.refuse(USED_ERROR_INFO);
        }

        if (isOtherChannelPointRecord(interactive.getRewardsRecordScope(), checkPO, memberId)) {
            return RewardInfo.refuse(USED_ERROR_INFO);
        }

        /*
         * 完善信息送奖励
         */
        Interactive.CompleteInfo completeInfo = interactive.fetchCompleteInfo();
        Integer pointNum = completeInfo.getPointNum();
        return RewardInfo.point(pointNum);
    }
}
