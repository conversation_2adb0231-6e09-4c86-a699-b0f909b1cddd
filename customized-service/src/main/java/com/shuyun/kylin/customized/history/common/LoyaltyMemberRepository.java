package com.shuyun.kylin.customized.history.common;

import com.shuyun.kylin.crm.openapi.core.dto.wechat.WechatRegisterRequest;
import com.shuyun.kylin.customized.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.base.feign.dao.LoyaltyFacadeDto;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class LoyaltyMemberRepository extends BaseDsRepository<WechatRegisterRequest> {

    public List<WechatRegisterRequest> selectSendPoint(String index, int pageSize, int completedNum) {

        //执行前确保源数据表中mobile唯一且非空，记得给mobile加上索引
        String change = " mobile > " + index + " order by mobile limit " + pageSize;

        String querySql = " SELECT unionId,appId,openId,memberSource as district,channelType,memberSourceDetail as province," +
                "gender,nickname,birthDay,birthYear,isKoMember as shopCode,mobile,cityName as city,email,registerTime,headImgUrl,memberType,appType " +
                " FROM  " + ConfigurationCenterUtil.HISTORY_MEMBER + " a where " + change + "";
        log.info("历史会员查询批次:{},sql:{}",completedNum,querySql);
        List<WechatRegisterRequest> list = executeSQL(querySql, Collections.EMPTY_MAP);
        return list;
    }
}
