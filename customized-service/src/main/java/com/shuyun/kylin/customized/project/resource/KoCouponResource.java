package com.shuyun.kylin.customized.project.resource;


import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.project.request.GrantCouponRequest;
import com.shuyun.kylin.customized.project.request.OfferProjectRequest;
import com.shuyun.kylin.customized.project.response.*;
import com.shuyun.kylin.customized.project.service.KoCouponService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@Validated
@RestController
@RequestMapping("/project")
public class KoCouponResource {


    @Resource
    private KoCouponService koCouponService;

    //卡券项目
    @GetMapping("/list")
    public CrmProjectResponse projectList(@RequestParam String brand,
                                          @RequestParam String channelType,
                                          @RequestParam(required = false) Integer pageNum,
                                          @RequestParam(required = false) Integer pageSize) {
        log.info("CouponResource...projectList...brand:{},channelType:{},pageNum:{},pageSize:{}", brand, channelType, pageNum, pageSize);
        OfferProjectRequest request = new OfferProjectRequest();
        request.setBrand(brand);
        request.setChannelType(channelType);
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);
        CrmProjectResponse obj = koCouponService.projectList(request);
        return obj;
    }


    //卡券发放
    @PostMapping("/instance/grant")
    public GrantCouponResponse instanceGrant(@Validated @RequestBody GrantCouponRequest request) {
        log.info("CouponResource...instanceGrant...request:{}", JSON.toJSONString(request));
        GrantCouponResponse response = koCouponService.instanceGrant(request);
        return response;
    }



    //卡券实例查询
    @GetMapping("/instance/list")
    public InstanceListResponse instanceList(@RequestParam String brand,
                                             @RequestParam String channelType,
                                             @RequestParam String identify,
                                             @RequestParam (required = false) String campaignId,
                                             @RequestParam(required = false) Integer pageNum,
                                             @RequestParam(required = false) Integer pageSize) {
        log.info("CouponResource...instanceList...brand:{},channelType:{},identify:{},campaignId:{},pageNum:{},pageSize:{}", brand, channelType, identify,campaignId, pageNum, pageSize);
        OfferProjectRequest request = new OfferProjectRequest();
        request.setBrand(brand);
        request.setChannelType(channelType);
        request.setIdentify(identify);
        request.setCampaignId(campaignId);
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);
        InstanceListResponse response = koCouponService.instanceList(request);
        return response;
    }

    //卡券未使用量（余量）查询
    @GetMapping("/balance")
    public ResponseResult<ProjectBalance> getBalance(@RequestParam String projectId) {
        log.info("CouponResource...getBalance...projectId:{}", projectId);
        return koCouponService.getBalance(projectId);
    }

    // 券核销
    @PostMapping("/instance/use")
    public InstanceUseResponse instanceUse(@RequestBody InstanceUseRequest request) {
        log.info("CouponResource...instanceUse...request:{}", JSON.toJSONString(request));
        InstanceUseResponse response = koCouponService.instanceUse(request);
        return response;
    }
}
