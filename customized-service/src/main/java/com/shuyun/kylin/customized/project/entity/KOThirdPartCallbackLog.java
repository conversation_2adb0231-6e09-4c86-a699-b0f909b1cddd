package com.shuyun.kylin.customized.project.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 可乐权益项目三方回调日志
 * @author: Jingwei
 * @date: 2024-12-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KOThirdPartCallbackLog {

    /**
     * 业务编码
     */
    private String businessCode;
    /**
     * 回调参数
     */
    private String callbackParam;
    /**
     * 回调时间
     */
    private String createAt;
    /**
     * 回调来源
     */
    private String callbackSource;
}
