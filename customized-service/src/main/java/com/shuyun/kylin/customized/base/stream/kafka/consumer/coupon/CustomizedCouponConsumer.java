package com.shuyun.kylin.customized.base.stream.kafka.consumer.coupon;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.dm.AppletSubcriberRepository;
import com.shuyun.kylin.customized.base.dm.CouponCampaignRepository;
import com.shuyun.kylin.customized.base.stream.kafka.dao.*;
import com.shuyun.kylin.customized.base.stream.kafka.messaging.*;
import com.shuyun.kylin.customized.base.stream.kafka.producer.coupon.BehaviorProducer;
import com.shuyun.kylin.customized.base.stream.kafka.producer.coupon.CallbackProducer;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.base.util.DataApiUtil;
import com.shuyun.kylin.customized.member.dm.CepAppletTemplaterRepository;
import com.shuyun.kylin.customized.member.dm.MemberRepository;
import com.shuyun.kylin.customized.member.dto.CepAppletSubscribeDto;
import com.shuyun.kylin.customized.member.dto.CepAppletTemplateDto;
import com.shuyun.kylin.customized.member.dto.CepCustomizationCouponDto;
import com.shuyun.kylin.customized.member.service.CepMemberCouponService;
import com.shuyun.kylin.customized.swire.dto.SwireMemberDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

@Slf4j
@Component
public class CustomizedCouponConsumer {

    @Autowired
    private BehaviorProducer behaviorProducer;

    @Autowired
    private CallbackProducer callbackProducer;

    @Autowired
    private MemberRepository memberRepository;

    @Autowired
    private CepMemberCouponService cepMemberCouponService;

    @Autowired
    private CouponCampaignRepository couponCampaignRepository;

    @Autowired
    private AppletSubcriberRepository appletSubcriberRepository;

    @Autowired
    private CepAppletTemplaterRepository cepAppletTemplaterRepository;


    @StreamListener(KafkaNotification.KAFKA_INPUT)
    public void processUser(String json) {
        try {
            NotificationCouponDto notificationCouponDto = JSON.parseObject(json, NotificationCouponDto.class);
            log.info("接收到自定义节点消息 :{}", JSON.toJSONString(notificationCouponDto));
            //查询是否是小程序订阅消息,总数量
            Map<String, String> subscriber = appletSubcriberRepository.getSubscriber(notificationCouponDto.getTaskId());
            log.info("查询结果:{}", JSON.toJSONString(subscriber));
            if (!"0".equals(subscriber.get("countSum"))) {
                if (Integer.valueOf(subscriber.get("countSum")) > 1) {
                    log.info("批量执行count:{},taskId:{}", subscriber.get("countSum"), notificationCouponDto.getTaskId());
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", notificationCouponDto.getId());
                    map.put("taskId", notificationCouponDto.getTaskId());
                    map.put("tenant", notificationCouponDto.getTenant());
                    map.put("campaignId", notificationCouponDto.getCampaignId());
                    map.put("campaignName", notificationCouponDto.getCampaignName());
                    map.put("status", notificationCouponDto.getStatus());
                    map.put("processId", notificationCouponDto.getProcessId());
                    map.put("nodeId", notificationCouponDto.getNodeId());
                    map.put("nodeName", notificationCouponDto.getNodeName());
                    map.put("consumptionStatus", "new");
                    map.put("lastSync", ZonedDateTime.now());
                    //查询 该任务是否存在
                    String taskId = memberRepository.getSubscribeTaskId(notificationCouponDto.getTaskId());
                    if (StringUtils.isNotBlank(taskId)) {
                        log.info("过滤重复调用:{}", taskId);
                        return;
                    }
                    //插入批量数据
                    DataApiUtil.insert(ModelConstants.APPLETTASK, map);
                } else {
                    sendAppletSubscriber(notificationCouponDto);
                }
            } else {
                //查询优惠券
                Map<String, String> memberCoupons = couponCampaignRepository.getCouponSum(notificationCouponDto.getTaskId());
                if (Integer.valueOf(memberCoupons.get("countSum")) > 1) {
                    log.info("优惠券批量执行count:{},taskId:{}", memberCoupons.get("countSum"), notificationCouponDto.getTaskId());
                    //插入批量数据
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", notificationCouponDto.getId());
                    map.put("taskId", notificationCouponDto.getTaskId());
                    map.put("tenant", notificationCouponDto.getTenant());
                    map.put("campaignId", notificationCouponDto.getCampaignId());
                    map.put("campaignName", notificationCouponDto.getCampaignName());
                    map.put("status", notificationCouponDto.getStatus());
                    map.put("processId", notificationCouponDto.getProcessId());
                    map.put("nodeId", notificationCouponDto.getNodeId());
                    map.put("nodeName", notificationCouponDto.getNodeName());
                    map.put("consumptionStatus", "new");
                    map.put("lastSync", ZonedDateTime.now());
                    //查询 该任务是否存在
                    String taskId = memberRepository.getCouponsTaskId(notificationCouponDto.getTaskId());
                    if (StringUtils.isNotBlank(taskId)) {
                        log.info("优惠券过滤重复调用:{}", taskId);
                        return;
                    }
                    DataApiUtil.insert(ModelConstants.COUPONYASK, map);
                } else {
                    sendMemberCoupons(notificationCouponDto);
                }
            }
        } catch (NumberFormatException e) {
            log.error("接收到自定义节点消息异常msg:{}", e.getMessage());
        }

    }

    /**
     * 发送优惠券 (单条)
     *
     * @param notificationCouponDto
     */
    public void sendMemberCoupons(NotificationCouponDto notificationCouponDto) {

        //是否测试执行
           /* Boolean tastTask = couponCampaignRepository.getCountCoupons(notificationCouponDto.getTaskId());
            if (tastTask) {
                return;
            }*/
        List<CampaignActionOutputDto> memberCoupons = null;
        try {
            memberCoupons = couponCampaignRepository.getMemberCoupons(notificationCouponDto.getTaskId());
        } catch (Exception e) {
            log.error("查询优惠券数据失败taskId:{},msg:{}", notificationCouponDto.getTaskId(), e.getMessage());
        }
        if (CollectionUtils.isEmpty(memberCoupons)) {
            log.info("没有查询到该发送优惠券记录taskId:{}", notificationCouponDto.getTaskId());
            return;
        }
        List<CampaignActionOutputDto> couponDtoList = JSON.parseArray(JSON.toJSONString(memberCoupons), CampaignActionOutputDto.class);
        //调cep发券接口
        for (CampaignActionOutputDto coupon : couponDtoList) {
            //查询会员CustomerNo
            //String memberId = memberRepository.queryCustomerNoUnioId(coupon.getObjectId());
            //String customerNo = memberRepository.queryCustomerNo(memberId);
            Map<String, String> memberMap = null;
            CepCustomizationCouponDto couponDto = new CepCustomizationCouponDto();
            try {
                String memberId = memberRepository.queryCustomerNoUnioId(coupon.getObjectId());
                log.info("查询会员id:{}",memberId);
                memberMap = memberRepository.getCustomerNo(memberId);
                log.info("查询会员id:{}",JSON.toJSONString(memberMap));
                couponDto.setActivityCode(coupon.getScene());
                //couponDto.setActivityCode("KOPLUS_UPGRADE_VIP");
                couponDto.setUid(memberMap.get("customerNo"));
                couponDto.setAccountType("CRM_ID");
                couponDto.setResourceId(replaceCoup(coupon.getCoupon()));
                couponDto.setSendCostCenterCode(replaceCoup(coupon.getCostCenter()));
                HashMap<Object, Object> hashMap = new HashMap<>();
                hashMap.put("campaignId", coupon.getCampaignId());
                hashMap.put("nodeId", coupon.getNodeId());
                couponDto.setExtra(hashMap);
                couponDto.setBindBenefit(coupon.getIsSendCardsOffers());
                //新增幂等参数(取主键id)
                couponDto.setTransactionId(coupon.getId());
                log.info("优惠券封装发送第三方对象:{}", JSON.toJSONString(couponDto));
            } catch (Exception e) {
                log.error("查询会员CustomerNo失败unionid:{},msg{}:", coupon.getObjectId(), e.getMessage());
            }

            Long millisB = System.currentTimeMillis();

            CepResponseResult responseResult = null;
            String msg = null;
            try {
                responseResult = cepMemberCouponService.saveMemberCoupon(ConfigurationCenterUtil.MEMBER_CEP_COUPON, couponDto);
            } catch (Exception e) {
                msg = e.getMessage();
                log.info("调cep优惠券接口失败,unionid:{},msg:{}", coupon.getObjectId(), e.getMessage());
            }

            log.info("调cep优惠券接口,unionid:{},耗时:{}ms", coupon.getObjectId(), System.currentTimeMillis() - millisB);

            Object data = "";
            Map<String, String> jsonMap;
            //每次发送推送一个记录
            ReachBehaviorDto reachBehaviorDto = new ReachBehaviorDto();
            HashMap<String, String> commentMap = new HashMap<>();
            reachBehaviorDto.setBehavior("N");
            try {
                if ("网络IO操作异常".equals(msg)) {
                    reachBehaviorDto.setBehavior("N");
                    //更新记录成功失败
                    commentMap.put("failedReason", "500_cep接口超时");
                    reachBehaviorDto.setComment(JSON.toJSONString(commentMap));
                    couponCampaignRepository.updateMemberCoupons(memberMap.get("memberId"), notificationCouponDto.getTaskId(), coupon.getObjectId(), "N", "500" + "_" + "cep接口超时");
                } else {
                    if (null == responseResult.getData()) {
                        data = responseResult.getError();
                        jsonMap = JSONObject.toJavaObject(JSONObject.parseObject(data.toString()), Map.class);
                        reachBehaviorDto.setBehavior("N");
                        //更新记录成功失败
                        commentMap.put("failedReason", jsonMap.get("code") + "_" + jsonMap.get("message"));
                        reachBehaviorDto.setComment(JSON.toJSONString(commentMap));
                        couponCampaignRepository.updateMemberCoupons(memberMap.get("memberId"), notificationCouponDto.getTaskId(), coupon.getObjectId(), "N", jsonMap.get("code") + "_" + jsonMap.get("message"));
                    } else {
                        data = responseResult.getData();
                        jsonMap = JSONObject.toJavaObject(JSONObject.parseObject(data.toString()), Map.class);
                        if ("FAILED".equals(jsonMap.get("status"))) {
                            reachBehaviorDto.setBehavior("N");
                            String extra = jsonMap.get("extra");
                            Map map = JSONObject.toJavaObject(JSONObject.parseObject(extra), Map.class);
                            //更新记录成功失败
                            commentMap.put("failedReason", jsonMap.get("message"));
                            reachBehaviorDto.setComment(JSON.toJSONString(commentMap));
                            couponCampaignRepository.updateMemberCoupons(memberMap.get("memberId"), notificationCouponDto.getTaskId(), coupon.getObjectId(), "N", map.get("message").toString());
                        }
                        if ("SUCCESS".equals(jsonMap.get("status"))) {
                            reachBehaviorDto.setBehavior("Y");
                            //更新记录成功失败
                            couponCampaignRepository.updateMemberCoupons(memberMap.get("memberId"), notificationCouponDto.getTaskId(), coupon.getObjectId(), "Y", "发送成功");
                        }
                    }
                }
            } catch (Exception e) {
                log.info("更新优惠券状态失败unionid:{},msg:{}", coupon.getObjectId(), e.getMessage());
            }
            reachBehaviorDto.setTaskId(notificationCouponDto.getTaskId());
            reachBehaviorDto.setTenant(notificationCouponDto.getTenant());
            reachBehaviorDto.setReachId(coupon.getObjectId());
            reachBehaviorDto.setOccId(coupon.getOccId());
            try {
                behaviorProducer.sendMsg(reachBehaviorDto);
            } catch (Exception e) {
                log.error("推送单条优惠券记录至kafka失败unionid:{},msg:{}", coupon.getObjectId(), e.getMessage());
            }
        }
        //全部完成时推送完成
        CallbackDto callbackDto = new CallbackDto();
        callbackDto.setId(notificationCouponDto.getId());
        callbackDto.setTaskId(notificationCouponDto.getTaskId());
        callbackDto.setTenant(notificationCouponDto.getTenant());
        callbackDto.setProcessId(notificationCouponDto.getProcessId());
        callbackDto.setNodeId(notificationCouponDto.getNodeId());
        callbackDto.setStatus("COMPLETED");
        try {
            callbackProducer.sendMsg(callbackDto);
        } catch (Exception e) {
            log.error("推送总条优惠券记录至kafka失败taskId:{},msg:{}", notificationCouponDto.getTaskId(), e.getMessage());
        }

    }


    /**
     * 发送优惠券 (批量)
     *
     * @param notificationCouponDto
     */
    public void sendMemberCouponsRunnable(NotificationCouponDto notificationCouponDto) {
        //创建线程池
        ExecutorService executorService = DataApiUtil.newBlockingThreadPool(ConfigurationCenterUtil.THREAD__COUPONS_CORES, ConfigurationCenterUtil.THREAD_COUPONS_QUEUE);
        Long start = System.currentTimeMillis();
        final int pageSize = ConfigurationCenterUtil.THREAD_COUPONS_PAGESIZE;
        String index = "0";
        int completedNum = 0;
        List<Future<?>> futures = new ArrayList<>();

        try {
            while (true) {
                List<CampaignActionOutputDto> memberCoupons;
                try {
                    memberCoupons = couponCampaignRepository.getMemberCouponsList(notificationCouponDto.getTaskId(), index, pageSize);
                } catch (Exception e) {
                    log.error("优惠券批次查询超时index:{},pageSize:{}; 继续查询...", index, pageSize);
                    continue;
                }
                if (CollectionUtils.isEmpty(memberCoupons)) {
                    log.info("没有查询到该发送优惠券记录taskId:{}", notificationCouponDto.getTaskId());
                    break;
                }
                List<CampaignActionOutputDto> couponDtoList = JSON.parseArray(JSON.toJSONString(memberCoupons), CampaignActionOutputDto.class);
                //记录该批次中最大id值
                index = couponDtoList.get(couponDtoList.size() - 1).getId();
                completedNum += couponDtoList.size();
                CouponsRunnable sendRunnable = new CouponsRunnable(index, completedNum, couponDtoList, notificationCouponDto.getTaskId(), notificationCouponDto.getTenant());
                Future<?> submit = executorService.submit(sendRunnable);
                futures.add(submit);
                if (couponDtoList.size() < pageSize) {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("batch查询优惠券数据失败taskId:{},msg:{}", notificationCouponDto.getTaskId(), e.getMessage());
        }
        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                log.error("优惠券线程等待异常终止!", e);
            }
        }
        try {
            executorService.shutdown();
        } catch (Exception e) {
            log.error("优惠券线程关闭异常!", e.getMessage());
        }
        //全部完成时推送完成
        CallbackDto callbackDto = new CallbackDto();
        callbackDto.setId(notificationCouponDto.getId());
        callbackDto.setTaskId(notificationCouponDto.getTaskId());
        callbackDto.setTenant(notificationCouponDto.getTenant());
        callbackDto.setProcessId(notificationCouponDto.getProcessId());
        callbackDto.setNodeId(notificationCouponDto.getNodeId());
        callbackDto.setStatus("COMPLETED");
        try {
            callbackProducer.sendMsg(callbackDto);
        } catch (Exception e) {
            log.error("batch推送总条优惠券记录至kafka失败taskId:{},msg:{}", notificationCouponDto.getTaskId(), e.getMessage());
        }
        //更新优惠券批量任务状态
        try {
            memberRepository.updateCouponsTask(notificationCouponDto.getTaskId(), "execution", "complete");
            log.info("batch优惠券任务分发完成total:{}+,分发耗时:{}ms,线程执行完毕...", completedNum, System.currentTimeMillis() - start);
        } catch (Exception e) {
            log.error("batch更新小程序批量任务状态失败msg:{}", e.getMessage());
        }
        return;
    }

    class CouponsRunnable implements Runnable {
        String index;
        int completedNum;
        String taskId;
        String tenant;
        List<CampaignActionOutputDto> couponDtoList;

        private CouponsRunnable(String index, int completedNum, List<CampaignActionOutputDto> couponDtoList, String taskId, String tenant) {
            this.index = index;
            this.completedNum = completedNum;
            this.taskId = taskId;
            this.tenant = tenant;
            this.couponDtoList = couponDtoList;
        }

        @Override
        public void run() {
            try {
                Long start = System.currentTimeMillis();
                sendCouponsTable(couponDtoList, taskId, tenant);
                log.info("子线程执行completedNum:{},index:{},耗时:{}ms", completedNum, index, System.currentTimeMillis() - start);
            } catch (Exception e) {
                log.info("子线程执行失败completedNum:{},index:{},taskId:{},message:{}", completedNum, index, taskId, e.getMessage());
            }
        }
    }

    public void sendCouponsTable(List<CampaignActionOutputDto> couponDtoList, String taskId, String tenant) {
        //调cep发券接口
        for (CampaignActionOutputDto coupon : couponDtoList) {
            Map<String, String> memberMap = null;
            CepCustomizationCouponDto couponDto = new CepCustomizationCouponDto();
            try {

                String memberId = memberRepository.queryCustomerNoUnioId(coupon.getObjectId());
                //查询会员CustomerNo
                memberMap = memberRepository.getCustomerNo(memberId);
                couponDto.setActivityCode(coupon.getScene());
                couponDto.setUid(memberMap.get("customerNo"));
                couponDto.setAccountType("CRM_ID");
                couponDto.setResourceId(replaceCoup(coupon.getCoupon()));
                couponDto.setSendCostCenterCode(replaceCoup(coupon.getCostCenter()));
                HashMap<Object, Object> hashMap = new HashMap<>();
                hashMap.put("campaignId", coupon.getCampaignId());
                hashMap.put("nodeId", coupon.getNodeId());
                couponDto.setExtra(hashMap);
                couponDto.setBindBenefit(coupon.getIsSendCardsOffers());
                //新增幂等参数(取主键id)
                couponDto.setTransactionId(coupon.getId());
                log.info("batch优惠券封装发送第三方对象:{}", JSON.toJSONString(couponDto));
            } catch (Exception e) {
                log.error("batch查询会员CustomerNo失败unionid:{},msg{}:", coupon.getObjectId(), e.getMessage());
            }

            Long millisB = System.currentTimeMillis();

            CepResponseResult responseResult = null;
            String msg = null;
            try {
                responseResult = cepMemberCouponService.saveMemberCoupon(ConfigurationCenterUtil.MEMBER_CEP_COUPON, couponDto);
            } catch (Exception e) {
                //新增重试机制(仅针对'网络IO操作异常')
                if ("网络IO操作异常".equals(e.getMessage())) {
                    msg = e.getMessage();
                    //重试次数(默认3次, -1:无限重试)
                    int retryNum = ConfigurationCenterUtil.SUBSCRIPTION_RETRY_NUM;
                    int index = 1;
                    while (index <= retryNum || retryNum == -1) {
                        //重试间隔
                        try {
                            Thread.sleep(Long.valueOf(ConfigurationCenterUtil.SEND_SUBSCRIPTION_SLEEP));
                        } catch (InterruptedException interruptedException) {
                            log.error("InterruptedException", interruptedException);
                            Thread.currentThread().interrupt();
                        }
                        try {
                            responseResult = cepMemberCouponService.saveMemberCoupon(ConfigurationCenterUtil.MEMBER_CEP_COUPON, couponDto);
                            msg = null;
                            log.info("第{}次重试-【batch】unionid:{}调cep发券结束,result:{}", index, coupon.getObjectId(), responseResult);
                            break;
                        } catch (Exception retryE) {
                            msg = retryE.getMessage();
                            log.info("第{}次重试-【batch】unionid:{}调cep发券失败,msg:{}", index, coupon.getObjectId(), msg);
                            if (!"网络IO操作异常".equals(msg)) {
                                break;
                            }
                        }
                        index++;
                    }
                } else {
                    //若getToken不异常，理论不会到这
                    msg = e.getMessage();
                    log.info("【batch】unionid:{}调cep发券失败,msg:{}", coupon.getObjectId(), msg);
                }
            }
            log.info("batch调cep优惠券接口,unionid:{},耗时:{}ms", coupon.getObjectId(), System.currentTimeMillis() - millisB);

            Object data = "";
            Map<String, String> jsonMap;
            //每次发送推送一个记录
            ReachBehaviorDto reachBehaviorDto = new ReachBehaviorDto();
            HashMap<String, String> commentMap = new HashMap<>();
            reachBehaviorDto.setBehavior("N");
            try {
                if ("网络IO操作异常".equals(msg)) {
                    reachBehaviorDto.setBehavior("N");
                    //更新记录成功失败
                    commentMap.put("failedReason", "500_cep接口超时");
                    reachBehaviorDto.setComment(JSON.toJSONString(commentMap));
                    couponCampaignRepository.updateMemberCoupons(memberMap.get("memberId"), taskId, coupon.getObjectId(), "N", "500" + "_" + "cep接口超时");
                } else {
                    if (null == responseResult.getData()) {
                        data = responseResult.getError();
                        jsonMap = JSONObject.toJavaObject(JSONObject.parseObject(data.toString()), Map.class);
                        reachBehaviorDto.setBehavior("N");
                        //更新记录成功失败
                        commentMap.put("failedReason", jsonMap.get("code") + "_" + jsonMap.get("message"));
                        reachBehaviorDto.setComment(JSON.toJSONString(commentMap));
                        couponCampaignRepository.updateMemberCoupons(memberMap.get("memberId"), taskId, coupon.getObjectId(), "N", jsonMap.get("code") + "_" + jsonMap.get("message"));
                    } else {
                        data = responseResult.getData();
                        jsonMap = JSONObject.toJavaObject(JSONObject.parseObject(data.toString()), Map.class);
                        if ("FAILED".equals(jsonMap.get("status"))) {
                            reachBehaviorDto.setBehavior("N");
                            String extra = jsonMap.get("extra");
                            Map map = JSONObject.toJavaObject(JSONObject.parseObject(extra), Map.class);
                            //更新记录成功失败
                            commentMap.put("failedReason", jsonMap.get("message"));
                            reachBehaviorDto.setComment(JSON.toJSONString(commentMap));
                            couponCampaignRepository.updateMemberCoupons(memberMap.get("memberId"), taskId, coupon.getObjectId(), "N", map.get("message").toString());
                        }
                        if ("SUCCESS".equals(jsonMap.get("status"))) {
                            reachBehaviorDto.setBehavior("Y");
                            //更新记录成功失败
                            couponCampaignRepository.updateMemberCoupons(memberMap.get("memberId"), taskId, coupon.getObjectId(), "Y", "发送成功");
                        }
                    }
                }
            } catch (Exception e) {
                log.info("batch更新优惠券状态失败unionid:{},msg:{}", coupon.getObjectId(), e.getMessage());
            }
            reachBehaviorDto.setTaskId(taskId);
            reachBehaviorDto.setTenant(tenant);
            reachBehaviorDto.setReachId(coupon.getObjectId());
            reachBehaviorDto.setOccId(coupon.getOccId());
            try {
                behaviorProducer.sendMsg(reachBehaviorDto);
            } catch (Exception e) {
                log.error("batch推送单条优惠券记录至kafka失败unionid:{},msg:{}", coupon.getObjectId(), e.getMessage());
            }
        }
    }

    /**
     * 小程序订阅 （多批量）
     *
     * @param notificationCouponDto
     */
    public void sendAppletSubscriberRunnable(NotificationCouponDto notificationCouponDto) {
        //创建线程池
        ExecutorService executorService = DataApiUtil.newBlockingThreadPool(ConfigurationCenterUtil.THREAD__SUBSCRIBED_CORES, ConfigurationCenterUtil.THREAD_SUBSCRIBED_QUEUE);
        Long start = System.currentTimeMillis();
        final int pageSize = ConfigurationCenterUtil.THREAD_SUBSCRIBED_PAGESIZE;
        String index = "0";
        int completedNum = 0;
        List<Future<?>> futures = new ArrayList<>();
        try {
            while (true) {
                List<AppletSubscriberDto> appletSubscribed;
                try {
                    appletSubscribed = appletSubcriberRepository.getAppletSubscribersBatch(notificationCouponDto.getTaskId(), index, pageSize);
                } catch (Exception e) {
                    log.error("批次查询超时index:{},pageSize:{}; 继续查询...", index, pageSize);
                    continue;
                }
                if (CollectionUtils.isEmpty(appletSubscribed)) {
                    break;
                }
                List<AppletSubscriberDto> appletSub = JSON.parseArray(JSON.toJSONString(appletSubscribed), AppletSubscriberDto.class);
                //查询订阅模板
                String templateId = appletSub.get(0).getTemplateId();
                CepAppletTemplateDto subscribers = cepAppletTemplaterRepository.getSubscribers(replaceCoup(templateId));
                //记录该批次中最大id值
                index = appletSub.get(appletSub.size() - 1).getId();
                completedNum += appletSub.size();
                SubscribersRunnable sendRunnable = new SubscribersRunnable(index, completedNum, appletSub, subscribers, notificationCouponDto.getTaskId(), notificationCouponDto.getTenant());
                Future<?> submit = executorService.submit(sendRunnable);
                futures.add(submit);
                if (appletSub.size() < pageSize) {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("小程序订阅任务异常终止!", e);
        }
        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                log.error("小程序订阅子线程等待异常终止!", e);
            }
        }
        try {
            executorService.shutdown();
        } catch (Exception e) {
            log.error("线程关闭异常!", e.getMessage());
        }
        log.info("batch小程序订阅任务分发完成total:{}+,分发耗时:{}ms,线程执行完毕...", completedNum, System.currentTimeMillis() - start);
        //全部完成时推送完成
        CallbackDto callbackDto = new CallbackDto();
        callbackDto.setId(notificationCouponDto.getId());
        callbackDto.setTaskId(notificationCouponDto.getTaskId());
        callbackDto.setTenant(notificationCouponDto.getTenant());
        callbackDto.setProcessId(notificationCouponDto.getProcessId());
        callbackDto.setNodeId(notificationCouponDto.getNodeId());
        callbackDto.setStatus("COMPLETED");
        try {
            callbackProducer.sendMsg(callbackDto);
        } catch (Exception e) {
            log.error("batch小程序订阅推送总数至kafka失败msg:{}", e.getMessage());
        }
        //更新小程序批量任务状态
        try {
            memberRepository.updateAppletTask(notificationCouponDto.getTaskId(), "execution", "complete");
        } catch (Exception e) {
            log.error("batch更新小程序批量任务状态失败msg:{}", e.getMessage());
        }
        return;
    }

    class SubscribersRunnable implements Runnable {
        String index;
        int completedNum;
        String taskId;
        String tenant;
        CepAppletTemplateDto subscribers;
        List<AppletSubscriberDto> appletSub;

        private SubscribersRunnable(String index, int completedNum, List<AppletSubscriberDto> appletSub, CepAppletTemplateDto subscribers, String taskId, String tenant) {
            this.index = index;
            this.completedNum = completedNum;
            this.taskId = taskId;
            this.tenant = tenant;
            this.subscribers = subscribers;
            this.appletSub = appletSub;
        }

        @Override
        public void run() {
            try {
                Long start = System.currentTimeMillis();
                sendMemberSubscribersTable(appletSub, subscribers, taskId, tenant);
                log.info("子线程执行completedNum:{},index:{},耗时:{}ms", completedNum, index, System.currentTimeMillis() - start);
            } catch (Exception e) {
                log.info("子线程执行失败completedNum:{},index:{},taskId:{},message:{}", completedNum, index, taskId, e.getMessage());
            }
        }
    }

    public void sendMemberSubscribersTable(List<AppletSubscriberDto> appletSub, CepAppletTemplateDto subscribers, String taskId, String tenant) {
        for (AppletSubscriberDto appletSubscriberDto : appletSub) {
            String openId = null;
            try {
                //查询openid
                if (StringUtils.isBlank(appletSubscriberDto.getCustomerTheme()) || "MEMBER".equals(appletSubscriberDto.getCustomerTheme())) {
                    String memberId = memberRepository.queryOpenId(appletSubscriberDto.getObjectId());
                    openId = memberRepository.queryMemberOpneId(memberId);
                } else {
                    openId = appletSubscriberDto.getObjectId();
                }
            } catch (Exception e) {
                log.error("batch查询openid失败,unionid:{},msg:{}", appletSubscriberDto.getObjectId(), e.getMessage());
            }
            CepAppletSubscribeDto subscribeDto = new CepAppletSubscribeDto();
            subscribeDto.setToUser(openId);
            subscribeDto.setTemplateId(replaceCoup(appletSubscriberDto.getTemplateId()));
            subscribeDto.setPage(appletSubscriberDto.getLandingPage());
            //判断是平台还是活动
            if ("PLATFORM".equals(appletSubscriberDto.getType())) {
                subscribeDto.setCampaignCode(appletSubscriberDto.getSceneCommen());
            }
            if ("CAMPAIGN".equals(appletSubscriberDto.getType())) {
                subscribeDto.setCampaignCode(replaceCoup(appletSubscriberDto.getSceneCampaign()));
            }
            HashMap<String, String> map = new HashMap<>();
            //map.put("time", DateHelper.getCepDate());
            if (StringUtils.isNotBlank(subscribers.getFieldsKey1())) {
                map.put(subscribers.getFieldsKey1(), appletSubscriberDto.getFields1());
            }
            if (StringUtils.isNotBlank(subscribers.getFieldsKey2())) {
                map.put(subscribers.getFieldsKey2(), appletSubscriberDto.getFields2());
            }
            if (StringUtils.isNotBlank(subscribers.getFieldsKey3())) {
                map.put(subscribers.getFieldsKey3(), appletSubscriberDto.getFields3());
            }
            if (StringUtils.isNotBlank(subscribers.getFieldsKey4())) {
                map.put(subscribers.getFieldsKey4(), appletSubscriberDto.getFields4());
            }
            if (StringUtils.isNotBlank(subscribers.getFieldsKey5())) {
                map.put(subscribers.getFieldsKey5(), appletSubscriberDto.getFields5());
            }
            if (StringUtils.isNotBlank(subscribers.getFieldsKey6())) {
                map.put(subscribers.getFieldsKey6(), appletSubscriberDto.getFields6());
            }
            if (StringUtils.isNotBlank(subscribers.getFieldsKey7())) {
                map.put(subscribers.getFieldsKey7(), appletSubscriberDto.getFields7());
            }
            subscribeDto.setData(map);
            subscribeDto.setMiniProgramState(ConfigurationCenterUtil.CEP_MINIPROGRAM_STATE);
            log.info("batch封装发送第三方对象:{}", JSON.toJSONString(subscribeDto));
            Long millisB = System.currentTimeMillis();
            //调第三cep接口body
            CepResponseResult responseResult = null;
            String msg = null;
            //此catch核心作用为捕获doPost层throw的'网络IO操作异常'=超时,以便做重试补偿机制
            try {
                responseResult = cepMemberCouponService.saveAppletSubscription(ConfigurationCenterUtil.MEMBER_CEP_APPLET, subscribeDto);
            } catch (Exception e) {
                //其他异常
                msg = e.getMessage();
                //新增重试机制(仅针对'网络IO操作异常')
                if ("网络IO操作异常".equals(e.getMessage())) {
                    msg = e.getMessage();
                    //重试次数(默认3次, -1:无限重试)
                    int retryNum = ConfigurationCenterUtil.SUBSCRIPTION_RETRY_NUM;
                    int index = 1;
                    while (index <= retryNum || retryNum == -1) {
                        //重试间隔
                        try {
                            Thread.sleep(Long.valueOf(ConfigurationCenterUtil.SEND_SUBSCRIPTION_SLEEP));
                        } catch (InterruptedException interruptedException) {
                            log.error("InterruptedException", interruptedException);
                            Thread.currentThread().interrupt();
                        }
                        try {
                            responseResult = cepMemberCouponService.saveAppletSubscription(ConfigurationCenterUtil.MEMBER_CEP_APPLET, subscribeDto);
                            msg = null;
                            log.info("第{}次重试-【batch】openid:{}调cep接口结束,result:{}", index, openId, responseResult);
                            break;
                        } catch (Exception retryE) {
                            msg = retryE.getMessage();
                            log.info("第{}次重试-【batch】openid:{}调cep接口失败,msg:{}", index, openId, msg);
                            if (!"网络IO操作异常".equals(msg)) {
                                break;
                            }
                        }
                        index++;
                    }
                } else {
                    //若getToken不异常，理论不会到这
                    msg = e.getMessage();
                    log.info("【batch】openid:{}调cep接口失败,msg:{}", openId, msg);
                }
            }
            log.info("调cep接口,openid:{},耗时:{}ms", openId, System.currentTimeMillis() - millisB);
            //每次发送推送一个记录
            ReachBehaviorDto reachBehaviorDto = new ReachBehaviorDto();
            HashMap<String, String> commentMap = new HashMap<>();
            Object data = "";
            Map<String, String> jsonMap = null;
            try {
                if (null == responseResult) {
                    if (StringUtils.isNotBlank(msg)) {
                        //更新记录失败
                        reachBehaviorDto.setBehavior("N");
                        commentMap.put("failedReason", "500_cep接口超时");
                        reachBehaviorDto.setComment(JSON.toJSONString(commentMap));
                        if ("网络IO操作异常".equals(msg)) {
                            couponCampaignRepository.updateMemberSubscribe(taskId, appletSubscriberDto.getObjectId(), "N", "500" + "_" + "cep接口超时");
                        } else {
                            //需考虑其它例.getToken异常
                            couponCampaignRepository.updateMemberSubscribe(taskId, appletSubscriberDto.getObjectId(), "N", "500" + "_" + "其它异常");
                        }
                    } else {
                        reachBehaviorDto.setBehavior("Y");
                        //更新记录成功
                        couponCampaignRepository.updateMemberSubscribe(taskId, appletSubscriberDto.getObjectId(), "Y", "发送成功");
                    }
                } else {
                    data = responseResult.getError();
                    jsonMap = JSONObject.toJavaObject(JSONObject.parseObject(data.toString()), Map.class);
                    reachBehaviorDto.setBehavior("N");
                    //更新记录成功失败
                    commentMap.put("failedReason", jsonMap.get("code") + "_" + jsonMap.get("message"));
                    reachBehaviorDto.setComment(JSON.toJSONString(commentMap));
                    couponCampaignRepository.updateMemberSubscribe(taskId, appletSubscriberDto.getObjectId(), "N", jsonMap.get("code") + "_" + jsonMap.get("message"));
                }
            } catch (Exception e) {
                log.error("batch更新小程序订阅状态失败openid:{},msg:{}", openId, e.getMessage());
            }
            reachBehaviorDto.setTaskId(taskId);
            reachBehaviorDto.setTenant(tenant);
            reachBehaviorDto.setReachId(appletSubscriberDto.getObjectId());
            reachBehaviorDto.setOccId(appletSubscriberDto.getOccId());
            try {
                behaviorProducer.sendMsg(reachBehaviorDto);
            } catch (Exception e) {
                log.error("batch小程序订阅推送单条至kafka失败openid:{}.msg:{}", openId, e.getMessage());
            }
        }
    }


    /**
     * 小程序订阅 （单批量）
     *
     * @param notificationCouponDto
     */
    public void sendAppletSubscriber(NotificationCouponDto notificationCouponDto) {


        final int pageSize = ConfigurationCenterUtil.THREAD_SUBSCRIBED_PAGESIZE;
        List<AppletSubscriberDto> appletSub = null;
        CepAppletTemplateDto subscribers = null;
        try {
            List<AppletSubscriberDto> appletSubscribed = appletSubcriberRepository.getAppletSubscribers(notificationCouponDto.getTaskId(), pageSize);
            appletSub = JSON.parseArray(JSON.toJSONString(appletSubscribed), AppletSubscriberDto.class);
            //查询订阅模板
            String templateId = appletSub.get(0).getTemplateId();
            subscribers = cepAppletTemplaterRepository.getSubscribers(replaceCoup(templateId));
        } catch (Exception e) {
            log.error("查询小程序订阅消息失败,taskId:{},msg:{}", notificationCouponDto.getTaskId(), e.getMessage());
        }
        for (AppletSubscriberDto appletSubscriberDto : appletSub) {
            //查询openid
            String openId = null;
            try {
                if (StringUtils.isBlank(appletSubscriberDto.getCustomerTheme()) || "MEMBER".equals(appletSubscriberDto.getCustomerTheme())) {
                    String memberId = memberRepository.queryOpenId(appletSubscriberDto.getObjectId());
                    openId = memberRepository.queryMemberOpneId(memberId);
                } else {
                    openId = appletSubscriberDto.getObjectId();
                }
            } catch (Exception e) {
                log.error("查询openid失败,unionid:{},msg:{}", appletSubscriberDto.getObjectId(), e.getMessage());
            }
            CepAppletSubscribeDto subscribeDto = new CepAppletSubscribeDto();
            subscribeDto.setToUser(openId);
            subscribeDto.setTemplateId(replaceCoup(appletSubscriberDto.getTemplateId()));
            subscribeDto.setPage(appletSubscriberDto.getLandingPage());
            //判断是平台还是活动
            if ("PLATFORM".equals(appletSubscriberDto.getType())) {
                subscribeDto.setCampaignCode(appletSubscriberDto.getSceneCommen());
            }
            if ("CAMPAIGN".equals(appletSubscriberDto.getType())) {
                subscribeDto.setCampaignCode(replaceCoup(appletSubscriberDto.getSceneCampaign()));
            }
            HashMap<String, String> map = new HashMap<>();
            //map.put("time", DateHelper.getCepDate());
            if (StringUtils.isNotBlank(subscribers.getFieldsKey1())) {
                map.put(subscribers.getFieldsKey1(), appletSubscriberDto.getFields1());
            }
            if (StringUtils.isNotBlank(subscribers.getFieldsKey2())) {
                map.put(subscribers.getFieldsKey2(), appletSubscriberDto.getFields2());
            }
            if (StringUtils.isNotBlank(subscribers.getFieldsKey3())) {
                map.put(subscribers.getFieldsKey3(), appletSubscriberDto.getFields3());
            }
            if (StringUtils.isNotBlank(subscribers.getFieldsKey4())) {
                map.put(subscribers.getFieldsKey4(), appletSubscriberDto.getFields4());
            }
            if (StringUtils.isNotBlank(subscribers.getFieldsKey5())) {
                map.put(subscribers.getFieldsKey5(), appletSubscriberDto.getFields5());
            }
            if (StringUtils.isNotBlank(subscribers.getFieldsKey6())) {
                map.put(subscribers.getFieldsKey6(), appletSubscriberDto.getFields6());
            }
            if (StringUtils.isNotBlank(subscribers.getFieldsKey7())) {
                map.put(subscribers.getFieldsKey7(), appletSubscriberDto.getFields7());
            }
            subscribeDto.setData(map);
            subscribeDto.setMiniProgramState(ConfigurationCenterUtil.CEP_MINIPROGRAM_STATE);
            log.info("单条封装发送第三方对象:{}", JSON.toJSONString(subscribeDto));
            //调第三cep接口body
            CepResponseResult responseResult = null;
            String msg = null;
            try {
                responseResult = cepMemberCouponService.saveAppletSubscription(ConfigurationCenterUtil.MEMBER_CEP_APPLET, subscribeDto);
            } catch (Exception e) {
                msg = e.getMessage();
                log.error("单条调cep接口失败,openid:{},message:{}", openId, e.getMessage());
            }
            //每次发送推送一个记录
            ReachBehaviorDto reachBehaviorDto = new ReachBehaviorDto();
            HashMap<String, String> commentMap = new HashMap<>();
            Object data = "";
            Map<String, String> jsonMap = null;
            try {
                if (null == responseResult) {
                    if (StringUtils.isNotBlank(msg)) {
                        //更新记录失败
                        reachBehaviorDto.setBehavior("N");
                        commentMap.put("failedReason", "500_cep接口超时");
                        reachBehaviorDto.setComment(JSON.toJSONString(commentMap));
                        if ("网络IO操作异常".equals(msg)) {
                            couponCampaignRepository.updateMemberSubscribe(notificationCouponDto.getTaskId(), appletSubscriberDto.getObjectId(), "N", "500" + "_" + "cep接口超时");
                        } else {
                            //需考虑其它例.getToken异常
                            couponCampaignRepository.updateMemberSubscribe(notificationCouponDto.getTaskId(), appletSubscriberDto.getObjectId(), "N", "500" + "_" + "其它异常");
                        }
                    } else {
                        reachBehaviorDto.setBehavior("Y");
                        //更新记录成功
                        couponCampaignRepository.updateMemberSubscribe(notificationCouponDto.getTaskId(), appletSubscriberDto.getObjectId(), "Y", "发送成功");
                    }
                } else {
                    data = responseResult.getError();
                    jsonMap = JSONObject.toJavaObject(JSONObject.parseObject(data.toString()), Map.class);
                    reachBehaviorDto.setBehavior("N");
                    //更新记录成功失败
                    commentMap.put("failedReason", jsonMap.get("code") + "_" + jsonMap.get("message"));
                    reachBehaviorDto.setComment(JSON.toJSONString(commentMap));
                    couponCampaignRepository.updateMemberSubscribe(notificationCouponDto.getTaskId(), appletSubscriberDto.getObjectId(), "N", jsonMap.get("code") + "_" + jsonMap.get("message"));
                }
            } catch (Exception e) {
                log.error("更新小程序订阅状态失败openid:{},msg:{}", openId, e.getMessage());
            }
            reachBehaviorDto.setTaskId(notificationCouponDto.getTaskId());
            reachBehaviorDto.setTenant(notificationCouponDto.getTenant());
            reachBehaviorDto.setReachId(appletSubscriberDto.getObjectId());
            reachBehaviorDto.setOccId(appletSubscriberDto.getOccId());
            try {
                behaviorProducer.sendMsg(reachBehaviorDto);
            } catch (Exception e) {
                log.error("小程序订阅推送单条至kafka失败openid:{}.msg:{}", openId, e.getMessage());
            }
        }
        //全部完成时推送完成
        CallbackDto callbackDto = new CallbackDto();
        callbackDto.setId(notificationCouponDto.getId());
        callbackDto.setTaskId(notificationCouponDto.getTaskId());
        callbackDto.setTenant(notificationCouponDto.getTenant());
        callbackDto.setProcessId(notificationCouponDto.getProcessId());
        callbackDto.setNodeId(notificationCouponDto.getNodeId());
        callbackDto.setStatus("COMPLETED");
        try {
            callbackProducer.sendMsg(callbackDto);
        } catch (Exception e) {
            log.error("小程序订阅推送总数至kafka失败msg:{}", e.getMessage());
        }
        return;

    }


    public String replaceCoup(String res) {
        String s = res.replace("[", "");
        String rep = s.replace("]", "");
        String replace = rep.replace("\"", "");
        return replace;
    }

}

