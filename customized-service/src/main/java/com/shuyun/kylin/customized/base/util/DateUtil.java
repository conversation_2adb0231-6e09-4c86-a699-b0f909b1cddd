package com.shuyun.kylin.customized.base.util;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Slf4j
public class DateUtil extends DateUtils{
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    /**
     * The constant DATE_TIME_FORMAT.
     */
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_TIME_FORMAT1 = "yyyy/MM/dd HH:mm:ss";
    // 世界标准时间UTC
    public static final String DATE_FORMAT_T = "yyyy-MM-dd'T'HH:mm:ss";
    // 事件服务DateTime格式
    public static final String ES_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX";
    // 数据服务DateTime格式
    public static final String DS_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

    private static String[] parsePatterns = {"yyyy-MM-dd'T'HH:mm:ss.SSS","yyyy-MM-dd'T'HH:mm:ss",
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM",
            "yyyy-MM-dd HH:mm:ss.SSS", "yyyyMMdd", "yyyy-MM-dd'T'HH:mm:ss'Z'",
            "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", "yyyy-MM-dd'T'HH:mm:ss.SSS+0000",
            "yyyy-MM-dd HH:mm:ss.S","yyyyMMddHHmmss","yyyy-MM-dd'T'HH:mm:ss.SSS+SSSS"
    };

    public static Date parseStrToDate(String datetime,String dateFormat){
        Date date = null;
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormat);//注意月份是MM
            date = simpleDateFormat.parse(datetime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    public static String parseDateToStr(Date currentTime,String dateFormat){
        SimpleDateFormat formatter = new SimpleDateFormat(dateFormat);
        String dateString = formatter.format(currentTime);
        return dateString;
    }
    /**
     * date1 是否在 date2 之后
     *
     * @param date1
     * @param date2
     * @return
     */
    public static Boolean ifAfter(String date1, String date2) {
        try {
            return parseDate(date1).after(parseDate(date2));
        } catch (Exception e) {
        }
        return false;
    }
    public static Date parseTimeToDate(Long datetime,String dateFormat){
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormat);
        long longTimeStamp = datetime * 1000;
        Date date = new Date(longTimeStamp);
        return date;
    }

    public static ZonedDateTime parseStrToDateform(String datetime,String dateFormat){
        return ZonedDateTime.parse(datetime, DateTimeFormatter.ofPattern(dateFormat));
    }
    /**
     * 格式化
     *
     * @param date
     * @param pattern
     * @return
     */
    public static String format(Date date, String pattern) {
        DateFormat format = new SimpleDateFormat(pattern);
        return format.format(date);
    }
    /**
     * 格式化
     *
     * @param date
     * @param returnPattern
     * @return
     */
    public static String format(String date, String returnPattern) {
        try {
            date = format(parseDate(date), returnPattern);
        } catch (Exception e) {
        }
        return date;
    }
    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }
    public static Date getGMT8(Date date) {
        try {
            return addHours(date, -8);
        } catch (Exception e) {
            return null;
        }

    }
    public static String getGMT8Str(Date date) {
        try {
            return format(addHours(date, -8),DateUtil.DATE_FORMAT_T);
        } catch (Exception e) {
            return null;
        }

    }
    public static String getDateTime() {
        try {
            return format(new Date(),DateUtil.DATE_TIME_FORMAT);
        } catch (Exception e) {
            return null;
        }

    }
    public static boolean isEffectiveDate(String startTime, String endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeStrart = startTime.substring(0, 19).replace("T", " ");
        String timeEndTime = endTime.substring(0, 19).replace("T", " ");
        Date startTimeDate = getStartTime(timeStrart);
        Date endTimeDate = getStartTime(timeEndTime);
        //当前时间
        Date nowTime = new Date();
        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);
        Calendar begin = Calendar.getInstance();
        begin.setTime(startTimeDate);

        Calendar end = Calendar.getInstance();
        end.setTime(endTimeDate);
        //比较
        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }

    public static Date getStartTime(String time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date timeDate = null;
        try {
            Date dt = sdf.parse(time);
            Calendar rightNow = Calendar.getInstance();
            rightNow.setTime(dt);
            rightNow.add(Calendar.HOUR, 8);
            timeDate = rightNow.getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return timeDate;
    }

    public static boolean isTimeDate(String created) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String createds = created.substring(0, 19).replace("T", " ");
        //查询的时间
        Date nowTime = getStartTime(createds);

        //当天开始时间
        Date startTime = DateHelper.transferString2Date(DateHelper.getDateDay());
        //当天结束时间
        Date endTime = DateHelper.transferString2Date(DateHelper.getEendTimeDay());

        String a = sdf.format(nowTime);
        String b = sdf.format(startTime);
        String c = sdf.format(endTime);


        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);
        //比较
        if (date.after(begin) && date.before(end)) {
            log.info("开始时间:{},结束时间:{},true当前时间:{}", a,b,c);
            return true;
        } else {
            log.info("开始时间:{},结束时间:{},false当前时间:{}", a,b,c);
            return false;
        }
    }

}
