package com.shuyun.kylin.customized.member.service.impl;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.enums.ResponseCodeEnum;
import com.shuyun.kylin.customized.base.feign.client.OpenApiFeignClient;
import com.shuyun.kylin.customized.base.stream.kafka.dao.CampaignActionOutputDto;
import com.shuyun.kylin.customized.base.util.DateHelper;
import com.shuyun.kylin.customized.member.dm.CepStatisticRepository;
import com.shuyun.kylin.customized.member.dto.CepMemberSumDto;
import com.shuyun.kylin.customized.member.service.CepStatisticService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentSkipListMap;

@Slf4j
@Service
public class CepStatisticServiceImpl implements CepStatisticService {

    @Autowired
    private CepStatisticRepository cepStatisticRepository;

    @Override
    public ResponseResult getmemberSum(String memberSourceDetail, String startTime, String endTime, String groupingTime) {
        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        CepMemberSumDto groupingNum = new CepMemberSumDto();
        try {
            startTime = DateHelper.getZone(startTime);
            endTime = DateHelper.getZone(endTime);
        } catch (Exception e) {
            return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT);
        }
        try {
            if ("byHour".equals(groupingTime)) {
                List<Map<String, String>> getByHourNumbers = cepStatisticRepository.getByHourMember(memberSourceDetail, startTime, endTime);
                log.info("byHour返还结果:{}", JSON.toJSONString(getByHourNumbers));
                if (!CollectionUtils.isEmpty(getByHourNumbers)) {
                    for (Map<String, String> getByHourNumber : getByHourNumbers) {
                        map.put(String.valueOf(getByHourNumber.get("time")), JSON.toJSONString(getByHourNumber.get("countNum")));
                    }
                    groupingNum.setGroupingNum(map);
                }
                return new ResponseResult(ResponseCodeEnum.SUCCESS, groupingNum);
            }
        } catch (Exception e) {
            log.error("byHour返还结果错误e:{}",e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, ResponseCodeEnum.OPENAPI_FAILED.getMsg());

        }
        try {
            if ("byDay".equals(groupingTime)) {
                List<Map<String, String>> getByDayNumbers = cepStatisticRepository.getByDayMember(memberSourceDetail, startTime, endTime);
                log.info("byDay返还结果:{}", JSON.toJSONString(getByDayNumbers));
                if (!CollectionUtils.isEmpty(getByDayNumbers)) {
                    for (Map<String, String> getByDayNumber : getByDayNumbers) {
                        map.put(String.valueOf(getByDayNumber.get("time")), JSON.toJSONString(getByDayNumber.get("countNum")));
                    }
                    groupingNum.setGroupingNum(map);
                }
                return new ResponseResult(ResponseCodeEnum.SUCCESS, groupingNum);
            }
        } catch (Exception e) {
            log.error("byDay返还结果错误e:{}",e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, ResponseCodeEnum.OPENAPI_FAILED.getMsg());
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }

    @Override
    public ResponseResult getActivitySum(String scene, String changeSourceDetail, String startTime, String endTime) {

        HashMap<String, String> map = new HashMap<>();
        try {
            startTime = DateHelper.getZone(startTime);
            endTime = DateHelper.getZone(endTime);
        } catch (Exception e) {
            return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT);
        }
        //统计正积分次数
        List<Map<String, String>> pointsNum;
        try {
            if (StringUtils.isBlank(scene)) {
                pointsNum = cepStatisticRepository.getAccumulatePointsNum(changeSourceDetail, startTime, endTime);
            } else {
                pointsNum = cepStatisticRepository.getAccumulatePointsNum(scene, changeSourceDetail, startTime, endTime);
            }
            log.info("统计正积分次数:{}", pointsNum);
            for (Map<String, String> pointMap : pointsNum) {
                if ("null".equals(JSON.toJSONString(pointMap.get("accumulatePoints")))) {
                    map.put("accumulatePoints", "0");
                } else {
                    String b = String.valueOf(pointMap.get("accumulatePoints"));
                    Double accumulatePoints = Double.valueOf(b);
                    //int i = accumulatePoints.intValue();
                    map.put("accumulatePoints", JSON.toJSONString(accumulatePoints));
                }
                map.put("accumulatePointsNum", JSON.toJSONString(pointMap.get("accumulatePointsNum")));
            }
        } catch (Exception e) {
            log.error("统计正积分次数e:{}",e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, ResponseCodeEnum.OPENAPI_FAILED.getMsg());
        }
        //统计负积分次数
        List<Map<String, String>> deductionPointsNum;
        try {
            if (StringUtils.isBlank(scene)) {
                deductionPointsNum = cepStatisticRepository.getDeductionPointsNum(changeSourceDetail, startTime, endTime);
            } else {
                deductionPointsNum = cepStatisticRepository.getDeductionPointsNum(scene, changeSourceDetail, startTime, endTime);
            }
            log.info("统计负积分次数:{}", deductionPointsNum);
            for (Map<String, String> deductionPointsNumMap : deductionPointsNum) {
                if ("null".equals(JSON.toJSONString(deductionPointsNumMap.get("deductionPoints")))) {
                    map.put("deductionPoints", "0");
                } else {
                    String b = String.valueOf(deductionPointsNumMap.get("deductionPoints"));
                    Double deductionPoints = Double.valueOf(b);
                    //int i = deductionPoints.intValue();
                    map.put("deductionPoints", JSON.toJSONString(deductionPoints));
                }
                map.put("deductionPointsNum", JSON.toJSONString(deductionPointsNumMap.get("deductionPointsNum")));
            }

        } catch (Exception e) {
            log.error("统计负积分次数e:{}",e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, ResponseCodeEnum.OPENAPI_FAILED.getMsg());
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS, map);
    }

    @Override
    public ResponseResult getExchangeSum(String startTime, String endTime) {
        HashMap<String, String> map = new HashMap<>();
        try {
            startTime = DateHelper.getZone(startTime);
            endTime = DateHelper.getZone(endTime);
        } catch (Exception e) {
            return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT);
        }
        try {
            //统计积分兑换
            List<Map<String, String>> exchangeNum = cepStatisticRepository.getExchangeNum(startTime, endTime);
            log.info("统计积分兑换:{}", exchangeNum);
            for (Map<String, String> exchMap : exchangeNum) {
                if ("null".equals(JSON.toJSONString(exchMap.get("exchangePoints")))) {
                    map.put("exchangePoints", "0");
                } else {
                    String b = String.valueOf(exchMap.get("exchangePoints"));
                    Double exchangePoints = Double.valueOf(b);
                    //int i = exchangePoints.intValue();
                    map.put("exchangePoints", JSON.toJSONString(exchangePoints));
                }
                map.put("exchangeNum", JSON.toJSONString(exchMap.get("exchangeNum")));
            }
            //统计积分取消/返还
            List<Map<String, String>> cancelNum = cepStatisticRepository.getCancelNum(startTime, endTime);
            log.info("统计积分取消/返还:{}", cancelNum);
            for (Map<String, String> cancelMap : cancelNum) {
                if ("null".equals(JSON.toJSONString(cancelMap.get("cancelPoints")))) {
                    map.put("cancelPoints", "0");
                } else {
                    String b = String.valueOf(cancelMap.get("cancelPoints"));
                    Double cancelPoints = Double.valueOf(b);
                    //int i = cancelPoints.intValue();
                    map.put("cancelPoints", JSON.toJSONString(cancelPoints));
                }
                map.put("cancelNum", JSON.toJSONString(cancelMap.get("cancelNum")));
            }
        } catch (Exception e) {
            log.error("统计积分兑换:{}",e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, ResponseCodeEnum.OPENAPI_FAILED.getMsg());
        }

        return new ResponseResult(ResponseCodeEnum.SUCCESS, map);
    }

}
