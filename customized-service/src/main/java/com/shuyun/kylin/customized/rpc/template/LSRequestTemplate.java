package com.shuyun.kylin.customized.rpc.template;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shuyun.kylin.customized.base.feign.dao.MemberBingDto;
import com.shuyun.kylin.customized.base.feign.dao.OfferProjectDetailClientResponse;
import com.shuyun.kylin.customized.project.request.GrantCouponRequest;
import com.shuyun.kylin.customized.rpc.dto.BKResponseDto;
import com.shuyun.kylin.customized.rpc.dto.LSResponseDto;
import com.shuyun.lite.util.Common;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

@Slf4j
public class LSRequestTemplate {

    private static final ObjectMapper MAPPER = new ObjectMapper();
    private static final OkHttpClient CLIENT = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .build();

    /**
     *罗森发券
     * @return SyncMemberResponse
     */
    public static LSResponseDto sendCoupon(GrantCouponRequest grantCouponRequest, OfferProjectDetailClientResponse projectDetail, MemberBingDto memberBindInfo) throws IOException {
        String url = Common.getSysOrEnv("ls.pick.coupon.url","https://srslawson-dev.yorentown.com/market/security/pass/external/pickCoupon");
        String organizationId = Common.getSysOrEnv("ls.organizationId.key","kele");
        HashMap<String, String> map = new HashMap<>();
        map.put("businessNo",grantCouponRequest.getTransactionId());
        map.put("activityKey",projectDetail.getData().getExtData().getExternalProjectId());
        map.put("userId",memberBindInfo.getOpenId());
        map.put("organizationId",organizationId);
        // 1. 构造全部待签参数
        TreeMap<String, String> signMap = new TreeMap<>();
        signMap.put("organizationId", organizationId);
        signMap.putAll(map);
        String ts = String.valueOf(System.currentTimeMillis());
        signMap.put("timestamp", ts);

        // 2. 计算签名
        String signStr = buildSignString(signMap);
        String signature = sha256(signStr);
        log.info("罗森发券接口transactionId:{},map:{}",grantCouponRequest.getTransactionId(),JSON.toJSON(map));
        // 3. 请求体（仅业务参数，不含timestamp）
        RequestBody body = RequestBody.create(
                MAPPER.writeValueAsString(map),
                MediaType.parse("application/json; charset=utf-8")
        );

        Request request = new Request.Builder()
                .url(url)
                .header("timestamp", ts)
                .header("signature", signature)
                .post(body)
                .build();
        log.info("罗森发券接口transactionId:{},request:{}",grantCouponRequest.getTransactionId(),JSON.toJSON(request));
        try (Response response = CLIENT.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("HTTP " + response.code());
            }
            String respBody = response.body() == null ? "" : response.body().string();
            LSResponseDto bkResponseDto = JSON.parseObject(respBody, LSResponseDto.class);
            return bkResponseDto;
        }
    }

    /* ---------- 工具 ---------- */

    /** 生成待签串：key=value&key=value（已排序） */
    private static String buildSignString(TreeMap<String, String> map) {
        StringBuilder sb = new StringBuilder();
        map.forEach((k, v) -> {
            if (sb.length() > 0) sb.append('&');
            sb.append(k).append('=').append(v);
        });
        return sb.toString();
    }

    /** SHA-256（小写 64 位） */
    private static String sha256(String content) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(content.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : hash) sb.append(String.format("%02x", b));
            return sb.toString();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
