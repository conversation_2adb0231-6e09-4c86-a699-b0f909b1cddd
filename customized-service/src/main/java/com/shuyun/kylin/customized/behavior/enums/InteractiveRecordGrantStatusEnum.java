package com.shuyun.kylin.customized.behavior.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@Getter
@AllArgsConstructor
public enum InteractiveRecordGrantStatusEnum {

    SUCCEED("0", "全部发放成功"),

    PART("1", "部分发放成功"),

    FAIL("2", "发放失败");

    private String str;
    private String desc;

    public static List<Object> getSucceedOrPartObject() { return Lists.newArrayList(InteractiveRecordGrantStatusEnum.SUCCEED.str,
            InteractiveRecordGrantStatusEnum.PART.str); }

}
