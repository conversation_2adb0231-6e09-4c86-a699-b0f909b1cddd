package com.shuyun.kylin.customized.member.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
/**
 *活动积分规则模型
 * <AUTHOR>
 * @Date 2021/12/17 16:39
 */
@Data
public class CampaignPointsRuleDto {

    private String scene;

    private String type;

    private String ruleType;

    private String task;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String startTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String endTime;

    private Double sendOrDeductValuePerTime;

    private String sendPointTimeliness;

    private Integer sendTimeLimitPerDay;

    private Integer sendTimeLimitPerMonth;

    private Integer sendTimeTotalLimit;

    private Boolean isTransactionRelated;

}
