package com.shuyun.kylin.customized.swire.repository;


import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.base.stream.kafka.dao.CampaignActionOutputDto;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.base.util.DateHelper;
import com.shuyun.kylin.customized.swire.dto.DailyLimitedPointsRuleDto;
import com.shuyun.kylin.customized.swire.dto.RulePointRecordDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.time.ZonedDateTime;
import java.util.*;

@Slf4j
@Component
public class DailyLimitedPointsRuleRepository extends BaseDsRepository<DailyLimitedPointsRuleDto> {

    public List<DailyLimitedPointsRuleDto> querySceneRule(String scene,String channelType) {

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("scene",scene);
        queryMap.put("channelType",channelType);
        queryMap.put("created", DateHelper.getZone(DateHelper.getDateTimeFormat()));
        String sql = " SELECT ruleCode,scene,pointsThreshold,isDailyLimited,channelType FROM  " + ModelConstants.KAILY_LIMITED_POINTSRULE + "  where scene = :scene and channelType= :channelType and startTime <=:created and endTime >=:created ";
        //log.info("查询规则sql:{},scene:{},created:{}",sql,scene, JSON.toJSONString(queryMap.get("created")));
        List<DailyLimitedPointsRuleDto> ruleDtos = executeSQL(sql, queryMap);
        return ruleDtos;
    }


    public Integer getMaxRuleId() {
        String sql = " select ruleCode from " + ModelConstants.KAILY_LIMITED_POINTSRULE + " order by ruleCode desc  limit 1 " ;
        List<Map<String,Object>> list = execute(sql, Collections.emptyMap()).getData();
        Integer id = 0;
        if (CollectionUtils.isNotEmpty(list)) {
            for (Map map : list) {
                id = Integer.valueOf(String.valueOf(map.get("ruleCode")));
            }
        }
        return id;
    }

    public List<HashMap<String,Object>> queryRuleByRSCC(Integer ruleCode, String scene, String channelType, String channelFrom) {
        String now  = DateHelper.getZone(DateHelper.getDateTimeFormat());
        String sql = ("SELECT ruleCode, ruleName, scene, channelType, channelFrom, pointsThreshold, isDailyLimited, " +
                " date_format(sub_hours(startTime, -8),'yyyy-MM-dd HH:mm:ss') as startTime, date_format(sub_hours(endTime, -8),'yyyy-MM-dd HH:mm:ss') as endTime FROM ")
                .concat(ModelConstants.KAILY_LIMITED_POINTSRULE)
                .concat(" where startTime <= '").concat(now).concat("' and endTime >= '").concat(now).concat("' ")
                .concat(" and isOpenQuery is true ");
        if( !ObjectUtils.isEmpty(ruleCode) ){
            sql = sql.concat( " and ruleCode = " ).concat( String.valueOf(ruleCode) ).concat(" ");
        }
        if( !ObjectUtils.isEmpty(scene) ){
            sql = sql.concat( " and scene = '" ).concat(scene).concat("' ");
        }
        if( !ObjectUtils.isEmpty(channelType) ){
            sql = sql.concat( " and channelType = '" ).concat(channelType).concat("' ");
        }
        if( !ObjectUtils.isEmpty(channelFrom) ){
            sql = sql.concat( " and channelFrom = '" ).concat(channelFrom).concat("' ");
        }
        sql = sql.concat(" group by ruleCode ");
        log.info("查询queryRuleByRSCC执行SQL:{}", sql);
        List<HashMap<String,Object>> ruleList = execute(sql, Collections.emptyMap()).getData();
        return ruleList;
    }


}
