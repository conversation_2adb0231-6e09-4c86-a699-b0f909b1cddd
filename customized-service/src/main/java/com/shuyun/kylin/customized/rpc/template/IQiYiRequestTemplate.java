package com.shuyun.kylin.customized.rpc.template;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.util.XmlUtil;
import com.shuyun.kylin.customized.project.dto.IQiYiCallbackDto;
import com.shuyun.kylin.customized.project.strategy.router.SendCouponEnum;
import com.shuyun.kylin.customized.rpc.dto.IQiYiRequestDto;
import com.shuyun.kylin.customized.rpc.dto.IQiYiResponseDto;
import com.shuyun.kylin.customized.rpc.entity.IQiYiConfigEntity;
import com.shuyun.kylin.customized.rpc.template.base.KoRequestTemplate;
import com.shuyun.motor.common.cons.PropsUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 爱奇艺充值（券）请求模板
 */
@Slf4j
public class IQiYiRequestTemplate extends KoRequestTemplate {

    static final IQiYiConfigEntity iQiYiConfigEntity =JSON.parseObject(PropsUtil.getSysOrEnv("iqiyi.api.config"), IQiYiConfigEntity.class);

    /**
     * 爱奇艺请求结果需要回调，回调接口见：
     * @see com.shuyun.kylin.customized.project.callback.IQiYiCallbackController#sendCouponCallback(String, String, String, String)
     */
    public static IQiYiResponseDto sendCoupon(IQiYiRequestDto iQiYiRequestDto, String projectIdAndCouponCode) {
        log.info("开始请求爱奇艺发券接口，入参iQiYiRequestDto={}", JSON.toJSONString(iQiYiRequestDto));
        try {
            String body = HttpUtil.createPost(iQiYiConfigEntity.getOrderUrl())
                    .form("userid", iQiYiRequestDto.getUserId())
                    .form("userpws", iQiYiRequestDto.getUserPws())
                    .form("cardid", iQiYiRequestDto.getCardId())
                    .form("cardnum", iQiYiRequestDto.getCardNum())
                    .form("sporder_id", iQiYiRequestDto.getSporderId())
                    .form("sporder_time", iQiYiRequestDto.getSporderTime())
                    .form("game_userid", iQiYiRequestDto.getGameUserId())
                    .form("game_userpsw", iQiYiRequestDto.getGameUserpsw())
                    .form("game_area", iQiYiRequestDto.getGameArea())
                    .form("game_srv", iQiYiRequestDto.getGameSrv())
                    .form("md5_str", iQiYiRequestDto.getMd5Str())
                    .form("phoneno", iQiYiRequestDto.getPhoneno())
                    .form("ret_url", iQiYiRequestDto.getRetUrl())
                    .form("version", iQiYiRequestDto.getVersion())
                    .form("userip", iQiYiRequestDto.getUserIp())
                    .execute().body();
            log.info("爱奇艺sendCoupon响应结果，body={}", body);
            pushKoRpcRequestLog(iQiYiConfigEntity.getOrderUrl(), iQiYiRequestDto, body, projectIdAndCouponCode, SendCouponEnum.IQIYI.getCode());
            if(ObjectUtil.isEmpty(body)) {
                return null;
            }
            IQiYiResponseDto iQiYiResponseDto = convertXmlToObj(body);
            log.info("爱奇艺sendCoupon响应对对象，iQiYiResponseDto={}", JSON.toJSONString(iQiYiResponseDto));
            return iQiYiResponseDto;
        } catch (Exception e) {
            log.error("爱奇艺sendCoupon请求异常，", e);
            pushKoRpcRequestLog(iQiYiConfigEntity.getOrderUrl(), iQiYiRequestDto, e.getMessage(), projectIdAndCouponCode, SendCouponEnum.IQIYI.getCode());
            return null;
        }
    }

    private static IQiYiResponseDto convertXmlToObj(String xml) {
        return XmlUtil.convertXmlStrToObject(IQiYiResponseDto.class, xml);
    }
}
