package com.shuyun.kylin.customized.member.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.dm.AppletSubcriberRepository;
import com.shuyun.kylin.customized.base.dm.CouponCampaignRepository;
import com.shuyun.kylin.customized.base.stream.kafka.dao.AppletSubscriberDto;
import com.shuyun.kylin.customized.base.stream.kafka.dao.CallbackDto;
import com.shuyun.kylin.customized.base.stream.kafka.dao.CepResponseResult;
import com.shuyun.kylin.customized.base.stream.kafka.dao.ReachBehaviorDto;
import com.shuyun.kylin.customized.base.stream.kafka.producer.coupon.BehaviorProducer;
import com.shuyun.kylin.customized.base.stream.kafka.producer.coupon.CallbackProducer;
import com.shuyun.kylin.customized.base.util.AuthenticationUtil;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.base.util.DataApiUtil;
import com.shuyun.kylin.customized.base.util.HttpClientHelper;
import com.shuyun.kylin.customized.behavior.util.LoggingRequestInterceptor;
import com.shuyun.kylin.customized.member.dm.CepAppletTemplaterRepository;
import com.shuyun.kylin.customized.member.dm.MemberRepository;
import com.shuyun.kylin.customized.member.dto.CepAppletSubscribeDto;
import com.shuyun.kylin.customized.member.dto.CepAppletTemplateDto;
import com.shuyun.kylin.customized.member.dto.CepCustomizationCouponDto;
import com.shuyun.kylin.customized.member.dto.MemberUserDto;
import com.shuyun.kylin.customized.member.service.CepMemberCouponService;
import com.shuyun.kylin.starter.redis.ICache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

@Slf4j
@Service
public class CepMemberCouponServiceImpl implements CepMemberCouponService {

    @Autowired
    private BehaviorProducer behaviorProducer;

    @Autowired
    private CallbackProducer callbackProducer;

    @Autowired
    private MemberRepository memberRepository;

    @Autowired
    private CepMemberCouponService cepMemberCouponService;

    @Autowired
    private CepMemberCouponServiceImpl cepMemberCouponServiceImpl;

    @Autowired
    private CouponCampaignRepository couponCampaignRepository;

    @Autowired
    private AppletSubcriberRepository appletSubcriberRepository;

    @Autowired
    private CepAppletTemplaterRepository cepAppletTemplaterRepository;

    ExecutorService executorService = DataApiUtil.newBlockingThreadPool(ConfigurationCenterUtil.THREAD_CORE_SUM, ConfigurationCenterUtil.THREAD_QUEUE_SUM);


    @Autowired
    @Qualifier("redisCache")
    private ICache redisCache;

    @Override
    public CepResponseResult saveMemberCoupon(String url, CepCustomizationCouponDto body) {

        //获取token
        Object token = redisCache.get("cep_token");
        //log.info("token值:{}", token);
        if (null == token) {
            token = getToken();
        }
        Map headers = AuthenticationUtil.methodPostJson(url, token.toString(), body);
        try {
            String s1 = HttpClientHelper.doPostJSON(ConfigurationCenterUtil.CEP_URL + url, body, headers);
            CepResponseResult responseResult = JSONObject.parseObject(s1, CepResponseResult.class);
            log.info("优惠券发放结果封装customerNo:{},json:{}",body.getUid(),JSON.toJSONString(responseResult));
            return responseResult;
        } catch (RestClientException e) {
            log.info("调用cep注销接口异常customerNo:{},msg:{}", body.getUid(),e.getMessage());
        }
        return null;
    }

    @Override
    public CepResponseResult saveAppletSubscription(String url, CepAppletSubscribeDto body) {
        //获取token
        Object token = redisCache.get("cep_token");
        //log.info("token值:{}", token);
        if (null == token) {
            token = getToken();
        }
        Map headers = AuthenticationUtil.methodPostJson(url, token.toString(), body);
        String s1 = HttpClientHelper.doPostJSON(ConfigurationCenterUtil.CEP_URL + url, body, headers);
        CepResponseResult responseResult = JSONObject.parseObject(s1, CepResponseResult.class);
        log.info("小程序订阅返回结果 openid:{},json:{}", body.getToUser(),JSON.toJSONString(s1));
        return responseResult;
    }

    /**
     * 会员注销
     *
     * @param url
     * @param customerNo
     * @param operation  注销会员：cep打标记-DELETE_MEMBERSHIP
     * @return
     */
    public String deleteMember(String url, String customerNo, String operation) {
        //获取token
        Object token = redisCache.get("cep_token");
        if (null == token) {
            token = getToken();
        }
        log.info("token值:{}", token);
        HashMap<Object, String> hashMap = new HashMap<>();
        hashMap.put("uid", customerNo);
        hashMap.put("type", "CRM_ID");
        hashMap.put("operation", operation);
        Map headers = AuthenticationUtil.methodPostJson(url, token.toString(), hashMap);
        //HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(hashMap), headers);
        //RestTemplate restTemplate = new RestTemplate();
        String s1= "";
        try {
            log.info("请求地址:{}", ConfigurationCenterUtil.CEP_URL + url);
            s1 = HttpClientHelper.doPostJSON(ConfigurationCenterUtil.CEP_URL + url, hashMap, headers);
            //ResponseEntity<String> responseEntity = restTemplate.postForEntity(ConfigurationCenterUtil.CEP_URL + "/backend/open-api/v1/admin/users/delete", requestEntity, String.class);
            log.info("会员注销返回cep json:{}", s1);
        } catch (RestClientException e) {
            log.info("调用cep注销接口异常", e);
        }
        return s1;
    }

    /**
     * 用户注销
     *
     * @param url
     * @param openId
     * @return
     */
    public String deleteConsumer(String url, String openId) {
        //获取token
        Object token = redisCache.get("cep_token");
        if (null == token) {
            token = getToken();
        }
        log.info("token值:{}", token);
        HashMap<Object, String> hashMap = new HashMap<>();
        hashMap.put("uid", openId);
        hashMap.put("type", "WECHAT_OPEN_ID");
        hashMap.put("operation", "DELETE_ALL");
        Map headers = AuthenticationUtil.methodPostJson(url, token.toString(), hashMap);
        log.info("请求地址:{}", ConfigurationCenterUtil.CEP_URL + url);
        String s1 = HttpClientHelper.doPostJSON(ConfigurationCenterUtil.CEP_URL + url, hashMap, headers);
        log.info("用户注销返回cep json:{}", s1);
        return s1;
    }


 /*   @Override
    public ResponseResult saveAppletSubscription(CepAppletSubscribeDto cepAppletSubscribeDto) {
        //获取token
        Object token = redisCache.get("cep_token");
        Map headers = AuthenticationUtil.methodPostJson("/cre/open-api/v1/subscribe-message/send", token.toString(), JSON.toJSONString(cepAppletSubscribeDto));
       *//* HttpHeaders headers = AuthenticationUtil.methodPostJson("/cre/open-api/v1/subscribe-message/send",token.toString(),JSON.toJSONString(cepAppletSubscribeDto));
        HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(cepAppletSubscribeDto), headers);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(ConfigurationCenterUtil.CEP_URL + "/cre/open-api/v1/subscribe-message/send", requestEntity, String.class);*//*

        if (null == token) {
            token = getToken();
        }

        try {
            String s = HttpClientHelper.doPostJSON(ConfigurationCenterUtil.CEP_URL + "/cre/open-api/v1/subscribe-message/send", cepAppletSubscribeDto, headers);
            ResponseResult<String> results = JSON.parseObject(s, new TypeReference<ResponseResult<String>>() {
            }, Feature.AllowUnQuotedFieldNames);
            return results;
        } catch (RestClientException e) {
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, "调用第三放接口异常");
        }
        // return new ResponseResult(ResponseCodeEnum.SUCCESS, ResponseCodeEnum.SUCCESS.getDesc());
    }*/


    public String getToken() {
        log.info("url:{},username:{},password:{}", ConfigurationCenterUtil.CEP_URL, ConfigurationCenterUtil.CEP_USERNAME, ConfigurationCenterUtil.CEP_PASSWOED);
        HashMap<String, String> map = new HashMap<>();
        map.put("username", ConfigurationCenterUtil.CEP_USERNAME);
        map.put("password", ConfigurationCenterUtil.CEP_PASSWOED);

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(map), headers);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(ConfigurationCenterUtil.CEP_URL + "/backend/open-api/v1/tokens/bearer", requestEntity, String.class);
        String body = responseEntity.getBody();
        log.info("cep-token接口响应body:{}",body);
        JSONObject object = JSONObject.parseObject(body);
        Object data = object.get("data");
        Map<String, String> maps = JSONObject.parseObject(JSONObject.toJSONString(data), Map.class);
        String token = maps.get("token");
        log.info("cep最新token值: {}", token);
        redisCache.put("cep_token", token);
        return token;
    }

    public ResponseResult cepriskRecordUpdate(String body, String url1) {

        //获取token
        Object token = redisCache.get("cep_token");
        //Object token = "7b52fe88332d43e1983825d42c5e3f4e";
        log.info("token值:{}", token);
        if (null == token) {
            token = getToken();
            log.info("重新获取的token值:{}", token);
        }
        //String url2 = "/dev" + url1;
        String sign = AuthenticationUtil.createSign(url1, body);
        log.info("签名获取:{}", sign);
        HttpHeaders header = new HttpHeaders();
        header.add("Content-Type", "application/json");
        header.add("Authorization", token.toString());
        header.add("X-TW-SIGNATURE", sign);
        RestTemplate restTemplate = new RestTemplate(new BufferingClientHttpRequestFactory(new SimpleClientHttpRequestFactory()));
        String utls = ConfigurationCenterUtil.CEP_URL + url1;
        HttpEntity<String> requestEntity = new HttpEntity<>(body, header);
        String url = String.format(utls, 1, 2);
        log.info("请求地址url:{}", url);
        List<ClientHttpRequestInterceptor> interceptors = new ArrayList<>();
        interceptors.add(new LoggingRequestInterceptor());
        restTemplate.setInterceptors(interceptors);
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
        log.info("调用第三方接口返回实体:{}", responseEntity);
        log.info("调用第三方接口返回实体body:{}", responseEntity.getBody());
        ResponseResult<MemberUserDto> results = JSON.parseObject(responseEntity.getBody(), ResponseResult.class);
        return results;

    }


    public CepResponseResult cepRecordUpdate(String body, String url1) {

        //获取token
        Object token = redisCache.get("cep_token");
        //Object token = "7b52fe88332d43e1983825d42c5e3f4e";
        log.info("token值:{}", token);
        if (null == token) {
            token = getToken();
            log.info("重新获取的token值:{}", token);
        }
        String url2 = "/dev" + url1;
        String sign = AuthenticationUtil.createSign(url2, body);
        log.info("签名获取:{}", sign);
        HttpHeaders header = new HttpHeaders();
        header.add("Content-Type", "application/json");
        header.add("Authorization", token.toString());
        header.add("X-TW-SIGNATURE", sign);
        RestTemplate restTemplate = new RestTemplate(new BufferingClientHttpRequestFactory(new SimpleClientHttpRequestFactory()));
        String utls = ConfigurationCenterUtil.CEP_URL + url1;
        HttpEntity<String> requestEntity = new HttpEntity<>(body, header);
        String url = String.format(utls, 1, 2);
        log.info("请求地址url:{}", url);
        List<ClientHttpRequestInterceptor> interceptors = new ArrayList<>();
        interceptors.add(new LoggingRequestInterceptor());
        restTemplate.setInterceptors(interceptors);
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
        log.info("调用第三方接口返回实体:{}", responseEntity);
        log.info("调用第三方接口返回实体body:{}", responseEntity.getBody());
        CepResponseResult results = JSON.parseObject(responseEntity.getBody(), CepResponseResult.class);
        return results;
    }

    /*@Override
    public void saveAppletSubscriptionTask(String id, String processId, String nodeId, String taskId, String tenant) {
        Long start = System.currentTimeMillis();
        final int pageSize = ConfigurationCenterUtil.THREAD_SUBSCRIBED_PAGESIZE;
        String index = "0";
        int completedNum = 0;
        List<Future<Boolean>> futures = new ArrayList<>();
        try {
            while (true) {
                log.info("小程序订阅活动taskId:{},活动Id:{}", taskId);
                List<AppletSubscriberDto> memberCoupons;
                try {
                    memberCoupons = appletSubcriberRepository.getAppletSubscribersBatch(taskId, index, pageSize);
                } catch (Exception e) {
                    log.error("批次查询超时id:{},pageSize:{}; 继续查询...", index, pageSize);
                    continue;
                }
                if (CollectionUtils.isEmpty(memberCoupons)) {
                    break;
                }
                List<AppletSubscriberDto> appletSub = JSON.parseArray(JSON.toJSONString(memberCoupons), AppletSubscriberDto.class);
                //查询订阅模板
                String templateId = appletSub.get(0).getTemplateId();
                CepAppletTemplateDto subscribers = cepAppletTemplaterRepository.getSubscribers(replaceCoup(templateId));
                //记录该批次中最大id值
                index = appletSub.get(appletSub.size() - 1).getId();
                completedNum += appletSub.size();
                SubscribersRunnable sendRunnable = new SubscribersRunnable(index, completedNum, appletSub, subscribers, taskId, tenant);
                Future<Boolean> submit = (Future<Boolean>) executorService.submit(sendRunnable);
                futures.add(submit);
                //executorService.execute(sendRunnable);
                if (appletSub.size() < pageSize) {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("小程序订阅任务异常终止!", e);
        }

        for (Future<Boolean> future : futures) {
            try {
                Boolean b = future.get();
                log.info("小程序订阅任务分发完成total:{}+,分发耗时:{}ms,待子线程执行完毕...", completedNum, System.currentTimeMillis() - start);
            } catch (Exception e) {
                log.error("小程序订阅子线程等待异常终止!", e);
            }
        }
        //全部完成时推送完成
        CallbackDto callbackDto = new CallbackDto();
        callbackDto.setId(id);
        callbackDto.setTaskId(taskId);
        callbackDto.setTenant(tenant);
        callbackDto.setProcessId(processId);
        callbackDto.setNodeId(nodeId);
        callbackDto.setStatus("COMPLETED");
        callbackProducer.sendMsg(callbackDto);
    }*/

    /*class SubscribersRunnable implements Runnable {
        String index;
        int completedNum;
        String taskId;
        String tenant;
        CepAppletTemplateDto subscribers;
        List<AppletSubscriberDto> appletSub;

        private SubscribersRunnable(String index, int completedNum, List<AppletSubscriberDto> appletSub, CepAppletTemplateDto subscribers, String taskId, String tenant) {
            this.index = index;
            this.completedNum = completedNum;
            this.taskId = taskId;
            this.tenant = tenant;
            this.subscribers = subscribers;
            this.appletSub = appletSub;
        }

        @Override
        public void run() {
            try {
                Long start = System.currentTimeMillis();
                sendMemberSubscribersTable(appletSub, subscribers, taskId, tenant);
                log.info("子线程执行completedNum:{},index:{},耗时:{}ms", completedNum, index, System.currentTimeMillis() - start);
            } catch (Exception e) {
                log.info("子线程执行失败completedNum:{},index:{},taskId:{}", completedNum, index, taskId);
            }
        }
    }*/


    /*public void sendMemberSubscribersTable(List<AppletSubscriberDto> appletSub, CepAppletTemplateDto subscribers, String taskId, String tenant) {
        for (AppletSubscriberDto appletSubscriberDto : appletSub) {
            //查询openid
            //String memberId = memberRepository.queryCustomerNoUnioId(appletSubscriberDto.getObjectId());
            String openId = memberRepository.queryOpenId(appletSubscriberDto.getObjectId());
            CepAppletSubscribeDto subscribeDto = new CepAppletSubscribeDto();
            subscribeDto.setToUser(openId);
            subscribeDto.setTemplateId(replaceCoup(appletSubscriberDto.getTemplateId()));
            subscribeDto.setPage(appletSubscriberDto.getLandingPage());

            //判断是平台还是活动
            if ("PLATFORM".equals(appletSubscriberDto.getType())) {
                subscribeDto.setCampaignCode(appletSubscriberDto.getSceneCommen());
            }
            if ("CAMPAIGN".equals(appletSubscriberDto.getType())) {
                subscribeDto.setCampaignCode(replaceCoup(appletSubscriberDto.getSceneCampaign()));
            }
            HashMap<String, String> map = new HashMap<>();
            //map.put("time", DateHelper.getCepDate());
            if (StringUtils.isNotBlank(subscribers.getFieldsKey1())) {
                map.put(subscribers.getFieldsKey1(), appletSubscriberDto.getFields1());
            }
            if (StringUtils.isNotBlank(subscribers.getFieldsKey2())) {
                map.put(subscribers.getFieldsKey2(), appletSubscriberDto.getFields2());
            }
            if (StringUtils.isNotBlank(subscribers.getFieldsKey3())) {
                map.put(subscribers.getFieldsKey3(), appletSubscriberDto.getFields3());
            }
            if (StringUtils.isNotBlank(subscribers.getFieldsKey4())) {
                map.put(subscribers.getFieldsKey4(), appletSubscriberDto.getFields4());
            }
            if (StringUtils.isNotBlank(subscribers.getFieldsKey5())) {
                map.put(subscribers.getFieldsKey5(), appletSubscriberDto.getFields5());
            }
            if (StringUtils.isNotBlank(subscribers.getFieldsKey6())) {
                map.put(subscribers.getFieldsKey6(), appletSubscriberDto.getFields6());
            }
            if (StringUtils.isNotBlank(subscribers.getFieldsKey7())) {
                map.put(subscribers.getFieldsKey7(), appletSubscriberDto.getFields7());
            }
            subscribeDto.setData(map);
            subscribeDto.setMiniProgramState(ConfigurationCenterUtil.CEP_MINIPROGRAM_STATE);
            log.info("封装发送第三方对象:{}", JSON.toJSONString(subscribeDto));
            //调第三cep接口body
            CepResponseResult responseResult = cepMemberCouponService.saveAppletSubscription(ConfigurationCenterUtil.MEMBER_CEP_APPLET, subscribeDto);

            //每次发送推送一个记录
            ReachBehaviorDto reachBehaviorDto = new ReachBehaviorDto();
            Object data = "";
            Map<String, String> jsonMap;
            if (null == responseResult) {
                reachBehaviorDto.setBehavior("Y");
                //更新记录成功
                couponCampaignRepository.updateMemberSubscribe(taskId, appletSubscriberDto.getObjectId(), "Y", "发送成功");
            } else {
                data = responseResult.getError();
                jsonMap = JSONObject.toJavaObject(JSONObject.parseObject(data.toString()), Map.class);
                reachBehaviorDto.setBehavior("N");
                //更新记录成功失败
                couponCampaignRepository.updateMemberSubscribe(taskId, appletSubscriberDto.getObjectId(), "N", jsonMap.get("code") + "_" + jsonMap.get("message"));
            }
            reachBehaviorDto.setTaskId(taskId);
            reachBehaviorDto.setTenant(tenant);
            reachBehaviorDto.setReachId(appletSubscriberDto.getObjectId());
            reachBehaviorDto.setOccId(appletSubscriberDto.getOccId());
            behaviorProducer.sendMsg(reachBehaviorDto);
        }
    }*/

    public String replaceCoup(String res) {
        String s = res.replace("[", "");
        String rep = s.replace("]", "");
        String replace = rep.replace("\"", "");
        return replace;
    }

}
