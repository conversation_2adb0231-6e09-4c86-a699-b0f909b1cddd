package com.shuyun.kylin.customized.base.feign.dao;

import lombok.Data;

import java.util.Map;

/**
 * 卡劵 核销 (使用)feign dto
 */
@Data
public class OfferInstanceGrantClientRequestDto {
    /**
     * 品牌（必填）
     */
    private String brand;

    /**
     * 渠道类型（必填）
     */
    private String channelType;

    /**
     * 事务ID（必填）
     */
    private String transactionId;

    /**
     * 业务发起人
     */
    private String businessInitiator;

    /**
     * 会员发放标识（必填）
     */
    private MemberGrantIdentify memberGrantIdentify;



    @Data
    public static class MemberGrantIdentify {
        /**
         *
         * 会员标识（必填） 会员ID或渠道ID二者选其一,建议传入渠道ID;优先级:渠道ID > 会员ID
         */
        private Identify identify;

        /**
         * 二选一（必填）
         */
        @Data
        public static class Identify {
            private String memberId;
            private String customerNo;
        }

        /**
         * 发放数量（必填）
         */
        private Integer num;
    }

    /**
     * 项目id（必填）
     */
    private String projectId;
    /**
     * 发放原因
     */
    private String grantReason;

    /**
     * 发放门店(店铺)
     */
    private String grantShop;

    /**
     * 发放渠道
     */
    private String grantPlatform;

    /**
     * 来源模块
     */
    private String sourceModule;

    /**
     * 营销节点id
     */
    private String marketingNodeId;

    /**
     * 营销活动id
     */
    private String marketingActivityId;

    private Extension extension;

    @Data
    public static class Extension {
        private String govId;
        private String govIdType;
        private String guestName;
        private String mobile;
        private String member;
        /**
         * 发放券对应的lbs省
         */
        private String sendLBSProvince;
        /**
         * 发放券对应的lbs市
         */
        private String sendLBSCity;
        /**
         * 发放券对应的lbs区
         */
        private String sendLBSDistrict;
        /**
         * 发放券对应的成本中心
         * 说明：该字段由以上三个字段查询成本中心模型匹配得到
         */
        private String sendCostCenterCode;
        /**
         * 外部编码/三券码（当模板类型为三方券码时传入该值）
         * @see com.shuyun.kylin.customized.project.strategy.router.SendCouponEnum#THREE_PARTIES
         */
        private String externalCode;

        /**
         * 来源活动id
         */
        private String campaignId;
        /**
         * 来源活动名称
         */
        private String campaignName;

        /**
         * 积分流水id (例：在参与抽奖活动中扣减积分的幂等id)
         */
        private String businessId;

        /**
         * 使用方式（兑换类型）
         */
        private String redeemType;
        /**
         * 跳转参数
         */
        private String jumpParameter;
        /**
         * 外部项目id
         */
        private String externalProjectId;

        private String utmSource;

        private String utmMedia;
    }
}
