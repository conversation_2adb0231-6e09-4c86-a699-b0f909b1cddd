package com.shuyun.kylin.customized.project.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 汉堡王券核销或者反核销回调实体
 */
@Data
public class BKInstanceUseOrUnUseRequest {

    /**
     * BK回调事件类型 VERIFY:核销事件 ；  REVOKE:撤销核销事件
     */
    @JsonProperty("event_type")
   private String eventType;

    /**
     * 核销数据
     */
    @JsonProperty("verify_data")
    private VerifyData verifyData;

    @Data
    public static class VerifyData {
        /**
         * 请求单号,32位不可重复（通过该字段实现幂等保证）
         */
        @JsonProperty("verify_request_sn")
        private String verifyRequestSn;

        /**
         * 券码
         */
        @JsonProperty("ticket_code")
        private String ticketCode;

         /**
         * 券批次号
         */
         @JsonProperty("ticket_id")
        private String ticketId;

         /**
         * 核销门店编号
         */
         @JsonProperty("verify_store_sn")
        private String verifyStoreSn;

         /**
         * 核销时间(格式: YYYY-MM-DD HH:mm:ss 例如： 2024-04-15 18:11:10)
         */
         @JsonProperty("verify_time")
        private String verifyTime;

    }

    /**
     * 反核销数据
     */
    @JsonProperty("revoke_data")
    private RevokeData revokeData;

    @Data
    public static class RevokeData {
        /**
         * 请求单号,32位不可重复（通过该字段实现幂等保证）
         */
        @JsonProperty("verify_request_sn")
        private String verifyRequestSn;

        /**
         * 券码
         */
        @JsonProperty("ticket_code")
        private String ticketCode;

        /**
         * 券批次号
         */
        @JsonProperty("ticket_id")
        private String ticketId;

        /**
         * 反核销时间(格式: YYYY-MM-DD HH:mm:ss 例如： 2024-04-15 18:11:10)
         */
        @JsonProperty("revoke_time")
        private String revokeTime;

    }

}
