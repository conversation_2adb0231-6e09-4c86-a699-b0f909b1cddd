package com.shuyun.kylin.customized.base.feign.client;

import com.shuyun.kylin.customized.base.feign.dao.ChecklistDao;
import com.shuyun.kylin.customized.base.feign.dao.ChecklistDto;
import com.shuyun.kylin.customized.base.feign.dao.MemberPointModifyRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = "memberChecklistClient")
@RequestMapping("/risk-control/v1")
public interface MemberChecklistClient {

    @PutMapping("/api/checklist")
    String memberChenklist(@RequestBody ChecklistDto checklistDto);

    @GetMapping("/api/checklist")
    ChecklistDao getMemberChenklist(@RequestParam String checklistType,
                                    @RequestParam(required = false) String fqn,
                                    @RequestParam(required = false) String groupIds,
                                    @RequestParam String customer);

    @DeleteMapping("/api/checklist")
    String deleteMemberChenklist(@RequestBody ChecklistDto checklistDto);
}
