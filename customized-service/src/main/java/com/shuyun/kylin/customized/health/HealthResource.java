package com.shuyun.kylin.customized.health;

import com.shuyun.cdp.tags.response.OriginTagsResponse;
import com.shuyun.kylin.customized.customer.dto.CommonTagSingleResponse;
import com.shuyun.kylin.customized.customer.service.CustomerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "运维接口")
@RestController
@RequestMapping("/health")
class HealthResource {

    @Autowired
    CustomerService customerService;

    @Operation(summary = "健康检查")
    @GetMapping
    public String healthCheck() {
        return "ping";
    }

    /**
     * 手动提交CDP标签人群包生成任务
     * @return
     */
    @GetMapping(value = "/tagTask")
    public CommonTagSingleResponse submitCdpTagTask() {
        return customerService.submitCdpTagTask();
    }

}
