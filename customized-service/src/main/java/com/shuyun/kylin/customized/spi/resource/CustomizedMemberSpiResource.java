package com.shuyun.kylin.customized.spi.resource;

import com.shuyun.kylin.crm.openapi.core.dto.common.UpdateMemberDto;
import com.shuyun.kylin.crm.openapi.core.dto.rules.MemberIdQueryRequest;
import com.shuyun.kylin.crm.openapi.core.dto.rules.ModifyMemberIdentifyDto;
import com.shuyun.kylin.crm.openapi.spi.IMemberService;
import com.shuyun.pip.component.json.JsonUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Description 用于定制化 Openapi 会员识别策略 自定义策略逻辑
 * 前提:  /customized-service/customized-service.openapi.spi.enabled=true
 * @date 2021/2/1
 */
@Tag(name = "定制会员策略接口", description = "用于定制化 Openapi 会员策略 自定义策略逻辑")
@RestController
@RequestMapping("/openapi")
@ConditionalOnExpression("${customized-service.openapi.spi.enabled}")
public class CustomizedMemberSpiResource implements IMemberService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 查询会员id
     */
    @Override
    @PostMapping("/queryMemberId")
    public String queryMemberId(@Valid @RequestBody MemberIdQueryRequest request) {
        logger.info("接收到来自于openapi的识别会员id请求，参数:{}", JsonUtils.toJson(request));
        //TODO
        return null;
    }

    /**
     * 更新全渠道会员识别记录 -> 会员绑定时 需要更新会员识别记录即MemberIdentify
     */
    @Override
    @PostMapping("/modifyMemberIdentify")
    public void modifyMemberIdentify(@Valid @RequestBody ModifyMemberIdentifyDto request) {
        logger.info("接收到来自于openapi的更新全渠道会员识别记录请求，参数:{}", JsonUtils.toJson(request));
        //TODO
    }

    /**
     * 会员解绑 扩展点->解绑会员识别记录
     */
    @Override
    @PostMapping("/unbindMemberIdentify")
    public void unbindMemberIdentify(@NotNull @RequestParam(value = "memberType") String memberType,
                                     @NotNull @RequestParam(value = "channelType") String channelType,
                                     @NotNull @RequestParam(value = "memberId") String memberId) {
        logger.info("接收到来自于openapi的更新全渠道会员识别记录请求，品牌={},渠道={},会员id={},", memberType, channelType, memberId);
        //TODO
    }

    /**
     * 更新全渠道会员信息
     */
    @Override
    @PostMapping("/modifyCommonMember")
    public void modifyCommonMember(@Valid @RequestBody UpdateMemberDto request) {
        logger.info("接收到来自于openapi的更新全渠道会员信息请求，参数:{}", JsonUtils.toJson(request));
        //TODO
    }


}
