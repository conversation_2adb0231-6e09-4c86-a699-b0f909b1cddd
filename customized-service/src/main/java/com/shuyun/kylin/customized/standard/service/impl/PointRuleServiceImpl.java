package com.shuyun.kylin.customized.standard.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.dm.api.response.DMLResponse;
import com.shuyun.kylin.crm.openapi.core.dto.common.PointRequestDto;
import com.shuyun.kylin.crm.openapi.core.dto.customer.CustomerQueryDto;
import com.shuyun.kylin.crm.openapi.core.dto.customer.SimpleMemberDto;
import com.shuyun.kylin.crm.openapi.sdk.client.common.IMemberClient;
import com.shuyun.kylin.crm.openapi.sdk.client.customer.ICustomerClient;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.enums.ChannelTypeEnum;
import com.shuyun.kylin.customized.base.enums.ResponseCodeEnum;
import com.shuyun.kylin.customized.base.exception.CustomizeException;
import com.shuyun.kylin.customized.base.feign.client.MemberPointModifyClient;
import com.shuyun.kylin.customized.base.feign.dao.MemberPointModifyRequest;
import com.shuyun.kylin.customized.base.util.*;
import com.shuyun.kylin.customized.behavior.resource.InteractionBehaviorResource;
import com.shuyun.kylin.customized.behavior.resource.po.CheckPO;
import com.shuyun.kylin.customized.behavior.resource.vo.CheckResponse;
import com.shuyun.kylin.customized.behavior.resource.vo.CommonApiResponse;
import com.shuyun.kylin.customized.member.dm.*;
import com.shuyun.kylin.customized.member.dto.*;
import com.shuyun.kylin.customized.member.service.ICepDataSyncService;
import com.shuyun.kylin.customized.member.service.ICepMemberService;
import com.shuyun.kylin.customized.standard.service.PointRuleService;
import com.shuyun.kylin.starter.redis.ICache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

@Slf4j
@Service
public class PointRuleServiceImpl implements PointRuleService {

    @Autowired
    private MemberRepository memberRepository;

    @Autowired
    private ICepMemberService iCepMemberService;

    @Autowired
    public MemberPointModifyClient memberPointModifyClient;

    @Autowired
    private CepRegulationPointRepository cepRegulationPointRepository;

    @Autowired
    private CepCostCenterRepository cepCostCenterRepository;

    @Autowired
    private CepMemberScenBindingRepository cepMemberScenBindingRepository;

    @Autowired
    private InteractionBehaviorResource interactionBehaviorResource;

    @Autowired
    private ICustomerClient customerClient;


    @Autowired
    @Qualifier("redisCache")
    private ICache redisCache;

    final static String REDIS_BNSID = "bns_key";


    /**
     * 活动变更积分
     *
     * @param cepMemberDeductionDto
     * @return
     */
    @Override
    public ResponseResult updateRuleMemberPoint(CepMemberDeductionDto cepMemberDeductionDto) {
        //customerNo和memberId二选一
        if (StringUtils.isBlank(cepMemberDeductionDto.getMemberId()) && StringUtils.isBlank(cepMemberDeductionDto.getCustomerNo())) {
            return new ResponseResult(ResponseCodeEnum.MEMBERID_OR_CUSTOMERNO_NOT_NULL);
        }
        //扣减取消   hashMap返回值的封装
        Map<Object, Object> hashMap = new HashMap<>();
        //查询该笔积分是否已发送
        /*if (!"扣减取消".equals(cepMemberDeductionDto.getChangeSource())) {
            Double changePoints = memberRepository.queryMemberTraceId(cepMemberDeductionDto.getBusinessId());
            if (0.0 != changePoints) {
                hashMap.put("changePoint", changePoints);
                return new ResponseResult(ResponseCodeEnum.SUCCESS, hashMap);
            }
        }*/
        if (!"扣减取消".equals(cepMemberDeductionDto.getChangeSource())) {
            String key = REDIS_BNSID + cepMemberDeductionDto.getBusinessId();
            Object reValue = redisCache.get(key);
            if (null != reValue) {
                log.info("redis查询该笔积分是否已发送key:{}，reValue：{}",key,reValue);
                hashMap.put("changePoint", reValue);
                return new ResponseResult(ResponseCodeEnum.SUCCESS, hashMap);
            }
        }
        String  memberId = null;
        //如果渠道为KO_MP时memberId 必传
        if (ChannelTypeEnum.KO_MP.getValue().equals(cepMemberDeductionDto.getChannelType())) {
            if (StringUtils.isBlank(cepMemberDeductionDto.getMemberId())) {
                return new ResponseResult(ResponseCodeEnum.MEMBERID_NOT_NULL);
            } else {
                //如果会员id不为空，根据会员id查询会员信息
                memberId = cepMemberDeductionDto.getMemberId();
            }
        }else {
            memberId = cepMemberDeductionDto.getMemberId();
        }

        if (StringUtils.isNotBlank(memberId)){
            log.info("开始根据会员id查询会员是否存在:{}",memberId);
            Boolean memberIsExist = memberRepository.memberIsExist(memberId);
            if (!memberIsExist) {
                log.info("根据会员id查询会员不存在:{}",memberId);
                return new ResponseResult(ResponseCodeEnum.MEMBER_FAILED);
            }else{
                memberId = cepMemberDeductionDto.getMemberId();
            }
        }

        //查询会员
        if (StringUtils.isNotBlank(cepMemberDeductionDto.getCustomerNo())) {
            log.info("开始根据客户id查询会员是否存在:{}",cepMemberDeductionDto.getMemberId());
            memberId = memberRepository.queryMemberId(cepMemberDeductionDto.getCustomerNo());
            if (StringUtils.isBlank(memberId)) {
                log.info("根据客户id查询会员不存在:{}",cepMemberDeductionDto.getCustomerNo());
                return new ResponseResult(ResponseCodeEnum.MEMBER_FAILED);
            }
        }

        if (cepMemberDeductionDto.getScene().equals("REGISTER") || cepMemberDeductionDto.getScene().equals("COMPLETE_INFO") || cepMemberDeductionDto.getScene().equals("CHECK_IN") || cepMemberDeductionDto.getScene().equals("INVITE_REGISTER")){
            CheckPO po = new CheckPO();
            po.setScene(cepMemberDeductionDto.getScene());
            po.setMembershipId(cepMemberDeductionDto.getCustomerNo());
            po.setChannelType(cepMemberDeductionDto.getChannelType());
            CommonApiResponse<CheckResponse> check = interactionBehaviorResource.check(po);
            if (null != check.getData()){
                hashMap.put("changePoint", check.getData().getTransactionPoint());
            }
            return new ResponseResult(check.getErrorCode(),check.getMsg(), hashMap);
        }
        //查询场景活动
        CepRegulationPointDto activityTheme = cepRegulationPointRepository.getActivityTheme(cepMemberDeductionDto.getScene());
        log.info("查询场景活动:{}",JSON.toJSONString(activityTheme));
        //判断活动是否存在
        if (null == activityTheme) {
            return new ResponseResult(ResponseCodeEnum.MEMBER_THEME);
        }
        PointRequestDto requestDto = new PointRequestDto();
        MemberPointModifyRequest memberPointDto = new MemberPointModifyRequest();
        //默认使用活动规则成本中心
        memberPointDto.setShopId(activityTheme.getCostCenterCode());
        requestDto.setShopCode(activityTheme.getCostCenterCode());

        //根据lbs查询成本中心
        if ("Y".equals(activityTheme.getIsLBSCostCenterCode())){
            if (StringUtils.isNotBlank(cepMemberDeductionDto.getSendLBSCity())){
                String costCenter = cepCostCenterRepository.getLbsCostCenter(cepMemberDeductionDto.getSendLBSProvince(),cepMemberDeductionDto.getSendLBSCity(),cepMemberDeductionDto.getSendLBSDistrict());
                Map<String, Object> paramsMap = new HashMap<>();
                paramsMap.put("traceId",cepMemberDeductionDto.getBusinessId());
                paramsMap.put("memberId",memberId);
                paramsMap.put("sendLBSProvince",cepMemberDeductionDto.getSendLBSProvince());
                paramsMap.put("sendLBSCity",cepMemberDeductionDto.getSendLBSCity());
                paramsMap.put("sendLBSDistrict",cepMemberDeductionDto.getSendLBSDistrict());
                paramsMap.put("creatAt", ZonedDateTime.now());
                DMLResponse response = DataApiUtil.upsertIsData(ModelConstants.POINT_LBS_LOG, cepMemberDeductionDto.getBusinessId(), paramsMap);
                if (!response.getIsSuccess()) {
                    log.error("积分lbs记录:{}", response.getOperation());
                }
                if (StringUtils.isNotBlank(costCenter)){
                    requestDto.setShopCode(costCenter);
                    memberPointDto.setShopId(costCenter);
                }
            }
        }
        //封装实体
        memberPointDto.setKZZD1(cepMemberDeductionDto.getChangeSourceDetail());
        memberPointDto.setKZZD2(cepMemberDeductionDto.getChangeSource());
        memberPointDto.setKZZD3(cepMemberDeductionDto.getCostTracing());
        memberPointDto.setChangeMode("INTERFACE");
        memberPointDto.setChannelType(cepMemberDeductionDto.getChannelType());
        memberPointDto.setDesc(cepMemberDeductionDto.getScene() + "#" + activityTheme.getTask());
        memberPointDto.setMemberId(memberId);
        //log.info("忠诚度积分账号类型id:{}", ConfigurationCenterUtil.ACCOUNT_POINT);
        memberPointDto.setPointAccountId(Integer.valueOf(ConfigurationCenterUtil.ACCOUNT_POINT));
        memberPointDto.setIdempotentMode(1);
        try {
            if ("扣减取消".equals(cepMemberDeductionDto.getChangeSource())) {
                memberPointDto.setRecordType("DEDUCT");
                memberPointDto.setTradeId(cepMemberDeductionDto.getBusinessId());
                memberPointDto.setTriggerId(UUID.randomUUID().toString().replaceAll("-", ""));
                log.info("封装的实体: {}", memberPointDto);
                CustomizeException map = memberPointModifyClient.memberReversePoint(memberPointDto);
                log.info("扣减取消忠诚度返回值: {}", JSON.toJSONString(map));
                if (!Objects.isNull(map)) {
                    return new ResponseResult(map.getError_code(), map.getMsg());
                }
                //更新会员活动次数记录
                cepMemberScenBindingRepository.deleteActivity(cepMemberDeductionDto.getBusinessId());
                hashMap.put("changePoint", activityTheme.getSendOrDeductValuePerTime());
                return new ResponseResult(ResponseCodeEnum.SUCCESS, hashMap);
            }
        } catch (Exception e) {
            if ("未找到正向操作".equals(e.getMessage())) {
                return new ResponseResult(ResponseCodeEnum.MEMBER_POINT_ERROR);
            }
            if ("正向积分已经被使用".equals(e.getMessage())) {
                return new ResponseResult(ResponseCodeEnum.MEMBER_POINT_USED);
            }
            log.error("规则场景变更会员积分(瓶子)memberId:{},e:{}", memberId, e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED.getCode(),e.getMessage());
        }
        //判断活动是否过期
        boolean effectiveDate = DateUtil.isEffectiveDate(activityTheme.getStartTime(), activityTheme.getEndTime());
        if (!effectiveDate) {
            return new ResponseResult(ResponseCodeEnum.TIEM_RANGE);
        }
        if (activityTheme.getSendTimeLimitPerDay() != null) {
            //查询活动每天次数
            String dayTime = cepMemberScenBindingRepository.getDayTime(memberId, cepMemberDeductionDto.getScene());
            log.info("查询活动每天次数:{},:{}",dayTime,Integer.parseInt(dayTime));
            if (Integer.parseInt(dayTime) == activityTheme.getSendTimeLimitPerDay()) {
                return new ResponseResult(ResponseCodeEnum.MEMBER_DAY_LIMITS);
            }
        }
        if (activityTheme.getSendTimeLimitPerMonth() != null) {
            //查询活动每月次数
            String monthTime = cepMemberScenBindingRepository.getMonthTime(memberId, cepMemberDeductionDto.getScene());
            if (Integer.parseInt(monthTime) == activityTheme.getSendTimeLimitPerMonth()) {
                return new ResponseResult(ResponseCodeEnum.MEMBER_MONTH_LIMITS);
            }
        }
        if (activityTheme.getSendTimeTotalLimit() != null) {
            //查询活动上线次数
            String maximumTime = cepMemberScenBindingRepository.getMaximumTime(memberId, cepMemberDeductionDto.getScene());
            if (Integer.parseInt(maximumTime) == activityTheme.getSendTimeTotalLimit()) {
                return new ResponseResult(ResponseCodeEnum.MEMBER_FREQUENCY_LIMITS);
            }
        }
        //封装积分变更实体
        requestDto.setMemberId(memberId);
        //积分的值
        double point = activityTheme.getSendOrDeductValuePerTime();
        if (activityTheme.getIsTransactionRelated()) {
            double k = activityTheme.getSendOrDeductValuePerTime() / 3;
            point = new BigDecimal(k).setScale(1, BigDecimal.ROUND_UP).doubleValue();
            requestDto.setPoint(point);
            memberPointDto.setPoint(point);
        } else {
            requestDto.setPoint(activityTheme.getSendOrDeductValuePerTime());
            memberPointDto.setPoint(activityTheme.getSendOrDeductValuePerTime());
        }
        //判断积分生效时间
        if ("NEXT_DAY".equals(activityTheme.getSendPointTimeliness())) {
            requestDto.setEffectTime(DateHelper.getDateTime());
            memberPointDto.setEffectDate((DateHelper.changeShanghaiToUTC(DateHelper.getTomorrowTime())));
        }
        requestDto.setExpiredTime(DateHelper.getLastDayOfMonth());

        try {
            if ("DEDUCT_POINT".equals(activityTheme.getRuleType())) {
                requestDto.setChangeType("DEDUCT");
                memberPointDto.setRecordType("DEDUCT");
                memberPointDto.setOverdueDate(DateHelper.changeShanghaiToUTC(DateHelper.getDayOfMonth()));
                memberPointDto.setTriggerId(cepMemberDeductionDto.getBusinessId());
                log.info("封装的实体: {}", memberPointDto);
                memberPointModifyClient.memberModifyPoint(memberPointDto);
                //更新会员活动次数记录
                updateMemberActivityNum(cepMemberDeductionDto, memberId);
                hashMap.put("changePoint", Double.valueOf("-" + point));
                return new ResponseResult(ResponseCodeEnum.SUCCESS, hashMap);
            }
        } catch (Exception e) {
            if ("此X-Business-Token已执行".equals(e.getMessage())) {
                hashMap.put("changePoint", Double.valueOf("-" + point));
                return new ResponseResult(ResponseCodeEnum.SUCCESS_IDEMPOTENT, hashMap);
            }
            if ("扣减失败，可用余额不足".equals(e.getMessage())) {
                return new ResponseResult(ResponseCodeEnum.MEMBER_POINT);
            }
            log.error("规则场景变更会员积分(瓶子)BusinessId:{},msg:{}", cepMemberDeductionDto.getBusinessId(), e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED.getCode(),e.getMessage());
        }
        requestDto.setDescription(cepMemberDeductionDto.getScene() + "#" + activityTheme.getTask());
        requestDto.setChannelType(cepMemberDeductionDto.getChannelType());
        requestDto.setMemberType(cepMemberDeductionDto.getMemberType());
        requestDto.setKZZD1(cepMemberDeductionDto.getChangeSourceDetail());
        requestDto.setKZZD2(cepMemberDeductionDto.getChangeSource());
        requestDto.setKZZD3(cepMemberDeductionDto.getCostTracing());

        //积分发放类型
        if ("SEND_POINT".equals(activityTheme.getRuleType())) {
            String lockKey = ChannelTypeEnum.KO_MP.getValue() + requestDto.getMemberId();
            log.info("初始化分布式锁:{}", lockKey);
            Lock lock = redisCache.getLock(lockKey);
            if (lock.tryLock()) {
                try {
                    requestDto.setChangeType("SEND");
                    ResponseResult result = iCepMemberService.updatePoint(requestDto, cepMemberDeductionDto.getBusinessId(), cepMemberDeductionDto.getScene(),cepMemberDeductionDto.getChannelType());
                    //更新会员活动次数记录
                    log.info("积分发放会员memberId:{},data:{}", memberId, JSON.toJSON(result));
                    if ("200".equals(result.getCode())) {
                        redisCache.put(REDIS_BNSID+cepMemberDeductionDto.getBusinessId(), requestDto.getPoint(), 1, TimeUnit.DAYS);
                        updateMemberActivityNum(cepMemberDeductionDto, memberId);
                    }
                    return result;
                } catch (Exception e) {
                    log.error("规则场景变更会员积分(瓶子)BusinessId:{},msg:{}",cepMemberDeductionDto.getBusinessId(), e.getMessage());
                    return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, e.getMessage());
                } finally {
                    //释放锁
                    lock.unlock();
                }
            }else {
                log.warn("规则场景变更会员积分抢锁失败:{}", JSON.toJSONString(cepMemberDeductionDto));
                return new ResponseResult(ResponseCodeEnum.MEMBER_POINT_LOKE);
            }
        }
        return new ResponseResult(ResponseCodeEnum.ERROR, "积分变更类型ruleType有误：" + activityTheme.getRuleType());
    }

    public static ResponseResult updateMemberActivityNum(CepMemberDeductionDto cepMemberDeductionDto, String memberId) {
        CepMemberSceneBindingLogDto pointDto = new CepMemberSceneBindingLogDto();
        pointDto.setScene(cepMemberDeductionDto.getScene());
        pointDto.setCustomerNo(cepMemberDeductionDto.getCustomerNo());
        pointDto.setLastSync(DateHelper.getDateDay());
        pointDto.setMemberId(memberId);
        pointDto.setBusinessId(cepMemberDeductionDto.getBusinessId());
        pointDto.setId(cepMemberDeductionDto.getBusinessId());
        DMLResponse response = DataApiUtil.upsertIsData(ModelConstants.CAMPAIGN_SCENE_BINDINGLOG, cepMemberDeductionDto.getBusinessId(), JSONObject.parseObject(JSON.toJSONString(pointDto)));
        if (!response.getIsSuccess()) {
            log.error("更新会员活动次数记录:{}", response.getOperation());
            return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT.getCode(), response.getOperation());
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }
}
