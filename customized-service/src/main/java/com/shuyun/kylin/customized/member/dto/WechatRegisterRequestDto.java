package com.shuyun.kylin.customized.member.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

@Data
public class WechatRegisterRequestDto {

//    @NotBlank(message = "入参Mobile手机号码不能为空")
    private String mobile;
//    @NotBlank(message = "入参Gender性别不能为空")
    private String gender;
    private String openId;
    private String appId;
    private String unionId;
//    @NotBlank(message = "入参ChannelType渠道类型不能为空")
    private String channelType;
    private String appType;
    private String memberType;
    private String registerTime;
    private String nickname;
    private String headImgUrl;
    private String memberName;
    private String birthDay;
    private String birthYear;
    private String email;
    private String city;
    private String ouCode;
    private String ouName;
    private String updateTime;
    private Map customizedProperties;
    private Map bindingExtProperties;
    private String originLbsCity;
    private String originLbsProvince;
    private String originLbsDistrict;
    private String agreementVersion;
    //联合会员AppId
    private String externalAppId;
    //联合会员OpenId
    private String externalOpenId;
    //联合会员渠道LS_MP：罗森小程序
    private String externalChannelType;
    private Boolean isFirstBinding;
    //会员id 用于联合会员注册标识
    private String memberId;
}
