package com.shuyun.kylin.customized.rpc.template;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shuyun.kylin.customized.base.enums.ResponseCodeEnum;
import com.shuyun.kylin.customized.base.util.ApiSignUtils;
import com.shuyun.kylin.customized.project.strategy.router.SendCouponEnum;
import com.shuyun.kylin.customized.rpc.dto.WxSendCouponRequestDto;
import com.shuyun.kylin.customized.rpc.dto.WxSendCouponResponseDto;
import com.shuyun.kylin.customized.rpc.entity.WxCouponConfigEntity;
import com.shuyun.kylin.customized.rpc.template.base.KoRequestTemplate;
import com.shuyun.motor.common.cons.PropsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 微信代金券请求模板
 *
 * @author: Jingwei
 * @date: 2024-12-14
 */
@Slf4j
public class WxRequestTemplate extends KoRequestTemplate {

    static final WxCouponConfigEntity wxCouponConfig = JSON.parseObject(PropsUtil.getSysOrEnv("ko.wx.api.config"), WxCouponConfigEntity.class);

    private static final String ZERX_MSY = "调用第三方接口失败";

    /**
     * 发送代金券
     *
     * @param requestDto
     * @param openId
     * @param transactionId
     * @param stockMchid 发券商户号
     * @return
     */
    public static WxSendCouponResponseDto sendCoupon(WxSendCouponRequestDto requestDto, String openId, String stockMchid, String transactionId) {
        log.info("代金券业务，开始请求obj，入参为，requestDto={},openId={},transactionId={}", JSON.toJSONString(requestDto), openId, transactionId);
        String requestUrl = getRequestUrl(openId, stockMchid);
        WxSendCouponResponseDto responseDto = new WxSendCouponResponseDto();
        try {
            String body = HttpUtil.createPost(requestUrl)
                    .body(JSON.toJSONString(requestDto))
                    .execute().body();
            log.info("请求微信代金券接口返回结果={}", body);
            pushKoRpcRequestLog(requestUrl, requestDto, body, transactionId, SendCouponEnum.WX.getCode());
            if(Objects.isNull(body) || body.isEmpty()) {
                responseDto.setCode(ResponseCodeEnum.GRANT_COUPON_FAILED.getCode());
                responseDto.setMessage(ZERX_MSY);
                return responseDto;
            }
            responseDto = JSON.parseObject(body, WxSendCouponResponseDto.class);
            if(ObjectUtil.isEmpty(responseDto.getCouponId())) {
                if (StringUtils.isEmpty(responseDto.getCode())){
                    responseDto.setCode(ResponseCodeEnum.GRANT_COUPON_FAILED.getCode());
                    responseDto.setMessage(ZERX_MSY);
                } else {
                    switch (responseDto.getCode()) {
                        case "NOT_ENOUGH":
                        case "INVALID_REQUEST":
                            responseDto.setCode("30001");
                            responseDto.setMessage("活动已结束");
                            break;
                        case "RULE_LIMIT":
                        case "USER_ACCOUNT_ABNORMAL":
                        case "REQUEST_BLOCKED":
                            responseDto.setCode("30001");
                            responseDto.setMessage("用户已达最大领取次数");
                            break;
                        default:
                            responseDto.setCode(ResponseCodeEnum.GRANT_COUPON_FAILED.getCode());
                            responseDto.setMessage(ZERX_MSY);
                    }
                }
                log.error("微信发券返回结果code异常，code={}", JSON.toJSONString(responseDto));
                return responseDto;
            }
            return responseDto;
        } catch (Exception e) {
            log.error("代金券业务，请求微信代金券接口异常", e);
            pushKoRpcRequestLog(requestUrl, requestDto, e.getMessage(), transactionId, SendCouponEnum.WX.getCode());
            responseDto.setCode(ResponseCodeEnum.GRANT_COUPON_FAILED.getCode());
            responseDto.setMessage(ZERX_MSY);
            return responseDto;
        }
    }


    public static String getTokenEncryptTokenParams(String mchId) {
        Map<String, String> tokenMap = new HashMap<>();
        tokenMap.put("mchId", mchId);
        tokenMap.put("subMchId", mchId);
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            String tokenBase64 = new String(Base64.getEncoder().withoutPadding().encode(objectMapper.writeValueAsBytes(tokenMap)), StandardCharsets.UTF_8);
            log.info("微信代金券tokenBase64={}", tokenBase64);
            return tokenBase64;
        } catch (JsonProcessingException e) {
            log.error("微信代金券token参数加密失败，", e);
            throw new RuntimeException("微信代金券token参数加密失败");
        }
    }

    /**
     * 获取代金券请求地址
     * @param openId 用户的openId（即发给谁）
     * @return
     */
    public static String getRequestUrl(String openId, String stockMchid) {
        log.info("代金券业务，获取代金券请求地址，openId={}", openId);
        if(ObjectUtil.isEmpty(openId)) {
            throw new RuntimeException("微信代金券发放，openId为空");
        }
        Map<String, String> queryParam = new HashMap<>();
        queryParam.put("accesstoken", getTokenEncryptTokenParams(stockMchid));
        String url;
        try {
            url = ApiSignUtils.buildUrl(wxCouponConfig.getObjBaseUrl().concat(String.format("/v3/marketing/favor/users/%s/coupons", openId)),
                    queryParam, wxCouponConfig.getAppId(), wxCouponConfig.getAppSecret());
        } catch (IOException e) {
            log.error("微信代金券请求url获取失败，", e);
            throw new RuntimeException("微信代金券请求url获取失败");
        }
        return url;
    }
}
