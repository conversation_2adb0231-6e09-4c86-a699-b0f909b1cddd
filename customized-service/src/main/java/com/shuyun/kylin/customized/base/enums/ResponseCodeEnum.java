package com.shuyun.kylin.customized.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/12/11
 */
@Getter
@AllArgsConstructor
public enum ResponseCodeEnum {

    PROCESSING("102", "请求正在处理"),
    SUCCESS("200", "成功"),
    SUCCESS_IDEMPOTENT("200", "成功"),
    FAILED("500", "未知错误"),
    ERROR("400", "未知错误"),
    BATCH_FAILED("522", "批量未知错误"),
    RECORD_NOT_FOUND("10001", "没有找到对应记录"),
    BEAN_CONVERT_FAILED("10002", "对象转换失败"),
    ILLEGAL_ARGUMENT("10003", "非法参数"),
    SQL_INJECT_RISK("30033","有sql注入风险"),
    BLACK_LIST("30034","用户校验未通过"),
    OPENAPI_FAILED("500", "服务接口异常"),
    MEMBER_FAILED("30036", "会员不存在"),
    MEMBER_POINT("30038", "会员积分不足"),
    MEMBER_ACTIVITY("30037", "会员活动达到上限"),
    MEMBER_THEME("30039", "会员活动主题不存在"),
    TIEM_RANGE("30040", "不在活动时间范围内"),
    MEMBER_DAY_LIMITS("30041", "会员活动次数当天达到上限"),
    MEMBER_MONTH_LIMITS("30042", "会员活动次数当月达到上限"),
    MEMBER_FREQUENCY_LIMITS("30043", "会员活动次数达到上限"),
    MEMBER_POINT_IDEMPOTENT("30044", "幂等检查已经处理"),
    MEMBER_POINT_ERROR("30045", "未找到正向操作"),
    MEMBER_POINT_USED("30046", "正向积分已经被使用"),
    MEMBER_COStCENTER("30047", "成本中心不存在"),
    MEMBER_BINDING_EXIST("30048", "会员绑定信息已存在"),
    MEMBER_POINT_OVER_THRESHOLD("30049", "会员变更积分超过最大阈值"),
    STARTIME_POINT_RULE("500", ": 活动开始要大于规则的结束时间"),
    ENDTIME_POINT_RULE("500", ": 活动结束时间要大于规则的开始时间"),
    RULE_POINT_RULE("500", "规则开始时间要大于结束时间"),
    MEMBER_POINT_LOKE("500", "限流处理"),
    MEMBER_ISVAIID_AGE("500", "会员的生日要在14岁至99岁之间"),
    MEMBER_POINT_LIMIT("30050", "会员获取积分达到上限"),
    POINT_NOT_ENOUGH("30051", "会员积分不足"),
    GRANT_COUPON_FAILED("30052", "发券失败"),
    USE_COUPON_FAILED("30053", "券核销失败"),
    GRANT_CHANNEL_NOT_MATCH("30054", "【权益/发券渠道】不匹配"),
    GRANT_CHANNEL_NOT_SUPPORT("30055", "发券渠道暂不支持"),
    SIGN_VALID_FAILED("30056", "签名校验失败"),
    ILLEGAL_REGISTRATION_SOURCE("31000", "注册来源非法"),
    ;

    private String code;
    private String msg;

}