package com.shuyun.kylin.customized.behavior.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.kylin.customized.base.util.DataapiSdkUtil;
import com.shuyun.kylin.customized.behavior.domain.Interactive;
import com.shuyun.kylin.customized.behavior.resource.po.CheckPO;
import com.shuyun.kylin.customized.behavior.util.DateUtils;
import com.shuyun.kylin.customized.behavior.util.String2G08DateConvertor;
import com.shuyun.pip.component.json.JsonUtils;
import com.shuyun.pip.util.ConvertUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@Service
@SuppressWarnings("unchecked")
public class DMService {

    final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();


    public Interactive getInteractive(CheckPO.SceneType scene, String channelType) {
        String sql = "Select id,interactiveId,interactiveName,awardType,couponList,created,enableState,endTime," +
                "everydaySignLimit,extraDayNo,extraPoints,inviteLimit,isExtraAward," +
                "isRewardCoupon,isRewardPoint,lastSync,pointNum,remarks,scene,couponSceneCode,sceneDesc,startTime,status,taskType,costCenterField,costCenter,channelType,rewardsRecordScope " +
                "From data.prctvmkt.KO.interactive " +
                "Where 1=1 " +
                "And status = '已生效' " +
                "And scene = '" + scene.name() + "'" +
                "And channelType = '" + channelType + "'";

        return queryForObject(sql, Interactive.class, "获取互动主数据");
    }

    private boolean isNull(Collection<?> data1) {
        return data1 == null || data1.isEmpty();
    }

    private <T> T convertTo(Map<String, Object> data, Class<T> clazz) throws Exception {
        T t = clazz.getDeclaredConstructor().newInstance();

        //字符串转时间，由于数据模型存储0时区，实际使用需要 +8 小时变成东8区
        ConvertUtils.addConverter(new String2G08DateConvertor());

        PropertyDescriptor[] propertyDescriptors = BeanUtils.getPropertyDescriptors(clazz);
        for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
            String name = propertyDescriptor.getName();
            if (!data.containsKey(name)) {
                continue;
            }

            Method writeMethod = propertyDescriptor.getWriteMethod();
            writeMethod.setAccessible(true);

            Object sourceValue = data.get(name);
            if (sourceValue == null) {
                continue;
            }

            //如果类型不一致，则尝试进行转换
            Class<?> targetPropertyType = propertyDescriptor.getPropertyType();
            Object convertValue = sourceValue;
            Class<?> sourceType = sourceValue.getClass();
            if (sourceType != targetPropertyType) {
                convertValue = ConvertUtils.convert(sourceValue, targetPropertyType);
            }
            writeMethod.invoke(t, convertValue);
        }

        return t;
    }

    public <T> T queryForObject(String sql, Class<T> tClass, String desc) {
        log.info("--> Action：{}，SQL：{}", desc, sql);
        BaseResponse<Map<String, Object>> data = dataapiHttpSdk.execute(sql, Maps.newHashMap());
        List<Map<String, Object>> data1 = data.getData();
        if (isNull(data1)) {
            log.info("<-- Action：{}，返回值：无结果", desc);
            return null;
        }

        //如果查出了多条，也要视为错误
        if (data1.size() > 1) {
            log.warn("单行查询返回了多行结果，SQL：{}", sql);
            return null;
        }

        //如果是原始类型，则直接返回
        Map<String, Object> dataMap = data1.get(0);
        if (tClass.isPrimitive()) {
            ArrayList<String> keys = new ArrayList<>(dataMap.keySet());
            if (isNull(keys)) {
                return null;
            }
            //忽略字段名，转换成目标类型
            T convert = ConvertUtils.convert(dataMap.get(keys.get(0)), tClass);
            log.info("<-- Action：{}，返回值：{}", desc, JsonUtils.toJson(convert));
            return convert;
        }

        try {
            T t = convertTo(dataMap, tClass);
            log.info("<-- Action：{}，返回值：{}", desc, JsonUtils.toJson(t));
            return t;
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return null;
        }
    }

    public <T> List<T> queryForList(String sql, Class<T> tClass, String desc) {
        log.info("--> Action：{}，SQL：{}", desc, sql);
        BaseResponse<Map<String, Object>> data = dataapiHttpSdk.execute(sql, Maps.newHashMap());
        List<Map<String, Object>> data1 = data.getData();
        if (isNull(data1)) {
            log.info("<-- Action：{}，返回行数：0", desc);
            return Lists.newArrayList();
        }

        try {
            List<T> lists = Lists.newArrayList();
            for (Map<String, Object> map : data1) {
                T item = convertTo(map, tClass);
                lists.add(item);
            }
            log.info("<-- Action：{}，返回行数：{}", desc, lists.size());
            return lists;
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return null;
        }
    }

    public void execute(String sql, String desc) {
        long start = System.currentTimeMillis();
        log.info("--> Action：{}，SQL：{}", desc, sql);
        dataapiHttpSdk.execute(sql, Maps.newHashMap());
        log.info("<-- Action：{}，完成，用时：{}，SQL：{}", desc, System.currentTimeMillis() - start, sql);
    }

    public void save(String fqn, Object x, Predicate<String> editablePredicate) {
        log.info("保存对象：[{}]，到：{}", JsonUtils.toJson(x), fqn);
        PropertyDescriptor[] propertyDescriptors = BeanUtils.getPropertyDescriptors(x.getClass());
        ArrayList<PropertyDescriptor> propertyDescriptorList = Lists.newArrayList(propertyDescriptors);

        //移除class属性
        List<String> cols = Lists.newArrayList();
        propertyDescriptorList.removeIf(p -> p.getName().equals("class"));
        String values = propertyDescriptorList.stream()
                .filter(f -> editablePredicate.test(f.getName()))
                .map(f -> {
                    String name = f.getName();
                    cols.add(name);

                    Method readMethod = f.getReadMethod();
                    readMethod.setAccessible(true);
                    Object invoke;
                    try {
                        invoke = readMethod.invoke(x);
                    } catch (IllegalAccessException | InvocationTargetException e) {
                        log.error("获取属性值失败，属性名：{}，对象：{}", name, JsonUtils.toJson(x));
                        return "null";
                    }
                    if (invoke == null) {
                        return "null";
                    }

                    //要将其它类型转换为字符串类型
                    Class<?> propertyType = f.getPropertyType();
                    if (propertyType == ZonedDateTime.class) {
                        invoke = DateUtils.DataModel.format((ZonedDateTime) invoke);
                    }
                    return "'" + invoke + "'";
                })
                .collect(Collectors.joining(","));

        String insert = String.format("INSERT INTO %s(%s) VALUES(%s)", fqn, String.join(",", cols), values);
        execute(insert, "插入活动记录");
    }

    public DataapiHttpSdk getDataapiSdk() {
        return dataapiHttpSdk;
    }
}
