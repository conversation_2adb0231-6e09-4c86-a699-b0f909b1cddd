package com.shuyun.kylin.customized.member.resource;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.common.ResponsesVo;
import com.shuyun.kylin.customized.member.dto.CostCenterSaveDto;
import com.shuyun.kylin.customized.member.service.CostCenterService;
import com.shuyun.pip.component.json.JsonUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Base64;
import java.util.Map;

@Slf4j
@Tag(name = "成本中心保存修改接口22")
@RestController
@RequestMapping("/cost/center")
public class CostCenterResource {

    @Autowired
    CostCenterService costCenterService;

    /**
     * workshop保存或者更新模型
     * @param costCenterDto
     * @return Authorization
     */
    @PostMapping("/upset")
    @Operation(summary = "成本中心保存修改22", tags = "成本中心保存修改22")
    @ApiResponses(value =  {@ApiResponse(description = "成本中心保存修改22",content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,schema = @Schema(implementation = ResponsesVo.class))})})
    public ResponsesVo upsetCostCenter(@RequestBody CostCenterSaveDto costCenterDto) {
        log.info("成本中心保存入参：{}",JsonUtils.toJson(costCenterDto));
        return costCenterService.upsetCostCenter(costCenterDto);
    }
}
