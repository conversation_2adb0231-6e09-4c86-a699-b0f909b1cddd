package com.shuyun.kylin.customized.rpc.template;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.kylin.customized.base.util.SpringBeanFetcherUtil;
import com.shuyun.kylin.customized.project.strategy.router.SendCouponEnum;
import com.shuyun.kylin.customized.rpc.dto.UBRCouponCodeRequestDto;
import com.shuyun.kylin.customized.rpc.dto.UBRCouponResponseDto;
import com.shuyun.kylin.customized.rpc.entity.UbrConfigEntity;
import com.shuyun.kylin.customized.rpc.template.base.KoRequestTemplate;
import com.shuyun.kylin.starter.redis.ICache;
import com.shuyun.motor.common.cons.PropsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHeaders;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * urb请求模板
 * <pre>
 * ubr.token_url=https://apiq.eubrmb.com/getTokengetToken
 * ubr.send_coupon_url=https://apiq.eubrmb.com/eco/qa/v1.0/coupons/%s/instance
 * ubr.client_id=p0028
 * ubr.client_secret=cmroDhogV6dTBhjStuuFwCMpIBldCF6U
 * ubr.grant_type=client_credentials
 * ubr.sign_method=sha256
 * ubr.sign_secret=1234567890
 * ubr.api_version=10
 * </pre>
 */
@Slf4j
public class UbrRequestTemplate extends KoRequestTemplate {

    static final UbrConfigEntity ubrConfig = JSON.parseObject(PropsUtil.getSysOrEnv("ubr.api.config"), UbrConfigEntity.class);

    public static String getToken() {
        log.info("开始获取urb getToken method start");
        ICache redisCache = (ICache) SpringBeanFetcherUtil.getBean("redisCache");
        String urbToken = redisCache.get("urb_token");
        log.info("urb token缓存值，urbToken={}...", CharSequenceUtil.sub(urbToken, 0, 10));
        if (ObjectUtil.isNotEmpty(urbToken)) {
            return urbToken;
        }
        log.info("开始刷新ubr token");
        long start = System.currentTimeMillis();
        String body = HttpUtil.createPost(ubrConfig.getTokenUrl())
                .form("client_id", ubrConfig.getClientId())
                .form("client_secret", ubrConfig.getClientSecret())
                .form("grant_type", ubrConfig.getGrantType())
                .execute().body();
        log.info("urb刷token耗时：{}ms", System.currentTimeMillis() - start);
        log.info("urb刷token返回结束");
        JSONObject jsonObject = JSON.parseObject(body, JSONObject.class);
        String accessToken = jsonObject.get("access_token").toString();
        log.info("刷新ubr缓存token，accessToken={}...", CharSequenceUtil.sub(urbToken, 0, 10));
        redisCache.put("urb_token", accessToken, 7000, TimeUnit.SECONDS);
        log.info("ubr刷token流程结束");
        return accessToken;
    }

    /**
     *
     * @param ubrProjectCode
     * @param ubrCouponCodeRequestDto
     * @param transactionId 本次请求事务id，用于记录日志
     * @return
     */
    public static UBRCouponResponseDto sendCoupon(String ubrProjectCode, UBRCouponCodeRequestDto ubrCouponCodeRequestDto, String transactionId) {
        log.info("发送urb发券请求原始入参，ubrProjectCode={}，ubrCouponCodeRequestDto={}", ubrProjectCode, JSON.toJSONString(ubrCouponCodeRequestDto));
        String url = String.format(ubrConfig.getSendCouponUrl(), ubrProjectCode);
        Map<String, Object> signMap = BeanUtil.beanToMap(ubrCouponCodeRequestDto, false, true);
        signMap.put("SignSecret", ubrConfig.getSignSecret());
        String now = DateUtil.now();
        signMap.put("timestamp", now);
        log.info("urb签名原始参数，signMap={}", JSON.toJSONString(signMap));
        String sign = getSign(JSON.toJSONString(signMap));
        log.info("urb签名结果，sign={}", sign);
        String token = getToken();
        try {
            log.info("开始请求ubr接口");
            long start = System.currentTimeMillis();
            HttpResponse execute = HttpUtil
                    .createPost(url)
                    .header("v", ubrConfig.getApiVersion())
                    .header(HttpHeaders.AUTHORIZATION, "Bearer ".concat(token))
                    .header("timestamp", now)
                    .header("sign_method", ubrConfig.getSignMethod())
                    .header("sign", sign)
                    .body(JSON.toJSONString(ubrCouponCodeRequestDto)).execute();
            pushKoRpcRequestLog(url, ubrCouponCodeRequestDto, execute.body(), transactionId, SendCouponEnum.UBR.getCode());
            log.info("urb发券请求耗时：{}ms", System.currentTimeMillis() - start);
            log.info("ubr发券请求原始结果，status={}，body={}", execute.getStatus(), execute.body());
            int status = execute.getStatus();
            if (ObjectUtil.notEqual(status, HttpStatus.HTTP_CREATED)) {
                log.info("urb发券请求失败，token={}, execute={}", token, JSON.toJSONString(execute));
                return null;
            }
            String body = execute.body();
            log.info("urb发券请求结果，execute={}", body);
            return JSON.parseObject(body, UBRCouponResponseDto.class);
        } catch (Exception e) {
            log.error("urb发券请求异常", e);
            pushKoRpcRequestLog(url, ubrCouponCodeRequestDto, e.getMessage(), transactionId, SendCouponEnum.UBR.getCode());
            return null;
        }
    }

    private static String getSign(String body) {
        MessageDigest digest = null;
        try {
            digest = MessageDigest.getInstance("SHA-256");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
        byte[] hashBytes = digest.digest(JSON.toJSONString(body).getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(hashBytes);
    }
}
