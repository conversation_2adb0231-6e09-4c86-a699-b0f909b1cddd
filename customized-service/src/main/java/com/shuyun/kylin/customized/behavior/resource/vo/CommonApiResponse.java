package com.shuyun.kylin.customized.behavior.resource.vo;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CommonApiResponse<T> {
    private String module = "customized-service";
    private String service = "interaction";
    private String errorCode = "200";
    private String msg = "success";
    private T data;

    public static CommonApiResponse<CheckResponse> buildOf(InteractiveResponseVO.InteractiveDateRangeCheckCode noValidActivity) {
        return getCheckResponseCommonApiResponse(noValidActivity.getCode(), noValidActivity.getMessage());
    }

    public static <T> CommonApiResponse<T> success(T t) {
        CommonApiResponse<T> checkResponseCommonApiResponse = new CommonApiResponse<>();
        checkResponseCommonApiResponse.setData(t);
        return checkResponseCommonApiResponse;
    }

    public static CommonApiResponse<CheckResponse> buildOf(String code, String message) {
        return getCheckResponseCommonApiResponse(code, message);
    }

    private static CommonApiResponse<CheckResponse> getCheckResponseCommonApiResponse(String code, String message) {
        CommonApiResponse<CheckResponse> checkResponseCommonApiResponse = new CommonApiResponse<>();
        checkResponseCommonApiResponse.setErrorCode(code);
        checkResponseCommonApiResponse.setMsg(message);
        return checkResponseCommonApiResponse;
    }
}
