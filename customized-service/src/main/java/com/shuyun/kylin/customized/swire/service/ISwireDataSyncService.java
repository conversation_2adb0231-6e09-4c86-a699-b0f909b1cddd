package com.shuyun.kylin.customized.swire.service;

import com.shuyun.kylin.crm.openapi.core.dto.wechat.WechatRegisterRequest;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.common.ResponsesVo;
import com.shuyun.kylin.customized.swire.dto.PointRuleDto;
import com.shuyun.kylin.customized.swire.dto.SwireMemberDto;
import com.shuyun.kylin.customized.swire.dto.SwireMemberRecordsDto;

import java.util.Map;

public interface ISwireDataSyncService {


    ResponseResult registerSyncMember(SwireMemberRecordsDto swireMemberRecordsDto, String url, String status);

    ResponsesVo sendPointRule(PointRuleDto pointRuleDto);

    Map<String, String> dataSyncMemberPoint(Map<String,String> request);

    Map<String, String> dataSyncHistoryPoint(Map<String, String> request);

    //void dataSyncMember(WechatRegisterRequest request);

    //void dataSyncUpdateMember(SwireMemberDto swireMemberDto);
}
