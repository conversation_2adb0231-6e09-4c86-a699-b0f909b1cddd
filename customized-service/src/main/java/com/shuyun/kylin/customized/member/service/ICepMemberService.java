package com.shuyun.kylin.customized.member.service;

import com.shuyun.kylin.crm.openapi.core.dto.common.PointRequestDto;
import com.shuyun.kylin.crm.openapi.core.dto.wechat.WechatRegisterRequest;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.common.ResponsesVo;
import com.shuyun.kylin.customized.member.dto.*;

import java.util.List;
import java.util.Map;

public interface ICepMemberService {

    ResponseResult wechatReg(WechatRegisterRequestDto request);

    ResponseResult updatePoint(PointRequestDto pointRequestDto, String tradeNo,String scene,String channelType);

    ResponseResult getablePoint(String memberId, Integer ruleCode, String scene,String channelType, String channelFrom);

    ResponseResult queryMember(String customerNo,String memberId,String memberType,String channelType);

    ResponseResult queryMemberGrade(String customerNo,String memberType,String channelType);

    ResponseResult queryCostCenter();

    ResponseResult updateMemberScenePoint(CepMemberPointRecordDto cepMemberPointRecordDto);

    ResponsesVo deleteMember(MemberCancelDto memberCancelDto);

    CepPointRecordListPageResponse queryPointRecord(CepPointBehaviorRecordDto cepPointBehaviorRecordDto);

    CepMemberPointItemDto getMemberPointItems(String customerNo, String startTime, String endTime, Integer page, Integer pageSize, List<String> scene,String channelType);

    ResponseResult<Map<String, Object>> getMemberPoint(String channelType, String customerNo, String memberType);

    ResponsesVo getCommonUser(String appid, String user_id);

    ResponseResult updateMemberByChannelId(CepUpdateMemberDto request);

    Map<String, Object> queryMemberuat(OcpMemberBindingDto ocpMemberBindingDto);

    Map<String, Object> queryCustomizedPropertiest(Map request);

    ResponsesVo consumerCancel(ConsumerCancelDto koids,String memberId,String operator);

    Map<String, String> deleteConsumerInfo(ConsumerInfoDto consumerInfoDto);

    ResponseResult saveManualPoint(Map<String, String> pointRequestDto);


    ResponseResult changeMemberScenePoint(CepLbsMemberPointRecordDto cepLbsMemberPointRecordDto );

    ResponseResult getCheckMobile(String mobile);

    CepMemberPointItemDto getPointRecords(String customerNo, String startTime, String endTime, Integer page, Integer pageSize, String channelType);
}
