package com.shuyun.kylin.customized;


import com.shuyun.pip.BaseApplication;
import com.shuyun.pip.autoconfiguration.ReloadableMessageSourceAutoConfiguration;
import com.shuyun.pip.autoconfigure.LockerAutoConfiguration;
import com.shuyun.pip.autoconfigure.PipAutoConfiguration;
import com.shuyun.pip.autoconfigure.RedisLockerAutoConfigure;
import com.shuyun.pip.autoconfigure.ValidatorConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 * @date 2019/4/06
 */
@EnableFeignClients(basePackages = {"com.shuyun.kylin.starter.cdp.client","com.shuyun.kylin.crm.openapi.sdk.client","com.shuyun.kylin.customized.base.feign.client"})
@EnableTransactionManagement
@SpringBootApplication(
        scanBasePackages = {"com.shuyun.kylin.starter.redis",
                           "com.shuyun.kylin.customized",
                           "com.shuyun.kylin.starter.i18n",
                           "com.shuyun.kylin.starter.swagger3",
                           "com.shuyun.kylin.starter.exception"},
        exclude = {MultipartAutoConfiguration.class, DataSourceAutoConfiguration.class, LockerAutoConfiguration.class,  RedisLockerAutoConfigure.class,
                ReloadableMessageSourceAutoConfiguration.class, ValidatorConfiguration.class, PipAutoConfiguration.class})
@EnableRetry
@EnableAspectJAutoProxy(exposeProxy = true)
public class CustomizedServiceApplication extends BaseApplication  {
    private static Logger logger = LoggerFactory.getLogger(CustomizedServiceApplication.class);
    public CustomizedServiceApplication() {
        super(CustomizedServiceApplication.class);
    }

    public static void main(String[] args) {
        logger.info("定制服务启动开始...");
        new CustomizedServiceApplication().run(args);
        logger.info("定制服务启动结束...");
    }
}
