package com.shuyun.kylin.customized.history.common;

import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.base.feign.dao.LoyaltyFacadeDto;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.history.dao.LoyaltyPointDto;
import com.shuyun.kylin.customized.member.dto.CampaignPointsRuleDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class LoyaltyFacadeRepository extends BaseDsRepository<LoyaltyPointDto> {



    public List<LoyaltyPointDto> selectReducePoint(int index, int pageSize, int completedNum) {

        //用id作条件记得表中生成自增id {因源表id字段为varchar,需加''处理,否则乱序}
        String change = " a.id > '" + index + "' order by a.id limit " + pageSize;

        String querySql = " SELECT id,memberId,channelType,changeMode,pointAccountId,businessId,changePoint,changeSource,changeSourceDetail,costTracing,`desc`,shopId,changeType,effectiveDate,overdueDate " +
                "  FROM  " + ConfigurationCenterUtil.HISTORY_UPDATE_POINTLOG + " a where " + change + "";
        log.info("历史积分记录查询批次:{},sql:{}",completedNum,querySql);
        List<LoyaltyPointDto> list = executeSQL(querySql, Collections.EMPTY_MAP);
        return list;
    }
}
