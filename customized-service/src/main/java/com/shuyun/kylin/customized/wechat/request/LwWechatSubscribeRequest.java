package com.shuyun.kylin.customized.wechat.request;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.HashMap;

@Data
public class LwWechatSubscribeRequest {


    //活动编码
    private String campaignCode;

    //订阅场景类型 PLATFORM：平台通用CAMPAIGN：活动
    private String  type;

    //接收者（用户）的 openid
    private String  toUser;

    //所需下发的订阅模板 id
    private String templateId;
    //MA发送记录流水号
    private String traceId;


    //点击模板卡片后的跳转页面，仅限本 小程序内的页面。
    //支持带参数 （示例 pages/campaign/h5/index?code={c ampaignCode}）。该字段不填则无跳转。
    private String  page ;

    //模板内容  { "thing1": “value” , "thing2": “value” }
    private JSONObject data;


    @Data
    public static class data  {
        private String thing1;
        private String thing2;
        private String thing3;
        private String thing4;
        private String thing5;
        private String thing6;
        private String thing7;

    }
}
