package com.shuyun.kylin.customized.project.strategy;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.feign.dao.MemberBingDto;
import com.shuyun.kylin.customized.base.feign.dao.OfferProjectDetailClientResponse;
import com.shuyun.kylin.customized.project.dto.SendCouponResultDto;
import com.shuyun.kylin.customized.project.request.GrantCouponRequest;
import com.shuyun.kylin.customized.rpc.dto.McdSendCouponRequestDto;
import com.shuyun.kylin.customized.rpc.dto.McdSendCouponResponseDto;
import com.shuyun.kylin.customized.rpc.entity.McdConfigEntity;
import com.shuyun.kylin.customized.rpc.exception.KoOutRequestException;
import com.shuyun.kylin.customized.rpc.template.McdRequestTemplate;
import com.shuyun.motor.common.cons.PropsUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;

/**
 * 麦当劳优惠券发放策略
 *
 * @author: Jingwei
 * @date: 2025-01-08
 */
@Slf4j
public class McdCouponStrategy extends SendCouponStrategy{

    static final McdConfigEntity mcdConfig = JSON.parseObject(PropsUtil.getSysOrEnv("ko.mcd.api.config"), McdConfigEntity.class);

    @Override
    public SendCouponResultDto sendCoupon(String templateId, OfferProjectDetailClientResponse projectDetail, GrantCouponRequest grantCouponRequest, MemberBingDto memberBindInfo) {
        if (ObjectUtil.isEmpty(memberBindInfo.getMobile())) {
            log.error("麦当劳发券策略入参mobile为空，grantCouponRequest={}", JSON.toJSONString(grantCouponRequest));
            throw new KoOutRequestException("内部错误，手机号空");
        }
        log.info("请求麦当劳发券策略入参，templateId={}，projectDetail={}，grantCouponRequest={}，memberBindInfo={}", templateId, JSON.toJSONString(projectDetail), JSON.toJSONString(grantCouponRequest), JSON.toJSONString(memberBindInfo));
        OfferProjectDetailClientResponse.RspData data = projectDetail.getData();
        if(ObjectUtil.isNull(data)) {
            log.error("麦当劳策略内部接口返回失败，data is empty!");
            return new SendCouponResultDto(Boolean.FALSE, null, null,null);
        }
        OfferProjectDetailClientResponse.RspData.ExtData extData = data.getExtData();
        if(ObjectUtil.isNull(extData)) {
            log.error("麦当劳策略内部接口返回失败，extData is empty!");
            return new SendCouponResultDto(Boolean.FALSE, null, null,null);
        }
        String externalProjectId = extData.getExternalProjectId();
        if(ObjectUtil.isEmpty(externalProjectId)) {
            log.error("麦当劳策略内部接口返回失败，externalProjectId is empty!");
            return new SendCouponResultDto(Boolean.FALSE, null, null,null);
        }
        // 组装发券请求参数
        McdSendCouponRequestDto mcdSendCouponRequestDto = assembleMcdRequestParam(externalProjectId, memberBindInfo.getMobile(), grantCouponRequest.getTransactionId());
        // 发券
        McdSendCouponResponseDto mcdSendCouponResponseDto = McdRequestTemplate.sendCoupon(mcdSendCouponRequestDto, grantCouponRequest.getTransactionId());
        return new SendCouponResultDto(Boolean.TRUE, null, mcdSendCouponResponseDto.getData().getCoupons().get(0).getCouponCodes().get(0).getCouponCode(),null);
    }

    /**
     * 组装麦当劳发券请求参数
     *
     * @param mobile
     * @param transactionId
     * @return
     */
    private McdSendCouponRequestDto assembleMcdRequestParam(String externalProjectId, String mobile, String transactionId) {
        log.info("组装麦当劳发券请求参数，externalProjectId={}，mobile={}，transactionId={}", externalProjectId, mobile, transactionId);
        AES aes = new AES(Mode.ECB, Padding.PKCS5Padding, mcdConfig.getAesKey().getBytes());
        McdSendCouponRequestDto mcdSendCouponRequestDto = new McdSendCouponRequestDto();
        mcdSendCouponRequestDto.setChannelCode(mcdConfig.getChannelCode());
        mcdSendCouponRequestDto.setReceiveType(6);
        mcdSendCouponRequestDto.setCustomerMobileNo(aes.encryptHex(mobile));
        mcdSendCouponRequestDto.setTradeNo(transactionId);
        mcdSendCouponRequestDto.setTradeSystem(mcdConfig.getTradeSystem());
        McdSendCouponRequestDto.Coupon coupon = new McdSendCouponRequestDto.Coupon();
        coupon.setCouponId(externalProjectId);
        mcdSendCouponRequestDto.setCoupons(Collections.singletonList(coupon));
        return mcdSendCouponRequestDto;
    }
}
