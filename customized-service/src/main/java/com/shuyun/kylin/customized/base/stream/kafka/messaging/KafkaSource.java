package com.shuyun.kylin.customized.base.stream.kafka.messaging;

import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @Description 此类定义Kafka的发送队列
 * @date 2019/5/15
 */
@Component
@ConditionalOnExpression("${system.kafka.enabled:true}")
public interface KafkaSource {
    String KAFKA_OUTPUT = "kafka_output";
    String KAFKA_OUTPUT1 = "kafka_output1";


    @Output(KAFKA_OUTPUT)
    MessageChannel output();
    @Output(KAFKA_OUTPUT1)
    MessageChannel output1();

/*    String KAFKA_SEND_SWIRE_MEMBER_OUTPUT = "kafka_send_swire_member_output";
    @Output(KAFKA_SEND_SWIRE_MEMBER_OUTPUT)
    MessageChannel swireMemberOutput();

    String KAFKA_UPDATE_SWIRE_MEMBER_OUTPUT = "kafka_update_swire_member_output";
    @Output(KAFKA_UPDATE_SWIRE_MEMBER_OUTPUT)
    MessageChannel swireUpdateMemberOutput();*/


    String KAFKA_MEMBER_CAMPAIGN_OUTPUT = "kafka_member_campaign_output";
    @Output(KAFKA_MEMBER_CAMPAIGN_OUTPUT)
    MessageChannel memberCampaignOutput();


}
