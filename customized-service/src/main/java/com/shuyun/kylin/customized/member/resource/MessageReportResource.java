package com.shuyun.kylin.customized.member.resource;


import com.shuyun.kylin.crm.openapi.core.dto.wechat.WechatRegisterRequest;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.member.service.MessageReportService;
import com.shuyun.pip.component.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/outside/node")
public class MessageReportResource {

    @Autowired
    MessageReportService messageReportService;
    /**
     * 触达消息接口
     * @param request
     * @return
     */
    @PostMapping("/message/report")
    public Map messageReport(@RequestBody Map request) {
        log.info("触达事件入参:{}", JsonUtils.toJson(request));
        return messageReportService.messageReport(request);
    }


}
