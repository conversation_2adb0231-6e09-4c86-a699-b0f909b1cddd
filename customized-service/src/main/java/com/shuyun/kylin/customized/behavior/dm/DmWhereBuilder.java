package com.shuyun.kylin.customized.behavior.dm;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

public final class DmWhereBuilder {

    private final List<String> wheres = Lists.newArrayList();

    public DmWhereBuilder add(String where) {
        wheres.add(where);
        return this;
    }

    public String build() {
        wheres.removeIf(Objects::isNull);
        if (wheres.size() == 0) {
            return "";
        }
        return "Where " + Joiner.on(" And ").join(wheres);
    }
}
