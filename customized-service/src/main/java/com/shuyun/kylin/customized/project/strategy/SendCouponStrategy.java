package com.shuyun.kylin.customized.project.strategy;

import com.shuyun.kylin.customized.base.feign.dao.MemberBingDto;
import com.shuyun.kylin.customized.base.feign.dao.OfferProjectDetailClientResponse;
import com.shuyun.kylin.customized.project.dto.SendCouponResultDto;
import com.shuyun.kylin.customized.project.request.GrantCouponRequest;

public abstract class SendCouponStrategy {

    /**
     * 发三方券
     * <p>
     * 关于入参：后续如果入参还有扩展，可以把所有入参封装到一个对象中，避免入参过多
     * </p>
     * @param templateId 模板id，暂时没用，主要在接口外部根据此值判断使用哪种券策略
     * @param projectDetail 券项目详情
     * @param grantCouponRequest 券发放原始请求入参
     * @return 发券结果
     * <ul>
     *     <li>成功示例 return new SendCouponResultDto(Boolean.TRUE, null, "三方接口返回的券码",null);</li>
     *     <li>失败示例 return new SendCouponResultDto(Boolean.FALSE, null, null,null);</li>
     *
     *     说明：返回结果尽量以示例形式返回，明确三方调用结果，避免出现异常情况；如果确实有某些可预知的异常，可直接抛出明确信息，
     *     比如：throw new RuntimeException("券码已存在");
     * </ul>
     */
    public abstract SendCouponResultDto sendCoupon(String templateId, OfferProjectDetailClientResponse projectDetail, GrantCouponRequest grantCouponRequest, MemberBingDto memberBindInfo);
}
