package com.shuyun.kylin.customized.member.dto;

import com.shuyun.kylin.customized.member.dao.CouponResponseDao;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;


import javax.validation.constraints.NotBlank;
import java.util.Map;

@Slf4j
@Data
public class CepCouponCallbackDto {

    @NotBlank(message = "uid不能为空: appid_openid，同customerNo")
    private String uid;
    @NotBlank(message = "券项目ID不能为空")
    private String projectId;
    @NotBlank(message = "必填字段，是否发放成功：1成功，0失败")
    private String isGrantSuccess;
    @NotBlank(message = "必填字段，结果描述,成功-券码，失败-失败原因")
    private String grantResult;
    //@NotBlank(message = "必填字段，crm发券透传字段回传")
    private Map extra;
    @NotBlank(message = "必填字段，是否同步微信券包：1 是，0 否")
    private String isSyncCouponBag;

    private String created;
    private String lastSync;

    private String nodeId;
    private String campaignId;
    private String memberId;

    public CouponResponseDao getCouponResponseDao() {
        CouponResponseDao couponResponseDao = new CouponResponseDao();
        couponResponseDao.setUid(uid);
        couponResponseDao.setProjectId(projectId);
        if (StringUtils.isNotBlank(isGrantSuccess)){
            couponResponseDao.setIsGrantSuccess("1".equals(isGrantSuccess) ? true : false);
        }
        couponResponseDao.setGrantResult(grantResult);
        couponResponseDao.setIsSyncCouponBag(isSyncCouponBag);
        couponResponseDao.setCampaignId(extra.get("campaignId").toString());
        log.info("活动id: {}",extra.get("campaignId").toString());
        couponResponseDao.setNodeId(extra.get("nodeId").toString());
        return couponResponseDao;
    }
}
