package com.shuyun.kylin.customized.base.feign.dao;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 卡劵 核销 (使用)feign dto
 */
@Data
public class OfferInstanceUseClientRequestDto {
    /**
     * 品牌
     */
    private String brand;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 事务ID
     */
    private String transactionId;

    /**
     * 业务发起人
     */
    private String businessInitiator;

    /**
     * 店铺编码
     */
    private String shopCode;

    /**
     * 证书
     */
    private String certificate;

    /**
     * 会员标识(会员ID或渠道ID二者选其一,建议传入渠道ID;优先级:渠道ID > 会员ID)
     */
    private Identify identify;

    @Data
    public static class Identify {
        private String memberId;
        private String customerNo;
    }

    private List<String> couponCodes;

    private List<Order> order;

    @Data
    public static class Order {
        /**
         * 订单id
         */
        private String orderId;
        /**
         * 订单金额
         */
        private BigDecimal amount;
        /**
         * 订单实付金额
         */
        private String paymentAmount;
        /**
         * 优惠金额
         */
        private String discountAmount;

        /**
         * 子订单
         */
        private List<Items> items;

        @Data
        public static class Items {
            /**
             * 子订单id
             */
            private String orderItemId;

            /**
             * 商品价格
             */
            private BigDecimal price;

            /**
             * 商品特价
             */
            private String specialPrice;

            /**
             * 商品Id
             */
            private String goodsId;

            /**
             * 商品数量
             */
            private Integer num;

            /**
             * 子订单金额
             */
            private BigDecimal amount;

            /**
             * 子订单实付金额
             */
            private String paymentAmount;
            /**
             * 子优惠金额
             */
            private String discountAmount;
        }
    }

    /**
     * 平台ID
     */
    private String platformId;

    /**
     * 锁定时使用的事务ID，如果先执行了锁定，再执行核销，则该参数必填
     */
    private String lockTransactionId;

    /**
     * 扩展字段
     */
    private Extension extension;

    @Data
    public static class Extension {
        private String memberName;
        private String province;
        private String city;
        private String district;
        private String address;
        private String postalCode;
        private String recipientmobile;
        private String usedLBSProvince;
        private String usedLBSCity;
        private String usedLBSDistrict;
        private String usedCostCenterCode;
    }
}
