package com.shuyun.kylin.customized.rpc.dto;

import lombok.Data;

/**
 * 知而行券请求参数
 */
@Data
public class ZerXCouponRequestDto {
    /**
     * OCP⼩程序appid，默认值指定如下
     * <p>
     * 测试：wxad63eb2ce3f0d7d5
     * <br/>
     * ⽣产：wxa5811e0426a94686
     * </p>
     */
    private String appid;
    /**
     * 用户openid
     */
    private String openId;
    /**
     * 券ID
     */
    private Integer id;
    /**
     * 指定默认值
     */
    private String scene;
    /**
     * ⾃定义场景值，如使⽤utm_source值标记⽤⼾的领券场景
     * <br/>
     * utm_source：广告系列来源，使用 utm_source 来标识搜索引擎、简报名称或其他来源。例如：utm_source=google
     */
    private String qrcodeScene;
    /**
     * ⾃定义兑换（交易）ID，保证唯⼀性，⼤⼩写不敏感， 建议格式: [0-9a-z-]{1,64}
     */
    private String transactionId;
    /**
     * 领券所在位置的地理经度
     */
    private String lng;
    /**
     * 领券所在位置的地理纬度
     */
    private String lat;
    /**
     * 13位时间戳
     */
    private String timestamp;
    /**
     * 签名
     */
    private String sign;
}
