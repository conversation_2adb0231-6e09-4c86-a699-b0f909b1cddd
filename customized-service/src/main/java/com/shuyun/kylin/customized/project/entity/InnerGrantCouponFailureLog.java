package com.shuyun.kylin.customized.project.entity;

import lombok.Data;

/**
 * 内部券券发放失败日志
 *
 * @author: Jingwei
 * @date: 2024-12-20
 */
@Data
public class InnerGrantCouponFailureLog {
    /**
     * 请求参数
     */
    private String requestParams;
    /**
     * 内部发券失败类型：1-扣积分失败；2-发券失败；3- 爱奇艺特殊流程（爱奇艺核销失败）
     */
    private Integer failureType;
    /**
     * 是否积分兑换券 1-否；2-是，只有发券时才有指定状态，否则默认0
     */
    private Integer isPointExchange;
    /**
     * 事务Id，用于关联三方券发放日志
     */
    private String transactionId;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 推送状态：0-默认；1-成功；2-失败
     */
    private Integer status;
}
