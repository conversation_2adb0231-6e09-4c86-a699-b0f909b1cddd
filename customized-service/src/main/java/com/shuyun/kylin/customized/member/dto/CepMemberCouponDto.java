package com.shuyun.kylin.customized.member.dto;


import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;


@Data
public class CepMemberCouponDto {

    @NotBlank(message = "唯一标识id不能为空")
    private String id;
    @NotBlank(message = "客户编号不能为空，格式：appid_openid")
    private String customerNo;
    @NotBlank(message = "券项目id不能为空")
    private String projectId;
    private String couponCode;
    //Enum
    private String sendChannelType;
    //Enum
    private String usedChannelType;
    private String sendCostCenterCode;
    private String usedCostCenterCode;
    private Double couponCost;
    private String grantTime;
    private String useTime;
    private String orderNo;
    private String expireTime;
    private String status;
    private String remarks;
    private String memberIDStr;
    private String lastSync;
    private Boolean isSendCardsOffers;
    private String campaignId;


    private Map member;
    private Map project;
    private Map campaign;
    private Map sendCostCenter;
    private Map usedCostCenter;

}
