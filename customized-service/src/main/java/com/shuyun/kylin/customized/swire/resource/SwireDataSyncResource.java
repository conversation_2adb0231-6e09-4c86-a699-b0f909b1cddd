package com.shuyun.kylin.customized.swire.resource;
import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.crm.openapi.core.dto.wechat.WechatRegisterRequest;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.member.dto.CepUpdateMemberDto;
import com.shuyun.kylin.customized.swire.dto.SwireMemberDto;
import com.shuyun.kylin.customized.swire.dto.SwireMemberRecordsDto;
import com.shuyun.kylin.customized.swire.service.ISwireDataSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> @Description 数据同步
 * @date 2023/05/15
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/swire/data/sync")
public class SwireDataSyncResource {

    @Autowired
    private ISwireDataSyncService iSwireDataSyncService;

    /**
     * 同步太古会员
     * <p>
     *
     * @param swireMemberRecordsDto
     * @return
     */
    @PostMapping("/member")
    public ResponseResult dataSyncMember(@RequestBody SwireMemberRecordsDto swireMemberRecordsDto) {
        log.info("同步太古会员: {}", swireMemberRecordsDto);
        String registerUrl = ConfigurationCenterUtil.HTTP_SWIRE_MEMBER_UPDATALNFO;
        return iSwireDataSyncService.registerSyncMember(swireMemberRecordsDto,registerUrl, "0");
    }
    @PostMapping("/registerSyncMember")
    public ResponseResult registerSyncMember(@RequestBody SwireMemberRecordsDto swireMemberRecordsDto) {
        log.info("注册会员同步太古: {}", swireMemberRecordsDto);
        String registerUrl = ConfigurationCenterUtil.HTTP_SWIRE_MEMBER_REGISTER;
        return iSwireDataSyncService.registerSyncMember(swireMemberRecordsDto,registerUrl,"1");
    }

    @PostMapping("/point")
    public Map<String, String> dataSyncPoint(@RequestBody Map<String,String> request){
        log.info("同步太古积分明细入参: {}", JSON.toJSONString(request));
        return iSwireDataSyncService.dataSyncMemberPoint(request);
    }

    @PostMapping("/history/point")
    public Map<String, String> dataSyncHistoryPoint(@RequestBody Map<String,String> request){
        log.info("同步历史积分明细入参: {}", JSON.toJSONString(request));
        return iSwireDataSyncService.dataSyncHistoryPoint(request);
    }
}
