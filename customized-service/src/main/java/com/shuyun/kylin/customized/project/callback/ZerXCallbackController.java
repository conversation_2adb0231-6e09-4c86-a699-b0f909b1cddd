package com.shuyun.kylin.customized.project.callback;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.project.dto.ZerXCallbackDto;
import com.shuyun.kylin.customized.project.service.SendCouponService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: Jingwei
 * @date: 2024-12-12
 */
@Slf4j
@RestController
@RequestMapping("/zerx")
public class ZerXCallbackController {

    @Resource
    private SendCouponService sendCouponService;

    @RequestMapping("/sendCouponCallback")
    public ResponseResult<String> sendCouponCallback(@RequestBody ZerXCallbackDto zerXCallbackDto) {
        log.info("知而行回调接口入参，zerXCallbackDto={}", JSON.toJSONString(zerXCallbackDto));
        return sendCouponService.zerXCallback(zerXCallbackDto);
    }
}
