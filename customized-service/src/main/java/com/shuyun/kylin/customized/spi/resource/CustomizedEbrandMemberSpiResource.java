package com.shuyun.kylin.customized.spi.resource;

import com.shuyun.crmep.ebrand.spi.IEbrandMemberCustomizedSPI;
import com.shuyun.crmep.ebrand.spi.dto.GradeInheritResult;
import com.shuyun.crmep.ebrand.spi.dto.InheritedGradeFetchRequest;
import com.shuyun.crmep.ebrand.spi.dto.InheritedPointFetchRequest;
import com.shuyun.crmep.ebrand.spi.dto.PointInheritResult;
import com.shuyun.pip.component.json.JsonUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 定制会员通spi
 *
 * <AUTHOR>
 * @since 2021/09/29 18:20
 */
@Tag(name = "定制会员通spi", description = "用于定制化 会员通服务 部分spi接口")
@Slf4j
@RestController
@RequestMapping("/ebrandMember")
public class CustomizedEbrandMemberSpiResource implements IEbrandMemberCustomizedSPI {

    /**
     * 获取继承 后的最终等级
     * 注意： 需要返回等级过期时间，若不限 为null
     */
    @Override
    @PostMapping("/getInheritedGrade")
    public GradeInheritResult getInheritedGrade(@RequestBody InheritedGradeFetchRequest request) {
        log.info("接收到获取继承后的最终等级请求,request:{}", JsonUtils.toJson(request));
        // TODO 计算等级有效期 以及 继承后的最终麒麟等级
        return GradeInheritResult.Companion.build(true, null, null);
    }

    /**
     * 获取继承后的 变更积分(本次继承需要加上的积分)
     * 注意： 需要返回积分有效期，若不限，请返回null
     */
    @Override
    @PostMapping("/getInheritedPoint")
    public PointInheritResult getInheritedPoint(@RequestBody InheritedPointFetchRequest request) {
        log.info("接收到获取继承后的变更积分请求,request:{}", JsonUtils.toJson(request));
        // TODO 计算积分有效期 以及继承后的 需要增加的积分数
        return PointInheritResult.Companion.build(true, null, null, null);
    }

}
