package com.shuyun.kylin.customized.base.exception;


import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.enums.ExceptionEnum;
import com.shuyun.kylin.starter.exception.model.ThirdPartException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolationException;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2021-12-16
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionController {


    /**
     * 处理项目中已知异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(CustomizeException.class)
    ResponseResult handleCraftException(CustomizeException e) {
        return new ResponseResult(e.getError_code(), e.getMsg());
    }

    /**
     * BindException
     * 处理 @Controller 对象(DTO/VO等) @NotNull @NotBlank
     * 需要 @Validated 配合
     * @param e
     * @return
     */
    @ExceptionHandler(BindException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    ResponseResult handleBindException(BindException e) {
        return new ResponseResult(ExceptionEnum.ILLEGAL_ARGUMENT, e.getFieldError().getDefaultMessage());
    }

    /**
     * ConstraintViolationException
     * 处理 @Controller 中方法参数效验 @NotNull
     * 需要 @Validated 配合
     *
     * @param e
     * @return
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    ResponseResult handleConstraintViolationException(ConstraintViolationException e) {
        return new ResponseResult(ExceptionEnum.ILLEGAL_ARGUMENT, e.getMessage());
    }

    /**
     * MethodArgumentNotValidException
     * 处理 javax.validation.constraints.* 下对象验证异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    ResponseResult handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        return new ResponseResult(ExceptionEnum.ILLEGAL_ARGUMENT, e.getBindingResult()
                .getAllErrors()
                .stream()
                .map(err -> {
                    if (err.getCodes().length > 0) {
                        String code = err.getCodes()[0];
                        StringBuilder message = new StringBuilder();
                        if ((code.indexOf(".") != -1)) {
                            message.append(code, code.indexOf(".") + 1, code.length());
                        }
                        return message.append(err.getDefaultMessage()).toString();
                    }else{
                        return err.getDefaultMessage();
                    }
                })
                .sorted()
                .collect(Collectors.joining(", ")));
    }

    /**
     * MissingServletRequestParameterException
     * 处理 @RequestParam 等中的 requred = true, 而参数为空时的异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler( MissingServletRequestParameterException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    ResponseResult handleMissingServletRequestParameterException(MissingServletRequestParameterException e) {
        return new ResponseResult(ExceptionEnum.ILLEGAL_ARGUMENT, e.getMessage());
    }

    /**
     * 处理未知异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(value = Throwable.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    ResponseResult handleGlobalException(Exception e) {

        log.error(e.getMessage(), e);
        return new ResponseResult(ExceptionEnum.INTERNAL_ERROR, ExceptionEnum.INTERNAL_ERROR.getMsg());
    }

}