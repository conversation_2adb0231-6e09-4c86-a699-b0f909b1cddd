package com.shuyun.kylin.customized.dataflow.ct;


import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.enums.ChannelTypeEnum;
import com.shuyun.kylin.customized.base.util.*;
import com.shuyun.kylin.crm.openapi.core.dto.common.PointRequestDto;
import com.shuyun.kylin.customized.member.dm.CepCostCenterRepository;
import com.shuyun.kylin.customized.member.dm.CepRegulationPointRepository;
import com.shuyun.kylin.customized.member.dto.CepRegulationPointDto;
import com.shuyun.kylin.customized.member.service.ICepMemberService;
import com.shuyun.kylin.starter.redis.ICache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;

@Slf4j
@RestController
@RequestMapping("/dataflow")
public class CtPointDataSupplementation {

    @Autowired
    private ICepMemberService iCepMemberService;

    @Autowired
    private CepRegulationPointRepository cepRegulationPointRepository;

    @Autowired
    private CepCostCenterRepository cepCostCenterRepository;

    @Autowired
    @Qualifier("redisCache")
    private ICache redisCache;


    @PostMapping("/ct/point/scene")
    public Map<String, Object> syncDeleteMember(@RequestBody Map<String, Object> request) {
        log.info("point请求入参:{}", JSON.toJSONString(request.get("request")));
        ConcurrentHashMap<String, Object> res = new ConcurrentHashMap<>();
        //扣减取消   hashMap返回值的封装
        Map<Object, Object> hashMap = new HashMap<>();

        //查询场景活动
        CepRegulationPointDto activityTheme = cepRegulationPointRepository.getActivityTheme(request.get("scene").toString());
        log.info("查询场景活动:{}",JSON.toJSONString(activityTheme));

        PointRequestDto requestDto = new PointRequestDto();
        //默认使用活动规则成本中心
        requestDto.setShopCode(activityTheme.getCostCenterCode());

        //根据lbs查询成本中心
        if ("Y".equals(activityTheme.getIsLBSCostCenterCode())){
            if (request.get("sendLBSCity") != null){
                String costCenter = cepCostCenterRepository.getLbsCostCenter(request.get("usedLBSProvince").toString(),request.get("sendLBSCity").toString(),request.get("sendLBSDistrict").toString());
                if (StringUtils.isNotBlank(costCenter)){
                    requestDto.setShopCode(costCenter);
                }
            }
        }
        //封装积分变更实体
        requestDto.setMemberId(request.get("memberId").toString());
        //积分的值
        double point = activityTheme.getSendOrDeductValuePerTime();
        if (activityTheme.getIsTransactionRelated()) {
            double k = activityTheme.getSendOrDeductValuePerTime() / 3;
            point = new BigDecimal(k).setScale(1, BigDecimal.ROUND_UP).doubleValue();
            requestDto.setPoint(point);
        } else {
            requestDto.setPoint(activityTheme.getSendOrDeductValuePerTime());
        }
        //判断积分生效时间
        if ("NEXT_DAY".equals(activityTheme.getSendPointTimeliness())) {
            requestDto.setEffectTime(DateHelper.getDateTime());
        }
        requestDto.setExpiredTime(DateHelper.getLastDayOfMonth());

        requestDto.setDescription(request.get("scene").toString() + "#" + activityTheme.getTask());
        requestDto.setChannelType(request.get("channelType").toString());
        requestDto.setMemberType(request.get("memberType").toString());
        requestDto.setKZZD1(request.get("changeSourceDetail").toString());
        requestDto.setKZZD2(request.get("changeSource").toString());

        //积分发放类型
        if ("SEND_POINT".equals(activityTheme.getRuleType())) {
            String lockKey = ChannelTypeEnum.KO_MP.getValue() + requestDto.getMemberId();
            log.info("初始化分布式锁:{}", lockKey);
            Lock lock = redisCache.getLock(lockKey);
            if (lock.tryLock()) {
                try {
                    requestDto.setChangeType("SEND");
                    ResponseResult result = iCepMemberService.updatePoint(requestDto, request.get("id").toString(), request.get("scene").toString(), request.get("channelType").toString());
                    //更新会员活动次数记录
                    log.info("积分发放会员memberId:{}", request.get("memberId"),JSON.toJSONString(result));
                } catch (Exception e) {
                    log.error("规则场景变更会员积分(瓶子)BusinessId:{},msg:{}",request.get("id"), e.getMessage());
                } finally {
                    //释放锁
                    lock.unlock();
                }
            }else {
                log.warn("规则场景变更会员积分抢锁失败:{}", request.get("id"));
            }
        }
        res.put("code","200");
        res.put("id",request.get("id").toString());
        return res;
    }
}
