package com.shuyun.kylin.customized.base.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

import java.io.IOException;
import java.net.URLEncoder;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @date 2020/3/3 下午3:41
 * @version V1.0
 */
@Slf4j
public class ApiSignUtils {
    public static final String X_API_KEY = "x_api_key";
    public static final String X_API_TIMESTAMP = "x_api_timestamp";
    public static final String X_API_SIGN = "x_api_sign";

    public static boolean checkSign(Map<String, String> parameters, String secret) {
        String remoteSign = parameters.get(X_API_SIGN);
        String localSign = generateSign(signParams(parameters), secret);
        return localSign.equalsIgnoreCase(remoteSign);
    }

    public static boolean checkTimestamp(String timestamp, int minutes) {
        try {
            if (timestamp != null) {
                long remote = Long.parseLong(timestamp);
                long local = Instant.now().toEpochMilli();
                return local - remote <= (long) (minutes * 60) * 1000L;
            } else {
                return false;
            }
        } catch (NumberFormatException e) {
            log.error("parse timestamp error. exception: {}, timestamp: {}", e.getMessage(), timestamp);
            return false;
        }
    }

    public static Map<String, String> signParams(Map<String, String> parameters) {
        return parameters.entrySet().stream()
                .filter(e -> !X_API_SIGN.equals(e.getKey()))
                .collect(Collectors.toMap(e -> e.getKey(), e -> e.getValue()));
    }

    public static String generateSign(Map<String, String> parameters, String secret) {
        String[] keys = parameters.keySet().toArray(new String[0]);
        Arrays.sort(keys);

        StringBuilder buffer = new StringBuilder().append(secret);
        for (int i = 0; i < keys.length; ++i) {
            buffer.append(keys[i]).append(parameters.get(keys[i]));
        }
        buffer.append(secret);

        return DigestUtils.md5Hex(buffer.toString());
    }

    public static final String buildUrl(String baseUrl, Map<String, String> params, String apiKey, String apiSecret) throws IOException {
        params.put(X_API_KEY, apiKey);
        params.put(X_API_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        return buildUrl(baseUrl, params, apiSecret);
    }

    public static final String buildUrl(String baseUrl, Map<String, String> params, String apiSecret) throws IOException {
        params.put(X_API_SIGN, generateSign(params, apiSecret));
        String query = buildQuery(params, "UTF-8");
        return buildRequestUrl(baseUrl, query);
    }

    public static final String buildUrl(String baseUrl, Map<String, String> params) throws IOException {
        String query = buildQuery(params, "UTF-8");
        return buildRequestUrl(baseUrl, query);
    }

    public static String buildQuery(Map<String, String> params, String charset) throws IOException {
        if (params != null && !params.isEmpty()) {
            StringBuilder query = new StringBuilder();
            Set<Map.Entry<String, String>> entries = params.entrySet();
            boolean hasParam = false;
            Iterator i = entries.iterator();
            while(i.hasNext()) {
                Map.Entry<String, String> entry = (Map.Entry)i.next();
                String name = entry.getKey();
                String value = entry.getValue();
                if (name != null && !name.isEmpty() && value != null && !value.isEmpty()) {
                    if (hasParam) {
                        query.append("&");
                    } else {
                        hasParam = true;
                    }
                    query.append(name).append("=").append(URLEncoder.encode(value, charset));
                }
            }
            return query.toString();
        } else {
            return null;
        }
    }

    public static String buildRequestUrl(String url, String... queries) {
        if (queries != null && queries.length != 0) {
            StringBuilder newUrl = new StringBuilder(url);
            boolean hasQuery = url.contains("?");
            boolean hasPrepend = url.endsWith("?") || url.endsWith("&");
            String[] arr = queries;
            int len = queries.length;
            for(int i = 0; i < len; ++i) {
                String query = arr[i];
                if (query != null && !query.isEmpty()) {
                    if (!hasPrepend) {
                        if (hasQuery) {
                            newUrl.append("&");
                        } else {
                            newUrl.append("?");
                            hasQuery = true;
                        }
                    }
                    newUrl.append(query);
                    hasPrepend = false;
                }
            }
            return newUrl.toString();
        } else {
            return url;
        }
    }
}
