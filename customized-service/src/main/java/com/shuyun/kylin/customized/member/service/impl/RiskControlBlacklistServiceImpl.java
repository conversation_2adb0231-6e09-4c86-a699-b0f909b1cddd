package com.shuyun.kylin.customized.member.service.impl;

import com.alibaba.fastjson.JSON;
import com.shuyun.dm.api.response.DMLResponse;
import com.shuyun.kylin.crm.openapi.core.dto.common.PointDto;
import com.shuyun.kylin.crm.openapi.core.dto.common.PointFreezeRequestDto;
import com.shuyun.kylin.crm.openapi.sdk.client.common.IMemberClient;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.common.ResponsesVo;
import com.shuyun.kylin.customized.base.enums.ResponseCodeEnum;
import com.shuyun.kylin.customized.base.feign.client.MemberChecklistClient;
import com.shuyun.kylin.customized.base.feign.dao.ChecklistDao;
import com.shuyun.kylin.customized.base.feign.dao.ChecklistDto;
import com.shuyun.kylin.customized.base.stream.kafka.messaging.KafkaSink;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.base.util.DateHelper;
import com.shuyun.kylin.customized.member.dm.CampaignPointsRuleRepository;
import com.shuyun.kylin.customized.member.dm.MemberRepository;
import com.shuyun.kylin.customized.member.dto.*;
import com.shuyun.kylin.customized.member.service.CepMemberCouponService;
import com.shuyun.kylin.customized.member.service.IRiskControlBlacklistService;
import com.shuyun.mc.ds.suql.utils.MCDataFactoryUtil;
import com.shuyun.pip.component.json.JsonUtils;
import com.shuyun.pip.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class RiskControlBlacklistServiceImpl implements IRiskControlBlacklistService {
    @Autowired
    private IMemberClient memberClient;
    @Autowired
    private MemberRepository memberRepository;
    @Autowired
    private MemberChecklistClient memberChecklistClient;
    @Autowired
    private CepMemberCouponServiceImpl cepMemberCouponServiceImpl;

    @Autowired
    private CampaignPointsRuleRepository campaignPointsRuleRepository;

    @Override
    public ResponsesVo freezePoint(PointFreezeDto request){
        String memberId = request.getMemberId();
        if (StringUtils.isBlank(memberId)) {
            return new ResponsesVo(30036, "会员ID不能为空");
        }
        //查询可用积分
        PointDto pointDto = memberClient.getPointInfo(memberId,null,"KO",null,null);
        double point = pointDto.getPoint();
        log.info("查询出来的可用积分:{}", point);
        if(point > 0){
            PointFreezeRequestDto pointFreezeRequestDto = new PointFreezeRequestDto();
            pointFreezeRequestDto.setPoint(point);
            pointFreezeRequestDto.setChannelType("loyalty");
            pointFreezeRequestDto.setDescription("黑名单积分冻结");
            pointFreezeRequestDto.setMemberId(request.getMemberId());
            pointFreezeRequestDto.setMemberType("KO");
            String businessId = UUID.randomUUID().toString().replaceAll("-", "");
            try {
                log.info("内部积分积分冻结接口入参:{}", pointFreezeRequestDto+"    业务编码唯一表示"+businessId);
                memberClient.freezePoint(pointFreezeRequestDto,businessId);
                log.info("冻结积分开始更新member表blackistFRozenPoint字段:{}", "会员id："+memberId+" 冻结积分值："+point);
                //更新会员表blackistFRozenPoint
                Map<String, Object> parameters = new HashMap<>();
                parameters.put("memberId", memberId);
                parameters.put("memberType", "KO");
                parameters.put("blacklistFrozenPoint", point);
                MCDataFactoryUtil.INSTANCE.getDataapiSdk().update(ModelConstants.Member, (String)parameters.get("memberId"), parameters, false);
               // memberRepository.updateMemberDto(memberId, parameters);
                log.info("冻结积分更新member表blackistFRozenPoint字段结束");
            } catch (Exception e) {
                log.error("冻结积分异常错误。。。。。。。。。:{}",e.getMessage());
                return new ResponsesVo(500, "冻结积分异常错误");
            }
        }
        return new ResponsesVo(200, "积分冻结成功");
    }

    @Override
    public ResponsesVo unfreezePoint(PointFreezeDto request){
        String memberId = request.getMemberId();
        if (StringUtils.isBlank(memberId)) {
            return new ResponsesVo(30036, "会员ID不能为空");
        }
        //查询被冻结的积分
        List<String> traceIdList = memberRepository.queryfreezePointTraceId(memberId);
        log.info("解冻积分查询出来被冻结的所有积分:{}", traceIdList);
        if(traceIdList.size() > 0){
            try {
                for(String value: traceIdList){
                    if(StringUtils.isNotBlank(value)){
                        PointFreezeRequestDto pointFreezeRequestDto = new PointFreezeRequestDto();
                        pointFreezeRequestDto.setChannelType("loyalty");
                        pointFreezeRequestDto.setDescription("黑名单积分解冻");
                        pointFreezeRequestDto.setMemberId(request.getMemberId());
                        pointFreezeRequestDto.setMemberType("KO");
                        log.info("解冻积分内部接口入参:{}", pointFreezeRequestDto.toString()+"    业务编码唯一表示"+value);
                        memberClient.unfreezePoint(pointFreezeRequestDto,value);
                    }
                }
            } catch (Exception e) {
                log.error("解冻积分异常错误。。。。。。。。。:{}",e.getMessage());
                return new ResponsesVo(500, "解冻积分异常错误");
            }
        }
        log.info("解冻积分开始更新member表blackistFRozenPoint字段:{}", "会员id："+memberId);
        //更新会员表blackistFRozenPoint
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("blacklistFrozenPoint", 0);
        parameters.put("memberId", memberId);
        parameters.put("memberType", "KO");
        MCDataFactoryUtil.INSTANCE.getDataapiSdk().update(ModelConstants.Member, (String)parameters.get("memberId"), parameters, false);
        log.info("解冻积分更新member表blackistFRozenPoint字段结束");
        return new ResponsesVo(200, "积分解冻成功");
    }
    @StreamListener(KafkaSink.KAFKA_INPUT_ADD_CHECKLIST)
    public void addCheckList1(String jsonStr){
        JSONObject dataObject = JSONObject.parseObject(jsonStr);
        log.info("消费Kafka Topic：KafkaTopic:KAFKA_INPUT_ADD_CHECKLIST =======> 入参:{}", dataObject);
        String memberId=dataObject.getString("memberId");

        String sendPoint = memberRepository.queryPointReord(memberId,"SEND");
        log.info("单日累计积分达15个及以上sendPoint:{}", sendPoint);
        Map<String,String> valueMap = memberRepository.queryThreshold();
        log.info("查询出来的阈值:{}", valueMap.toString());
        String value = valueMap.get("dailyPointsThreshold");
        String reason = "单日累计积分达"+value+"个及以上";
        log.info("单日累计积分阈值:{}", reason);
        //每日累计积分达15个及以上    累计不足扣减值达20分及以上
        if(Double.parseDouble(sendPoint) >= Double.parseDouble(value)){
                HashMap<String, Object> insertMap = new HashMap<String, Object>();
                insertMap.put("id",memberId);
                insertMap.put("joinTime",DateHelper.getZone(DateHelper.getDateTimeFormat()));
                insertMap.put("reason",reason);
                insertMap.put("lastSync", DateHelper.getZone(DateHelper.getDateTimeFormat()));
                insertMap.put("operator","Root");
                insertMap.put("source","RULE");
                insertMap.put("created", DateHelper.getZone(DateHelper.getDateTimeFormat()));
                HashMap<Object, Object> member = new HashMap<>();
                member.put("id", memberId);
                insertMap.put("member", member);
                try {
                    log.info("kaishi 加入模型预警名单:{}", insertMap.toString());
                    MCDataFactoryUtil.INSTANCE.getDataapiSdk().upsert(ModelConstants.WARNING_LIST, (String)insertMap.get("id"), insertMap, false);
                    log.info(" 加入模型警名单结束");
                } catch (Exception e) {
                    e.printStackTrace();
                }
        }
    }
    @StreamListener(KafkaSink.KAFKA_INPUT_BEYOND_POINT_ADD_CHECKLIST)
    public void addCheckList(String jsonStr){
        JSONObject dataObject = JSONObject.parseObject(jsonStr);
        log.info("消费Kafka Topic：KafkaTopic:KAFKA_INPUT_BEYOND_POINT_ADD_CHECKLIST =======> 入参:{}", dataObject);
        String memberId=dataObject.getString("memberId");
        String total = memberRepository.queryByondPointReord(memberId);
        log.info("查询不足扣减的次数:{}", total);
        String diffPoint = memberRepository.queryByondPointCount(memberId);
        log.info("累计扣减值不足积分:{}", diffPoint);
        Map<String,String> valueMap = memberRepository.queryThreshold();
        log.info("查询出来的阈值:{}", valueMap.toString());
        //扣减次数
        String valueNum = valueMap.get("timesThreshold");
        //累计扣减积分
        String valueTotal = valueMap.get("refundPointsThreshold");
        String reason = "扣减达"+valueNum+"次及以上或者减值达"+valueTotal+"分及以上";
        log.info("黑名单阈值:{}", reason);
        //扣减达2次及以上或者减值达20分及以上
        if(Double.parseDouble(total) >= Double.parseDouble(valueNum) || Double.parseDouble(diffPoint) >= Double.parseDouble(valueTotal)){
            String occId = memberRepository.getMemberByOccId(memberId);
            //查询此会员是否存在黑名单
           /* ChecklistDto getchecklist = new ChecklistDto();
            getchecklist.setChecklistType("BLACK");
            getchecklist.setCustomer(memberId);
            getchecklist.setFqn("data.prctvmkt.KO.Member");*/
            ChecklistDao checklistDao = memberChecklistClient.getMemberChenklist("BLACK","data.prctvmkt.KO.Member","rvvbg_fengkongheimingdan",memberId);
            log.info("消费Kafka Topic：KafkaTopic:KAFKA_INPUT_BEYOND_POINT_ADD_CHECKLIST =======> 此会员是否已存在黑名单:{}", checklistDao);
            if(checklistDao == null){
                //会员memberId记录黑名单
                ChecklistDto checklistDto = new ChecklistDto();
                checklistDto.setChecklistType("BLACK");
                checklistDto.setCustomer(memberId);
                checklistDto.setFqn("data.prctvmkt.KO.Member");
                checklistDto.setGroupId(ConfigurationCenterUtil.MEMBER_BLACK_GROUPID);
                checklistDto.setRemark(reason);

                ChecklistDto checklistDto1 = new ChecklistDto();
                checklistDto1.setChecklistType("BLACK");
                checklistDto1.setCustomer(occId);
                checklistDto1.setFqn("data.prctvmkt.KO.Member");
                checklistDto1.setGroupId(ConfigurationCenterUtil.MEMBER_BLACK_GROUPID);
                checklistDto1.setRemark(reason);
                try {
                     memberChecklistClient.memberChenklist(checklistDto);
                    //加入occid黑名单
                     memberChecklistClient.memberChenklist(checklistDto1);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                HashMap<String, Object> insertMap = new HashMap<String, Object>();
                insertMap.put("id",memberId);
                insertMap.put("joinTime",DateHelper.getZone(DateHelper.getDateTimeFormat()));
                insertMap.put("reason",reason);
                insertMap.put("lastSync", DateHelper.getZone(DateHelper.getDateTimeFormat()));
                insertMap.put("status", "BLACKLIST_ONLY");
                insertMap.put("operator","Root");
                insertMap.put("source","RULE");
                insertMap.put("created", DateHelper.getZone(DateHelper.getDateTimeFormat()));
                HashMap<Object, Object> member = new HashMap<>();
                member.put("id", memberId);
                insertMap.put("member", member);
                try {
                    log.info("kaishi 加入模型黑名单:{}", insertMap.toString());
                    MCDataFactoryUtil.INSTANCE.getDataapiSdk().upsert(ModelConstants.BLACKILST, (String)insertMap.get("id"), insertMap, false);
                    log.info(" 加入模型黑名单结束");
                    //更新会员表blackistFRozenPoint
                    double blacklistFrozenPoint = memberRepository.queryBlacklistFrozenPoint(memberId);
                    Map<String, Object> parameters = new HashMap<>();
                    parameters.put("blacklistFrozenPoint", blacklistFrozenPoint);
                    parameters.put("memberId", memberId);
                    parameters.put("memberType", "KO");
                    MCDataFactoryUtil.INSTANCE.getDataapiSdk().update(ModelConstants.Member, (String)parameters.get("memberId"), parameters, false);
                    String openId = memberRepository.queryMemberOpneId(memberId);
                    if (StringUtils.isNotBlank(openId)) {
                        List listBody = new ArrayList();
                        CepriskRecordDto cepriskRecord = new CepriskRecordDto();
                        cepriskRecord.setBehavior("LOTTERY_DRAW");
                        cepriskRecord.setBehavior_description(reason);
                        cepriskRecord.setChannel("CRM");
                        cepriskRecord.setSubChannel("CRM");
                        cepriskRecord.setIdentityRuleType("FREQUENCY");
                        cepriskRecord.setUid(openId);
                        cepriskRecord.setAccountType("OPEN_ID");
                        //cepriskRecord.setUserIP(memberId);
                        cepriskRecord.setLevel(10);
                        listBody.add(cepriskRecord);
                        log.info("封装发送第三方对象:{}", JSON.toJSONString(listBody));
                        ResponseResult responseResult = cepMemberCouponServiceImpl.cepriskRecordUpdate(JSON.toJSONString(listBody),ConfigurationCenterUtil.CEP_RISKRECORD_URL);
                        log.info("同步第三方风控中心返回结果:{}", JSON.toJSONString(responseResult));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

    }
    @Override
    public ResponsesVo addCheckList2(BlacListRequestDto request){
        String memberId = request.getMemberId();
        List<String> memberList = request.getMemberIdList();
        if(StringUtils.isBlank(memberId) && memberList.size() == 0){
            return new ResponsesVo(10003, "请选择会员信息");
        }
        List listBody = new ArrayList();
        if(StringUtils.isNotBlank(memberId)){
            ChecklistDao checklistDao = memberChecklistClient.getMemberChenklist("BLACK","data.prctvmkt.KO.Member","rvvbg_fengkongheimingdan",request.getMemberId());
            log.info(" 是否加入模型黑名单:{}",JsonUtils.toJson(checklistDao));
            String id = memberRepository.queryBlackList(request.getMemberId());
            log.info(" 是否加入风控黑名单:{}",id);
            if(checklistDao == null || StringUtils.isBlank(id)){
                String occId = memberRepository.queryOccId(request.getMemberId());
                //会员memberId记录黑名单
                ChecklistDto checklistDto = new ChecklistDto();
                checklistDto.setChecklistType("BLACK");
                checklistDto.setCustomer(request.getMemberId());
                checklistDto.setFqn("data.prctvmkt.KO.Member");
                checklistDto.setGroupId(ConfigurationCenterUtil.MEMBER_BLACK_GROUPID);
                checklistDto.setRemark(request.getReason());

                ChecklistDto checklistDto1 = new ChecklistDto();
                checklistDto1.setChecklistType("BLACK");
                checklistDto1.setCustomer(occId);
                checklistDto1.setFqn("data.prctvmkt.KO.Member");
                checklistDto1.setGroupId(ConfigurationCenterUtil.MEMBER_BLACK_GROUPID);
                checklistDto1.setRemark(request.getReason());
                try {
                    memberChecklistClient.memberChenklist(checklistDto);
                    memberChecklistClient.memberChenklist(checklistDto1);
                } catch (Exception e) {
                    return new ResponsesVo(500, "加入黑名单异常错误");
                }
                HashMap<String, Object> insertMap = new HashMap<String, Object>();
                insertMap.put("id",request.getMemberId());
                insertMap.put("joinTime", DateHelper.getZone(DateHelper.getDateTimeFormat()));
                insertMap.put("reason",request.getReason() );
                insertMap.put("lastSync", DateHelper.getZone(DateHelper.getDateTimeFormat()));
                insertMap.put("status", "BLACKLIST_ONLY");
                insertMap.put("operator",request.getOperator());
                insertMap.put("source",request.getSource());
                insertMap.put("created",DateHelper.getZone(DateHelper.getDateTimeFormat()));
                HashMap<Object, Object> member = new HashMap<>();
                member.put("id", request.getMemberId());
                insertMap.put("member", member);
                try{
                    MCDataFactoryUtil.INSTANCE.getDataapiSdk().upsert(ModelConstants.BLACKILST, (String)insertMap.get("id"), insertMap, false);
                    //更新会员表blackistFRozenPoint   加入黑名单的会员如果没有冻结积分则默认设置为0
                    double blacklistFrozenPoint = memberRepository.queryBlacklistFrozenPoint(memberId);
                    log.info(" 冻结积分则默认设置:{}",blacklistFrozenPoint);
                    Map<String, Object> parameters = new HashMap<>();
                    parameters.put("blacklistFrozenPoint", blacklistFrozenPoint);
                    parameters.put("memberId", memberId);
                    parameters.put("memberType", "KO");
                    MCDataFactoryUtil.INSTANCE.getDataapiSdk().update(ModelConstants.Member, (String)parameters.get("memberId"), parameters, false);
                    //删除预警名单信息
                    Map<String, Object> filter = new HashMap<>();
                    filter.put("member", memberId);
                    campaignPointsRuleRepository.deleteByFilter(ModelConstants.WARNING_LIST, filter);
                   MCDataFactoryUtil.INSTANCE.getDataapiSdk().update(ModelConstants.Member, (String)parameters.get("memberId"), parameters, false);
                   String openId = memberRepository.queryMemberOpneId(memberId);
                    if (StringUtils.isNotBlank(openId)) {
                        CepriskRecordDto cepriskRecord = new CepriskRecordDto();
                        cepriskRecord.setBehavior("LOTTERY_DRAW");
                        cepriskRecord.setBehavior_description(request.getReason());
                        cepriskRecord.setChannel("CRM");
                        cepriskRecord.setSubChannel("CRM");
                        cepriskRecord.setIdentityRuleType("MANUAL");
                        cepriskRecord.setUid(openId);
                        cepriskRecord.setAccountType("OPEN_ID");
                        //cepriskRecord.setUserIP(memberId);
                        cepriskRecord.setLevel(10);
                        listBody.add(cepriskRecord);
                    }
                }catch (Exception e) {
                    log.info(" 加入黑名单异常:{}",e.getMessage());
                   // return new ResponsesVo(500, "加入黑名单异常错误");
                }
                try {
                    log.info("封装发送第三方对象:{}", JSON.toJSONString(listBody));
                    ResponseResult responseResult = cepMemberCouponServiceImpl.cepriskRecordUpdate(JSON.toJSONString(listBody),ConfigurationCenterUtil.CEP_RISKRECORD_URL);
                    log.info("同步手动加入黑名单第三方风控中心返回结果:{}", JSON.toJSONString(responseResult));
                } catch (Exception e) {
                    log.error("同步手动加入黑名单第三方风控中心返回异常错误。。。。。。。。。:{}",e.getMessage());
                    //return new ResponsesVo(500, "同步手动加入黑名单第三方风控中心异常");
                }
                return new ResponsesVo(200, "加入黑名单成功");
            }else{
                return new ResponsesVo(300, "该会员已加入过黑名单");
            }
        }
        if(memberList.size() > 0){
            for(int i=0;i<memberList.size();i++){
                String memberLis = memberList.get(i);
                ChecklistDao checklistDao = memberChecklistClient.getMemberChenklist("BLACK","data.prctvmkt.KO.Member","rvvbg_fengkongheimingdan",memberLis);
                log.info(" 是否加入模型黑名单:{}",JsonUtils.toJson(checklistDao));
                String id = memberRepository.queryBlackList(memberLis);
                log.info(" 是否加入风控黑名单:{}",id);
                log.info(" 加入风控黑名单会员id:{}",memberLis);
                if(checklistDao == null || StringUtils.isBlank(id)){
                    String occId = memberRepository.queryOccId(memberLis);
                    //会员memberId记录黑名单
                    ChecklistDto checklistDto = new ChecklistDto();
                    checklistDto.setChecklistType("BLACK");
                    checklistDto.setCustomer(memberLis);
                    checklistDto.setFqn("data.prctvmkt.KO.Member");
                    checklistDto.setGroupId(ConfigurationCenterUtil.MEMBER_BLACK_GROUPID);
                    checklistDto.setRemark(request.getReason());

                    ChecklistDto checklistDto1 = new ChecklistDto();
                    checklistDto1.setChecklistType("BLACK");
                    checklistDto1.setCustomer(occId);
                    checklistDto1.setFqn("data.prctvmkt.KO.Member");
                    checklistDto1.setGroupId(ConfigurationCenterUtil.MEMBER_BLACK_GROUPID);
                    checklistDto1.setRemark(request.getReason());
                    try {
                        memberChecklistClient.memberChenklist(checklistDto);
                        memberChecklistClient.memberChenklist(checklistDto1);
                    } catch (Exception e) {
                        return new ResponsesVo(500, "加入黑名单异常错误");
                    }
                    HashMap<String, Object> insertMap = new HashMap<String, Object>();
                    insertMap.put("id",memberLis);
                    insertMap.put("joinTime", DateHelper.getZone(DateHelper.getDateTimeFormat()));
                    insertMap.put("reason",request.getReason() );
                    insertMap.put("lastSync", DateHelper.getZone(DateHelper.getDateTimeFormat()));
                    insertMap.put("status", "BLACKLIST_ONLY");
                    insertMap.put("operator",request.getOperator());
                    insertMap.put("source",request.getSource());
                    insertMap.put("created",DateHelper.getZone(DateHelper.getDateTimeFormat()));
                    HashMap<Object, Object> member = new HashMap<>();
                    member.put("id", memberLis);
                    insertMap.put("member", member);
                    try{
                        MCDataFactoryUtil.INSTANCE.getDataapiSdk().upsert(ModelConstants.BLACKILST, (String)insertMap.get("id"), insertMap, false);
                        //更新会员表blackistFRozenPoint   加入黑名单的会员默认设置为0
                        double blacklistFrozenPoint = memberRepository.queryBlacklistFrozenPoint(memberLis);
                        Map<String, Object> parameters = new HashMap<>();
                        parameters.put("blacklistFrozenPoint", blacklistFrozenPoint);
                        parameters.put("memberId", memberLis);
                        parameters.put("memberType", "KO");
                        MCDataFactoryUtil.INSTANCE.getDataapiSdk().update(ModelConstants.Member, (String)parameters.get("memberId"), parameters, false);
                        //删除预警名单信息
                        Map<String, Object> filter = new HashMap<>();
                        filter.put("member", memberLis);
                        campaignPointsRuleRepository.deleteByFilter(ModelConstants.WARNING_LIST, filter);
                        MCDataFactoryUtil.INSTANCE.getDataapiSdk().update(ModelConstants.Member, (String)parameters.get("memberId"), parameters, false);
                        String openId = memberRepository.queryMemberOpneId(memberLis);
                        if (StringUtils.isNotBlank(openId)) {
                            CepriskRecordDto cepriskRecord = new CepriskRecordDto();
                            cepriskRecord.setBehavior("LOTTERY_DRAW");
                            cepriskRecord.setBehavior_description(request.getReason());
                            cepriskRecord.setChannel("CRM");
                            cepriskRecord.setSubChannel("CRM");
                            cepriskRecord.setIdentityRuleType("MANUAL");
                            cepriskRecord.setUid(openId);
                            cepriskRecord.setAccountType("OPEN_ID");
                            //cepriskRecord.setUserIP(memberLis);
                            cepriskRecord.setLevel(10);
                            listBody.add(cepriskRecord);
                           /* log.info("封装发送第三方对象:{}", JSON.toJSONString(cepriskRecord));
                            ResponseResult responseResult = cepMemberCouponServiceImpl.cepriskRecordUpdate(JSON.toJSONString(cepriskRecord),ConfigurationCenterUtil.CEP_RISKRECORD_URL);
                            log.info("同步批量手动加入黑名单第三方风控中心返回结果:{}", JSON.toJSONString(responseResult));*/
                        }
                    }catch (Exception e) {
                        return new ResponsesVo(500, "加入黑名单异常错误");
                    }
                }else{
                    return new ResponsesVo(300, memberLis+"会员已加入过黑名单");
                }
            }
            try {
                log.info("封装发送第三方对象:{}", JSON.toJSONString(listBody));
                ResponseResult responseResult = cepMemberCouponServiceImpl.cepriskRecordUpdate(JSON.toJSONString(listBody),ConfigurationCenterUtil.CEP_RISKRECORD_URL);
                log.info("同步手动加入黑名单第三方风控中心返回结果:{}", JSON.toJSONString(responseResult));
            } catch (Exception e) {
                log.error("同步手动加入黑名单第三方风控中心返回异常错误。。。。。。。。。:{}",e.getMessage());
               // return new ResponsesVo(500, "同步手动加入黑名单第三方风控中心异常");
            }
            return new ResponsesVo(200, "加入黑名单成功");
        }
        return new ResponsesVo(200, "加入黑名单成功");
    }

    @Override
    public ResponsesVo freezeMember(BlacListRequestDto request){
        /*//先删除黑名单分组再加入冻结账户分组
        ChecklistDto deleteChecklistDto = new ChecklistDto();
        deleteChecklistDto.setChecklistType("BLACK");
        deleteChecklistDto.setCustomer(memberId);
        deleteChecklistDto.setFqn("data.prctvmkt.KO.Member");
            try {
            memberChecklistClient.memberChenklist(deleteChecklistDto);
        } catch (Exception e) {
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, ResponseCodeEnum.OPENAPI_FAILED.getDesc());
        }*/
        String reson = request.getReason();
        ChecklistDto checklistDto = new ChecklistDto();
        checklistDto.setChecklistType("BLACK");
        checklistDto.setCustomer(request.getMemberId());
        checklistDto.setFqn("data.prctvmkt.KO.Member");
        checklistDto.setGroupId(ConfigurationCenterUtil.MEMBER_FROZEN_GROUPID);
        /*if (StringUtils.isNotBlank(reson)) {
            checklistDto.setRemark(reson);
        }else{
            checklistDto.setRemark("账户冻结");
        }*/

        String occId = memberRepository.queryOccId(request.getMemberId());
        ChecklistDto occIdChecklistDto = new ChecklistDto();
        occIdChecklistDto.setChecklistType("BLACK");
        occIdChecklistDto.setCustomer(occId);
        occIdChecklistDto.setFqn("data.prctvmkt.KO.Member");
        occIdChecklistDto.setGroupId(ConfigurationCenterUtil.MEMBER_FROZEN_GROUPID);
       /* if (StringUtils.isNotBlank(reson)) {
            occIdChecklistDto.setRemark(reson);
        }else{
            occIdChecklistDto.setRemark("账户冻结");
        }*/

        try {
            log.info("账户冻结memberId入参:{}", checklistDto);
            log.info("账户冻结occId入参:{}", checklistDto);
            memberChecklistClient.memberChenklist(checklistDto);
            memberChecklistClient.memberChenklist(occIdChecklistDto);
        } catch (Exception e) {
            log.error("冻结账户异常错误。。。。。。。。。:{}",e.getMessage());
            return new ResponsesVo(500, "冻结账户异常错误");
        }
        //修改业务模型黑名单状态
        HashMap<String, Object> insertMap = new HashMap<String, Object>();
        insertMap.put("id",request.getMemberId());
        //insertMap.put("joinTime", DateHelper.getZone(DateHelper.getDateTimeFormat()));
        /*if (StringUtils.isNotBlank(reson)) {
            insertMap.put("reason",reson );
        }else{
            insertMap.put("reason","从黑名单改为冻结账户状态" );
        }*/
        insertMap.put("lastSync", DateHelper.getZone(DateHelper.getDateTimeFormat()));
        insertMap.put("status", "ACCOUNT_FROZEN");
        insertMap.put("operator",request.getOperator());
        insertMap.put("source",request.getSource());
        //insertMap.put("created", DateHelper.getZone(DateHelper.getDateTimeFormat()));
        try {
            MCDataFactoryUtil.INSTANCE.getDataapiSdk().upsert(ModelConstants.BLACKILST, (String)insertMap.get("id"), insertMap, false);
            String openId = memberRepository.queryMemberOpneId(request.getMemberId());
            if (StringUtils.isNotBlank(openId)) {
                List listBody = new ArrayList();
                CepriskRecordDto cepriskRecord = new CepriskRecordDto();
                cepriskRecord.setBehavior("LOTTERY_DRAW");
                cepriskRecord.setBehavior_description("从黑名单改为冻结账户状态");
                cepriskRecord.setChannel("CRM");
                cepriskRecord.setSubChannel("CRM");
                cepriskRecord.setIdentityRuleType("MANUAL");
                cepriskRecord.setUid(openId);
                cepriskRecord.setAccountType("OPEN_ID");
                //cepriskRecord.setUserIP(request.getMemberId());
                cepriskRecord.setLevel(20);
                listBody.add(cepriskRecord);
                log.info("封装发送第三方对象:{}", JSON.toJSONString(listBody));
                ResponseResult responseResult = cepMemberCouponServiceImpl.cepriskRecordUpdate(JSON.toJSONString(listBody),ConfigurationCenterUtil.CEP_RISKRECORD_URL);
                log.info("同步账户冻结第三方风控中心返回结果:{}", JSON.toJSONString(responseResult));
            }
        } catch (Exception e) {
            log.error("冻结账户异常错误。。。。。修改黑名单状态为冻结账户。。。。:{}",e.getMessage());
            //return new ResponsesVo(500, "冻结账户异常错误");
        }
        return new ResponsesVo(200, "账户冻结成功");
    }

    @Override
    public ResponsesVo unfreezeMember(BlacListRequestDto request){
        /*//先删除冻结账户分组再加入黑名单分组
        ChecklistDto deleteChecklistDto = new ChecklistDto();
        deleteChecklistDto.setChecklistType("BLACK");
        deleteChecklistDto.setCustomer(memberId);
        deleteChecklistDto.setFqn("data.prctvmkt.KO.Member");
        try {
            memberChecklistClient.deleteMemberChenklist(deleteChecklistDto);
        } catch (Exception e) {
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, ResponseCodeEnum.OPENAPI_FAILED.getDesc());
        }*/
        String reson = request.getReason();
        ChecklistDto checklistDto = new ChecklistDto();
        checklistDto.setChecklistType("BLACK");
        checklistDto.setCustomer(request.getMemberId());
        checklistDto.setFqn("data.prctvmkt.KO.Member");
        checklistDto.setGroupId(ConfigurationCenterUtil.MEMBER_BLACK_GROUPID);
        /*if (StringUtils.isNotBlank(reson)) {
            checklistDto.setRemark(reson);
        }else{
            checklistDto.setRemark("加入风控黑名单");
        }*/
        String occId = memberRepository.queryOccId(request.getMemberId());
        ChecklistDto occIdChecklistDto = new ChecklistDto();
        occIdChecklistDto.setChecklistType("BLACK");
        occIdChecklistDto.setCustomer(occId);
        occIdChecklistDto.setFqn("data.prctvmkt.KO.Member");
        occIdChecklistDto.setGroupId(ConfigurationCenterUtil.MEMBER_BLACK_GROUPID);
       /* if (StringUtils.isNotBlank(reson)) {
            occIdChecklistDto.setRemark(reson);
        }else{
            occIdChecklistDto.setRemark("加入风控黑名单");
        }
*/
        try {
            memberChecklistClient.memberChenklist(checklistDto);
            memberChecklistClient.memberChenklist(occIdChecklistDto);
        } catch (Exception e) {
            log.error("调接口解冻账户异常错误。。。。。修改冻结账户为黑名单状态。。。。:{}",e.getMessage());
            return new ResponsesVo(500, "解冻账户异常错误");
        }

        //修改业务模型黑名单状态
        HashMap<String, Object> insertMap = new HashMap<String, Object>();
        insertMap.put("id",request.getMemberId());
        //insertMap.put("joinTime", DateHelper.getZone(DateHelper.getDateTimeFormat()));
       /* if (StringUtils.isNotBlank(reson)) {
            insertMap.put("reason",reson );
        }else{
            insertMap.put("reason","解除账户冻结状态" );
        }*/
        insertMap.put("lastSync", DateHelper.getZone(DateHelper.getDateTimeFormat()));
        insertMap.put("status", "BLACKLIST_ONLY");
        insertMap.put("operator",request.getOperator());
        insertMap.put("source",request.getSource());
        //insertMap.put("created", DateHelper.getZone(DateHelper.getDateTimeFormat()));
        try {
            MCDataFactoryUtil.INSTANCE.getDataapiSdk().upsert(ModelConstants.BLACKILST, (String)insertMap.get("id"), insertMap, false);
            String openId = memberRepository.queryMemberOpneId(request.getMemberId());
            if (StringUtils.isNotBlank(openId)) {
                List listBody = new ArrayList();
                CepriskRecordDto cepriskRecord = new CepriskRecordDto();
                cepriskRecord.setBehavior("LOTTERY_DRAW");
                cepriskRecord.setBehavior_description("解除账户冻结状态");
                cepriskRecord.setChannel("CRM");
                cepriskRecord.setSubChannel("CRM");
                cepriskRecord.setIdentityRuleType("MANUAL");
                cepriskRecord.setUid(openId);
                cepriskRecord.setAccountType("OPEN_ID");
                //cepriskRecord.setUserIP(request.getMemberId());
                cepriskRecord.setLevel(10);
                listBody.add(cepriskRecord);
                log.info("封装发送第三方对象:{}", JSON.toJSONString(listBody));
                ResponseResult responseResult = cepMemberCouponServiceImpl.cepriskRecordUpdate(JSON.toJSONString(listBody),ConfigurationCenterUtil.CEP_RISKRECORD_URL);
                log.info("同步账户解冻第三方风控中心返回结果:{}", JSON.toJSONString(responseResult));
            }
        } catch (Exception e) {
            log.error("修改模型解冻账户异常错误。。。。。修改冻结账户为黑名单状态。。。。:{}",e.getMessage());
            //return new ResponsesVo(500, "解冻账户异常错误");
        }
        return new ResponsesVo(200, "账户解冻成功");
    }

    @Override
    public ResponsesVo deleteMember(String memberId) {
        //删除黑名单分组
        ChecklistDto deleteChecklistDto = new ChecklistDto();
        deleteChecklistDto.setChecklistType("BLACK");
        deleteChecklistDto.setCustomer(memberId);
        deleteChecklistDto.setFqn("data.prctvmkt.KO.Member");

        String occId = memberRepository.queryOccId(memberId);

        ChecklistDto deleteOccId = new ChecklistDto();
        deleteOccId.setChecklistType("BLACK");
        deleteOccId.setCustomer(occId);
        deleteOccId.setFqn("data.prctvmkt.KO.Member");
        try {
            //删除memberId黑名单
            memberChecklistClient.deleteMemberChenklist(deleteChecklistDto);
            //删除occId黑名单
            memberChecklistClient.deleteMemberChenklist(deleteOccId);
            //根据id删除data.prctvmkt.KO.Blacklist模型的数据
            Map<String, Object> filter = new HashMap<>();
            filter.put("id", memberId);
            campaignPointsRuleRepository.deleteByFilter(ModelConstants.BLACKILST, filter);
        } catch (Exception e) {
            log.error("删除黑名单异常错误。。。。。。。。。:{}",e.getMessage());
            return new ResponsesVo(500, "删除黑名单异常错误");
        }
        try{
            Map<String, String> memberInfo = memberRepository.queryMemberInfo(memberId);
            if (memberInfo != null) {
                CepAccountsDto cepAccountsDtoopenId = new CepAccountsDto();
                cepAccountsDtoopenId.setAccountType("OPEN_ID");
                cepAccountsDtoopenId.setUid(memberInfo.get("openId"));
                CepAccountsDto cepAccountsDtounionId = new CepAccountsDto();
                cepAccountsDtounionId.setAccountType("UNION_ID");
                cepAccountsDtounionId.setUid(memberInfo.get("unionId"));
                CepAccountsDto cepAccountsDtomobile = new CepAccountsDto();
                cepAccountsDtomobile.setAccountType("PHONE_NUMBER");
                cepAccountsDtomobile.setUid(memberInfo.get("mobile"));
                CepRiskDeletDto cepRiskDelet = new CepRiskDeletDto();
                List<CepAccountsDto> list = new ArrayList();
                list.add(cepAccountsDtoopenId);
                list.add(cepAccountsDtounionId);
                list.add(cepAccountsDtomobile);
                cepRiskDelet.setAccounts(list);
                cepRiskDelet.setChannel("CRM");
                cepRiskDelet.setSubChannel("CRM");
                cepRiskDelet.setReason("手工移除黑名单");
                log.info("封装发送第三方对象:{}", JSON.toJSONString(cepRiskDelet));
                ResponseResult responseResult = cepMemberCouponServiceImpl.cepriskRecordUpdate(JSON.toJSONString(cepRiskDelet), ConfigurationCenterUtil.CEP_RISKRELEASE_URL);
                log.info("同步账户解冻第三方风控中心返回结果:{}", JSON.toJSONString(responseResult));
            }
        }catch (Exception e){
            log.error("删除黑名单异常错误。。。。。。。。。:{}",e.getMessage());
            //return new ResponsesVo(200, "删除黑名单成功");
        }
        return new ResponsesVo(200, "删除黑名单成功");
    }

    @Override
    public ResponsesVo setThreshold(ThresholdDto request){

        HashMap<String, Object> insertMap = new HashMap<String, Object>();
        insertMap.put("id","1");
        insertMap.put("lastSync", DateHelper.getZone(DateHelper.getDateTimeFormat()));
        insertMap.put("created", DateHelper.getZone(DateHelper.getDateTimeFormat()));
        if(StringUtils.isNotBlank(request.getAddTotalType())){
            insertMap.put("dailyPointsThreshold",request.getAddTotalValue());
        }
        if(StringUtils.isNotBlank(request.getDeductNumType())){
            insertMap.put("timesThreshold",request.getDeductNumValue());
        }
        if(StringUtils.isNotBlank(request.getDeductTotalType())){
            insertMap.put("refundPointsThreshold",request.getDeductTotalValue());
        }
        try {
            MCDataFactoryUtil.INSTANCE.getDataapiSdk().upsert(ModelConstants.CONFIG_LIST, (String)insertMap.get("id"), insertMap, false);

        } catch (Exception e) {
            log.error("修改阈值异常:{}",e.getMessage());
            return new ResponsesVo(500, e.getMessage());
        }
        return new ResponsesVo(200, "修改成功");
    }
    @Override
    public ResponsesVo queryWaringMember(String memberId) {
        try{
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("member", memberId);
            String queryMemberSql = " select id from " + ModelConstants.WARNING_LIST + " where  member = :member ";
            List<Map<String, Object>> list = MCDataFactoryUtil.INSTANCE.getDataapiSdk().execute(queryMemberSql,queryMap).getData();
            log.info("预警校验查询sql:{}", queryMemberSql);
            log.info("预警校验查询结果:{}", list.size());
            if(list.size() > 0  ){
                return new ResponsesVo(200, "true");
            }
        }catch (Exception e){
            return new ResponsesVo(500, e.getMessage());
        }
        return new ResponsesVo(200, "false");
    }
}
