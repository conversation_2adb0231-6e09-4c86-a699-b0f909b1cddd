package com.shuyun.kylin.customized.project.callback;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.shuyun.epassport.sdk.enums.ResultCodeEnum;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.enums.ResponseCodeEnum;
import com.shuyun.kylin.customized.project.dto.IQiYiCallbackDto;
import com.shuyun.kylin.customized.project.service.SendCouponService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/iQiYi")
public class IQiYiCallbackController {

    @Resource
    private SendCouponService sendCouponService;

    /**
     * 爱奇艺回调
     *
     * @param retCode 响应码 1代表成功，9代表撤消
     * @param sporderId 发券时传给爱奇艺的businessId
     * @param orderSuccessTime 订单完成时间
     * @param errMsg 失败原因(ret_code为1时，该值为空)
     * @return
     */
    @PostMapping("/sendCouponCallback")
    public ResponseResult<String> sendCouponCallback(@RequestParam(value = "ret_code",required = false) String retCode,
                                                     @RequestParam(value = "sporder_id",required = false) String sporderId,
                                                     @RequestParam(value = "ordersuccesstime",required = false) String orderSuccessTime,
                                                     @RequestParam(value = "err_msg", required = false) String errMsg) {
        if(ObjectUtil.isAllEmpty(retCode, sporderId, orderSuccessTime, errMsg)) {
            return ResponseResult.responseError(ResponseCodeEnum.ILLEGAL_ARGUMENT.getCode(), ResponseCodeEnum.ILLEGAL_ARGUMENT.getMsg(), null);
        }
        IQiYiCallbackDto requestDto = new IQiYiCallbackDto();
        requestDto.setRetCode(retCode);
        requestDto.setSporderId(sporderId);
        requestDto.setOrderSuccessTime(orderSuccessTime);
        requestDto.setErrMsg(errMsg);
        log.info("爱奇艺回调开始，入参requestDto={}", JSON.toJSONString(requestDto));
        ResponseResult<String> r = sendCouponService.iQiYiCallback(requestDto);
        log.info("爱奇艺回调结束，返回结果r={}", JSON.toJSONString(r));
        return r;
    }
}
