package com.shuyun.kylin.customized.behavior.resource.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Schema(name = "InteractiveResponseVO")
public class InteractiveResponseVO {

    private String code;
    private String message;

    public static InteractiveResponseVO buildOf(InteractiveDateRangeCheckCode code) {
        InteractiveResponseVO x = new InteractiveResponseVO();
        x.setCode(code.getCode());
        x.setMessage(code.getMessage());
        return x;
    }

    @Getter
    @AllArgsConstructor
    public enum InteractiveDateRangeCheckCode {
        DATE_IS_NULL("500001", "日期范围不允许为空"),
        DATE_RANGE_INVALID("500002", "日期范围无效"),
        DATE_RANGE_CONFLICT("500003", "与相同类型的活动时间冲突"),
        NO_VALID_ACTIVITY("500004", "当前时段无可用的互动活动"),
        MEMBER_NOT_EXISTS("500005", "传入参数错误，未找到会员信息"),
        SCENE_NOT_EXISTS("500006", "传入参数错误，未找到对应场景信息"),
        DATE_RANGE_INVALID_ENDDATE_BEFORE("500007", "日期范围无效，结束日期不能小于当前日期"),
        CHANNEL_TYPE_NOT_EXISTS("500008", "传入参数错误，未找到对应渠道类型信息"),

        SUCCESS("200", "操作成功");

        private String code;
        private String message;
    }
}
