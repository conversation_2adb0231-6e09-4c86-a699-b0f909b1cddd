package com.shuyun.kylin.customized.member.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.dm.api.response.DMLResponse;
import com.shuyun.kylin.crm.openapi.core.dto.common.PointRequestDto;
import com.shuyun.kylin.crm.openapi.sdk.client.common.IMemberClient;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.enums.ChannelTypeEnum;
import com.shuyun.kylin.customized.base.enums.ResponseCodeEnum;
import com.shuyun.kylin.customized.base.exception.CustomizeException;
import com.shuyun.kylin.customized.base.feign.client.MemberPointModifyClient;
import com.shuyun.kylin.customized.base.feign.dao.MemberPointModifyRequest;
import com.shuyun.kylin.customized.base.util.*;
import com.shuyun.kylin.customized.behavior.resource.InteractionBehaviorResource;
import com.shuyun.kylin.customized.behavior.resource.po.CheckPO;
import com.shuyun.kylin.customized.behavior.resource.vo.CheckResponse;
import com.shuyun.kylin.customized.behavior.resource.vo.CommonApiResponse;
import com.shuyun.kylin.customized.behavior.service.impl.AbstractInteractionSceneResolver;
import com.shuyun.kylin.customized.member.dm.*;
import com.shuyun.kylin.customized.member.dto.*;
import com.shuyun.kylin.customized.member.service.ICampaignPointRuleService;
import com.shuyun.kylin.customized.member.service.ICepDataSyncService;
import com.shuyun.kylin.customized.member.service.ICepMemberService;
import com.shuyun.kylin.customized.swire.dto.DailyLimitedPointsRuleDto;
import com.shuyun.kylin.starter.redis.ICache;
import com.shuyun.pip.component.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.locks.Lock;

import static com.shuyun.kylin.customized.behavior.resource.vo.InteractiveResponseVO.InteractiveDateRangeCheckCode.NO_VALID_ACTIVITY;

@Slf4j
@Service
public class CampaignPointRuleServiceImpl implements ICampaignPointRuleService {

    @Autowired
    private IMemberClient memberClient;

    @Autowired
    private MemberRepository memberRepository;

    @Autowired
    private ICepMemberService iCepMemberService;

    @Autowired
    public MemberPointModifyClient memberPointModifyClient;

    @Autowired
    private ICepDataSyncService iCepDataSyncService;

    @Autowired
    private CampaignPointsRuleRepository campaignPointsRuleRepository;

    @Autowired
    private CepRegulationPointRepository cepRegulationPointRepository;

    @Autowired
    private CepCostCenterRepository cepCostCenterRepository;

    @Autowired
    private CepMemberScenBindingRepository cepMemberScenBindingRepository;

    @Autowired
    private InteractionBehaviorResource interactionBehaviorResource;

    @Autowired
    @Qualifier("redisCache")
    private ICache redisCache;

    private static volatile ExecutorService executorService;

    @Override
    public ResponseResult getCampaignPointsRule(String scene) {
        List<CampaignPointsRuleDto> campaignPointsRuleDtoList = new ArrayList<>();
        log.info("活动积分规则查询入参: {}", scene);
        try {
            if (StringUtils.isBlank(scene)) {
                List<Map<String, Object>> list = campaignPointsRuleRepository.selectCostCenter();
                String listJson = JSON.toJSONString(list);
                campaignPointsRuleDtoList = JSON.parseArray(listJson, CampaignPointsRuleDto.class);
                log.info("活动积分规则查询返回: {}", campaignPointsRuleDtoList);
                if (campaignPointsRuleDtoList == null) {
                    return new ResponseResult(ResponseCodeEnum.RECORD_NOT_FOUND, "没有找到对应记录");
                }
                for (CampaignPointsRuleDto campaignPointsRule : campaignPointsRuleDtoList) {
                    campaignPointsRule.setEndTime(CepMemberServiceImpl.getStartTimeDate(campaignPointsRule.getEndTime()));
                    campaignPointsRule.setStartTime(CepMemberServiceImpl.getStartTimeDate(campaignPointsRule.getStartTime()));
                }
            } else {
                Map<String, Object> filter = new HashMap<>();
                filter.put("scene", scene);
                CampaignPointsRuleDto campaignPointsRuleDto = campaignPointsRuleRepository.queryByFilter(ModelConstants.CAMPAIGN_CAMPAIGNPOINTSRULE, null, filter);
                if (campaignPointsRuleDto != null) {
                    campaignPointsRuleDto.setStartTime(CepMemberServiceImpl.getStartTimeDate(campaignPointsRuleDto.getStartTime()));
                    campaignPointsRuleDto.setEndTime(CepMemberServiceImpl.getStartTimeDate(campaignPointsRuleDto.getEndTime()));
                } else {
                    return new ResponseResult(ResponseCodeEnum.RECORD_NOT_FOUND, "没有找到对应记录");
                }
                log.info("活动积分规则查询返回: {}", campaignPointsRuleDto);

                campaignPointsRuleDtoList.add(campaignPointsRuleDto);
            }
        } catch (Exception e) {
            log.error("活动积分规则查询e: {}", e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, ResponseCodeEnum.OPENAPI_FAILED.getMsg());
        }
        log.info("活动积分规则查询结果: {}", campaignPointsRuleDtoList.size());
        return new ResponseResult(ResponseCodeEnum.SUCCESS, campaignPointsRuleDtoList);
    }

    @Override
    public ResponseResult deletCampaignPointsRule(String scene) {
        if (StringUtils.isBlank(scene)) {
            return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, "场景值不能为空");
        }
        try {
            Map<String, Object> filter = new HashMap<>();
            filter.put("scene", scene);
            CampaignPointsRuleDto campaignPointsRuleDto = campaignPointsRuleRepository.queryByFilter(ModelConstants.CAMPAIGN_CAMPAIGNPOINTSRULE, null, filter);
            if (campaignPointsRuleDto == null) {
                return new ResponseResult(ResponseCodeEnum.RECORD_NOT_FOUND, "场景值不存在");
            }
            campaignPointsRuleRepository.deleteByFilter(ModelConstants.CAMPAIGN_CAMPAIGNPOINTSRULE, filter);
        } catch (Exception e) {
            log.error("活动积分规则删除e:{}", e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, ResponseCodeEnum.OPENAPI_FAILED.getMsg());
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }

    @Override
    public ResponseResult saveCampaignPointsRule(CepRegulationPointDto cepRegulationPointDto) {

        /*if (StringUtils.isNotBlank(cepRegulationPointDto.getSendPointTimeliness())) {
            cepRegulationPointDto.setSendPointTimeliness("立即".equals(cepRegulationPointDto.getSendPointTimeliness()) ? "IMMEDIATELY" : "NEXT_DAY");
        }*/
        if (null == cepRegulationPointDto.getIsTransactionRelated()) {
            return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, "是否和交易相关不能为空");
        }
        //清除把有值的字段直接改成null值 （每天次数，每月次数，总次数）
        if (null == cepRegulationPointDto.getSendTimeLimitPerDay()) {
            memberRepository.queryCampaignNum("sendTimeLimitPerDay", cepRegulationPointDto.getScene());
        }
        if (null == cepRegulationPointDto.getSendTimeLimitPerMonth()) {
            memberRepository.queryCampaignNum("sendTimeLimitPerMonth", cepRegulationPointDto.getScene());
        }
        if (null == cepRegulationPointDto.getSendTimeTotalLimit()) {
            memberRepository.queryCampaignNum("sendTimeTotalLimit", cepRegulationPointDto.getScene());
        }

        if (StringUtils.isNotBlank(cepRegulationPointDto.getRuleType())) {
            cepRegulationPointDto.setRuleType(cepRegulationPointDto.getRuleType());
        }
        if (StringUtils.isBlank(cepRegulationPointDto.getChannelType())){
            cepRegulationPointDto.setChannelType("KO_MP");
        }
        cepRegulationPointDto.setLastSync(DateHelper.getNowZone());
        cepRegulationPointDto.setStartTime(StringUtils.isNotEmpty(cepRegulationPointDto.getStartTime()) ? DateHelper.getZone(cepRegulationPointDto.getStartTime()) : null);
        cepRegulationPointDto.setEndTime(StringUtils.isNotEmpty(cepRegulationPointDto.getEndTime()) ? DateHelper.getZone(cepRegulationPointDto.getEndTime()) : null);
        try {
            DMLResponse response = DataApiUtil.upsertIsData(ModelConstants.CAMPAIGN_POINTSRULE, cepRegulationPointDto.getScene(), JSONObject.parseObject(JSON.toJSONString(cepRegulationPointDto)));
            if (!response.getIsSuccess()) {
                log.error("活动积分规则保存/更新:{}", response.getOperation());
                return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT, response.getOperation());
            }
        } catch (Exception e) {
            log.error("活动积分规则保存/更新e:{}", e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED);
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }

    /**
     * 活动变更积分
     *
     * @param cepMemberDeductionDto
     * @return
     */
    @Override
    public ResponseResult updateMemberPoint(CepMemberDeductionDto cepMemberDeductionDto) {
        //扣减取消   hashMap返回值的封装
        Map<Object, Object> hashMap = new HashMap<>();
        //查询该笔积分是否已发送
        if (!"扣减取消".equals(cepMemberDeductionDto.getChangeSource())) {
            Double changePoints = memberRepository.queryMemberTraceId(cepMemberDeductionDto.getBusinessId());
            if (0.0 != changePoints) {
                hashMap.put("changePoint", changePoints);
                return new ResponseResult(ResponseCodeEnum.SUCCESS, hashMap);
            }
        }
        //查询会员
        String memberId = memberRepository.queryMemberId(cepMemberDeductionDto.getCustomerNo());
        if (StringUtils.isBlank(memberId)) {
            return new ResponseResult(ResponseCodeEnum.MEMBER_FAILED);
        }
        //查询场景活动
        CepRegulationPointDto activityTheme = cepRegulationPointRepository.getActivityTheme(cepMemberDeductionDto.getScene());
        //判断活动是否存在
        if (null == activityTheme) {
            return new ResponseResult(ResponseCodeEnum.MEMBER_THEME);
        }
        PointRequestDto requestDto = new PointRequestDto();
        MemberPointModifyRequest memberPointDto = new MemberPointModifyRequest();
        //查询成本中心
        requestDto.setShopCode(cepMemberDeductionDto.getCostCenter());
        memberPointDto.setShopId(cepMemberDeductionDto.getCostCenter());
        if (!cepMemberDeductionDto.getIsCostMapping()) {
            String costCenter = cepCostCenterRepository.selectCostCenter(cepMemberDeductionDto.getCostCenter());
            requestDto.setShopCode(costCenter);
            memberPointDto.setShopId(costCenter);
        }
        String costCenter = cepCostCenterRepository.getCostCenter(requestDto.getShopCode());
        //判断成本中心是否存在
        //log.info("查询成本中心:{}", costCenter);
        if (StringUtils.isBlank(costCenter)) {
            return new ResponseResult(ResponseCodeEnum.MEMBER_COStCENTER);
        }
        //封装实体
        memberPointDto.setKZZD1(cepMemberDeductionDto.getChangeSourceDetail());
        memberPointDto.setKZZD2(cepMemberDeductionDto.getChangeSource());
        memberPointDto.setKZZD3(cepMemberDeductionDto.getCostTracing());
        memberPointDto.setChangeMode("INTERFACE");
        memberPointDto.setChannelType(cepMemberDeductionDto.getChannelType());
        memberPointDto.setDesc(cepMemberDeductionDto.getScene() + "#" + activityTheme.getTask());
        memberPointDto.setMemberId(memberId);
        //log.info("忠诚度积分账号类型id:{}", ConfigurationCenterUtil.ACCOUNT_POINT);
        memberPointDto.setPointAccountId(Integer.valueOf(ConfigurationCenterUtil.ACCOUNT_POINT));
        memberPointDto.setIdempotentMode(1);
        try {
            if ("扣减取消".equals(cepMemberDeductionDto.getChangeSource())) {
                memberPointDto.setRecordType("DEDUCT");
                memberPointDto.setTradeId(cepMemberDeductionDto.getBusinessId());
                memberPointDto.setTriggerId(UUID.randomUUID().toString().replaceAll("-", ""));
                log.info("封装的实体: {}", memberPointDto);
                CustomizeException map = memberPointModifyClient.memberReversePoint(memberPointDto);
                log.info("扣减取消忠诚度返回值: {}", JSON.toJSONString(map));
                if (!Objects.isNull(map)) {
                    return new ResponseResult(map.getError_code(), map.getMsg());
                }
                //更新会员活动次数记录
                cepMemberScenBindingRepository.deleteActivity(cepMemberDeductionDto.getBusinessId());
                hashMap.put("changePoint", activityTheme.getSendOrDeductValuePerTime());
                return new ResponseResult(ResponseCodeEnum.SUCCESS, hashMap);
            }
        } catch (Exception e) {
            if ("未找到正向操作".equals(e.getMessage())) {
                return new ResponseResult(ResponseCodeEnum.MEMBER_POINT_ERROR);
            }
            if ("正向积分已经被使用".equals(e.getMessage())) {
                return new ResponseResult(ResponseCodeEnum.MEMBER_POINT_USED);
            }
            log.error("规则场景变更会员积分(瓶子)memberId:{},e:{}", memberId, e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED.getCode(),e.getMessage());
        }
        //判断活动是否过期
        boolean effectiveDate = DateUtil.isEffectiveDate(activityTheme.getStartTime(), activityTheme.getEndTime());
        if (!effectiveDate) {
            return new ResponseResult(ResponseCodeEnum.TIEM_RANGE);
        }
        if (activityTheme.getSendTimeLimitPerDay() != null) {
            //查询活动每天次数
            String dayTime = cepMemberScenBindingRepository.getDayTime(memberId, cepMemberDeductionDto.getScene());
            if (Integer.parseInt(dayTime) == activityTheme.getSendTimeLimitPerDay()) {
                return new ResponseResult(ResponseCodeEnum.MEMBER_DAY_LIMITS);
            }
        }
        if (activityTheme.getSendTimeLimitPerMonth() != null) {
            //查询活动每月次数
            String monthTime = cepMemberScenBindingRepository.getMonthTime(memberId, cepMemberDeductionDto.getScene());
            if (Integer.parseInt(monthTime) == activityTheme.getSendTimeLimitPerMonth()) {
                return new ResponseResult(ResponseCodeEnum.MEMBER_MONTH_LIMITS);
            }
        }
        if (activityTheme.getSendTimeTotalLimit() != null) {
            //查询活动上线次数
            String maximumTime = cepMemberScenBindingRepository.getMaximumTime(memberId, cepMemberDeductionDto.getScene());
            if (Integer.parseInt(maximumTime) == activityTheme.getSendTimeTotalLimit()) {
                return new ResponseResult(ResponseCodeEnum.MEMBER_FREQUENCY_LIMITS);
            }
        }
        //封装积分变更实体
        requestDto.setMemberId(memberId);
        //积分的值
        double point = activityTheme.getSendOrDeductValuePerTime();
        if (activityTheme.getIsTransactionRelated()) {
            double k = activityTheme.getSendOrDeductValuePerTime() / 3;
            point = new BigDecimal(k).setScale(1, BigDecimal.ROUND_UP).doubleValue();
            requestDto.setPoint(point);
            memberPointDto.setPoint(point);
        } else {
            requestDto.setPoint(activityTheme.getSendOrDeductValuePerTime());
            memberPointDto.setPoint(activityTheme.getSendOrDeductValuePerTime());
        }
        //判断积分生效时间
        if ("NEXT_DAY".equals(activityTheme.getSendPointTimeliness())) {
            requestDto.setEffectTime(DateHelper.getDateTime());
            memberPointDto.setEffectDate((DateHelper.changeShanghaiToUTC(DateHelper.getTomorrowTime())));
        }
        requestDto.setExpiredTime(DateHelper.getLastDayOfMonth());

        try {
            if ("DEDUCT_POINT".equals(activityTheme.getRuleType())) {
                //判断 积分是否足够扣减
                //查询会员积分
               /* PointDto pointInfo = memberClient.getPointInfo(memberId, null, ModelNameEnum.MEMBER_TYPE.getValue(), null, null);
                BigDecimal bigPoint = new BigDecimal(point);
                BigDecimal bigPointInfo = new BigDecimal(pointInfo.getPoint());
                if (bigPointInfo.compareTo(bigPoint) >= 0) {*/
                requestDto.setChangeType("DEDUCT");
                memberPointDto.setRecordType("DEDUCT");
                memberPointDto.setOverdueDate(DateHelper.changeShanghaiToUTC(DateHelper.getDayOfMonth()));
                memberPointDto.setTriggerId(cepMemberDeductionDto.getBusinessId());
                log.info("封装的实体: {}", memberPointDto);
                memberPointModifyClient.memberModifyPoint(memberPointDto);
                //更新会员活动次数记录
                updateMemberActivityNum(cepMemberDeductionDto, memberId);
                //开始参与活动积分记录推送到中台
                //pushPointRecordComsom(cepMemberDeductionDto, activityTheme);

                hashMap.put("changePoint", Double.valueOf("-" + point));
                return new ResponseResult(ResponseCodeEnum.SUCCESS, hashMap);
            }
        } catch (Exception e) {
            if ("此X-Business-Token已执行".equals(e.getMessage())) {
                hashMap.put("changePoint", Double.valueOf("-" + point));
                return new ResponseResult(ResponseCodeEnum.SUCCESS_IDEMPOTENT, hashMap);
            }
            if ("扣减失败，可用余额不足".equals(e.getMessage())) {
                return new ResponseResult(ResponseCodeEnum.MEMBER_POINT);
            }
            log.error("规则场景变更会员积分(瓶子)BusinessId:{},msg:{}", cepMemberDeductionDto.getBusinessId(), e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED.getCode(),e.getMessage());
        }
        requestDto.setDescription(cepMemberDeductionDto.getScene() + "#" + activityTheme.getTask());
        requestDto.setChannelType(cepMemberDeductionDto.getChannelType());
        requestDto.setMemberType(cepMemberDeductionDto.getMemberType());
        requestDto.setKZZD1(cepMemberDeductionDto.getChangeSourceDetail());
        requestDto.setKZZD2(cepMemberDeductionDto.getChangeSource());
        requestDto.setKZZD3(cepMemberDeductionDto.getCostTracing());

        //积分发放类型
        if ("SEND_POINT".equals(activityTheme.getRuleType())) {
            String lockKey = ChannelTypeEnum.KO_MP.getValue() + requestDto.getMemberId();
            log.info("初始化分布式锁:{}", lockKey);
            Lock lock = redisCache.getLock(lockKey);
            if (lock.tryLock()) {
                try {
                    requestDto.setChangeType("SEND");
                    ResponseResult result = iCepMemberService.updatePoint(requestDto, cepMemberDeductionDto.getBusinessId(), cepMemberDeductionDto.getScene(),cepMemberDeductionDto.getChannelType());
                    //更新会员活动次数记录
                    log.info("积分发放会员memberId:{},data:{}", memberId, JSON.toJSON(result));
                    if ("200".equals(result.getCode())) {
                        updateMemberActivityNum(cepMemberDeductionDto, memberId);
                    }
                    //开始参与活动积分记录推送到中台
                    //pushPointRecordComsom(cepMemberDeductionDto, activityTheme);
                    return result;
                } catch (Exception e) {
                    log.error("规则场景变更会员积分(瓶子)BusinessId:{},msg:{}",cepMemberDeductionDto.getBusinessId(), e.getMessage());
                    return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, e.getMessage());
                } finally {
                    //释放锁
                    lock.unlock();
                    //log.info("释放锁计算完成:{}", ChannelTypeEnum.KO_MP.getValue() + requestDto.getMemberId());
                }
            }else {
                log.warn("规则场景变更会员积分抢锁失败:{}", JSON.toJSONString(cepMemberDeductionDto));
                return new ResponseResult(ResponseCodeEnum.MEMBER_POINT_LOKE);
            }
        }
        return new ResponseResult(ResponseCodeEnum.ERROR, "积分变更类型ruleType有误：" + activityTheme.getRuleType());
    }


    @Override
    public ResponseResult queryGetablePoint(CepMemberGetableDto cepMemberGetableDto){
        //查询会员
        /*String memberId = memberRepository.queryMemberId(cepMemberGetableDto.getCustomerNo());
        if (StringUtils.isBlank(memberId)) {
            return new ResponseResult(ResponseCodeEnum.MEMBER_FAILED);
        }*/
        return iCepMemberService.getablePoint(cepMemberGetableDto.getMemberId(), cepMemberGetableDto.getRuleCode(), cepMemberGetableDto.getScene(), cepMemberGetableDto.getChannelType(), cepMemberGetableDto.getChannelFrom());
    }

    private void pushPointRecordComsom(CepMemberDeductionDto cepMemberDeductionDto, CepRegulationPointDto activityTheme) {
        if (ChannelTypeEnum.SWIRE_MP.getValue().equals(cepMemberDeductionDto.getChannelType())) {
            //太古渠道会员不推送中台
            return;
        }
        if ("参与活动".equals(cepMemberDeductionDto.getChangeSource())) {
            log.info("开始参与活动积分记录推送到中台");
            getExecutorService().execute(new Runnable() {
                @Override
                public void run() {
                    sendPointRecordComsom(cepMemberDeductionDto, activityTheme);
                }
            });
        }
    }

    /**
     * 单例线程池
     *
     * @return
     */
    private static ExecutorService getExecutorService() {
        if (null == executorService) {
            synchronized (AbstractInteractionSceneResolver.class) {
                if (null == executorService) {
                    executorService = DataApiUtil.newBlockingThreadPool(10, ConfigurationCenterUtil.THREAD_CORE_SUM, ConfigurationCenterUtil.THREAD_QUEUE_SUM);
                }
            }
        }
        return executorService;
    }

    protected void sendPointRecordComsom(CepMemberDeductionDto cepMemberDeductionDto, CepRegulationPointDto activityTheme) {
        try {
            Thread.sleep(Long.valueOf(ConfigurationCenterUtil.SEND_CMOSOM_SLEEP)); // 延迟3秒推送
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        //推送中台参与活动积分记录
        int index = cepMemberDeductionDto.getCustomerNo().indexOf("_");
        String openId = cepMemberDeductionDto.getCustomerNo().substring(index + 1);
        //String openId = cepMemberDeductionDto.getCustomerNo().split("_")[1];
        OcpInteractiveRecordDto interactiveRecordDto = new OcpInteractiveRecordDto();
        interactiveRecordDto.setDate(DateHelper.getDateToDay());
        interactiveRecordDto.setConsumer_id(openId);
        interactiveRecordDto.setCampaign_id("40000000");
        interactiveRecordDto.setOrganization_code("KO");
        interactiveRecordDto.setOrganization_name("Coca-Cola");
        interactiveRecordDto.setActivity_source("OCPMP");
        interactiveRecordDto.setActivity_type("CAMPAIGN");
        interactiveRecordDto.setCep_campaign_id(cepMemberDeductionDto.getChangeSourceDetail().split("_")[0]);
        Map activity_info = new HashMap<String, Object>();
        activity_info.put("activity_id", "CRM#" + cepMemberDeductionDto.getScene());
        activity_info.put("activity_name", cepMemberDeductionDto.getScene());
        activity_info.put("timestamp", DateHelper.getDateMilliseconds());
        Map activity_attribute = new HashMap<String, String>();
        activity_attribute.put("app_id", cepMemberDeductionDto.getCustomerNo().split("_")[0]);
        activity_attribute.put("record_id", cepMemberDeductionDto.getChangeSourceDetail().split("_")[1]);
        activity_attribute.put("points_type", "member_bottle");
        activity_attribute.put("points_status", "immediate effect");
        activity_attribute.put("points_channel", "KO_MP");
        activity_attribute.put("platform", "wechat");
        if ("DEDUCT_POINT".equals(activityTheme.getRuleType())) {
            activity_attribute.put("consume_point", activityTheme.getSendOrDeductValuePerTime());
        } else {
            activity_attribute.put("get_point", activityTheme.getSendOrDeductValuePerTime());
        }
        activity_attribute.put("interactive_name", activityTheme.getTask());
        activity_attribute.put("interactive_id", cepMemberDeductionDto.getScene());
        activity_attribute.put("interactive_type", cepMemberDeductionDto.getScene());
        activity_info.put("activity_attribute", activity_attribute);
        interactiveRecordDto.setActivity_info(activity_info);
        log.info("推送中台参与活动积分记录:{}", JSON.toJSONString(interactiveRecordDto));
        try {
            HashMap<String, String> header = new HashMap<>();
            header.put("CampaignId", "40000000");
            header.put("Content-Type", "application/json");
            header.put("Ocp-Apim-Subscription-Key", ConfigurationCenterUtil.MIDDLEGROUND_KEY);
            String body = HttpClientHelper.doPostJSON(ConfigurationCenterUtil.OCP_INTERACTIVE_RECORD, interactiveRecordDto, header);
            log.info("推送中台参与活动积分记录结果:{}", body);
        } catch (Exception e) {
            HashMap<String, Object> map = new HashMap<>();
            map.put("code", 500);
            map.put("message",e.getMessage());
            map.put("retryCount",1);
            map.put("newRequest",JsonUtils.toJson(interactiveRecordDto));
            map.put("lastSync", ZonedDateTime.now());
            map.put("state",false);
            DataApiUtil.upsertIsData(ModelConstants.POINT_CDP_INTERFACLOG,UUID.randomUUID().toString().replaceAll("-", ""), map);
            log.error("推送中台参与活动积分记录接口异常:{},interactiveRecordDto:{}", e.getMessage(), JSON.toJSONString(interactiveRecordDto));
        }


    }

    @Override
    public ResponseResult updateMemberPointLog(CepRulePointLogDto cepRulePointLogDto) {
        try {
            cepMemberScenBindingRepository.updateMemberPointLog(cepRulePointLogDto);
           /* try {
                Thread.sleep(200);
                //cepMemberScenBindingRepository.updatePointBehaviorRecord(cepRulePointLogDto);
                cepMemberScenBindingRepository.updatePointRecord(cepRulePointLogDto);
            } catch (InterruptedException e) {
                log.error("InterruptedException", e);
                Thread.currentThread().interrupt();
            }*/
        } catch (Exception e) {
            log.error("修改规则场景变更会员积分(KZZD3):{}", e.getMessage());
            return new ResponseResult(ResponseCodeEnum.OPENAPI_FAILED, e.getMessage());
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }


    public static ResponseResult updateMemberActivityNum(CepMemberDeductionDto cepMemberDeductionDto, String memberId) {
        CepMemberSceneBindingLogDto pointDto = new CepMemberSceneBindingLogDto();
        pointDto.setScene(cepMemberDeductionDto.getScene());
        pointDto.setCustomerNo(cepMemberDeductionDto.getCustomerNo());
        pointDto.setLastSync(DateHelper.getDateDay());
        pointDto.setMemberId(memberId);
        pointDto.setBusinessId(cepMemberDeductionDto.getBusinessId());
        pointDto.setId(cepMemberDeductionDto.getBusinessId());
        DMLResponse response = DataApiUtil.upsertIsData(ModelConstants.CAMPAIGN_SCENE_BINDINGLOG, cepMemberDeductionDto.getBusinessId(), JSONObject.parseObject(JSON.toJSONString(pointDto)));
        if (!response.getIsSuccess()) {
            log.error("更新会员活动次数记录:{}", response.getOperation());
            return new ResponseResult(ResponseCodeEnum.ILLEGAL_ARGUMENT.getCode(), response.getOperation());
        }
        return new ResponseResult(ResponseCodeEnum.SUCCESS);
    }

}
