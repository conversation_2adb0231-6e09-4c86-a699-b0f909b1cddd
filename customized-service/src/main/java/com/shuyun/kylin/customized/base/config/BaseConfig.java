package com.shuyun.kylin.customized.base.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Slf4j
@Configuration
@Component
@Data
public class BaseConfig {
    @Value("${base.generatecode.accoutNo:9999999}")
    private Integer accoutNo; //生成券数量
    @Value("${base.coupon.batchNo:99999}")
    private Integer batchNo; //批次
    @Value("${base.commit.type:SUBMIT_SUCCESS}")  //发放成功：GRANT_SUCCESS 提交成功：SUBMIT_SUCCESS
    private String  type; //提交或者投放方式
    @Value("${base.commit.successNo:500}")
    private Integer  successNo;  //提交成功数量
}
