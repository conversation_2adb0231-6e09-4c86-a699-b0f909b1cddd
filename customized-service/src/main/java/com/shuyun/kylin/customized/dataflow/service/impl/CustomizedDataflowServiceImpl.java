package com.shuyun.kylin.customized.dataflow.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.kylin.customized.base.dm.CouponCampaignRepository;
import com.shuyun.kylin.customized.base.dm.WechatSubActionRepository;
import com.shuyun.kylin.customized.base.repository.ReturnvisitRepository;
import com.shuyun.kylin.customized.base.stream.kafka.dao.*;
import com.shuyun.kylin.customized.base.stream.kafka.producer.coupon.BehaviorProducer;
import com.shuyun.kylin.customized.base.stream.kafka.producer.coupon.CallbackProducer;
import com.shuyun.kylin.customized.base.util.Base64Util;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.base.util.DataApiUtil;
import com.shuyun.kylin.customized.dataflow.dto.PmsCouponResponseDto;
import com.shuyun.kylin.customized.dataflow.service.CustomizedDataflowService;
import com.shuyun.kylin.customized.member.dm.CepAppletTemplaterRepository;
import com.shuyun.kylin.customized.member.dto.CepAppletTemplateDto;
import com.shuyun.kylin.customized.project.request.GrantCouponRequest;
import com.shuyun.kylin.customized.project.response.GrantCouponResponse;
import com.shuyun.kylin.customized.project.service.KoCouponService;
import com.shuyun.kylin.customized.wechat.request.LwWechatSubscribeRequest;
import com.shuyun.kylin.customized.wechat.service.WechatService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import static com.shuyun.kylin.customized.dataflow.resource.CustomizedDataflowRecource.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;


@Slf4j
@Service
public class CustomizedDataflowServiceImpl implements CustomizedDataflowService {


    @Autowired
    private ReturnvisitRepository returnvisitRepository;

    @Autowired
    private CallbackProducer callbackProducer;

    @Autowired
    private CouponCampaignRepository couponCampaignRepository;

    @Resource
    private KoCouponService koCouponService;

    @Autowired
    private BehaviorProducer behaviorProducer;

    @Autowired
    private WechatSubActionRepository wechatSubActionRepository;

    @Autowired
    private WechatService wechatService;


//    @Autowired
//    private AppletSubcriberRepository appletSubcriberRepository;

    @Autowired
    private CepAppletTemplaterRepository cepAppletTemplaterRepository;

    @Override
    public PmsCouponResponseDto pmsCoupon(Map pmsCouponRequest) {
        //更新任务状态
        String taskId = pmsCouponRequest.get("taskId").toString();
        String currExecuteStatus = EXECUTION_STATUS_INIT;
        try {
            returnvisitRepository.updateCustomizedTask(taskId, EXECUTION_STATUS_INIT, EXECUTION_STATUS_PROCESSING);
            currExecuteStatus = EXECUTION_STATUS_PROCESSING;
            sendCouponTask(taskId, pmsCouponRequest);
            returnvisitRepository.updateCustomizedTask(taskId, EXECUTION_STATUS_PROCESSING, EXECUTION_STATUS_FINISH);
            currExecuteStatus = EXECUTION_STATUS_FINISH;
        } catch (Exception e) {
            log.error("事件发送优惠券 接口报错taskId:{},msg:{}",taskId, e.getMessage());
        }
        return new PmsCouponResponseDto(currExecuteStatus, taskId);
    }



    //单条处理微信订阅消息
    @Override
    public PmsCouponResponseDto pmsWechatSubScribe(Map pmsCouponRequest) {
        //更新任务状态
        String taskId = pmsCouponRequest.get("taskId").toString();
        String currExecuteStatus = EXECUTION_STATUS_INIT;
        try {
            returnvisitRepository.updateCustomizedTask(taskId, EXECUTION_STATUS_INIT, EXECUTION_STATUS_PROCESSING);
            currExecuteStatus = EXECUTION_STATUS_PROCESSING;
            sendWechatSubTask(taskId, pmsCouponRequest);
            returnvisitRepository.updateCustomizedTask(taskId, EXECUTION_STATUS_PROCESSING, EXECUTION_STATUS_FINISH);
            currExecuteStatus = EXECUTION_STATUS_FINISH;
        } catch (Exception e) {
            log.error("事件发送小程序订阅消息接口报错taskId:{},msg:{}",taskId, e.getMessage());
        }
        return new PmsCouponResponseDto(currExecuteStatus, taskId);
    }

    //批量处理微信订阅消息
    @Override
    public PmsCouponResponseDto pmsBatchWechatSubScribe(Map pmsCouponRequest) {
        //更新任务状态
        String taskId = pmsCouponRequest.get("taskId").toString();
        String currExecuteStatus = EXECUTION_STATUS_INIT;
        try {
            returnvisitRepository.updateCustomizedTask(taskId, EXECUTION_STATUS_INIT, EXECUTION_STATUS_PROCESSING);
            currExecuteStatus = EXECUTION_STATUS_PROCESSING;
            sendBatchWechatSubTask(taskId, pmsCouponRequest);
            returnvisitRepository.updateCustomizedTask(taskId, EXECUTION_STATUS_PROCESSING, EXECUTION_STATUS_FINISH);
            currExecuteStatus = EXECUTION_STATUS_FINISH;
        } catch (Exception e) {
            log.error("new批量推送优惠券 接口报错taskId:{},msg:{}", taskId,e.getMessage());
        }
        return new PmsCouponResponseDto(currExecuteStatus, taskId);
    }


    @Override
    public PmsCouponResponseDto pmsBatchCoupons(Map pmsCouponRequest) {
        //更新任务状态
        String taskId = pmsCouponRequest.get("taskId").toString();
        String currExecuteStatus = EXECUTION_STATUS_INIT;
        try {
            returnvisitRepository.updateCustomizedTask(taskId, EXECUTION_STATUS_INIT, EXECUTION_STATUS_PROCESSING);
            currExecuteStatus = EXECUTION_STATUS_PROCESSING;
            snedBastchCouponTask(taskId, pmsCouponRequest);
            returnvisitRepository.updateCustomizedTask(taskId, EXECUTION_STATUS_PROCESSING, EXECUTION_STATUS_FINISH);
            currExecuteStatus = EXECUTION_STATUS_FINISH;
        } catch (Exception e) {
            log.error("new批量推送优惠券 接口报错taskId:{},msg:{}", taskId,e.getMessage());
        }
        return new PmsCouponResponseDto(currExecuteStatus, taskId);
    }

    /**
     * 发优惠券  （事件发送）
     * @param taskId
     * @param request
     */
    public void sendCouponTask(String taskId, Map request) {

        List<CampaignActionOutputDto> memberCoupons = null;
        try {
            //查询发送优惠券的会员
            memberCoupons = couponCampaignRepository.getMemberCouponsNew(taskId);
        } catch (Exception e) {
            log.error("查询优惠券数据失败taskId:{},msg:{}", taskId, e.getMessage());
        }
        if (CollectionUtils.isEmpty(memberCoupons)) {
            log.info("没有查询到该发送优惠券记录taskId:{}", taskId);
            return;
        }
        List<CampaignActionOutputDto> couponDtoList = JSON.parseArray(JSON.toJSONString(memberCoupons), CampaignActionOutputDto.class);
        //发放权益
        for (CampaignActionOutputDto coupon : couponDtoList) {
            try {
                GrantCouponRequest couponRequest = new GrantCouponRequest();
                couponRequest.setBrand("KO");
                couponRequest.setChannelType("KO_MP");
                couponRequest.setTransactionId(coupon.getId());
                couponRequest.setMemberGrantIdentify(coupon.getObjectId());
                couponRequest.setProjectId(replaceCoup(coupon.getProjectId()));
                couponRequest.setCampaignId(coupon.getCampaignId()+"_"+coupon.getNodeId());
                log.info("new优惠券封装发送第三方对象:{}", JSON.toJSONString(couponRequest));
                GrantCouponResponse response = koCouponService.instanceGrant(couponRequest);
                log.info("new优惠券返回结果:{}",JSON.toJSONString(response));
                if ("200".equals(response.getCode())){
                    //每次发送推送一个记录
                    couponCampaignRepository.updateMemberCouponsNew(taskId, coupon.getObjectId(), "Y", "发送成功");
                    reachBehaviorKafkaMsg(taskId,"Y",request.get("tenant").toString(),coupon.getObjectId(),coupon.getOccId(),null);
                }else {
                    //每次发送推送一个记录
                    couponCampaignRepository.updateMemberCouponsNew(taskId, coupon.getObjectId(), "N", response.getMsg());
                    HashMap<String, String> commentMap = new HashMap<>();
                    commentMap.put("failedReason",response.getMsg());
                    reachBehaviorKafkaMsg(taskId,"N",request.get("tenant").toString(),coupon.getObjectId(),coupon.getOccId(),commentMap);
                }
            } catch (Exception e) {
                log.info("new优惠券接口失败,memberId:{},msg:{}", coupon.getObjectId(), e.getMessage());
                couponCampaignRepository.updateMemberCouponsNew(taskId, coupon.getObjectId(), "N", e.getMessage());
                HashMap<String, String> commentMap = new HashMap<>();
                commentMap.put("failedReason",e.getMessage());
                reachBehaviorKafkaMsg(taskId,"N",request.get("tenant").toString(),coupon.getObjectId(),coupon.getOccId(),commentMap);
            }
        }
        //全部完成时推送完成
        try {
            sendCallbackKafkaMsg(request);
            log.info("new优惠券事件数据处理完成后，推送一条Kafka通知...");
        } catch (Exception e) {
            log.error("new优惠券事件处理完成后，推送一条Kafka通知，推送失败taskId:{},msg:{}", request.get("taskId").toString(), e.getMessage());
        }

    }

    /**
     * 批量发优惠券  （营销发送）
     * @param taskId
     * @param request
     */
    public void snedBastchCouponTask(String taskId, Map request) {
        //创建线程池
        ExecutorService executorService = DataApiUtil.newBlockingThreadPool(ConfigurationCenterUtil.THREAD__COUPONS_CORES, ConfigurationCenterUtil.THREAD_COUPONS_QUEUE);
        final int pageSize = ConfigurationCenterUtil.THREAD_COUPONS_PAGESIZE;
        String index = "0";
        int completedNum = 0;
        List<Future<?>> futures = new ArrayList<>();

        try {
            while (true) {
                List<CampaignActionOutputDto> memberCoupons;
                try {
                    memberCoupons = couponCampaignRepository.getMemberCouponsListNew(taskId, index, pageSize);
                } catch (Exception e) {
                    log.error("new优惠券批次查询超时index:{},pageSize:{}; 继续查询...", index, pageSize);
                    continue;
                }
                if (CollectionUtils.isEmpty(memberCoupons)) {
                    log.info("new没有查询到该发送优惠券记录taskId:{}", taskId);
                    break;
                }
                List<CampaignActionOutputDto> couponDtoList = JSON.parseArray(JSON.toJSONString(memberCoupons), CampaignActionOutputDto.class);
                //记录该批次中最大id值
                index = couponDtoList.get(couponDtoList.size() - 1).getId();
                completedNum += couponDtoList.size();
                CouponsRunnable sendRunnable = new CouponsRunnable(index, completedNum, couponDtoList, taskId, request.get("tenant").toString());
                Future<?> submit = executorService.submit(sendRunnable);
                futures.add(submit);
                if (couponDtoList.size() < pageSize) {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("newbatch查询优惠券数据失败taskId:{},msg:{}", taskId, e.getMessage());
        }
        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                log.error("new优惠券线程等待异常终止!", e);
            }
        }
        try {
            executorService.shutdown();
        } catch (Exception e) {
            log.error("new优惠券线程关闭异常!", e.getMessage());
        }
        //全部完成时推送完成
        try {
            sendCallbackKafkaMsg(request);
            log.info("pmsCouponByAsync_所有数据处理完成后，推送一条Kafka通知...taskId:{}",request.get("taskId").toString());
        } catch (Exception e) {
            log.error("pmsCouponByAsync_所有数据处理完成后，推送一条Kafka通知，推送失败taskId:{},msg:{}", request.get("taskId").toString(), e.getMessage());
        }
    }

    class CouponsRunnable implements Runnable {
        String index;
        int completedNum;
        String taskId;
        String tenant;
        List<CampaignActionOutputDto> couponDtoList;

        private CouponsRunnable(String index, int completedNum, List<CampaignActionOutputDto> couponDtoList, String taskId, String tenant) {
            this.index = index;
            this.completedNum = completedNum;
            this.taskId = taskId;
            this.tenant = tenant;
            this.couponDtoList = couponDtoList;
        }

        @Override
        public void run() {
            try {
                Long start = System.currentTimeMillis();
                sendCouponsTable(couponDtoList, taskId, tenant);
                log.info("子线程执行completedNum:{},index:{},耗时:{}ms", completedNum, index, System.currentTimeMillis() - start);
            } catch (Exception e) {
                log.info("子线程执行失败completedNum:{},index:{},taskId:{},message:{}", completedNum, index, taskId, e.getMessage());
            }
        }
    }

    public void sendCouponsTable(List<CampaignActionOutputDto> couponDtoList, String taskId, String tenant) {

        for (CampaignActionOutputDto coupon : couponDtoList) {
            try {
                GrantCouponRequest couponRequest = new GrantCouponRequest();
                couponRequest.setBrand("KO");
                couponRequest.setChannelType("KO_MP");
                couponRequest.setTransactionId(coupon.getId());
                couponRequest.setMemberGrantIdentify(coupon.getObjectId());
                couponRequest.setProjectId(replaceCoup(coupon.getProjectId()));
                couponRequest.setCampaignId(coupon.getCampaignId()+"_"+coupon.getNodeId());
                log.info("new优惠券封装发送第三方对象:{}", JSON.toJSONString(couponRequest));
                GrantCouponResponse response = koCouponService.instanceGrant(couponRequest);
                if ("200".equals(response.getCode())){
                    //每次发送推送一个记录
                    couponCampaignRepository.updateMemberCouponsNew(taskId, coupon.getObjectId(), "Y", "发送成功");
                    reachBehaviorKafkaMsg(taskId,"Y",tenant,coupon.getObjectId(),coupon.getOccId(),null);
                }else {
                    //每次发送推送一个记录
                    couponCampaignRepository.updateMemberCouponsNew(taskId, coupon.getObjectId(), "N", response.getMsg());
                    HashMap<String, String> commentMap = new HashMap<>();
                    commentMap.put("failedReason",response.getMsg());
                    reachBehaviorKafkaMsg(taskId,"N",tenant,coupon.getObjectId(),coupon.getOccId(),commentMap);
                }
            } catch (Exception e) {
                log.error("new_batch发送优惠券失败memberId:{},msg{}:", coupon.getObjectId(), e.getMessage());
                couponCampaignRepository.updateMemberCouponsNew(taskId, coupon.getObjectId(), "N", e.getMessage());
                HashMap<String, String> commentMap = new HashMap<>();
                commentMap.put("failedReason",e.getMessage());
                reachBehaviorKafkaMsg(taskId,"N",tenant,coupon.getObjectId(),coupon.getOccId(),commentMap);
            }
        }
    }



    private void reachBehaviorKafkaMsg(String taskId,String behavior,String tenant,String reachId,String occId,HashMap<String, String> commentMap) {
        ReachBehaviorDto reachBehaviorDto = new ReachBehaviorDto();
        reachBehaviorDto.setTaskId(taskId);
        reachBehaviorDto.setBehavior(behavior);
        reachBehaviorDto.setTenant(tenant);
        reachBehaviorDto.setReachId(reachId);
        reachBehaviorDto.setOccId(occId);
        reachBehaviorDto.setComment(JSON.toJSONString(commentMap));
        behaviorProducer.sendMsg(reachBehaviorDto);
    }

    private void sendCallbackKafkaMsg(Map request) {
        //全部完成时推送完成 Kafka(topic: marketing-action-task-notification-callback
        CallbackDto callbackDto = new CallbackDto();
        callbackDto.setId(request.get("id").toString());
        callbackDto.setTaskId(request.get("taskId").toString());
        callbackDto.setTenant(request.get("tenant").toString());
        callbackDto.setProcessId(request.get("processId").toString());
        callbackDto.setNodeId(request.get("nodeId").toString());
        callbackDto.setStatus("COMPLETED");
        callbackProducer.sendMsg(callbackDto);
    }

    public String replaceCoup(String res) {
        String s = res.replace("[", "");
        String rep = s.replace("]", "");
        String replace = rep.replace("\"", "");
        return replace;
    }




    public void sendWechatSubTask(String taskId, Map request) {
        log.info("sendWechatSubTask...taskId：{}",taskId);
        List<WechatSubActionOutputDto> wechatMemberInfo = null;
        final int pageSize = ConfigurationCenterUtil.THREAD_SUBSCRIBED_PAGESIZE;
        List<AppletSubscriberDto> appletSub = null;
        CepAppletTemplateDto subscribers = null;
        try {
            //查询发送消息的会员
            wechatMemberInfo = wechatSubActionRepository.getMemberWechatSubNew(taskId);
            List<WechatSubActionOutputDto> appletSubscribed = wechatSubActionRepository.getWechatSubListNew(taskId, "0", pageSize);
//            List<AppletSubscriberDto> appletSubscribed = appletSubcriberRepository.getAppletSubscribers(taskId, pageSize);
            appletSub = JSON.parseArray(JSON.toJSONString(appletSubscribed), AppletSubscriberDto.class);
            //查询订阅模板
            String templateId = appletSub.get(0).getTemplateId();
            subscribers = cepAppletTemplaterRepository.getSubscribers(replaceCoup(templateId));
        } catch (Exception e) {
            log.error("查询微信小程序订阅数据失败taskId:{},msg:{}", taskId, e.getMessage());
        }
        if (CollectionUtils.isEmpty(wechatMemberInfo)) {
            log.info("没有查询到该发送微信小程序订阅记录taskId:{}", taskId);
            return;
        }
        List<WechatSubActionOutputDto> wechatSubActionList = JSON.parseArray(JSON.toJSONString(wechatMemberInfo), WechatSubActionOutputDto.class);
        log.info("请求微信小程序订阅消息接口入参wechatSubActionList:{}", JSON.toJSONString(wechatSubActionList));
        //发放权益
        for (WechatSubActionOutputDto wechatList : wechatSubActionList) {
            try {
                log.info("进入循环处理wechatList：{}",wechatList);
                LwWechatSubscribeRequest lwWechatSubscribeRequest = new LwWechatSubscribeRequest();
                //选择订阅场景类型
                String type = wechatList.getType();
                lwWechatSubscribeRequest.setType(type);
                if ("CAMPAIGN".equals(wechatList.getType())){
                    //活动编码
                    lwWechatSubscribeRequest.setCampaignCode(replaceCoup(wechatList.getSceneCampaign()));
                }else {
                    lwWechatSubscribeRequest.setCampaignCode(wechatList.getSceneCommen());
                }
                //接收者openId
                lwWechatSubscribeRequest.setToUser(wechatList.getObjectId());
                //选择消息模板
                lwWechatSubscribeRequest.setTemplateId(replaceCoup(wechatList.getTemplateId()));
                //选择落地页
                lwWechatSubscribeRequest.setPage(wechatList.getPage());
                //MA发送记录流水号
                lwWechatSubscribeRequest.setTraceId(wechatList.getId());
                //模板内容
                JSONObject json = new JSONObject();
                if (StringUtils.isNotBlank(subscribers.getFieldsKey1())) {
                    json.put(subscribers.getFieldsKey1(), wechatList.getThing1());
                }
                if (StringUtils.isNotBlank(subscribers.getFieldsKey2())) {
                    json.put(subscribers.getFieldsKey2(), wechatList.getThing2());
                }
                if (StringUtils.isNotBlank(subscribers.getFieldsKey3())) {
                    json.put(subscribers.getFieldsKey3(), wechatList.getThing3());
                }
                if (StringUtils.isNotBlank(subscribers.getFieldsKey4())) {
                    json.put(subscribers.getFieldsKey4(), wechatList.getThing4());
                }
                if (StringUtils.isNotBlank(subscribers.getFieldsKey5())) {
                    json.put(subscribers.getFieldsKey5(), wechatList.getThing5());
                }
                if (StringUtils.isNotBlank(subscribers.getFieldsKey6())) {
                    json.put(subscribers.getFieldsKey6(), wechatList.getThing6());
                }
                if (StringUtils.isNotBlank(subscribers.getFieldsKey7())) {
                    json.put(subscribers.getFieldsKey7(), wechatList.getThing7());
                }
                lwWechatSubscribeRequest.setData(json);
                String encode = Base64Util.encode(JSON.toJSONString(lwWechatSubscribeRequest));
                log.info("请求微信小程序订阅消息接口入参lwWechatSubscribeRequest:{}", encode);
                JSONObject jsonObject = lwWechatSubscribeConsumerRetry(encode);
                log.info("请求微信小程序订阅消息接口返回结果:{}",jsonObject);
                if (jsonObject != null) {
                    if (0 == Integer.parseInt(jsonObject.getString("code"))) {
                        //每次发送推送一个记录
                        //wechatSubActionRepository.updateWechatSubNew(taskId, wechatList.getObjectId(), "Y", "发送成功");
                        reachBehaviorKafkaMsg(taskId, "Y", request.get("tenant").toString(), wechatList.getObjectId(), wechatList.getOccId(), null);
                    } else {
                        //每次发送推送一个记录
                        String message = jsonObject.getString("message");
                        //couponCampaignRepository.updateMemberCouponsNew(taskId, wechatList.getObjectId(), "N", message);
                        HashMap<String, String> commentMap = new HashMap<>();
                        commentMap.put("failedReason", message);
                        reachBehaviorKafkaMsg(taskId, "N", request.get("tenant").toString(), wechatList.getObjectId(), wechatList.getOccId(), commentMap);
                    }
                }
            } catch (Exception e) {
                log.info("小程序订阅消息接口失败,objectId:{},msg:{}", wechatList.getObjectId(), e.getMessage());
                //couponCampaignRepository.updateMemberCouponsNew(taskId, wechatList.getObjectId(), "N", e.getMessage());
                HashMap<String, String> commentMap = new HashMap<>();
                commentMap.put("failedReason",e.getMessage());
                reachBehaviorKafkaMsg(taskId,"N",request.get("tenant").toString(),wechatList.getObjectId(),wechatList.getOccId(),commentMap);
            }
        }
        //全部完成时推送完成
        try {
            sendCallbackKafkaMsg(request);
            log.info("new小程序订阅消息事件数据处理完成后，推送一条Kafka通知...");
        } catch (Exception e) {
            log.error("new小程序订阅消息事件处理完成后，推送一条Kafka通知，推送失败taskId:{},msg:{}", request.get("taskId").toString(), e.getMessage());
        }
    }



    /**
     * 批量发小程序订阅消息（营销发送）
     * @param taskId
     * @param request
     */
    public void sendBatchWechatSubTask(String taskId, Map request) {
        //创建线程池
        ExecutorService executorService = DataApiUtil.newBlockingThreadPool(ConfigurationCenterUtil.THREAD__COUPONS_CORES, ConfigurationCenterUtil.THREAD_COUPONS_QUEUE);
        final int pageSize = ConfigurationCenterUtil.THREAD_COUPONS_PAGESIZE;
        String index = "0";
        int completedNum = 0;
        List<Future<?>> futures = new ArrayList<>();

        try {
            while (true) {
                List<WechatSubActionOutputDto> wechatSubscribed;
                try {
                    wechatSubscribed = wechatSubActionRepository.getWechatSubListNew(taskId, index, pageSize);
                } catch (Exception e) {
                    log.error("new小程序订阅消息批次查询超时 index:{},pageSize:{}; 继续查询...", index, pageSize);
                    continue;
                }
                if (CollectionUtils.isEmpty(wechatSubscribed)) {
                    log.info("new没有查询到该发送小程序订阅消息记录taskId:{}", taskId);
                    break;
                }
//                List<WechatSubActionOutputDto> appletSub = wechatSubActionRepository.getWechatSubListNew(taskId, "0", pageSize);
                List<WechatSubActionOutputDto> appletSub = JSON.parseArray(JSON.toJSONString(wechatSubscribed), WechatSubActionOutputDto.class);
                //查询订阅模板
                String templateId = appletSub.get(0).getTemplateId();
                CepAppletTemplateDto subscribers = cepAppletTemplaterRepository.getSubscribers(replaceCoup(templateId));
                List<WechatSubActionOutputDto> wechatSubList = JSON.parseArray(JSON.toJSONString(wechatSubscribed), WechatSubActionOutputDto.class);
                //记录该批次中最大id值
                index = wechatSubList.get(wechatSubList.size() - 1).getId();
                completedNum += wechatSubList.size();
                WechatSubRunnable sendRunnable = new WechatSubRunnable(index, completedNum, wechatSubList,subscribers, taskId, request.get("tenant").toString());
                Future<?> submit = executorService.submit(sendRunnable);
                futures.add(submit);
                if (wechatSubList.size() < pageSize) {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("new小程序订阅消息批次查询数据失败taskId:{},msg:{}", taskId, e.getMessage());
        }
        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                log.error("new小程序订阅消息批次线程等待异常终止", e);
            }
        }
        try {
            executorService.shutdown();
        } catch (Exception e) {
            log.error("new小程序订阅消息批次线程关闭异常", e);
        }
        //全部完成时推送完成
        try {
            sendCallbackKafkaMsg(request);
            log.info("sendBatchWechatSubTask_所有数据处理完成后，推送一条Kafka通知...taskId:{}", request.get("taskId").toString());
        } catch (Exception e) {
            log.error("sendBatchWechatSubTask_所有数据处理完成后，推送一条Kafka通知，推送失败taskId:{},msg:{}", request.get("taskId").toString(), e.getMessage());
        }

    }


    class WechatSubRunnable implements Runnable {
        String index;
        int completedNum;
        String taskId;
        CepAppletTemplateDto subscribers;
        String tenant;
        List<WechatSubActionOutputDto>  list;

        private WechatSubRunnable(String index, int completedNum, List<WechatSubActionOutputDto> list,  CepAppletTemplateDto subscribers,String taskId, String tenant) {
            this.index = index;
            this.completedNum = completedNum;
            this.subscribers = subscribers;
            this.taskId = taskId;
            this.tenant = tenant;
            this.list = list;
        }

        @Override
        public void run() {
            try {
                Long start = System.currentTimeMillis();
                sendWechatSubTable(list,subscribers, taskId, tenant);
                log.info("子线程执行completedNum:{},index:{},耗时:{}ms", completedNum, index, System.currentTimeMillis() - start);
            } catch (Exception e) {
                log.info("子线程执行失败completedNum:{},index:{},taskId:{},message:{}", completedNum, index, taskId, e.getMessage());
            }
        }
    }

    public void sendWechatSubTable(List<WechatSubActionOutputDto> wechatSubDtoList, CepAppletTemplateDto subscribers, String taskId, String tenant) {
        for (WechatSubActionOutputDto wechatList : wechatSubDtoList) {
            try {
                LwWechatSubscribeRequest lwWechatSubscribeRequest = new LwWechatSubscribeRequest();
                //活动编码
                //判断是平台还是活动
                if ("CAMPAIGN".equals(wechatList.getType())){
                    //活动编码
                    lwWechatSubscribeRequest.setCampaignCode(replaceCoup(wechatList.getSceneCampaign()));
                }else {
                    lwWechatSubscribeRequest.setCampaignCode(wechatList.getSceneCommen());
                }
                //选择订阅场景类型
                lwWechatSubscribeRequest.setType(wechatList.getType());
                //接收者openId
                lwWechatSubscribeRequest.setToUser(wechatList.getObjectId());
                //选择消息模板
                lwWechatSubscribeRequest.setTemplateId(replaceCoup(wechatList.getTemplateId()));
                //选择落地页
                lwWechatSubscribeRequest.setPage(wechatList.getPage());
                //MA发送记录流水号
                lwWechatSubscribeRequest.setTraceId(wechatList.getId());
                //模板内容
                JSONObject json = new JSONObject();
                if (StringUtils.isNotBlank(subscribers.getFieldsKey1())) {
                    json.put(subscribers.getFieldsKey1(), wechatList.getThing1());
                }
                if (StringUtils.isNotBlank(subscribers.getFieldsKey2())) {
                    json.put(subscribers.getFieldsKey2(), wechatList.getThing2());
                }
                if (StringUtils.isNotBlank(subscribers.getFieldsKey3())) {
                    json.put(subscribers.getFieldsKey3(), wechatList.getThing3());
                }
                if (StringUtils.isNotBlank(subscribers.getFieldsKey4())) {
                    json.put(subscribers.getFieldsKey4(), wechatList.getThing4());
                }
                if (StringUtils.isNotBlank(subscribers.getFieldsKey5())) {
                    json.put(subscribers.getFieldsKey5(), wechatList.getThing5());
                }
                if (StringUtils.isNotBlank(subscribers.getFieldsKey6())) {
                    json.put(subscribers.getFieldsKey6(), wechatList.getThing6());
                }
                if (StringUtils.isNotBlank(subscribers.getFieldsKey7())) {
                    json.put(subscribers.getFieldsKey7(), wechatList.getThing7());
                }
                lwWechatSubscribeRequest.setData(json);
                String encode = Base64Util.encode(JSON.toJSONString(lwWechatSubscribeRequest));
                log.info("new_batch请求微信小程序订阅消息接口入参lwWechatSubscribeRequest:{}", encode);
                JSONObject jsonObject = lwWechatSubscribeConsumerRetry(encode);
                log.info("new_batch请求微信小程序订阅消息接口返回结果:{}",jsonObject);
                if (jsonObject != null) {
                    if (0 == Integer.parseInt(jsonObject.getString("code"))) {
                        //每次发送推送一个记录
                        //wechatSubActionRepository.updateWechatSubNew(taskId, wechatList.getObjectId(), "Y", "发送成功");
                        reachBehaviorKafkaMsg(taskId, "Y",tenant, wechatList.getObjectId(), wechatList.getOccId(), null);
                    } else {
                        //每次发送推送一个记录
                        String message = jsonObject.getString("message");
                        //couponCampaignRepository.updateMemberCouponsNew(taskId, wechatList.getObjectId(), "N", message);
                        HashMap<String, String> commentMap = new HashMap<>();
                        commentMap.put("failedReason", message);
                        reachBehaviorKafkaMsg(taskId, "N", tenant, wechatList.getObjectId(), wechatList.getOccId(), commentMap);
                    }
                }
            } catch (Exception e) {
                log.info("new_batch小程序订阅消息接口失败,memberId:{},msg:{}", wechatList.getObjectId(), e.getMessage());
                //couponCampaignRepository.updateMemberCouponsNew(taskId, wechatList.getObjectId(), "N", e.getMessage());
                HashMap<String, String> commentMap = new HashMap<>();
                commentMap.put("failedReason",e.getMessage());
                reachBehaviorKafkaMsg(taskId,"N",tenant,wechatList.getObjectId(),wechatList.getOccId(),commentMap);
            }
        }
    }

    private JSONObject lwWechatSubscribeConsumerRetry(String encode) {
        int retryCount = 0;

        while(retryCount < 3) {
            JSONObject jsonObject = null;
            try {
                jsonObject = wechatService.lwWechatSubscribeConsumer(encode);
            } catch (Exception e) {
                log.error("get lw.wechat.token error: ", e);
            }
            if (jsonObject != null) {
                return jsonObject;
            }
            retryCount++;
            try {
                Thread.sleep(20L);
            } catch (InterruptedException e) {
                log.error("thread sleep exception: ", e);
            }
        }
        return null;

    }

}

