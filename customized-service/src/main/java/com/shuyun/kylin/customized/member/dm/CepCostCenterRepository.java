package com.shuyun.kylin.customized.member.dm;


import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.member.dto.CepCostCenterDto;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class CepCostCenterRepository extends BaseDsRepository<CepCostCenterDto> {

    /**
     * 查询成本中心规则
     */
    public String selectCostCenter(String costCenterMappingMark) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("costCenterMappingMark", costCenterMappingMark);
        String queryMemberSql = " select costCenter from " + ModelConstants.CAMPAIGN_COSTCENTER + " where  costCenterMappingMark = :costCenterMappingMark ";
        List<Map<String,Object>> list = execute(queryMemberSql, queryMap).getData();
        String costCenter = null;
        for (Map map : list) {
            costCenter = map.get("costCenter").toString();
        }
        return costCenter;
    }

    /**
     * 查询成本中心
     */
    public String getCostCenter(String costCenterCode) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("costCenterCode", costCenterCode);
        String queryMemberSql = " select costCenterCode from " + ModelConstants.COSTCENTER + " where  costCenterCode = :costCenterCode ";
        List<Map<String,Object>> list = execute(queryMemberSql, queryMap).getData();
        String costCenter = null;
        for (Map map : list) {
            costCenter = map.get("costCenterCode").toString();
        }
        return costCenter;
    }


    /**
     * 根据lbs查询成本中心
     *
     * @param bottlerFactoryName
     * @param city
     * @param district
     * @return
     */
    public String getLbsCostCenter(String bottlerFactoryName, String city, String district) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("bottlerFactoryName", bottlerFactoryName);
        queryMap.put("city", city);
        queryMap.put("district", district);
        String queryMemberSql = " select costCenterCode from " + ModelConstants.LBS_COSTCENTER + " where  bottlerFactoryName = :bottlerFactoryName and city = :city and district = :district ";
        List<Map<String,Object>> list = execute(queryMemberSql, queryMap).getData();
        String costCenter = null;
        for (Map map : list) {
            costCenter = map.get("costCenterCode").toString();
        }
        return costCenter;
    }

}
