package com.shuyun.kylin.customized.base.feign.dao;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 卡券项目列表feign调用返回对象
 */
@Data
public class CouponProjectResponseDto {
    @JSONField(name = "error_code")
    private String errorCode;

    private String msg;

    private RspData data;

    @Data
    public static class RspData {
        private Integer page;

        private Integer pageSize;

        private Integer total;

        private List<ProjectData> data;

        @Data
        public static class ProjectData {
            /**
             * 项目id
             */
            private String id;

            /**
             * 发放总量
             */
            private Integer maxQuantity;

            /**
             * 权益类型
             */
            private String type;

            /**
             * 权益规则说明
             */
            private String description;

            /**
             * 券商
             */
            private String projectBusiness;

            /**
             * 发放限制
             */
            private GrantRestrict grantRestrict;

            @Data
            public static class GrantRestrict {
                /**
                 * 发放渠道
                 */
                private String platformsRef;
            }

            /**
             * 扩展字段(一些定制化字段在这里)
             */
            private ExtData extData;

            @Data
            public static class ExtData {
                /**
                 * 权益成本
                 */
                private String cost;

                /**
                 * 虚拟权益类型
                 */
                @Setter(onMethod_ = @JsonProperty("VirtualType"))
                @Getter
                private String VirtualType;

                /**
                 * 发放形式
                 */
                private String projectGrantType;

                /**
                 * 三方项目id
                 */
                private String externalProjectId;

                /**
                 * 券类型
                 */
                private String couponType;

                /**
                 * 是否积分兑换权益
                 */
                private String isPointExchange;

                private String projectBusiness;

                private Integer point;

                // 红包金额
                private Double amount;

            }

        }
    }


}
