package com.shuyun.kylin.customized.coupon.resource;

import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.coupon.dto.OtherBenefitsDto;
import com.shuyun.kylin.customized.coupon.service.ICouponService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description DataSyncResource
 * @PACKAGE_NAME com.shuyun.kylin.customized.coupon.resource
 * @date 2022/11/1 11:20
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/data/sync")
public class DataSyncResource {


    @Autowired
    private ICouponService couponService;


    /**
     * 权益信息保存
     * @param otherBenefitsDto
     * @return
     */
    @PostMapping("/equity")
    public ResponseResult saveOtherBenefits(@Validated @RequestBody OtherBenefitsDto otherBenefitsDto) {
        log.info("DataSyncResource...saveOtherBenefits...权益信息保存请求入参: {}", otherBenefitsDto);
        return  couponService.saveOtherBenefits(otherBenefitsDto);
    }


}
