//package com.shuyun.kylin.customized.base.stream.kafka.consumer.coupon;
//
//import com.alibaba.fastjson.JSON;
//import com.shuyun.kylin.customized.base.stream.kafka.messaging.KafkaSink;
//import com.shuyun.kylin.customized.medal.request.MedalProgressRequest;
//import com.shuyun.kylin.customized.medal.service.MedalService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.cloud.stream.annotation.StreamListener;
//import org.springframework.stereotype.Component;
//
////会员参与活动勋章计算
//@Slf4j
//@Component
//public class MedalProgressByCampaignConsumer {
//
//    @Autowired
//    private MedalService medalService;
//
//    @StreamListener(KafkaSink.KAFKA_MEDAL_PROGRESS_CAMPAIGN_INPUT)
//    public void medalProgressByCampaignConsumer(MedalProgressRequest medalProgressRequest) {
//        try {
//            log.info("接收到会员参与活动勋章计算kafka数据流入参:{}", JSON.toJSONString(medalProgressRequest));
//            medalService.medalProgressByCampaignConsumer(medalProgressRequest);
//         } catch (Exception e) {
//            log.error("接收到会员参与活动勋章计算kafka失败:{}", JSON.toJSONString(medalProgressRequest),e);
//        }
//    }
//
//}
