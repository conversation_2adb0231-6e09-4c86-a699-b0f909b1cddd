package com.shuyun.kylin.customized.base.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.starter.redis.ICache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
@EnableScheduling
public class CepSchedulingJob {

    @Autowired
    @Qualifier("redisCache")
    private ICache redisCache;

    //获取token
    //@Scheduled(cron = "0 0 */1 * * ?")
    //@Scheduled(cron = "0 */20 * * * ?")
    public void getCepToken(){
       // log.info("url:{},username:{},password:{}", ConfigurationCenterUtil.CEP_URL,ConfigurationCenterUtil.CEP_USERNAME,ConfigurationCenterUtil.CEP_PASSWOED);
        HashMap<String, String> map = new HashMap<>();
        map.put("username",ConfigurationCenterUtil.CEP_USERNAME);
        map.put("password",ConfigurationCenterUtil.CEP_PASSWOED);

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(map), headers);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(ConfigurationCenterUtil.CEP_URL + ConfigurationCenterUtil.CEP_TOCKEN, requestEntity, String.class);
        String body = responseEntity.getBody();
        log.info("查询token返回的body: {}",body);
        JSONObject object = JSONObject.parseObject(body);
        Object data = object.get("data");
        Map<String,String> maps = JSONObject.parseObject(JSONObject.toJSONString(data), Map.class);
        log.info("token: {}", maps.get("token"));
        String token = maps.get("token");
        redisCache.put("cep_token",token);
    }
}
