package com.shuyun.kylin.customized.member.dto;


import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/*
变更会员积分(瓶子)
 */
@Data
public class CepLbsMemberPointRecordDto {

    @NotBlank(message = "客户编号不能为空，格式：appid_openid")
    private String customerNo;
    @NotBlank(message = "变更唯一标识不能为空")
    private String businessId;
    @NotBlank(message = "会员渠道不能为空: TAOBAO：天猫,KO_MP：KO小程序,EC_SHOPPING：EC购物商城,EC_POINT：EC积分商城,H5：H5活动")
    private String channelType;
    @NotBlank(message = "会员类型不能为空,固定值KO")
    private String memberType;
    @NotNull(message = "变更积分值不能为空")
    private Double point;
    @NotBlank(message = "积分变更类型不能为空，格式：SEND: 立即发放,DEDUCT: 扣减")
    private String changeType;
    @NotBlank(message = "成本中心不能为空")
    private String costCenter;

    private String changeSource;
    private String changeSourceDetail;

    private String costTracing;

    private String desc;
    private String effectTime;
    private String expiredTime;
    private String excessPoints;
    private Double experience;
    private Double differenceValue;

}
