package com.shuyun.kylin.customized.rpc.template;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.project.strategy.router.SendCouponEnum;
import com.shuyun.kylin.customized.rpc.dto.ZerXCouponRequestDto;
import com.shuyun.kylin.customized.rpc.dto.ZerXCouponResponseDto;
import com.shuyun.kylin.customized.rpc.entity.ZerXConfigEntity;
import com.shuyun.kylin.customized.rpc.template.base.KoRequestTemplate;
import com.shuyun.motor.common.cons.PropsUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 知而行券请求模板
 */
@Slf4j
public class ZerXRequestTemplate extends KoRequestTemplate {

    static final ZerXConfigEntity zerXConfig = JSON.parseObject(PropsUtil.getSysOrEnv("zerx.api.config"), ZerXConfigEntity.class);

    /**
     *
     * @param zerxCouponRequestDto
     * @return
     */
    public static ZerXCouponResponseDto sendCoupon(ZerXCouponRequestDto zerxCouponRequestDto, String transactionId) {
        log.info("知而行请求原始参数，zerXCouponRequestDto={}", JSON.toJSONString(zerxCouponRequestDto));

        int retryCount = 0;
        while(retryCount < 3) {
            try {
                String body = HttpUtil.createPost(zerXConfig.getSendCouponUrl())
                        .timeout(10000)
                        .form("appid", zerxCouponRequestDto.getAppid())
                        .form("openId",zerxCouponRequestDto.getOpenId())
                        .form("id",zerxCouponRequestDto.getId())
                        .form("scene",zerxCouponRequestDto.getScene())
                        .form("qrcodeScene",zerxCouponRequestDto.getQrcodeScene())
                        .form("transactionId", zerxCouponRequestDto.getTransactionId())
                        .form("lng",zerxCouponRequestDto.getLng())
                        .form("lat",zerxCouponRequestDto.getLat())
                        .form("timestamp", zerxCouponRequestDto.getTimestamp())
                        .form("sign", zerxCouponRequestDto.getSign())
                        .execute().body();
                log.info("知而行响应结果,body={}", body);
                pushKoRpcRequestLog(zerXConfig.getSendCouponUrl(), zerxCouponRequestDto, body, transactionId, SendCouponEnum.ZERX.getCode());

                ZerXCouponResponseDto zerXCouponResponseDto = JSON.parseObject(body, ZerXCouponResponseDto.class);
                log.info("知之行券响应对象，zerXCouponResponseDto={}", JSON.toJSONString(zerXCouponResponseDto));
                return zerXCouponResponseDto;
            } catch (Exception e) {
                log.error("知之行券发放失败，zerxCouponRequestDto={}", zerxCouponRequestDto, e);
                pushKoRpcRequestLog(zerXConfig.getSendCouponUrl(), zerxCouponRequestDto, e.getMessage(), transactionId, SendCouponEnum.ZERX.getCode());
            }
            retryCount++;
            try {
                Thread.sleep(20L);
            } catch (InterruptedException e) {
                log.error("thread sleep exception: ", e);
            }
        }
        return null;
    }
}
