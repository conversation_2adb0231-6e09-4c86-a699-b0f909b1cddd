package com.shuyun.kylin.customized.member.service.TreadProcess;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.kylin.crm.openapi.core.dto.wechat.WechatRegisterRequest;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.dm.SwireMemberRepository;
import com.shuyun.kylin.customized.base.util.DateUtil;
import com.shuyun.kylin.customized.swire.dto.SwireMemberDto;
import com.shuyun.kylin.customized.swire.dto.SwireMemberRecordsDto;
import com.shuyun.kylin.customized.swire.service.ISwireDataSyncService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.concurrent.Future;

@Service
@Slf4j
public class ThreadProcessService {

    @Autowired
    ISwireDataSyncService iSwireDataSyncService;

    @Autowired
    private SwireMemberRepository swireMemberRepository;

    @Async("executorService")
    public Future sendSwireMember(SwireMemberRecordsDto swireMemberRecordsDto, String url, String status) {
        try {
            Long start = System.currentTimeMillis();
            log.info("注册swireMemberDto:{},url:{},status:{}",swireMemberRecordsDto,url,status);
            iSwireDataSyncService.registerSyncMember(swireMemberRecordsDto, url, status);
            log.info("同步会员注册mobile:{},耗时:{}ms,status:{}",swireMemberRecordsDto.getMobile() , System.currentTimeMillis() - start,status);
        } catch (Exception e) {
            upsertSwireMemberLog(swireMemberRecordsDto,e.getMessage(),status);
            log.error("多线程同步太古注册失败:{},msg:{}", JSON.toJSONString(swireMemberRecordsDto), e.getMessage());
        }
        return new AsyncResult("");
    }

    @Async("executorService")
    public Future updateSwireMember(SwireMemberRecordsDto swireMemberRecordsDto, String url, String status) {
        try {
            Long start = System.currentTimeMillis();
            log.info("更新swireMemberDto:{},url:{},status:{}",swireMemberRecordsDto,url,status);
            iSwireDataSyncService.registerSyncMember(swireMemberRecordsDto, url, status);
            log.info("同步会员更新mobile:{},耗时:{}ms,status:{}",swireMemberRecordsDto.getMobile() , System.currentTimeMillis() - start,status);
        } catch (Exception e) {
            upsertSwireMemberLog(swireMemberRecordsDto,e.getMessage(),status);
            log.error("多线程同步太古更新失败:{},msg:{}", JSON.toJSONString(swireMemberRecordsDto), e.getMessage());
        }
        return new AsyncResult("");
    }

    public void upsertSwireMemberLog(SwireMemberRecordsDto swireMemberRecordsDto, String msg, String status){
        SwireMemberDto registerMember = new SwireMemberDto();
        registerMember.setMobile(swireMemberRecordsDto.getMobile());
        registerMember.setStatus(status);
        registerMember.setLastSync(DateUtil.getGMT8Str(new Date()));
        registerMember.setCode(500);
        registerMember.setMsg(msg);
        swireMemberRepository.upsert(ModelConstants.SWIRE_MEMBER_SYNC_LOG, swireMemberRecordsDto.getMobile(), JSONObject.parseObject(JSONObject.toJSONString(registerMember), HashMap.class));
    }

}
