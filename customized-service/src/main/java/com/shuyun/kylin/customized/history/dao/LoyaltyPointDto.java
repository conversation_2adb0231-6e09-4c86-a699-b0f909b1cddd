package com.shuyun.kylin.customized.history.dao;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.ZonedDateTime;

@Data
public class LoyaltyPointDto {

    private Integer id;
    private String memberId;
    private String channelType;
    private String changeType;
    private Integer pointAccountId;
    private String shopId;
    private String businessId;
    private Integer idempotentMode;
    private String desc;
    private String actionName;
    private String changeMode;
    private boolean autoFillShopId;
    private Double changePoint;
    private ZonedDateTime effectiveDate;
    private ZonedDateTime overdueDate;
    private String changeSourceDetail;
    private String changeSource;
    private String costTracing;



}
