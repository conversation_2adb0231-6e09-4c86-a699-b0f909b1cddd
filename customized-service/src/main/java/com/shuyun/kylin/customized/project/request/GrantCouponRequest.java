package com.shuyun.kylin.customized.project.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 发券请求dto
 */
@Data
public class GrantCouponRequest {
    /**
     * 品牌
     * 固定KO
     */
    @NotEmpty(message = "品牌名称不能为空")
    private String brand;
    /**
     * 渠道类型
     * KO_MP：微信可口可乐吧
     * KO_Ali：支付宝可口可乐吧
     * {@link com.shuyun.kylin.customized.project.enums.CocoChannelTypeEnum}
     */
    @NotEmpty(message = "渠道类型不能为空")
    private String channelType;
    /**
     * 全局唯一
     */
    private String transactionId;
    /**
     * 填memberId
     */
    @NotEmpty(message = "会员ID不能为空")
    private String memberGrantIdentify;
    /**
     * 项目Id
     */
    @NotEmpty(message = "项目ID不能为空")
    private String projectId;
    /**
     * 活动id
     */
    @NotEmpty(message = "活动ID不能为空")
    private String campaignId;
    /**
     * 来源活动名称
     */
    private String campaignName;
    /**
     * 证件号码
     */
    private String govId;
    /**
     * 券核销所需的证件类型,为以下取值:
     * GovernmentId - 身份证
     * ChinaPR - 外国人永久居留证
     * TravelPermit - 港澳台居民来往内地通行证
     * ResidencePermit - 港澳台居民居住证
     * Passport - 护照;缺省为身份证
     * {@link com.shuyun.kylin.customized.project.enums.CocoGovIdTypeEnum}
     */
    private String govIdType;
    /**
     * 客户姓名
     */
    private String guestName;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 发放券对应的lbs省
     */
    private String sendLBSProvince;
    /**
     *发放券对应的lbs市
     */
    private String sendLBSCity;
    /**
     * 发放券对应的lbs区
     */
    private String sendLBSDistrict;

    /**
     * 积分流水id (例：在参与抽奖活动中扣减积分的幂等id)
     */
    private String businessId;

    /**
     * 来源模块
     */
    private String sourceModule;

    /**
     * 营销节点id
     */
    private String marketingNodeId;

    private String utmSource;

    private String utmMedia;

    private Integer point;

}
