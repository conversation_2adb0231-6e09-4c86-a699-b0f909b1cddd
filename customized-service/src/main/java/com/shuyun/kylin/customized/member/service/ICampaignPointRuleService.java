package com.shuyun.kylin.customized.member.service;

import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.member.dto.*;

public interface ICampaignPointRuleService {

    ResponseResult getCampaignPointsRule(String scene);

    ResponseResult deletCampaignPointsRule(String scene);

    ResponseResult saveCampaignPointsRule(CepRegulationPointDto cepRegulationPointDto);

    ResponseResult updateMemberPoint(CepMemberDeductionDto cepMemberDeductionDto);

    ResponseResult queryGetablePoint(CepMemberGetableDto cepMemberGetableDto);

    ResponseResult updateMemberPointLog(CepRulePointLogDto cepRulePointLogDto);
}
