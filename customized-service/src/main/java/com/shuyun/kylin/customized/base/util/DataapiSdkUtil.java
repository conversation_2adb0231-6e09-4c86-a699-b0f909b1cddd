package com.shuyun.kylin.customized.base.util;


import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.dm.dataapi.sdk.DataapiSdkFactory;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk;
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe;

import java.util.Collections;

/**
 * @Author: xiaolong.chang
 * @Date: 2020/12/11
 * @Description:
 */
public class DataapiSdkUtil {
//    private static final ThreadLocal<com.shuyun.dm.api.context.UserContext> USER_CONTEXT = new ThreadLocal<>();
//    static {
//        // 设置当前用户上下文对象的回调方法
//        DataapiSdkFactory.INSTANCE.userContextSupplier(USER_CONTEXT::get);
//    }


    public static DataapiHttpSdk getDataapiHttpSdk() {
        return dataapiHttpSdk;
    }

    public static DataapiWebSocketSdk getDataapiWebSocketSdk() {
        return dataapiHttpSdk.asDataapiWebSocketSdk();
    }

    final static DataapiHttpSdk dataapiHttpSdk;

    static {
        DataapiSdkFactory.INSTANCE.userContextSupplier(() -> {
            return UserContextThreadSafe.getInstance();
        });
        dataapiHttpSdk = DataapiSdkFactory.INSTANCE.createDataapiHttpSdk();
    }



//    public static BaseResponse execute(String sql){
//        try {
//            // 创建当前用户上下文对象
//            com.shuyun.dm.api.context.UserContext.DefaultUserContext uc = new com.shuyun.dm.api.context.UserContext.DefaultUserContext();
//            // 不要设置Token到当前用户上下文对象中
//            // uc.setToken(token);
//            // 不要设置用户ID到当前用户上下文对象中
//            // uc.setUserId(xxx);
//            // ***重要***如果当前服务已经进行多租户改造，则必须设置租户ID到当前用户上下文对象中***
////            uc.setTenantId("emybest");
//            // 设置其他上下文参数（如果需要的话）
////            uc.setXxx(xxx);
//            // 将当前用户上下文对象设置到线程本地变量
//            USER_CONTEXT.set(uc);
//
//            // 创建/获取客户端SDK实例，并执行业务
//            return dataapiHttpSdk.execute(sql, Collections.emptyMap());
//        } finally {
//            // 清理线程本地变量中的用户上下文对象
//            USER_CONTEXT.set(null);
//        }
//    }


//    public static void transactionBegin(DataapiWebSocketSdk dataapiWebSocketSdk){
//        try {
//            // 创建当前用户上下文对象
//            com.shuyun.dm.api.context.UserContext.DefaultUserContext uc = new com.shuyun.dm.api.context.UserContext.DefaultUserContext();
//            // 将当前用户上下文对象设置到线程本地变量
//            USER_CONTEXT.set(uc);
//            dataapiWebSocketSdk.transactionBegin();
//        } finally {
//            // 清理线程本地变量中的用户上下文对象
//            USER_CONTEXT.set(null);
//        }
//    }

//    public static void transactionCommit(DataapiWebSocketSdk dataapiWebSocketSdk){
//        try {
//            // 创建当前用户上下文对象
//            com.shuyun.dm.api.context.UserContext.DefaultUserContext uc = new com.shuyun.dm.api.context.UserContext.DefaultUserContext();
//            // 将当前用户上下文对象设置到线程本地变量
//            USER_CONTEXT.set(uc);
//            dataapiWebSocketSdk.transactionCommit();
//        } finally {
//            // 清理线程本地变量中的用户上下文对象
//            USER_CONTEXT.set(null);
//        }
//    }

//    public static void transactionRollback(DataapiWebSocketSdk dataapiWebSocketSdk){
//        try {
//            // 创建当前用户上下文对象
//            com.shuyun.dm.api.context.UserContext.DefaultUserContext uc = new com.shuyun.dm.api.context.UserContext.DefaultUserContext();
//            // 将当前用户上下文对象设置到线程本地变量
//            USER_CONTEXT.set(uc);
//            dataapiWebSocketSdk.transactionRollback();
//        } finally {
//            // 清理线程本地变量中的用户上下文对象
//            USER_CONTEXT.set(null);
//        }
//    }

    public static void close(DataapiWebSocketSdk dataapiWebSocketSdk) {
        dataapiWebSocketSdk.close();
    }

    public static void open(DataapiWebSocketSdk dataapiWebSocketSdk) {
        dataapiWebSocketSdk.open();
    }

}
