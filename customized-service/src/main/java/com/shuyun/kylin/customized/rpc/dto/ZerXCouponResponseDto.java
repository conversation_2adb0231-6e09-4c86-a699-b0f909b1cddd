package com.shuyun.kylin.customized.rpc.dto;

import lombok.Data;

/**
 * 知而行券返参
 */
@Data
public class ZerXCouponResponseDto {
    /**
     * <pre>
     * 状态码：
     * 200成功
     * 499-系统异常
     * 4004-优惠券已下架或驳回
     * 4006-领取上限
     * 4008-微信发放单品券失败
     * 4014-未到领券时间
     * 4015-领券已结束
     * 5000-调⽤微信接⼝异常
     * 5001-兑券执⾏中……（当同⼀个transactionId， 在兑换完成之前若收到2次以上兑换请求， 会触发幂等拦截返回此状态码； 兑换完成之后则返回实际兑换结果。）
     * 10001-参数校验不通过
     * 10004-暂⽆可领优惠券
     * 60001-库存不⾜，没券了
     * </pre>
     */
    private Integer code;
    /**
     * 状态码描述
     */
    private String msg;
    /**
     * 返回数据，不是必返回字段
     */
    private RspData data;

    @Data
    public static class RspData {
        /**
         * 此次领取序号
         */
        private Integer seqNo;
        /**
         * 此次领取券码
         */
        private String coupon;
        /**
         * 总的已领数量
         */
        private Integer receiveNum;
        /**
         * 券配置的领取上限/⼈
         */
        private Integer receiveLimit;
    }

}
