package com.shuyun.kylin.customized.base.util;


import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;

import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

/**
 * AES常用解密加密工具类
 */
public class AESUtil {

    /**
     * 16字节
     */
    private static final String ENCODE_KEY = "681cb8c0388c4615";
    private static final String IV_KEY = "9e39ae6dc8f14c3b";

    public static void main(String[] args) {
        String encryptData = encryptFromString(ENCODE_KEY, IV_KEY, "helloaaaa.", Mode.ECB, Padding.PKCS5Padding);
        System.out.println("加密：" + encryptData);
        String decryptData = decryptFromString(ENCODE_KEY, IV_KEY, encryptData, Mode.ECB, Padding.PKCS5Padding);
        System.out.println("解密：" + decryptData);
    }

    public static String encryptByCBC(String appKey, String appIv, String data) {
        return encryptFromString(appKey, appIv, data, Mode.CBC, Padding.PKCS5Padding);
    }

    public static String encryptByECB(String appKey, String appIv, String data) {
        return encryptFromString(appKey, appIv, data, Mode.ECB, Padding.PKCS5Padding);
    }

    public static String encryptFromString(String appKey, String appIv, String data, Mode mode, Padding padding) {
        AES aes;
        if (Mode.CBC == mode) {
            aes = new AES(mode, padding,
                    new SecretKeySpec(appKey.getBytes(), "AES"),
                    new IvParameterSpec(appIv.getBytes()));
        } else {
            aes = new AES(mode, padding,
                    new SecretKeySpec(appKey.getBytes(), "AES"));
        }
        return aes.encryptBase64(data, StandardCharsets.UTF_8);
    }

    public static String decryptFromString(String appKey, String appSecret, String data, Mode mode, Padding padding) {
        AES aes;
        if (Mode.CBC == mode) {
            aes = new AES(mode, padding,
                    new SecretKeySpec(appKey.getBytes(), "AES"),
                    new IvParameterSpec(appSecret.getBytes()));
        } else {
            aes = new AES(mode, padding,
                    new SecretKeySpec(appKey.getBytes(), "AES"));
        }
        byte[] decryptDataBase64 = aes.decrypt(data);
        return new String(decryptDataBase64, StandardCharsets.UTF_8);
    }

}


