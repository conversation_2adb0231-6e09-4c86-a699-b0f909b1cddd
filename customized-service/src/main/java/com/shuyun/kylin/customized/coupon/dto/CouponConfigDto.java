package com.shuyun.kylin.customized.coupon.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@Schema(description = "酷客多优惠券配置")
public class CouponConfigDto {

    @NotBlank
    @Schema(description = "酷客多appId", required = true)
    private String appId;

    @NotBlank
    @Schema(description = "麒麟租户apiKey，从boss系统获取", required = true)
    private String apiKey;

    @NotBlank
    @Schema(description = "麒麟租户apiSecret，从boss系统获取", required = true)
    private String apiSecret;

    @Schema(description = "品牌，选填", required = false)
    private String memberType;

    @Schema(description = "店铺编号，选填", required = false)
    private String shopCode;
}
