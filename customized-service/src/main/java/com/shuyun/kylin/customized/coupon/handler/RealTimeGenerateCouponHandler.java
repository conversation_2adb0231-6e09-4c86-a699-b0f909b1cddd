package com.shuyun.kylin.customized.coupon.handler;

import com.shuyun.kylin.customized.coupon.domain.Coupon;
import com.shuyun.kylin.customized.coupon.dto.CustomerCouponDto;
import com.shuyun.kylin.customized.coupon.service.ICouponService;
import com.shuyun.pip.component.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component("realTimeGenerateCouponHandler")
public class RealTimeGenerateCouponHandler implements CouponSendHandler {

    @Autowired
    private ICouponService iCouponService;

    @Override
    public void dealCoupon(CustomerCouponDto customerCouponDto) {
        log.info("进入获取卡券后同步人券关系 接收报文:{}", JsonUtils.toJson(customerCouponDto));
        if (!StringUtils.isEmpty(customerCouponDto.getProjectId())) {
            List<Coupon> coupons =  iCouponService.assemblyData(customerCouponDto,customerCouponDto.getData());
            iCouponService.searchDataList(coupons);
        }
    }
}
