package com.shuyun.kylin.customized.behavior.util;

import java.util.List;
import java.util.UUID;

public class StringUtils {
    public static String uuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    public static void main(String[] args) {
        System.out.println(uuid());
    }

    public static boolean isNullOrEmpty(String time) {
        return time == null || time.length() == 0;
    }

    public static boolean isNotNullOrEmpty(String time) {
        return !isNullOrEmpty(time);
    }

    public static String wrapperWith(String prefix, String value) {
        return prefix + value + prefix;
    }

    public static String wrapperWith(String prefix, List<String> values) {
        StringBuilder stringBuilder = new StringBuilder();

        for (String value : values) {
            stringBuilder.append(prefix)
                    .append(value)
                    .append(prefix)
                    .append(",");
        }

        return stringBuilder.deleteCharAt(stringBuilder.length() - 1)
                .toString();
    }

}
