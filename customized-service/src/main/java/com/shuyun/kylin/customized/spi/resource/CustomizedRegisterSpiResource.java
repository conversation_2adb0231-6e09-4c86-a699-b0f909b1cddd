package com.shuyun.kylin.customized.spi.resource;

import com.shuyun.kylin.crm.openapi.core.dto.tmall.TaobaoRegisterRequest;
import com.shuyun.kylin.crm.openapi.core.dto.tmall.TaobaoRegisterResponseDto;
import com.shuyun.kylin.crm.openapi.spi.IRegisterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Description 用于定制化 Openapi  注册业务逻辑
 * 前提:  /customized-service/customized-service.openapi.spi.enabled=true
 * @date 2020/4/12
 */
@Tag(name = "定制注册spi接口", description = "用于定制化 Openapi  注册业务逻辑")
@RestController
@RequestMapping("/openapi")
@ConditionalOnExpression("${customized-service.openapi.spi.enabled}")
public class CustomizedRegisterSpiResource implements IRegisterService {

    @Override
    @PostMapping("/tmall/register")
    @Operation(summary = "天猫会员通定制注册接口业务", description = "应用场景: 用于实现天猫会员通定制注册接口业务")
    public TaobaoRegisterResponseDto tmallRegister(@Valid @RequestBody TaobaoRegisterRequest taobaoRegisterRequest) {
        return null;
    }
}
