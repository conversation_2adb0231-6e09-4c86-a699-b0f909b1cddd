package com.shuyun.kylin.customized.base.common;

import com.shuyun.motor.common.cons.PropsUtil;

public final class Constants {
    private Constants() {
    }
    public static final String SUCESSCODE =                           "00";
    public static final String COMMONSUCCESSMSG =                     "处理成功";
    public static final String FAILCODE =                             "01";
    public static final String COMMONFAILMSG =                        "处理失败";
    public static final int len =                           9;
    public static final  String REDIS_KEY_MODELMAPPING = "CUSTOMIZED_MODELMAPPING";
    public static final String ZERO_STR = "0";
    public static final String ONE_STR = "1";

    /**
     * 数据接入相关 系统常量
      */
    public static class DataTransferConstant{
        // topic名称租户key
        public static final String TENANT_KEY_TOPIC = "#tenant";
        // 租户id
        public static final String TENANT_ID = PropsUtil.getSysOrEnv("system.tenant","ironman");
        // 系统环境，必须配置与后台api相关
        public static final String SYSTEM_ENVIRONMANT = PropsUtil.getSysOrEnv("system.environment","dironman");
        // kafka连接
        public static final String KAFKA_HOST = PropsUtil.getSysOrEnv("kafka.brokers", "localhost:9092");
        // 允许查询的行数
        private static final  String QUERY_ROWS_LIMIT_CONFIG = PropsUtil.getSysOrEnv("customized.query.rows.limit","100");
        public static final int DEFAULT_QUERY_ROWS_LIMIT = Integer.valueOf(QUERY_ROWS_LIMIT_CONFIG);
        // 查询配置 条件参数分隔符
        public static final  String QUERY_CONFIG_PARAMS_SEPARATOR = "_";
        // 默认kafka 消费组
        public static final String KAFKA_DATA_CONNECTOR_CONSUMERS_GROUP = "CRM_COUPONS_GROUP";
        // 数据模型类型的数据连接名称
        public static final String DATAMODEL_DATA_CONNECTOR_NAME = "dataModel-connector请勿删除";
        // kafka类型的数据连接名称
        public static final String KAFKA_DATA_CONNECTOR_NAME = "kafka-connector请勿删除";
        // 数据流版本 当前版本v3
        public static final String DATA_FLOW_VERSION = "v3";
        // epassport 版本
        public static final String EPASSPORT_API_VERSION = "v1";
    }
}