package com.shuyun.kylin.customized.project.callback;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.project.dto.McdCallbackRequestDto;
import com.shuyun.kylin.customized.project.service.KoCouponService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 麦当劳回调
 * @author: Jingwei
 * @date: 2025-01-09
 */
@Slf4j
@RestController
@RequestMapping("/mcd")
public class McdCallbackController {

    @Resource
    private KoCouponService koCouponService;

    @PostMapping("/notifyForCouponUse")
    public ResponseResult<String> notifyForCouponUse(@RequestBody McdCallbackRequestDto requestDto, HttpServletRequest request) {
        log.info("麦当劳回调开始，入参requestDto={}", JSON.toJSONString(requestDto));
        return koCouponService.notifyForCouponUse(requestDto, request);
    }
}
