package com.shuyun.kylin.customized.medal.response;

import lombok.Data;

import java.util.List;
//2.52.查询勋章等级
@Data
public class MedalGradeResponse {

    //当前勋章等级Id
    private Integer medalCurrentGradeDefinitionId;

    //当前勋章等级名称
    private String medalCurrentGradeName;

    //勋章等级生效时间
    private String effectDate;

    //已获取勋章总数
    private Integer medalTotal;

    //距离下勋章等级需要勋章数
    private Integer  medalRequired;

    private List<MedalLevelThreshold> MedalLevelThreshold;

    @Data
    public static class MedalLevelThreshold {
        //勋章等级Id
        private Integer medalGradeDefinitionId;
        //勋章等级名称
        private String medalCurrentGradeName;
        //勋章等级任务门槛值
        private Integer medalLevelThresholdValue;
    }
}
