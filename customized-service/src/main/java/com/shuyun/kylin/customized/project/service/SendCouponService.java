package com.shuyun.kylin.customized.project.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.enums.ResponseCodeEnum;
import com.shuyun.kylin.customized.base.exception.CustomizeException;
import com.shuyun.kylin.customized.base.feign.client.MemberPointModifyClient;
import com.shuyun.kylin.customized.base.feign.client.OpenApiFeignClientV3;
import com.shuyun.kylin.customized.base.feign.dao.*;
import com.shuyun.kylin.customized.base.util.ApiSignUtils;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.base.util.DataapiSdkUtil;
import com.shuyun.kylin.customized.base.util.DateHelper;
import com.shuyun.kylin.customized.project.dto.*;
import com.shuyun.kylin.customized.project.entity.InnerGrantCouponFailureLog;
import com.shuyun.kylin.customized.project.entity.KOThirdPartCallbackLog;
import com.shuyun.kylin.customized.project.enums.*;
import com.shuyun.kylin.customized.project.exception.CustomizedRetryException;
import com.shuyun.kylin.customized.project.request.GrantCouponRequest;
import com.shuyun.kylin.customized.project.response.BKInstanceUseOrUnUseRequest;
import com.shuyun.kylin.customized.project.response.GrantCouponResponse;
import com.shuyun.kylin.customized.project.strategy.SendCouponStrategy;
import com.shuyun.kylin.customized.project.strategy.dto.TemplateNameProxyDto;
import com.shuyun.kylin.customized.project.strategy.router.SendCouponEnum;
import com.shuyun.kylin.customized.rpc.dto.WxSendCouponResponseDto;
import com.shuyun.kylin.customized.rpc.enums.UbrCoupoonCreatedStatusEnum;
import com.shuyun.kylin.starter.exception.model.ThirdPartException;
import com.shuyun.kylin.starter.redis.ICache;
import com.shuyun.motor.common.cons.PropsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Slf4j
@Service
public class SendCouponService {

    static final DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();
    /**
     * 卡券项目列表内部接口地址
     */
    @Resource
    private OpenApiFeignClientV3 openApiFeignClientV3;

    /**
     * 积分兑换券内部接口地址
     */
    @Resource
    private MemberPointModifyClient memberPointModifyClient;

    @Resource
    @Qualifier("redisCache")
    private ICache redisCache;

    private static final String LOCK_PREFIX = "KO_SP:";

    private static final String KO = "KO";

    static final String COUPON_INSTANCE_MODEL = PropsUtil.getSysOrEnv("ko.coupon.instance.model");

    static final String WEHACT_CALLBACK_KEY = PropsUtil.getSysOrEnv("wechat.callback.key");

    /**
     * 发放优惠券
     * @param templateId
     * @param grantCouponRequest
     */
    public ResponseResult<GrantCouponResponse> doSendCoupon(String templateId, GrantCouponRequest grantCouponRequest, OfferProjectDetailClientResponse projectDetail) {
        log.info("外部发券，templateId={},grantCouponRequest={}", templateId, JSON.toJSONString(grantCouponRequest));
        ResponseResult<GrantCouponResponse> result = new ResponseResult<>();
        if(ObjectUtil.isEmpty(templateId)) {
            log.info("内部错误，未获取到模板ID");
            result.setCode(ResponseCodeEnum.GRANT_COUPON_FAILED.getCode());
            result.setMsg("内部错误，未获取到券项目模板ID");
            return result;
        }
        // 只有名称为三方券的模板才需要加锁
        //Lock lock = redisCache.getLock(LOCK_PREFIX.concat(grantCouponRequest.getProjectId()));

        String templateType = getTemplateTypeById(templateId);
        log.info("券项目模板类型:{}",templateType);
        if(ObjectUtil.isNull(templateType)) {
            log.info("内部错误，未获取到券项目模板类型，templateId={}", templateId);
            result.setCode(ResponseCodeEnum.GRANT_COUPON_FAILED.getCode());
            result.setMsg("内部错误，未获取到券项目模板类型");
            return result;
        }
        // 判断是否需要加锁
        /*boolean needHoldLock = ObjectUtil.equals(templateType, SendCouponEnum.THREE_PARTIES.getCode());
        boolean tryLock = needHoldLock && lock.tryLock();
        if(needHoldLock && !tryLock) {
            log.info("发券获取锁失败，templateId={},grantCouponRequest={}", templateId, JSON.toJSONString(grantCouponRequest));
            result.setCode(ResponseCodeEnum.GRANT_COUPON_FAILED.getCode());
            result.setMsg("权益发放失败，请稍后再试");
            return result;
        }*/
        try {
            SendCouponStrategy strategy = SendCouponEnum.router(templateType);
            Assert.notNull(strategy, "内部错误，未获取到模板策略");
            MemberBingDto memberBindInfo = getMemberBindInfo(grantCouponRequest.getMemberGrantIdentify(), grantCouponRequest.getChannelType());
            SendCouponResultDto sendCouponResultDto = strategy.sendCoupon(templateId, projectDetail, grantCouponRequest, memberBindInfo);
            if(ObjectUtil.isEmpty(sendCouponResultDto) || !sendCouponResultDto.getResult()) {
                log.info("内部错误，未获取到外部发券结果，sendCouponResultDto is empty");
                result.setCode(ResponseCodeEnum.GRANT_COUPON_FAILED.getCode());
                result.setMsg("调用第三方接口失败");
                if("zerx".equals(templateType) || "wx".equals(templateType)){
                    result.setCode(sendCouponResultDto.getCode());
                    result.setMsg(sendCouponResultDto.getMsg());
                }
                return result;
            }
            // 调用内部接口
            return callInnerInstanceGrantApi(grantCouponRequest, projectDetail, sendCouponResultDto, templateType);
        } catch (Exception e) {
            log.error("内部错误，发券异常，method=[doSendCoupon]", e);
            result.setCode(ResponseCodeEnum.GRANT_COUPON_FAILED.getCode());
            result.setMsg(e.getMessage());
            return result;
        }/*finally {
            if(needHoldLock) {
                lock.unlock();
            }
        }*/
    }

    /**
     * 获取会员绑定信息
     * @param memberId
     * @return
     */
    private static MemberBingDto getMemberBindInfo(String memberId, String channelType) {
        if(ObjectUtil.isEmpty(memberId)) {
            log.info("内部错误，未获取到会员ID");
            return new MemberBingDto();
        }
        String sql = String.format("select id,memberId,appId,openId,channelType,memberType,mobile from data.prctvmkt.KO.MemberBinding where memberId='%s' and channelType='%s'", memberId, channelType);
        log.info("获取会员绑定关系原始sql={}", sql);
        BaseResponse<Map> execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
        log.info("获取会员绑定关系原始结果，data={}", JSON.toJSONString(execute));
        List<Map> data = execute.getData();
        if (CollectionUtils.isEmpty(data)) {
            log.info("未查询到数据库会员绑定关系结果，execute is empty! memberId={}, channelType={}", memberId, channelType);
            throw new RuntimeException("会员渠道绑定信息不存在");
        }
        Map map = data.get(0);
        CopyOptions copyOptions = CopyOptions.create();
        copyOptions.setIgnoreNullValue(true);
        return BeanUtil.mapToBean(map, MemberBingDto.class, false, copyOptions);
    }

    /**
     * 模板值映射名称
     * {@link TemplateNameProxyDto}
     * @param templateId
     * @return
     */
    public String getTemplateTypeById(String templateId) {
        log.info("发券模板测试key映射，templateId={}", templateId);
        String projectTemplateJSon = PropsUtil.getSysOrEnv("project.templateId");
        Map<String, String> templateNameMap = JSON.parseObject(projectTemplateJSon, new TypeReference<HashMap<String, String>>(){});
        Set<Map.Entry<String, String>> entries = templateNameMap.entrySet();
        for (Map.Entry<String, String> entry : entries) {
            if(ObjectUtil.equals(templateId, entry.getValue())) {
                return entry.getKey();
            }
        }
        return null;
    }

    /**
     * 内部接口发券
     */
    private ResponseResult<GrantCouponResponse> callInnerInstanceGrantApi(GrantCouponRequest request,
                                                           OfferProjectDetailClientResponse projectDetail,
                                                           SendCouponResultDto outSendCouponResultDto,
                                                           String templateType) {
        log.info("内部发券业务开始，request={}，projectDetail={}", JSON.toJSONString(request), JSON.toJSONString(projectDetail));
        OfferProjectDetailClientResponse.RspData projectDetailData = projectDetail.getData();
        OfferProjectDetailClientResponse.RspData.ExtData extData = projectDetailData.getExtData();
        Boolean isPointExchange = extData.getIsPointExchange();
        ResponseResult<GrantCouponResponse> result = new ResponseResult<>();
        // 两种情况：1-扣积分，2-不扣积分
        // 扣积分情况：扣积分 -> 发券 -> 如果发券失败返还积分
        // 不扣积分情况：直接发券
        if(Boolean.TRUE.equals(isPointExchange)) {
            log.info("isPointExchange=true，需要扣积分");
            // 1. 先扣积分
            MemberPointModifyRequest requestForDeductPoint = assembleInnerDeductPoint(request, extData, projectDetail.getData());
            boolean deductPointResult = deductPoint(requestForDeductPoint);
            if(!deductPointResult){
                log.info("积分扣减失败");
                saveRecordIfInnerGrantFailed(projectDetailData, outSendCouponResultDto, request, requestForDeductPoint, request.getTransactionId(), true, true);
                result.setCode(ResponseCodeEnum.GRANT_COUPON_FAILED.getCode());
                result.setMsg("积分扣减失败");
                return result;
            }
            // 2. 发券
            SendCouponResultDto  innerGrantCouponResult = grantCoupon(request, outSendCouponResultDto.getExternalCode(), projectDetailData);
            log.info("内部券发券结果，ExternalCode:{},innerGrantCouponResult:{}",outSendCouponResultDto.getExternalCode(), JSON.toJSONString(innerGrantCouponResult));
            if(innerGrantCouponResult.getResult()) {
                log.info("发券成功");
                // 激活券并更新三方券状态
                activeAndModifyThirdCouponStatus(request, outSendCouponResultDto, templateType, innerGrantCouponResult);
                result.setCode(ResponseCodeEnum.SUCCESS.getCode());
                result.setMsg("发券成功");
                assembleCouponCodes(outSendCouponResultDto, innerGrantCouponResult, result);
                return result;
            }
            // 3. 如果发券失败，需要返还积分(只在发券失败时走该流程)
            // 处理积分返还逻辑
            log.info("发券失败:{},innerGrantCouponResult:{}",outSendCouponResultDto.getExternalCode(),JSON.toJSONString(innerGrantCouponResult));
            Boolean returnPointResult = ((SendCouponService)AopContext.currentProxy()).returnPoint(requestForDeductPoint);
            if(Boolean.FALSE.equals(returnPointResult)) {
                log.info("发券失败，返还积分失败");
                // 此时积分返还失败，不需要再次扣积分，只需要发一次内部券即可
                saveRecordIfInnerGrantFailed(projectDetailData, outSendCouponResultDto, request, requestForDeductPoint, request.getTransactionId(), true, false);
                result.setCode(ResponseCodeEnum.GRANT_COUPON_FAILED.getCode());
                result.setMsg("发券失败，返还积分失败");
                return result;
            }
            // 此时积分已返还，需要重新扣积分并发券
            saveRecordIfInnerGrantFailed(projectDetailData, outSendCouponResultDto, request, requestForDeductPoint, request.getTransactionId(), true, true);
            result.setCode(ResponseCodeEnum.GRANT_COUPON_FAILED.getCode());
            result.setMsg("发券失败，积分已返还");
            log.info("积分兑换权益失败:{}",JSON.toJSONString(result));
            return result;
        } else {
            log.info("isPointExchange=false，直接发券");
            SendCouponResultDto  grantCouponResult = grantCoupon(request, outSendCouponResultDto.getExternalCode(), projectDetailData);
            if(!grantCouponResult.getResult()) {
                saveRecordIfInnerGrantFailed(projectDetailData, outSendCouponResultDto, request, null, request.getTransactionId(), false, false);
                result.setCode(ResponseCodeEnum.GRANT_COUPON_FAILED.getCode());
                result.setMsg("发券失败");
                return result;
            }
            // 激活券并更新三方券状态
            activeAndModifyThirdCouponStatus(request, outSendCouponResultDto, templateType, grantCouponResult);
            result.setCode(ResponseCodeEnum.SUCCESS.getCode());
            result.setMsg("发券成功");
            assembleCouponCodes(outSendCouponResultDto, grantCouponResult, result);
            return result;
        }
    }

    /**
     * 如果外部成功，内部失败，需要保存记录，以便重新推送券码
     *
     * @param outSendCouponResultDto 外部券发放结果
     * @param bothRecord true-积分和发券都记录，false-只记录发券
     */
    private void saveRecordIfInnerGrantFailed(OfferProjectDetailClientResponse.RspData projectDetail,
                                              SendCouponResultDto outSendCouponResultDto,
                                              GrantCouponRequest grantClientRequestDto,
                                              MemberPointModifyRequest requestForDeductPoint,
                                              String transactionId,
                                              boolean isExchangePoint,
                                              boolean bothRecord) {
        // 外部券结果为空或者成功但是外部券码为空（外部券码为空说明不存在外部发券情况），则不保存记录
        if (ObjectUtil.isEmpty(outSendCouponResultDto)
                || !outSendCouponResultDto.getResult()
                || ObjectUtil.isEmpty(outSendCouponResultDto.getExternalCode())) {
            log.info("外部券结果为空或者成功但是外部券码为空（外部券码为空说明不存在外部发券情况），不保存记录，transactionId={}", transactionId);
            return;
        }
        try {
            if (!bothRecord) {
                saveCreatCouponFailureLog(projectDetail, outSendCouponResultDto, grantClientRequestDto, requestForDeductPoint, transactionId, isExchangePoint);
            } else {
                // 记录券日志
                saveCreatCouponFailureLog(projectDetail, outSendCouponResultDto, grantClientRequestDto, requestForDeductPoint, transactionId, isExchangePoint);
                // 记录积分日志
                savePointFailureLog(projectDetail, outSendCouponResultDto, grantClientRequestDto, requestForDeductPoint, transactionId, isExchangePoint);
            }
        } catch (Exception e) {
            log.error("保存内部发券失败记录失败，e=> ", e);
        }
    }

    private void savePointFailureLog(OfferProjectDetailClientResponse.RspData projectDetail, SendCouponResultDto outSendCouponResultDto, GrantCouponRequest grantClientRequestDto, MemberPointModifyRequest requestForDeductPoint, String transactionId, boolean isExchangePoint) {
        String externalCode = outSendCouponResultDto.getExternalCode();
        InnerGrantCouponFailureLog innerGrantCouponFailureLogForPoint = assembleGrantCouponLog(false, projectDetail, externalCode, grantClientRequestDto, requestForDeductPoint, transactionId, isExchangePoint);
        Map<String, Object> failureLogMapForPoint = BeanUtil.beanToMap(innerGrantCouponFailureLogForPoint, false, true);
        try {
            dataapiHttpSdk.insert("data.prctvmkt.KO.innerGrantCouponFailureLog", failureLogMapForPoint, false, true);
        } catch (Exception ex) {
            log.error("saveRecordIfInnerGrantFailed ====> save inner grant coupon failure log error !,msg:{}", ex);
        }
    }

    private void saveCreatCouponFailureLog(OfferProjectDetailClientResponse.RspData projectDetailData,
                                           SendCouponResultDto outSendCouponResultDto,
                                           GrantCouponRequest grantCouponRequest,
                                           MemberPointModifyRequest requestForDeductPoint,
                                           String transactionId,
                                           boolean isExchangePoint) {
        String externalCode = outSendCouponResultDto.getExternalCode() ;
        InnerGrantCouponFailureLog innerGrantCouponFailureLog = assembleGrantCouponLog(true, projectDetailData, externalCode, grantCouponRequest, requestForDeductPoint, transactionId, isExchangePoint);
        Map<String, Object> failureLogMap = BeanUtil.beanToMap(innerGrantCouponFailureLog, false, true);
        try {
            dataapiHttpSdk.insert("data.prctvmkt.KO.innerGrantCouponFailureLog", failureLogMap, false, true);
        } catch (Exception ex) {
            log.error("saveRecordIfInnerGrantFailed ====> save inner grant coupon failure log error !,msg:{}", ex);
        }
    }

    /**
     *
     * @param flag true-券参数组装，false-积分参数组装
     * @param grantCouponRequest
     * @param requestForDeductPoint
     * @param transactionId
     * @param isExchangePoint
     * @return
     */
    private InnerGrantCouponFailureLog assembleGrantCouponLog(boolean flag,
                                                              OfferProjectDetailClientResponse.RspData projectDetailData,
                                                              String externalCode,
                                                              GrantCouponRequest grantCouponRequest,
                                                              MemberPointModifyRequest requestForDeductPoint,
                                                              String transactionId,
                                                              boolean isExchangePoint) {
        InnerGrantCouponFailureLog innerGrantCouponFailureLog = new InnerGrantCouponFailureLog();
        if(flag) {
            // 获取券创建请求参数
            OfferInstanceGrantClientRequestDto couponCreatedDto = getOfferInstanceGrantClientRequestDto(grantCouponRequest, externalCode, projectDetailData);
            // 券参数组装
            innerGrantCouponFailureLog.setTransactionId(transactionId);
            innerGrantCouponFailureLog.setRequestParams(JSON.toJSONString(couponCreatedDto));
            innerGrantCouponFailureLog.setFailureType(InnerGrantFailureType.COUPON_GRANT_FAILURE.getCode());
            innerGrantCouponFailureLog.setIsPointExchange(isExchangePoint ? IsDeductPointEnum.YES.getCode() : IsDeductPointEnum.NO.getCode());
        } else {
            // 积分参数组装
            innerGrantCouponFailureLog.setTransactionId(transactionId);
            innerGrantCouponFailureLog.setFailureType(InnerGrantFailureType.POINT_FAILURE.getCode());
            innerGrantCouponFailureLog.setRequestParams(JSON.toJSONString(requestForDeductPoint));
        }
        innerGrantCouponFailureLog.setCreateTime(DateHelper.formatZonedDateTime(DateUtil.date(), DateHelper.DATE_FORMAT_T));
        innerGrantCouponFailureLog.setUpdateTime(DateHelper.formatZonedDateTime(DateUtil.date(), DateHelper.DATE_FORMAT_T));
        return innerGrantCouponFailureLog;
    }

    /**
     * 处理内部和外部券码
     * @param outSendCouponResultDto
     * @param innerGrantCouponResult
     */
    private static void assembleCouponCodes(SendCouponResultDto outSendCouponResultDto,
                                                           SendCouponResultDto innerGrantCouponResult,
                                                           ResponseResult<GrantCouponResponse> result) {
        // 此处只处理券码code，不关注grantCouponResponse的状态值
        GrantCouponResponse grantCouponResponse = new GrantCouponResponse();
        grantCouponResponse.setCouponCode(innerGrantCouponResult.getCouponCode());
        grantCouponResponse.setExternalCode(outSendCouponResultDto.getExternalCode());
        result.setData(grantCouponResponse);
    }

    /**
     * 激活券并更新三方券状态
     * @param sendCouponResultDto
     * @param templateType
     */
    private void activeAndModifyThirdCouponStatus(GrantCouponRequest request,
                                                  SendCouponResultDto sendCouponResultDto,
                                                  String templateType,
                                                  SendCouponResultDto innerGrantCouponResult) {
        log.info("激活券码并同步券状态，sendCouponResultDto={}，templateType={},innerGrantCouponResult={}",
                JSON.toJSONString(sendCouponResultDto), templateType, JSON.toJSONString(innerGrantCouponResult));
        if(ObjectUtil.notEqual(templateType, SendCouponEnum.THREE_PARTIES.getCode())) {
            log.info("非三方券模板，不做处理");
            return;
        }
        // 非导入逻辑，不做处理
        if(ObjectUtil.notEqual(templateType, SendCouponEnum.THREE_PARTIES.getCode())
                || ObjectUtil.isEmpty(sendCouponResultDto)
                || ObjectUtil.isEmpty(sendCouponResultDto.getExternalCouponId())
                || ObjectUtil.isEmpty(innerGrantCouponResult.getCouponCode())) {
            log.info("更新三方券状态失败，sendCouponResultDto={}，innerGrantCouponResult={}", JSON.toJSONString(sendCouponResultDto), JSON.toJSONString(innerGrantCouponResult));
            return;
        }
        // 激活券码
        activeCouponIfThirdPart(request, innerGrantCouponResult);
        // crm发券成功后修改三方券模型的这张券的状态为1=已匹配
        String sql = String.format("update data.prctvmkt.KO.ExternalCouponCode  set status = '1' , lastSync ='%s' where  id='%s' and status = '%s'",
                DateHelper.getZone(DateHelper.getDateTimeFormat()), sendCouponResultDto.getExternalCouponId(), '2');
        log.info("更新券匹配状态sql，sql={}", sql);
        BaseResponse execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
        log.info("更新券匹配状态结果，execute={}", JSON.toJSONString(execute));
    }

    /**
     * 激活三方券
     * @param request
     * @param innerGrantCouponResult
     */
    private void activeCouponIfThirdPart(GrantCouponRequest request, SendCouponResultDto innerGrantCouponResult) {
        if(ObjectUtil.isEmpty(innerGrantCouponResult) || ObjectUtil.isEmpty(innerGrantCouponResult.getCouponCode())) {
            return;
        }
        String transactionId = UUID.randomUUID().toString().replace("-", "");
        log.info("激活三方券，originTransactionId={}, currentTransactionId={}", request.getTransactionId(), transactionId);
        OfferInstanceActivateClientRequestDto offerInstanceActivateClientRequestDto = new OfferInstanceActivateClientRequestDto();
        offerInstanceActivateClientRequestDto.setBrand(request.getBrand());
        offerInstanceActivateClientRequestDto.setChannelType(request.getChannelType());
        offerInstanceActivateClientRequestDto.setTransactionId(transactionId);
        offerInstanceActivateClientRequestDto.setCouponCodes(Collections.singletonList(innerGrantCouponResult.getCouponCode()));
        log.info("激活三方券入参，offerInstanceActivateClientRequestDto={}", JSON.toJSONString(offerInstanceActivateClientRequestDto));
        ClientCommonResponseDto<OfferInstanceActivateClientResponseDto> activeResult =
                openApiFeignClientV3.instanceActivate(offerInstanceActivateClientRequestDto);
        log.info("激活三方券原始结果，activeResult={}", JSON.toJSONString(activeResult));
    }

    /**
     * 内部扣积分参数组装
     *
     * @param request
     * @return
     */
    private MemberPointModifyRequest assembleInnerDeductPoint(GrantCouponRequest request,
                                                              OfferProjectDetailClientResponse.RspData.ExtData extData,
                                                              OfferProjectDetailClientResponse.RspData projectDetailData) {
        MemberPointModifyRequest memberPointModifyRequest = new MemberPointModifyRequest();
        memberPointModifyRequest.setPointAccountId(Integer.valueOf(ConfigurationCenterUtil.ACCOUNT_POINT));
        memberPointModifyRequest.setMemberId(request.getMemberGrantIdentify());
        memberPointModifyRequest.setRecordType(PointModifyEnum.DEDUCT.getCode());
        Optional.ofNullable(extData.getPoint()).ifPresent(point -> memberPointModifyRequest.setPoint(Double.valueOf(point)));
        memberPointModifyRequest.setChannelType(request.getChannelType());
        memberPointModifyRequest.setTriggerId(request.getTransactionId());
        // 匹配成本中心
        matchLbsCodeForPointModify(memberPointModifyRequest, request, projectDetailData);
        memberPointModifyRequest.setDesc("积分兑换");
        // 自定义字段，这里定义为：积分兑换单号，值：正反链路都是transactionId
        memberPointModifyRequest.setKZZD1(request.getTransactionId());
        // 自定义字段，这里定义为：变更来源，值为：积分兑换
        memberPointModifyRequest.setKZZD2("积分兑换");
        // 自定义字段，这里定义为：coupon_cost_projectId_流水号或订单号（businessid），比如：coupon_100_A_A001
        memberPointModifyRequest.setKZZD3(String.format("coupon_%s_%s_%s", extData.getCost(), request.getProjectId(), request.getBusinessId()));
        return memberPointModifyRequest;
    }

    /**
     * 扣积分
     * @param requestForDeductPoint
     * @return
     */
    private boolean deductPoint(MemberPointModifyRequest requestForDeductPoint) {
        try {
            log.info("内部扣积分， memberPointModifyRequest={}", JSON.toJSONString(requestForDeductPoint));
            CustomizeException customizeExceptionForDeductPoint = memberPointModifyClient.memberModifyPoint(requestForDeductPoint);
            log.info("内部积分扣减结果，customizeExceptionForDeductPoint={}", JSON.toJSONString(customizeExceptionForDeductPoint));
            // 成功的情况下什么都不会返回，有返回代表失败
            if(ObjectUtil.isNotNull(customizeExceptionForDeductPoint)) {
                return Boolean.FALSE;
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("内部扣积分接口异常，", e);
            return Boolean.FALSE;
        }
    }

    /**
     * 发券 true-成功；false-失败
     */
    private SendCouponResultDto grantCoupon(GrantCouponRequest request, String externalCode, OfferProjectDetailClientResponse.RspData projectDetailData) {
        SendCouponResultDto sendCouponResultDto = new SendCouponResultDto();
        try {
            log.info("grantCoupon开始，request={}", JSON.toJSONString(request));
            OfferInstanceGrantClientRequestDto offerInstanceGrantClientRequestDto = getOfferInstanceGrantClientRequestDto(request, externalCode, projectDetailData);
            log.info("发放券入参，offerInstanceGrantClientRequestDto={}", JSON.toJSONString(offerInstanceGrantClientRequestDto));
            ClientCommonResponseDto<OfferInstanceGrantClientResponseDto> grantCouponResult =
                    openApiFeignClientV3.instanceBatchGrant(offerInstanceGrantClientRequestDto);
            log.info("发放券原始结果，grantCouponResult={}", JSON.toJSONString(grantCouponResult));
            String errorCode = grantCouponResult.getErrorCode();
            // error为空时表示成功
            if(ObjectUtil.isNotEmpty(errorCode)) {
                log.info("OfferInstanceGrantClientResponseDto is empty，发券失败");
                sendCouponResultDto.setResult(Boolean.FALSE);
                return sendCouponResultDto;
            }
            sendCouponResultDto.setResult(Boolean.TRUE);
            sendCouponResultDto.setCouponCode(grantCouponResult.getData().getSuccess().get(0).getCouponCode());
            return sendCouponResultDto;
        }catch (Exception e) {
            log.error("请求内部发券接口异常:{}", e.getMessage());
            sendCouponResultDto.setResult(Boolean.FALSE);
            return sendCouponResultDto;
        }
    }

    /**
     * 发券参数组装
     *
     * @param request
     * @param externalCode
     * @param projectDetailData
     * @return
     */
    private static OfferInstanceGrantClientRequestDto getOfferInstanceGrantClientRequestDto(GrantCouponRequest request, String externalCode, OfferProjectDetailClientResponse.RspData projectDetailData) {
        OfferInstanceGrantClientRequestDto offerInstanceGrantClientRequestDto = new OfferInstanceGrantClientRequestDto();
        offerInstanceGrantClientRequestDto.setBrand(request.getBrand());
        offerInstanceGrantClientRequestDto.setChannelType(request.getChannelType());
        offerInstanceGrantClientRequestDto.setTransactionId(request.getTransactionId());
        offerInstanceGrantClientRequestDto.setGrantPlatform(request.getChannelType());
        OfferInstanceGrantClientRequestDto.MemberGrantIdentify memberGrantIdentify = new OfferInstanceGrantClientRequestDto.MemberGrantIdentify();
        OfferInstanceGrantClientRequestDto.MemberGrantIdentify.Identify identify = new OfferInstanceGrantClientRequestDto.MemberGrantIdentify.Identify();
        identify.setMemberId(request.getMemberGrantIdentify());
        memberGrantIdentify.setIdentify(identify);
        memberGrantIdentify.setNum(1); // 默认1张
        offerInstanceGrantClientRequestDto.setMemberGrantIdentify(memberGrantIdentify);
        offerInstanceGrantClientRequestDto.setProjectId(request.getProjectId());
        offerInstanceGrantClientRequestDto.setSourceModule(request.getSourceModule());
        offerInstanceGrantClientRequestDto.setMarketingNodeId(request.getMarketingNodeId());
        OfferInstanceGrantClientRequestDto.Extension extension = getExtension(request, externalCode, projectDetailData);
        offerInstanceGrantClientRequestDto.setExtension(extension);
        return offerInstanceGrantClientRequestDto;
    }

    private static OfferInstanceGrantClientRequestDto.Extension getExtension(GrantCouponRequest request, String externalCode, OfferProjectDetailClientResponse.RspData projectDetailData) {
        OfferInstanceGrantClientRequestDto.Extension extension = new OfferInstanceGrantClientRequestDto.Extension();
        extension.setGovId(request.getGovId());
        extension.setGovIdType(request.getGovIdType());
        extension.setGuestName(request.getGuestName());
        extension.setMobile(request.getMobile());
        extension.setExternalCode(externalCode);
        extension.setCampaignId(request.getCampaignId());
        extension.setCampaignName(request.getCampaignName());
        extension.setBusinessId(request.getBusinessId());
        extension.setUtmSource(request.getUtmSource());
        extension.setMember(request.getMemberGrantIdentify());
        extension.setUtmMedia(request.getUtmMedia());
        OfferProjectDetailClientResponse.RspData.ExtData extData = projectDetailData.getExtData();
        if(ObjectUtil.isNotNull(extData)) {
            extension.setJumpParameter(extData.getJumpParameter());
            extension.setRedeemType(extData.getRedeemType());
            extension.setExternalProjectId(extData.getExternalProjectId());
        }
        // 匹配成本中心code
        matchLbsCode(extension, request, projectDetailData);
        return extension;
    }

    /**
     * 匹配成本中心code
     * <pre>
     * 1、如果活动/权益设置成按照LBS转化成本中心，且传入LBS信息，将按照转化后的LBS信息记录
     * 2、如果活动/权益设置成按照LBS转化成本中心，但实际用户不授权成本中心，将按照固定成本中心记录
     * 3、如果活动/权益设置不按照LBS转化，则统一按照固定成本中心记录
     * 4、如果活动/权益设置不按照LBS转化，且传入LBS信息，按照固定的成本中心记录取值
     *
     * <p>
     * tips：
     * <p>
     * a.固定就是指即使传了lbs市这个字段，但是前端页面设置的是否，我们就直接按照前端页面存的那个成本中心值存储，不走数据库的匹配逻辑了
     * b.转化后就是说的要按照接口中传的市值去匹配表里找对应的成本中心然后回写到发券对应的成本中心
     * </pre>
     * @param extension
     */
    private static void matchLbsCode(OfferInstanceGrantClientRequestDto.Extension extension,
                                     GrantCouponRequest request,
                                     OfferProjectDetailClientResponse.RspData projectDetailData) {
        log.info("匹配成本中心code，extension={}，request={}，projectDetailData={}",
                JSON.toJSONString(extension), JSON.toJSONString(request), JSON.toJSONString(projectDetailData));
        OfferProjectDetailClientResponse.RspData.ExtData extData = projectDetailData.getExtData();
        // 固定逻辑
        if(!extData.getIsLBSsendCostCenterCode() || ObjectUtil.isEmpty(request.getSendLBSCity())) {
            String sendCostCenterCodeJSONStr = extData.getSendCostCenterCode();
            log.info("成本中心sendCostCenterCodeJSONStr={}", sendCostCenterCodeJSONStr);
            List<String> sendCostCenterCodeList = JSON.parseArray(sendCostCenterCodeJSONStr, String.class);
            String sendCostCenterCode = sendCostCenterCodeList.get(0);
            extension.setSendCostCenterCode(sendCostCenterCode);
            // 取固定成本中心字段
            String sql = String.format("select costCenterCode,bottlerFactoryName,city,district from data.prctvmkt.KO.LbsOuMapping where costCenterCode = '%s' limit 1",
                    sendCostCenterCode);
            log.info("固定-匹配成本中心原始sql={}", sql);
            BaseResponse<Map> execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
            List<Map> data = execute.getData();
            log.info("固定-查询成本中心省市区匹配结果，data={}", JSON.toJSONString(data));
            if(!CollectionUtils.isEmpty(data)) {
                extension.setSendLBSProvince((String)data.get(0).get("bottlerFactoryName"));
                extension.setSendLBSCity((String)data.get(0).get("city"));
                extension.setSendLBSDistrict((String)data.get(0).get("district"));
            }
            return;
        }
        // 否则走成本中心匹配逻辑
        extension.setSendLBSProvince(request.getSendLBSProvince());
        extension.setSendLBSCity(request.getSendLBSCity());
        extension.setSendLBSDistrict(request.getSendLBSDistrict());
        String sql = String.format("select costCenterCode from data.prctvmkt.KO.LbsOuMapping where bottlerFactoryName = '%s'  and city = '%s' and district = '%s' limit 1",
                request.getSendLBSProvince(),request.getSendLBSCity(),request.getSendLBSDistrict());
        log.info("匹配成本中心原始sql={}", sql);
        BaseResponse<Map> execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
        List<Map> data = execute.getData();
        log.info("查询成本中心省市区匹配结果，data={}", JSON.toJSONString(data));
        if(CollectionUtils.isEmpty(data)) {
            String sendCostCenterCodeJSONStr = extData.getSendCostCenterCode();
            log.info("成本中心sendCostCenterCodeJSONStr={}", sendCostCenterCodeJSONStr);
            List<String> sendCostCenterCodeList = JSON.parseArray(sendCostCenterCodeJSONStr, String.class);
            String sendCostCenterCode = sendCostCenterCodeList.get(0);
            extension.setSendCostCenterCode(sendCostCenterCode);
            return;
        }
        String costCenterCode = (String)data.get(0).get("costCenterCode");
        extension.setSendCostCenterCode(costCenterCode);
    }

    /**
     * 积分扣减成本中心逻辑
     * @param memberPointModifyRequest
     * @param request
     * @param projectDetailData
     */
    private static void matchLbsCodeForPointModify(MemberPointModifyRequest memberPointModifyRequest,
                                     GrantCouponRequest request,
                                     OfferProjectDetailClientResponse.RspData projectDetailData) {
        log.info("积分变更-匹配成本中心code，extension={}，request={}，projectDetailData={}",
                JSON.toJSONString(memberPointModifyRequest), JSON.toJSONString(request), JSON.toJSONString(projectDetailData));
        OfferProjectDetailClientResponse.RspData.ExtData extData = projectDetailData.getExtData();
        // 固定逻辑
        if(!extData.getIsLBSsendCostCenterCode() || ObjectUtil.isEmpty(request.getSendLBSCity())) {
            String sendCostCenterCodeJSONStr = extData.getSendCostCenterCode();
            log.info("积分变更-成本中心sendCostCenterCodeJSONStr={}", sendCostCenterCodeJSONStr);
            List<String> sendCostCenterCodeList = JSON.parseArray(sendCostCenterCodeJSONStr, String.class);
            String sendCostCenterCode = sendCostCenterCodeList.get(0);
            memberPointModifyRequest.setShopId(sendCostCenterCode);
            return;
        }
        // 否则走成本中心匹配逻辑
        String sql = String.format("select costCenterCode from data.prctvmkt.KO.LbsOuMapping where city = '%s' limit 1",
                request.getSendLBSCity());
        log.info("积分变更-匹配成本中心原始sql={}", sql);
        BaseResponse<Map> execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
        List<Map> data = execute.getData();
        log.info("积分变更-查询成本中心省市区匹配结果，data={}", JSON.toJSONString(data));
        if(CollectionUtils.isEmpty(data)) {
            return;
        }
        String costCenterCode = (String)data.get(0).get("costCenterCode");
        memberPointModifyRequest.setShopId(costCenterCode);
    }

    /**
     * 返回积分
     * 关于重试说明：主要是为了解决返还积分时的数据库延迟问题，此情况下无法追溯到原始的积分发放信息，因此采用了重试的方式来解决
     * @param requestForDeductPoint
     * @return
     */
    @Retryable(value = CustomizedRetryException.class, maxAttempts = 5, backoff = @Backoff(delay = 300, multiplier = 0))
    public Boolean returnPoint(MemberPointModifyRequest requestForDeductPoint) {
        try {
            CopyOptions copyOptions = new CopyOptions();
            copyOptions.setIgnoreNullValue(true);
            MemberPointModifyRequest requestForReturnPoint = new MemberPointModifyRequest();
            BeanUtil.copyProperties(requestForDeductPoint, requestForReturnPoint, copyOptions);
            // 取上一步扣积分时的triggerId
            requestForReturnPoint.setTradeId(requestForDeductPoint.getTriggerId());
            // 随机
            requestForReturnPoint.setTriggerId(UUID.randomUUID().toString().replace("-", ""));
            // 积分不传
            requestForReturnPoint.setPoint(null);
            requestForReturnPoint.setRecordType(PointModifyEnum.DEDUCT.getCode());
            // 返还积分时该字段重新赋值
            requestForReturnPoint.setDesc("兑换失败");
            requestForReturnPoint.setKZZD2("积分兑换");


            log.info("内部积分返还原始入参，requestForReturnPoint:{}", JSON.toJSONString(requestForReturnPoint));
            CustomizeException customizeExceptionForReturnPoint = memberPointModifyClient.memberReversePoint(requestForReturnPoint);
            log.info("内部积分返还结果，customizeExceptionForReturnPoint:{}", JSON.toJSONString(customizeExceptionForReturnPoint));
            if(ObjectUtil.isNotNull(customizeExceptionForReturnPoint)) {
                return Boolean.FALSE;
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("内部返还积分失败，即将重试...:{},msg:{}", requestForDeductPoint.getTriggerId(),e);
            throw new CustomizedRetryException("内部发券失败，返还积分失败");
        }
    }

    /**
     * 爱奇艺回调接口
     *
     * @param requestDto
     * @return
     */
    public ResponseResult<String> iQiYiCallback(IQiYiCallbackDto requestDto) {
        saveCallbackLog(SendCouponEnum.IQIYI.getCode(), requestDto.getSporderId(), JSON.toJSONString(requestDto));
        if(ObjectUtil.equal(requestDto.getRetCode(), IQiYiCallbackEnum.SUCCESS.getCode())) {
            log.info("爱奇艺回调成功，不做其他处理，requestDto={}", JSON.toJSONString(requestDto));
            return ResponseResult.responseSuccess(ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getMsg(), null);
        }
        // 如果失败，需要反核销
        // 1. 根据外部单号查询内部券信息
        String sporderId = requestDto.getSporderId();
        // 按爱奇艺传参规则划分取出code
        String[] split = sporderId.split("_");
        String code = split[1];
        String sql = String.format("select code,grantPlatform,jsonData,state from %s where code = '%s'", COUPON_INSTANCE_MODEL, code);
        BaseResponse execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
        List<Map> data = execute.getData();
        if(CollectionUtils.isEmpty(data)) {
            log.info("券实例不存在，code={}", code);
            return ResponseResult.responseSuccess(ResponseCodeEnum.FAILED.getCode(), "反核销失败，coupon instance query is empty", null);
        }
        Map map = data.get(0);
        log.info("券实例信息，map={}", JSON.toJSONString(map));
        String state = (String)map.get("state");
        // 2.如果crm系统是已使用（即已核销），不做任何处理
        if(ObjectUtil.notEqual(state, CouponInstanceUseState.USED.getCode())) {
            log.info("券实例状态异常，state={}", state);
            return ResponseResult.responseSuccess(ResponseCodeEnum.FAILED.getCode(), "反核销失败，券非已使用状态", null);
        }
        // 3. 否则，进行反核销
        String couponCode = (String)map.get("code");
        String grantPlatform = (String)map.get("grantPlatform");
        String jsonData = (String)map.get("jsonData");
        JSONObject jsonObject = JSON.parseObject(jsonData, JSONObject.class);
        String lastTransactionId = String.valueOf(jsonObject.get("lastTransactionId"));

        OfferInstanceCancelUseClientRequestDto cancelUseClientRequestDto = new OfferInstanceCancelUseClientRequestDto();
        cancelUseClientRequestDto.setCouponCodes(Collections.singletonList(couponCode));
        cancelUseClientRequestDto.setTransactionId(UUID.randomUUID().toString().replace("-", ""));
        cancelUseClientRequestDto.setUseTransactionId(lastTransactionId);
        cancelUseClientRequestDto.setBrand(KO);
        cancelUseClientRequestDto.setChannelType(grantPlatform);
        try {
            log.info("反核销原始入参，cancelUseClientRequestDto={}", JSON.toJSONString(cancelUseClientRequestDto));
            ClientCommonResponseDto<?> cancelUseResult = openApiFeignClientV3.instanceCancelUse(cancelUseClientRequestDto);
            log.info("反核销结果，cancelUseResult={}", JSON.toJSONString(cancelUseResult));
            if(ObjectUtil.isNotEmpty(cancelUseResult.getErrorCode())) {
                return ResponseResult.responseError(cancelUseResult.getErrorCode(), cancelUseResult.getMsg(), null);
            }
            log.info("爱奇艺反核销成功");
            return ResponseResult.responseSuccess(cancelUseResult.getData());
        } catch (Exception e) {
            log.error("反核销失败，", e);
            return ResponseResult.responseError(ResponseCodeEnum.FAILED.getCode(), "爱奇艺反核销失败", null);
        }
    }

    /**
     * ubr回调接口
     */
    public ResponseResult<String> ubrCallback(UbrNotifyDto ubrNotifyDto) {
        saveCallbackLog(SendCouponEnum.UBR.getCode(), ubrNotifyDto.getExtOrderId(), JSON.toJSONString(ubrNotifyDto));
        if(ObjectUtil.notEqual(ubrNotifyDto.getAction(), UbrCoupoonCreatedStatusEnum.REDEEMED.getCode())) {
            return ResponseResult.responseSuccess(ResponseCodeEnum.FAILED.getCode(), ResponseCodeEnum.FAILED.getMsg(), "非已核销状态，不做处理");
        }
        OfferInstanceUseClientRequestDto requestDto = assembleUseDataByUbr(ubrNotifyDto);
        return instanceUse(requestDto);
    }

    private OfferInstanceUseClientRequestDto assembleUseDataByUbr(UbrNotifyDto ubrNotifyDto) {
        String sql = String.format("select code,grantPlatform,projectId,usedCostCenterCode from %s where code = '%s'", COUPON_INSTANCE_MODEL, ubrNotifyDto.getExtOrderId());
        log.info("查询券核销原始sql，sql={}", sql);
        BaseResponse execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
        List<Map> data = execute.getData();
        log.info("assembleUseDataByUbr-sql查询原始结果，data={}", JSON.toJSONString(data));
        if(CollectionUtils.isEmpty(data)) {
            return null;
        }
        Map map = data.get(0);
        OfferInstanceUseClientRequestDto offerInstanceUseClientRequestDto = new OfferInstanceUseClientRequestDto();
        offerInstanceUseClientRequestDto.setBrand(KO);
        offerInstanceUseClientRequestDto.setChannelType(String.valueOf(map.get("grantPlatform")));
        offerInstanceUseClientRequestDto.setTransactionId(UUID.randomUUID().toString().replace("-", ""));
        offerInstanceUseClientRequestDto.setCouponCodes(Collections.singletonList(ubrNotifyDto.getExtOrderId()));
        OfferInstanceUseClientRequestDto.Extension extension = matchCallbackLbsCode((String)map.get("usedCostCenterCode"));
        if(ObjectUtil.isNotNull(extension)) {
            offerInstanceUseClientRequestDto.setExtension(extension);
        }
        return offerInstanceUseClientRequestDto;
    }

    /**
     * 知而行回调
     */
    public ResponseResult<String> zerXCallback(ZerXCallbackDto zerXCallbackDto) {
        saveCallbackLog(SendCouponEnum.ZERX.getCode(), zerXCallbackDto.getBarcode(), JSON.toJSONString(zerXCallbackDto));
        OfferInstanceUseClientRequestDto requestDto = assembleUseDataByZerX(zerXCallbackDto);
        return instanceUse(requestDto);
    }

    private OfferInstanceUseClientRequestDto assembleUseDataByZerX(ZerXCallbackDto zerXCallbackDto) {
        String sql = String.format("select code,grantPlatform,projectId,usedCostCenterCode from %s where externalCode = '%s'", COUPON_INSTANCE_MODEL, zerXCallbackDto.getBarcode());
        log.info("assembleUseDataByZerX 原始sql，sql={}", sql);
        BaseResponse execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
        List<Map> data = execute.getData();
        log.info("assembleUseDataByZerX-sql查询原始结果，data={}", JSON.toJSONString(data));
        if(CollectionUtils.isEmpty(data)) {
            return null;
        }
        Map map = data.get(0);
        OfferInstanceUseClientRequestDto offerInstanceUseClientRequestDto = new OfferInstanceUseClientRequestDto();
        offerInstanceUseClientRequestDto.setBrand(KO);
        offerInstanceUseClientRequestDto.setChannelType(String.valueOf(map.get("grantPlatform")));
        offerInstanceUseClientRequestDto.setTransactionId(UUID.randomUUID().toString().replace("-", ""));
        offerInstanceUseClientRequestDto.setCouponCodes(Collections.singletonList(String.valueOf(map.get("code"))));
        OfferInstanceUseClientRequestDto.Extension extension = matchCallbackLbsCode((String)map.get("usedCostCenterCode"));
        if(ObjectUtil.isNotNull(extension)) {
            offerInstanceUseClientRequestDto.setExtension(extension);
        }
        return offerInstanceUseClientRequestDto;
    }

    private ResponseResult<String> instanceUse(OfferInstanceUseClientRequestDto offerInstanceUseClientRequestDto) {
        ResponseResult<String> result = new ResponseResult<>();
        try {
            log.info("券核销原始参数，offerInstanceUseClientRequestDto={}", JSON.toJSONString(offerInstanceUseClientRequestDto));
            ClientCommonResponseDto<OfferInstanceUseClientResponseDto> clientCommonResponseDto = openApiFeignClientV3.instanceUse(offerInstanceUseClientRequestDto);
            log.info("券核销原始结果，clientCommonResponseDto={}", JSON.toJSONString(clientCommonResponseDto));
            if(ObjectUtil.isNotEmpty(clientCommonResponseDto.getErrorCode())) {
                result.setCode(clientCommonResponseDto.getErrorCode());
                result.setMsg(clientCommonResponseDto.getMsg());
                return result;
            }
            result.setCode(ResponseCodeEnum.SUCCESS.getCode());
            result.setMsg(clientCommonResponseDto.getMsg());
            return result;
        } catch (ThirdPartException e) {
            log.error("券核销异常-ThirdPartException", e);
            result.setCode(ResponseCodeEnum.USE_COUPON_FAILED.getCode());
            result.setMsg(e.getMessage());
            return result;
        } catch (Exception e) {
            result.setCode(ResponseCodeEnum.USE_COUPON_FAILED.getCode());
            result.setMsg(ResponseCodeEnum.USE_COUPON_FAILED.getMsg());
            log.error("券核销异常-exception", e);
            return result;
        }
    }

    /**
     * 如果成本中心为空，则使用券项目配置的固定成本中心
     * @param usedCostCenterCodes 券核销成本中心code
     */
    private OfferInstanceUseClientRequestDto.Extension matchCallbackLbsCode(String usedCostCenterCodes) {
        log.info("匹配成本中心值");
        if(ObjectUtil.isEmpty(usedCostCenterCodes)) {
            log.info("成本中心不为空，默认不变");
            return null;
        }
        JSONArray useCostCenterCodeArr = JSON.parseArray(usedCostCenterCodes);
        String usedCostCenterCode = (String)useCostCenterCodeArr.get(0);
        String sql = String.format("select costCenterCode,bottlerFactoryName,city,district from data.prctvmkt.KO.LbsOuMapping where costCenterCode = '%s' limit 1",
                usedCostCenterCode);
        log.info("查询券项目原始sql，sql={}", sql);
        BaseResponse execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
        List<Map> data = execute.getData();
        log.info("查询券项目原始结果，data={}", JSON.toJSONString(data));
        if(CollectionUtils.isEmpty(data)) {
            return null;
        }
        Map costCenterMap = data.get(0);
        log.info("成本中心为空，使用券项目配置的固定成本中心");
        OfferInstanceUseClientRequestDto.Extension extension = new OfferInstanceUseClientRequestDto.Extension();
        // 取项目配置的固定成本中心
        extension.setUsedCostCenterCode(String.valueOf(usedCostCenterCode));
        Optional.ofNullable(costCenterMap.get("bottlerFactoryName")).ifPresent(usedLBSProvince -> extension.setUsedLBSProvince(String.valueOf(usedLBSProvince)));
        Optional.ofNullable(costCenterMap.get("city")).ifPresent(usedLBSCity -> extension.setUsedLBSCity(String.valueOf(usedLBSCity)));
        Optional.ofNullable(costCenterMap.get("district")).ifPresent(usedLBSDistrict -> extension.setUsedLBSDistrict(String.valueOf(usedLBSDistrict)));
        return extension;
    }

    /**
     * 保存回调日志
     *
     * @param callbackSource
     * @param businessCode
     * @param callbackParam
     */
    public void saveCallbackLog(String callbackSource, String businessCode, String callbackParam) {
        if(ObjectUtil.isEmpty(callbackParam)) {
            log.info("回调参数为空，callbackSource={}，businessCode={}，callbackParam={}", callbackSource, businessCode, callbackParam);
            return;
        }
        try {
            KOThirdPartCallbackLog koThirdPartCallbackLog = KOThirdPartCallbackLog.builder()
                    .businessCode(businessCode)
                    .callbackParam(callbackParam)
                    .createAt(DateHelper.formatZonedDateTime(DateUtil.date(), DateHelper.DATE_FORMAT_T))
                    .callbackSource(callbackSource).build();
            Map<String, Object> logMap = BeanUtil.beanToMap(koThirdPartCallbackLog, false, true);
            dataapiHttpSdk.insert("data.prctvmkt.KO.thirdPartCallbackLog", logMap, false, true);
        } catch (Exception e) {
            log.error("保存回调日志异常，callbackSource={}，callbackParam={}", callbackSource, callbackParam, e);
        }
    }

    public ResponseResult<String> weChatCallback(WechatCallbackDto weChatCallbackDto) {
        saveCallbackLog(SendCouponEnum.WX.getCode(), weChatCallbackDto.getId(), JSON.toJSONString(weChatCallbackDto));
        WechatCallbackDto.Resource resource = weChatCallbackDto.getResource();
        log.info("resource:{}",JSON.toJSONString(resource));
        //解密
        String decryptedJson;
        try{
            decryptedJson  = decrypt(WEHACT_CALLBACK_KEY, resource.getNonce(), resource.getAssociated_data(), resource.getCiphertext());
        }catch (Exception e){
            return ResponseResult.responseSuccess("FAIL", "解密失败");
        }

        log.info("wx解密后的JSON:{}",decryptedJson);
        CouponResourceDto couponResourceDto = JSON.parseObject(decryptedJson, CouponResourceDto.class);
        log.info("wx解密后的封装JSON:{}",JSON.toJSONString(couponResourceDto));
        // 进行数据核销数据封装
        OfferInstanceUseClientRequestDto offerInstanceUseClientRequestDto = assembleUseDataByWechat(couponResourceDto.getCouponId());
        if(ObjectUtil.isEmpty(offerInstanceUseClientRequestDto)){
            log.info("wx核销券码不存在:{}",couponResourceDto.getCouponId());
            return ResponseResult.responseSuccess("FAIL","券码不存在");
        }
        // 核销
        ResponseResult<String> result = instanceUse(offerInstanceUseClientRequestDto);
        log.info("wx反核销结果，code:{},cancelUseResult={}", couponResourceDto.getCouponId(),JSON.toJSONString(result));
        if(!result.getCode().equals("200")) {
            return ResponseResult.responseSuccess("FAIL", result.getMsg());
        }
        log.info("wx核销成功");
        return ResponseResult.responseSuccess("SUCCESS","成功");

    }

    private OfferInstanceUseClientRequestDto assembleUseDataByWechat(String code) {
        String sql = String.format("select code,grantPlatform,projectId,usedCostCenterCode from %s where externalCode = '%s' ", COUPON_INSTANCE_MODEL, code);
        log.info("wx查询券核销原始sql，sql={}", sql);
        BaseResponse execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
        List<Map> data = execute.getData();
        log.info("assembleUseDataByWechat-sql查询原始结果，data={}", JSON.toJSONString(data));
        if(CollectionUtils.isEmpty(data)) {
            return null;
        }
        Map map = data.get(0);
        OfferInstanceUseClientRequestDto offerInstanceUseClientRequestDto = new OfferInstanceUseClientRequestDto();
        offerInstanceUseClientRequestDto.setBrand(KO);
        offerInstanceUseClientRequestDto.setChannelType(String.valueOf(map.get("grantPlatform")));
        offerInstanceUseClientRequestDto.setTransactionId(UUID.randomUUID().toString().replace("-", ""));
        offerInstanceUseClientRequestDto.setCouponCodes(Collections.singletonList(String.valueOf(map.get("code"))));
        OfferInstanceUseClientRequestDto.Extension extension = matchCallbackLbsCode((String)map.get("usedCostCenterCode"));
        if(ObjectUtil.isNotNull(extension)) {
            offerInstanceUseClientRequestDto.setExtension(extension);
        }
        return offerInstanceUseClientRequestDto;
    }


    /**
     * 解密微信回调的加密数据
     *
     * @param key              APIv3密钥
     * @param nonce            随机串
     * @param associatedData   附加数据
     * @param ciphertextBase64 加密数据（Base64编码）
     * @return 解密后的JSON字符串
     */
    public static String decrypt(String key, String nonce, String associatedData, String ciphertextBase64) {
        log.info("wx开始解密入参key:{},nonce:{},associatedData:{},ciphertextBase64:{}",key,nonce,associatedData,ciphertextBase64);
        try {
            // Base64解码
            byte[] ciphertext = Base64.getDecoder().decode(ciphertextBase64);

            // 初始化Cipher
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(128, nonce.getBytes(StandardCharsets.UTF_8));

            cipher.init(Cipher.DECRYPT_MODE, keySpec, gcmParameterSpec);

            // 添加associated_data
            if (associatedData != null && !associatedData.isEmpty()) {
                cipher.updateAAD(associatedData.getBytes(StandardCharsets.UTF_8));
            }

            // 解密
            byte[] decryptedBytes = cipher.doFinal(ciphertext);

            // 返回解密后的字符串
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("wx回调解密失败:{}",e);
            throw new RuntimeException("解密失败:{}", e);
        }
    }

    public ResponseResult<String> weChatCallbackUrl(HashMap<String, Object> map) {
        Map<String, String> tokenMap = new HashMap<>();
        tokenMap.put("mchId", "1234657102");
        tokenMap.put("subMchId", "1234657102");
        com.fasterxml.jackson.databind.ObjectMapper objectMapper = new ObjectMapper();
        String tokenBase64 = null;
        try {
             tokenBase64 = new String(Base64.getEncoder().withoutPadding().encode(objectMapper.writeValueAsBytes(tokenMap)), StandardCharsets.UTF_8);
            log.info("微信代金券tokenBase64={}", tokenBase64);

        } catch (JsonProcessingException e) {
            log.error("微信代金券token参数加密失败，", e);
            throw new RuntimeException("微信代金券token参数加密失败");
        }
        Map<String, String> queryParam = new HashMap<>();
        queryParam.put("accesstoken", tokenBase64);
        try {
            String url = ApiSignUtils.buildUrl("https://obgwx.shuyun.com/obg/v1/wechatpay/api/v3/marketing/favor/callbacks", queryParam, "nxkkkjr3", "QtthW7Hc3I46V5fj");

            String body = HttpUtil.createPost(url)
                    .body(JSON.toJSONString(map))
                    .execute().body();
            log.info("请求微信代金券接口返回结果={}", body);
            if(ObjectUtil.isEmpty(body)) {
                return null;
            }
            WxSendCouponResponseDto responseDto = JSON.parseObject(body, WxSendCouponResponseDto.class);
            if(ObjectUtil.isEmpty(responseDto) || ObjectUtil.isEmpty(responseDto.getCouponId())) {
                log.error("微信发券返回结果code异常，code={}", JSON.toJSONString(responseDto));
                return null;
            }
        } catch (Exception e) {
            log.error("代金券业务，请求微信代金券接口异常", e);
        }
        return null;
    }

    public ResponseResult<String> weChatCouponCallback(String merchantId,WechatCallbackDto weChatCallbackDto) {
        saveCallbackLog(SendCouponEnum.WX.getCode(), weChatCallbackDto.getId(), JSON.toJSONString(weChatCallbackDto));
        WechatCallbackDto.Resource resource = weChatCallbackDto.getResource();
        log.info("微信核销回调resource:{}",JSON.toJSONString(resource));
        //根据商户号查询解密密钥
        String weChatMerchantInformation = getWeChatMerchantInformation(merchantId);
        if (StringUtils.isBlank(weChatMerchantInformation)){
            return ResponseResult.responseSuccess("FAIL", "商户号不存在");
        }
        //解密
        String decryptedJson;
        try{
            decryptedJson  = decrypt(weChatMerchantInformation, resource.getNonce(), resource.getAssociated_data(), resource.getCiphertext());
        }catch (Exception e){
            return ResponseResult.responseSuccess("FAIL", "解密失败");
        }

        log.info("wx解密后的JSON:{}",decryptedJson);
        CouponResourceDto couponResourceDto = JSON.parseObject(decryptedJson, CouponResourceDto.class);
        log.info("wx解密后的封装JSON:{}",JSON.toJSONString(couponResourceDto));
        // 进行数据核销数据封装
        OfferInstanceUseClientRequestDto offerInstanceUseClientRequestDto = assembleUseDataByWechat(couponResourceDto.getCouponId());
        if(ObjectUtil.isEmpty(offerInstanceUseClientRequestDto)){
            log.info("wx核销券码不存在:{}",couponResourceDto.getCouponId());
            return ResponseResult.responseSuccess("FAIL","券码不存在");
        }
        // 核销
        ResponseResult<String> result = instanceUse(offerInstanceUseClientRequestDto);
        log.info("wx反核销结果，code:{},cancelUseResult={}", couponResourceDto.getCouponId(),JSON.toJSONString(result));
        if(!result.getCode().equals("200")) {
            return ResponseResult.responseSuccess("FAIL", result.getMsg());
        }
        log.info("wx核销成功");
        return ResponseResult.responseSuccess("SUCCESS","成功");
    }

    private String getWeChatMerchantInformation(String id) {
        String sql = String.format("select id,apiV3Key from %s where id = '%s' ", ModelConstants.WECHAT_COUPON_INFORMATION, id);
        log.info(" 查询商户号sql:{}", sql);
        BaseResponse execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
        List<Map> data = execute.getData();
        log.info("getWeChatMerchantInformation-sql查询原始结果，data:{}", JSON.toJSONString(data));
        if(CollectionUtils.isEmpty(data)) {
            return null;
        }
        Map map = data.get(0);
        String key = String.valueOf(map.get("apiV3Key"));
        return key;
    }


    public  String  bKCallback(BKInstanceUseOrUnUseRequest request) {
        //event_type == VERIFY 核销事件
        if (BkInstanceUseOrUnUseEnum.VERIFY.getCode().equals(request.getEventType())) {
            saveCallbackLog(SendCouponEnum.BK.getCode(), request.getVerifyData().getVerifyRequestSn(), JSON.toJSONString(request));
            return  assembleUseDataByBK( request);
        }
        // event_type == REVOKE 撤销核销事件
//         if (BkInstanceUseOrUnUseEnum.REVOKE.getCode().equals(request.getEventType())) {
//             saveCallbackLog(SendCouponEnum.BK.getCode(), request.getRevokeData().getVerifyRequestSn(), JSON.toJSONString(request));
//             return  assembleUnUseDataByBK( request);
//         }
         return "fail";
    }



    private   String   assembleUseDataByBK(BKInstanceUseOrUnUseRequest request) {
        String sql = String.format("select code,grantPlatform,projectId,externalCode,state from %s where externalCode = '%s'", COUPON_INSTANCE_MODEL, request.getVerifyData().getTicketCode());
        log.info("assembleUseDataByBK查询汉堡王券核销原始sql，sql={}", sql);
        BaseResponse execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
        List<Map> data = execute.getData();
        log.info("assembleUseDataByBK-sql查询原始结果，data={}", JSON.toJSONString(data));
        if(CollectionUtils.isEmpty(data)) {
            log.info("汉堡王核销券码不存在:{}",request.getVerifyData().getTicketCode());
            return "fail" ;
        }
        Map map = data.get(0);
        OfferInstanceUseClientRequestDto offerInstanceUseClientRequestDto = new OfferInstanceUseClientRequestDto();
        offerInstanceUseClientRequestDto.setBrand(KO);
        offerInstanceUseClientRequestDto.setChannelType(CocoChannelTypeEnum.KO_MP.getCode());
        offerInstanceUseClientRequestDto.setTransactionId(request.getVerifyData().getVerifyRequestSn());
        offerInstanceUseClientRequestDto.setCouponCodes(Collections.singletonList(map.get("code").toString()));
        ResponseResult<String> result = instanceUse(offerInstanceUseClientRequestDto);
        if(!result.getCode().equals("200")) {
            return  "fail";
        }
        log.info("汉堡王核销成功");
        return  "success";
    }

    private    String  assembleUnUseDataByBK(BKInstanceUseOrUnUseRequest request) {
        String sql = String.format("select code,grantPlatform,projectId,externalCode,state,jsonData from %s where externalCode = '%s'", COUPON_INSTANCE_MODEL, request.getRevokeData().getTicketCode());
        log.info("assembleUnUseDataByBK查询汉堡王券反核销原始sql，sql={}", sql);
        BaseResponse execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
        List<Map> data = execute.getData();
        log.info("assembleUnUseDataByBK-sql查询原始结果，data={}", JSON.toJSONString(data));
        if(CollectionUtils.isEmpty(data)) {
            log.info("汉堡王反核销券码不存在:{}",request.getRevokeData().getTicketCode());
            return "fail" ;
        }
        Map map = data.get(0);
        log.info("assembleUnUseDataByBK-券实例信息，map={}", JSON.toJSONString(map));
        String state = (String)map.get("state");
        // 2.如果crm系统是已使用（即已核销），不做任何处理
        if(ObjectUtil.notEqual(state, CouponInstanceUseState.USED.getCode())) {
            log.info("assembleUnUseDataByBK-券实例状态异常，state={}", state);
            return "fail" ;
        }
        // 3. 否则，进行反核销
        String couponCode = (String)map.get("code");
        String jsonData = (String)map.get("jsonData");
        JSONObject jsonObject = JSON.parseObject(jsonData, JSONObject.class);
        String lastTransactionId = String.valueOf(jsonObject.get("lastTransactionId"));

        OfferInstanceCancelUseClientRequestDto cancelUseClientRequestDto = new OfferInstanceCancelUseClientRequestDto();
        cancelUseClientRequestDto.setCouponCodes(Collections.singletonList(couponCode));
        cancelUseClientRequestDto.setTransactionId(UUID.randomUUID().toString().replace("-", ""));
        cancelUseClientRequestDto.setUseTransactionId(lastTransactionId);
        cancelUseClientRequestDto.setBrand(KO);
        cancelUseClientRequestDto.setChannelType(CocoChannelTypeEnum.KO_MP.getCode());
        try {
            log.info("assembleUnUseDataByBK反核销原始入参，cancelUseClientRequestDto={}", JSON.toJSONString(cancelUseClientRequestDto));
            ClientCommonResponseDto<?> cancelUseResult = openApiFeignClientV3.instanceCancelUse(cancelUseClientRequestDto);
            log.info("assembleUnUseDataByBK反核销结果，cancelUseResult={}", JSON.toJSONString(cancelUseResult));
            if(ObjectUtil.isNotEmpty(cancelUseResult.getErrorCode())) {
                return "fail" ;
            }
            log.info("assembleUnUseDataByBK汉堡王反核销成功");
            return "success" ;
        } catch (Exception e) {
            log.error("assembleUnUseDataByBK汉堡王反核销失败，", e);
            return "fail" ;
        }
    }
}
