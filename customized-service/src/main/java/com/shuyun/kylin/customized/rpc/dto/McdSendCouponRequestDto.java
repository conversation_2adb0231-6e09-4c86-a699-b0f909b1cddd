package com.shuyun.kylin.customized.rpc.dto;

import lombok.Data;

import java.util.List;

/**
 * 麦当劳发券请求入参
 *
 * @author: Jingwei
 * @date: 2025-01-08
 */
@Data
public class McdSendCouponRequestDto {
    /**
     * 【必填】
     * 渠道编码对接时由麦当劳提供
     */
    private String channelCode;
    /**
     * 【必填】
     * 领取类型：6
     */
    private Integer receiveType;
    /**
     * 【必填】
     * 领取用户手机号码，手机号需使用 AES加密，密码请线下沟通获取，
     * AES/ECB/PKCS5Padding 输出为十六进制字符串
     */
    private String customerMobileNo;
    /**
     * 【必填】
     * 交易号，追溯系统间调用记录，需保证全局唯一；
     * 若出现领券失败，再次调用允许使用相同交易号
     */
    private String tradeNo;
    /**
     * 【必填】
     * 交易方(对接时由麦当劳提供)
     */
    private String tradeSystem;
    /**
     * 【必填】
     * 待领取卡券
     */
    private List<Coupon> coupons;

    @Data
    public static class Coupon {
        /**
         * 【必填】
         * 卡券 ID
         */
        private String couponId;
        /**
         * 非必须
         * 领取数量 不传默认为 1，即发单张券
         */
        private String receiveQuantity;
    }
}
