package com.shuyun.kylin.customized.rpc.enums;

import lombok.Getter;

/**
 * 麦当劳错误码映射
 * @author: Jingwei
 * @date: 2025-01-08
 */
@Getter
public enum McdErrorResponseEnum {
    SUCCESS("SUCCESS", "成功","成功"),
    CUSTOMER_ID_IS_BLANK("PMTCPN024001", "input_param_valid_fail,customer_id_is_blank", "用户 ID 不可为空"),
    COUPON_TRADE_NO_IS_BLANK("PMTCPN024001","input_param_valid_fail,coupon_trade_no_is_blank", "交易号不可为空"),
    COUPON_ID_IS_BLANK("PMTCPN024001", "input_param_valid_fail,coupon_id_is_blank","领券列表不可为空"),
    COUPON_PUT_RULE_CHANNEL_CODE_ERROR("PMTCPN024001", "input_param_valid_fail,coupon_put_rule_channel_code_error","领券渠道不可为空"),
    COUPON_TRADE_NO_EXISTS("PMTCPN025103", "coupon_trade_no_exists","交易号重复"),
    CP_COUPON_NOT_EXISTS("PMTCPN025002", "cp_coupon_not_exists","卡券不存在"),
    CP_COUPON_PUT_RULE_NOT_EXISTS("PMTCPN025010", "cp_coupon_put_rule_not_exists","投放规则不存在"),
    CP_COUPON_TRADE_RULE_NOT_EXIST("PMTCPN025015", "cp_coupon_trade_rule_not_exist","核销规则不存在"),
    CP_COUPON_NOT_AVAILABLE("PMTCPN025003", "cp_coupon_not_available","卡券不可用"),
    CP_COUPON_STATUS_INVALID("PMTCPN025004", "cp_coupon_status_invalid","卡券审核状态错误"),
    COUPON_PUT_RULE_CHANNEL_CODE_NOT_MATCH("PMTCPN025015", "coupon_put_rule_channel_code_error","领券渠道不匹配"),
    COUPON_PUT_RULE_CAMPAIGN_DATE_ERROR("PMTCPN025016", "coupon_put_rule_campaign_date_error","领券时间未到或已超期"),
    CP_COUPON_PUT_RULE_RECEIVE_LIMIT("PMTCPN025011", "cp_coupon_put_rule_receive_limit","领取数量超限或超过最大领取次数"),
    CP_COUPON_REMAIN_QUANTITY_NOT_ENOUGH("PMTCPN025005", "cp_coupon_remain_quantity_not_enough","卡券剩余数量不足"),
    CP_COUPON_CHANNEL_NOT_READY("PMTCPN025035", "cp_coupon_channel_not_ready","多渠道配置未完成"),
    CP_COUPON_CHANNEL_ALREADY_RECEIVE("PMTCPN025035", "cp_coupon_channel_already_receive","多渠道领取卡券失败"),
    CP_COUPON_CODE_RECEIVE_FAIL("PMTCPN025110", "cp_coupon_code_receive_fail","领券失败"),
    ;

    private String code;
    private String msg;
    private String desc;

    McdErrorResponseEnum(String code, String msg, String desc) {
        this.code = code;
        this.msg = msg;
        this.desc = desc;
    }

    public static McdErrorResponseEnum getByMsg(String msg) {
        for (McdErrorResponseEnum errorResponseEnum : McdErrorResponseEnum.values()) {
            if (errorResponseEnum.getMsg().equals(msg)) {
                return errorResponseEnum;
            }
        }
        return null;
    }
}
