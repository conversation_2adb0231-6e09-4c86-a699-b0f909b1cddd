package com.shuyun.kylin.customized.base.stream.kafka.dao;


import lombok.Data;

@Data
public class AppletSubscriberDto {

    private String id;
    private String templateId;
    private String title;
    private String fields1;
    private String fields2;
    private String fields3;
    private String fields4;
    private String fields5;
    private String fields6;
    private String fields7;
    private String campaignId;
    private String taskId;
    private String objectId;
    private String occId;
    private String nodeId;
    private String nodeName;
    private String campaignName;
    private String testRun;
    private String type;
    private String sceneCommen;
    private String sceneCampaign;
    private String landingPage;
    private String customerTheme;
}
