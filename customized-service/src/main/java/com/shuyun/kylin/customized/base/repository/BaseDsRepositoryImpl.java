package com.shuyun.kylin.customized.base.repository;

import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.kylin.customized.base.dm.BaseDsRepository;
import com.shuyun.mc.ds.suql.utils.MCDataFactoryUtil;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2022/4/1 17:29
 * @description
 */
@Repository
public class BaseDsRepositoryImpl extends BaseDsRepository<Object> {
    public DataapiHttpSdk getDataApi() {
        return MCDataFactoryUtil.INSTANCE.getDataapiHttpSdk();
    }
}
