package com.shuyun.kylin.customized.base.util;

import com.shuyun.kylin.customized.base.common.Constants;
import com.shuyun.kylin.customized.base.exception.Checker;
import com.shuyun.kylin.customized.base.exception.CustomizedErrorTypes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.CreateTopicsResult;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.common.errors.TopicExistsException;

import java.util.Arrays;
import java.util.Properties;
import java.util.concurrent.ExecutionException;

/**
 * kafka客户端
 * @Author: rong.zhang
 * @Created: 2020/4/15 18:52
 */
@Slf4j
public class KafkaClientUtil {

    private KafkaClientUtil(){

    }

    private static  AdminClient adminClient = null;

    private static AdminClient getAdminClient(){
        if(null == adminClient) {
            Properties properties = new Properties();
            properties.put(CommonClientConfigs.BOOTSTRAP_SERVERS_CONFIG, Constants.DataTransferConstant.KAFKA_HOST);
            adminClient = AdminClient.create(properties);
        }
        return adminClient;
    }

    /**
     * 创建topic
     * @param topicName         topic名称
     * @param numPartitions     分区数量
     * @param replicationFactor 副本数量
     */
    public static void createTopic(String topicName,int numPartitions,Short replicationFactor){
        Checker.requiresThrowRequestException(StringUtils.isNotBlank(topicName), CustomizedErrorTypes.INVALID_PARAMTER,"topicName");
        NewTopic newTopic = new NewTopic(topicName,numPartitions,replicationFactor);
        // Kafka实现的方式都是后台异步操作的，而且没有提供任何回调机制或返回任何结果给用户，所以用户除了捕获异常以及查询topic状态之外似乎并没有特别好的办法可以检测操作是否成功
        CreateTopicsResult result = getAdminClient().createTopics(Arrays.asList(newTopic));
        try {
            result.all().get();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("kafka创建主题线程中断异常",e);
        } catch (ExecutionException e) {
            if(e.getMessage().contains("TopicExistsException")){
                log.info("topic={}已存在，无需再次创建",topicName);
            }else{
                log.error("kafka创建主题执行异常",e);
            }
        }
        log.info("初始化topic={}完成",topicName);
        getAdminClient().close();
    }
}
