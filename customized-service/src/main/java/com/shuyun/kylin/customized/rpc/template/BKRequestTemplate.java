package com.shuyun.kylin.customized.rpc.template;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.net.HttpHeaders;
import com.shuyun.kylin.customized.base.feign.dao.MemberBingDto;
import com.shuyun.kylin.customized.base.feign.dao.OfferProjectDetailClientResponse;
import com.shuyun.kylin.customized.base.util.BKSignUtils;
import com.shuyun.kylin.customized.behavior.util.DateUtils;
import com.shuyun.kylin.customized.project.request.GrantCouponRequest;
import com.shuyun.kylin.customized.project.strategy.router.SendCouponEnum;
import com.shuyun.kylin.customized.rpc.dto.BKRequestDto;
import com.shuyun.kylin.customized.rpc.dto.BKResponseDto;
import com.shuyun.kylin.customized.rpc.entity.BKConfigEntity;
import com.shuyun.motor.common.cons.PropsUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

import static com.shuyun.kylin.customized.rpc.template.base.KoRequestTemplate.pushKoRpcRequestLog;

/**
 * 汉堡王BK发券
 * 正式地址	https://crm-api.bkchina.cn/burger-king/eticket/code/send
 * 测试地址	https://crm-api-test.bkchina.cn/burger-king/eticket/code/send
 */

@Slf4j
public class BKRequestTemplate {

    static final BKConfigEntity bkConfig = JSON.parseObject(PropsUtil.getSysOrEnv("ko.bk.api.config"), BKConfigEntity.class);

    /**
     * 汉堡王BK发券
     */
    public static BKResponseDto sendCoupon(GrantCouponRequest grantCouponRequest, OfferProjectDetailClientResponse projectDetail, MemberBingDto memberBindInfo) {
        //获取签名
        BKRequestDto bkRequestDto = new BKRequestDto();
        bkRequestDto.setSendRequestSn(grantCouponRequest.getTransactionId());
        bkRequestDto.setMobile(memberBindInfo.getMobile());
        bkRequestDto.setOpenId(memberBindInfo.getOpenId());
        bkRequestDto.setSendFrom(bkConfig.getSendFrom());
        bkRequestDto.setPlatformId(bkConfig.getPlatformId());

        BKRequestDto.CodeInfo codeInfo = new BKRequestDto.CodeInfo();
        List<BKRequestDto.CodeInfo> codeInfos = new ArrayList<>();
        codeInfo.setTicketId(projectDetail.getData().getExtData().getExternalProjectId());
        codeInfo.setSendNum(1);
        //order_info参数组装
        BKRequestDto.OrderInfo orderInfo = new BKRequestDto.OrderInfo();
        orderInfo.setOrderId(grantCouponRequest.getTransactionId());
        codeInfo.setOrderInfo(orderInfo);

        codeInfos.add(codeInfo);
        bkRequestDto.setCodeInfo(codeInfos);
        BKRequestDto.NotifyInfo notifyInfo = new BKRequestDto.NotifyInfo();
        notifyInfo.setNotifyUrl(bkConfig.getCallBackUrl());
        bkRequestDto.setNotifyInfo(notifyInfo);

        Map<String, Object> headers = new HashMap<>();
        String appId = bkConfig.getAppId();
        headers.put("x-very-app-id", appId);
        log.info("开始请求bk汉堡王发券接口，入参bkRequestDto={}", JSON.toJSONString(bkRequestDto));

        int retryCount = 0;

        while(retryCount < 3) {
            String now = DateUtils.format(new Date(), DateUtils.YYYY_MM_DD_HH_MM_SS);
            headers.put("x-very-timestamp", now);
            String signature = BKSignUtils.getSign("POST", bkConfig.getSendCouponUrl(), headers, JSON.toJSONString(bkRequestDto), bkConfig.getAppSecret(), BKSignUtils.Type.md5);
            try {
                HttpResponse response = HttpUtil.createPost(bkConfig.getDoMainUrl() + bkConfig.getSendCouponUrl())
                        .body(JSON.toJSONString(bkRequestDto))
                        .header("x-very-timestamp", now)
                        .header("x-very-app-id", appId)
                        .header(HttpHeaders.AUTHORIZATION, signature)
                        .header("Content-Type", "application/json;charset=utf-8")
                        .execute();
                if (!response.isOk()) {
                    log.warn("请求bk汉堡王发券接口失败 transactionId:{},response={},status={}", grantCouponRequest.getTransactionId(), response, response.getStatus());
                    continue;
                }
                String body = response.body();
                log.info("BK汉堡王transactionId：{},sendCoupon响应结果body={}",grantCouponRequest.getTransactionId(), body);
                pushKoRpcRequestLog(bkConfig.getSendCouponUrl(), bkRequestDto, body, grantCouponRequest.getTransactionId(), SendCouponEnum.BK.getCode());
                if (ObjectUtil.isEmpty(body)) {
                    continue;
                }
                BKResponseDto bkResponseDto = JSON.parseObject(body, BKResponseDto.class);
                log.info("BK汉堡王transactionId：{},sendCoupon响应对对象bkResponseDto={}", grantCouponRequest.getTransactionId(),JSON.toJSONString(bkResponseDto));
                return bkResponseDto;
            } catch (Exception e) {
                log.error("BK汉堡王sendCoupon请求transactionId：{},异常",grantCouponRequest.getTransactionId(), e);
                pushKoRpcRequestLog(bkConfig.getSendCouponUrl(), bkRequestDto, e.getMessage(), grantCouponRequest.getTransactionId(), SendCouponEnum.BK.getCode());
            }
            retryCount++;
            try {
                Thread.sleep(20L);
            } catch (InterruptedException e) {
                log.error("thread sleep exception: ", e);
            }
        }
        return null;
    }


}
