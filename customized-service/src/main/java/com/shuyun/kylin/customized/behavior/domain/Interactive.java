package com.shuyun.kylin.customized.behavior.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shuyun.kylin.customized.base.stream.kafka.dao.CepResponseResult;
import com.shuyun.kylin.customized.behavior.resource.po.CheckPO;
import com.shuyun.kylin.customized.behavior.util.StringUtils;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;

@Setter
@Getter
@Data
public class Interactive {
    private String id;                      //id	Id
    private String interactiveId;           //互动编号	String
    private String interactiveName;         //互动名称	String
    private String scene;                   //场景值	String
    private String sceneDesc;               //场景描述   String
    private String couponSceneCode;         //场景编号   String
    private String awardType;               //奖励方式	Enum
    private String status;                  //状态	String
    private Date startTime;              //互动开始时间	DateTime
    private Date endTime;                   //互动结束时间	DateTime


    private String taskType;                //任务类型	String
    private String couponList;              //选择的优惠券	String
    private Date created;                   //创建时间	DateTime
    private String enableState;             //启用状态	Enum
    private String everydaySignLimit;       //每日签到上限	Integer
    private Integer extraDayNo;             //连续签到天数	Integer
    private Integer extraPoints;            //额外签到赠送的积分数	Number
    private String interactiveRecordsList;  //互动行为记录列表	Array
    private Integer inviteLimit;            //邀请好友上限	Integer
    private Boolean isExtraAward;           //连续签到额外奖励	Boolean
    private Boolean isRewardCoupon;         //奖励优惠券	Boolean
    private Boolean isRewardPoint;          //奖励积分	Boolean
    private Date lastSync;                  //更新时间	DateTime
    private Integer pointNum;               //奖励积分数量	Number
    private String remarks;                 //备注	String

    private String costCenter;              //成本中心
    private String costCenterField;         //成本中心

    private String channelType;             //活动渠道类型    String
    private String rewardsRecordScope;      //互动规则校验范围     Enum

    public List<String> fetchCouponList() {
        if (StringUtils.isNullOrEmpty(couponList)) {
            return new ArrayList<>();
        }

        return Arrays.asList(couponList.split(","));
    }


    @Setter
    @Getter
    public static class CostCenterDomain {
        private String id;                      //id	Id
        private String costCenter;         //成本中心
        private String costCenterField;         //成本中心
    }

    @Setter
    @Getter
    public static class Invite {
        private Integer inviteLimit;            //邀请好友上限	Integer
        private Integer pointNum;               //奖励积分数量	Number
    }

    /**
     * 描述：
     * 完善信息
     */
    @Setter
    @Getter
    public static class CompleteInfo {
        private Integer pointNum;               //奖励积分数量	Number
    }

    /**
     * 描述：
     * 每日签到奖励多少瓶子，是否开启连续签到奖励，如果开启，多少天得到多少奖励。
     */
    @Setter
    @Getter
    public static class DailyCheckIn extends ConsecutiveCheckIn {
        private Boolean isExtraAward;           //连续签到额外奖励	Boolean
        private Integer extraPoints;            //额外签到赠送的积分数	Number
        private Boolean isRewardCoupon;         //奖励优惠券	Boolean

        @JsonIgnore
        public boolean isExtraAward() {
            return BooleanUtils.isTrue(isExtraAward);
        }
    }

    @JsonIgnore
    public boolean isRewardNothing() {
        //TODO 目前只关心积分
        return BooleanUtils.isFalse(isRewardPoint);
    }

    /**
     * 描述：
     * 每连续签到
     */
    @Setter
    @Getter
    public static class ConsecutiveCheckIn {
        private Boolean isRewardPoint;          //奖励积分	Boolean
        private Integer extraDayNo;             //连续签到天数	Integer
        private Integer pointNum;               //奖励积分数量	Number
    }

    public CompleteInfo fetchCompleteInfo() {
        if (!Objects.equals(this.scene, CheckPO.SceneType.COMPLETE_INFO.name())) {
            return null;
        }

        CompleteInfo completeInfo = new CompleteInfo();
        BeanUtils.copyProperties(this, completeInfo);
        return completeInfo;
    }

    public DailyCheckIn fetchDailyCheckIn() {
        if (!Objects.equals(this.scene, CheckPO.SceneType.CHECK_IN.name())) {
            return null;
        }

        DailyCheckIn dailyCheckIn = new DailyCheckIn();
        BeanUtils.copyProperties(this, dailyCheckIn);
        return dailyCheckIn;
    }


    public Invite fetchInvite() {
        if (!Objects.equals(this.scene, CheckPO.SceneType.INVITE_REGISTER.name())) {
            return null;
        }

        Invite invite = new Invite();
        BeanUtils.copyProperties(this, invite);
        return invite;
    }

    public ConsecutiveCheckIn fetchConsecutiveCheckIn() {
        if (!Objects.equals(this.scene, CheckPO.SceneType.CONSECUTIVE_CHECK_IN.name())) {
            return null;
        }

        ConsecutiveCheckIn consecutiveCheckIn = new ConsecutiveCheckIn();
        BeanUtils.copyProperties(this, consecutiveCheckIn);
        return consecutiveCheckIn;
    }
}