package com.shuyun.kylin.customized.member.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

@Data
public class CepConversionPointDto {

    @NotBlank(message = "客户编号不能为空，格式：appid_openid")
    private String customerNo;
    @NotBlank(message = "EC_POINT：EC积分商城")
    private String channelType;
    private String exchangeId;
    private String exchangeTime;
    private String exchangeMode;
    private String exchangeGiftType;
    private String exchangeGift;
    private String exchangeGiftName;
    private Double consumePoint;
    private Double payment;
    private Double pointsCost;
    private Integer quantity;
    private String receiveTime;
    private String shippingTime;
    private String memberIDStr;
    private String exchangeStatus;
    private String costCenterStr;
    private String lastSync;
    private Map member;
    private Map costCenter;
}
