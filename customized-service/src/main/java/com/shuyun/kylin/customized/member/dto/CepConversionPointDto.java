package com.shuyun.kylin.customized.member.dto;

import com.shuyun.kylin.customized.member.dto.ec.EcCustomizedPropertiesDto;
import com.shuyun.kylin.customized.member.dto.ec.EcNormalOrderDto;
import com.shuyun.kylin.customized.member.dto.ec.EcNormalOrderItemDto;
import lombok.Data;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@Data
public class CepConversionPointDto {

    @NotBlank(message = "客户编号不能为空，格式：appid_openid")
    private String customerNo;
    @NotBlank(message = "EC_POINT：EC积分商城")
    private String channelType;
    private String exchangeId;
    private String exchangeTime;
    private String exchangeMode;
    private String exchangeGiftType;
    private String exchangeGift;
    private String exchangeGiftName;
    private Double consumePoint;
    private Double payment;
    private Double pointsCost;
    private Integer quantity;
    private String receiveTime;
    private String shippingTime;
    private String memberIDStr;
    private String exchangeStatus;
    private String costCenterStr;
    private String lastSync;
    private Map member;
    private Map costCenter;

    private String orderId;
    //CREATED: 下单 CANCELLED: 订单取消 DELIVERED: 已发货 CONFIRMED: 确认收货 FINISHED: 订单完成
    private String orderStatus;
    private String memberType;
    private Double totalFee;
    private String costCenterMark;
    private String shopCode;
    private String shopName;
    private Integer totalQuantity;
    //下单时间 格式: yyyy-MM-dd HH:mm:ss
    private String orderTime;
    //订单付款时间 格式: yyyy-MM-dd HH:mm:ss
    private String payTime;
    //订单收货时间 格式: yyyy-MM-dd HH:mm:ss
    private String finishTime;
    private String updateTime;
    //折扣率 <=1
    private Double discountRate;
    //折扣金额
    private Double discountFee;
    //运费
    private Double freight;
    //收货人姓名
    private String receiverName;
    //收货人手机号
    private String receiverMobile;
    //收货人固定电话
    private String receiverTelephone;
    //省
    private String receiverProvince;
    //市
    private String receiverCity;
    //区/县
    private String receiverDistrict;
    //收货人地址
    private String receiverAddress;
    //邮编
    private String receiverZipCode;
    //订单备注
    private String description;

    //会员id
    private String memberId;

    private String orderType;
    private List<EcNormalOrderItemDto> orderItems;
    //扩展字段
    private EcCustomizedPropertiesDto customizedProperties;

}
