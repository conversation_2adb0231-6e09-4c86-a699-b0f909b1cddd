package com.shuyun.kylin.customized.base.feign.client;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.kylin.customized.base.exception.CustomizeException;
import com.shuyun.kylin.customized.base.feign.dao.LoyaltyFacadeDto;
import com.shuyun.kylin.customized.medal.request.MedalObtainRequest;
import org.jboss.logging.Param;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * 忠诚度内部接口
 */
@FeignClient(name = "loyaltyFacade")
@RequestMapping("loyalty-facade/v1")
public interface LoyaltyFacade {

    @PostMapping("/point:deduct")
    void deductPoint(@RequestBody LoyaltyFacadeDto loyaltyFacadeDto);

    @PostMapping("/point:send")
    void sendPoint(@RequestBody LoyaltyFacadeDto loyaltyFacadeDto);

    @GetMapping("/open/plan/gradeHierarchy")
    JSONObject getGradeHierarchy(@RequestParam("planId") @NotNull Long planId);


    //计划方案信息
    @GetMapping("/plan:info")
    JSONObject getMedal(@RequestParam("planId") @NotNull Integer planId);

    //勋章发放
    @PostMapping("/medal/obtain")
    JSONObject medalObtain(@RequestBody MedalObtainRequest medalObtainRequest);


    //分页查询会员勋章
    @GetMapping("/medal/query")
    JSONArray medalQuery(@RequestParam ("medalHierarchyId") Integer medalHierarchyId, @RequestParam("planId") Integer planId , @RequestParam("memberId") String memberId, @RequestParam("number") Integer number, @RequestParam("pageSize") Integer pageSize );

    //查询会员等级
    @GetMapping("/grade")
    JSONArray grade(@RequestParam("memberId") String memberId,@RequestParam("gradeHierarchyId") Integer gradeHierarchyId );

    //回收会员勋章
    @PostMapping("/medal/recycle")
    JSONObject medalRecycle(@RequestBody MedalObtainRequest medalObtainRequest);


}
