package com.shuyun.kylin.customized.customer.dm;

import com.shuyun.kylin.crm.openapi.core.dto.common.MemberDto;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.dm.BaseDsRepository;
import com.shuyun.pip.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/2/28 15:02
 * @description
 */
@Component
@Slf4j
public class CustomerRepository extends BaseDsRepository<MemberDto> {
    /**
     * 通过openId、unionId查询消费者信息
     * @param openId
     * @param unionId
     * @return
     */
    public Map<String, Object> queryCustomer(String openId, String unionId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("openId", openId);
        queryMap.put("unionId", unionId);
        String queryMemberSql = " select firstCustomerNo,firstChannelType from " + ModelConstants.CUSTOMER + " where  openId = :openId and unionId = :unionId ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        Map<String, Object> data = new HashMap<>();
        if(!CollectionUtils.isEmpty(list)){
            data = list.get(0);
        }
        return data;
    }

    /**
     * 通过mobile查询消费者信息
     * @param mobile
     * @return
     */
    public Map<String, Object> queryCustomer(String mobile) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("mobile", mobile);
        String queryMemberSql = " select firstCustomerNo,firstChannelType from " + ModelConstants.CUSTOMER + " where  mobile = :mobile ";
        List<Map<String, Object>> list = execute(queryMemberSql, queryMap).getData();
        Map<String, Object> data = new HashMap<>();
        if(!CollectionUtils.isEmpty(list)){
            data = list.get(0);
        }
        return data;
    }
}
