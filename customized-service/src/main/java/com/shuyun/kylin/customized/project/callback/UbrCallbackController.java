package com.shuyun.kylin.customized.project.callback;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.project.dto.UbrNotifyDto;
import com.shuyun.kylin.customized.project.service.SendCouponService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: Jingwei
 * @date: 2024-12-12
 */
@Slf4j
@RestController
@RequestMapping("/ubr")
public class UbrCallbackController {

    @Resource
    private SendCouponService sendCouponService;

    @PostMapping("/sendCouponCallback")
    public ResponseResult<String> sendCouponCallback(@RequestBody UbrNotifyDto ubrNotifyDto) {
        log.info("UBR回调开始，入参ubrNotifyDto={}", JSON.toJSONString(ubrNotifyDto));
        return sendCouponService.ubrCallback(ubrNotifyDto);
    }
}
