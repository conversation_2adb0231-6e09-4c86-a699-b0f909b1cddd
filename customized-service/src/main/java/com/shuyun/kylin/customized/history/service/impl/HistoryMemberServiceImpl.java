package com.shuyun.kylin.customized.history.service.impl;

import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.crm.openapi.core.dto.wechat.WechatRegisterRequest;
import com.shuyun.kylin.crm.openapi.sdk.client.wechat.IWechatClient;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.enums.ResponseCodeEnum;
import com.shuyun.kylin.customized.base.feign.dao.LoyaltyFacadeDto;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.base.util.DataApiUtil;
import com.shuyun.kylin.customized.history.common.LoyaltyFacadeRepository;
import com.shuyun.kylin.customized.history.common.LoyaltyMemberRepository;
import com.shuyun.kylin.customized.history.service.HistoryMemberService;
import com.shuyun.kylin.customized.history.service.HistoryPoint;
import com.shuyun.pip.component.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;

@Slf4j
@Service
public class HistoryMemberServiceImpl implements HistoryMemberService {

    @Autowired
    private IWechatClient iWechatClient;

    @Autowired
    private HistoryPoint historyPoint;

    @Autowired
    private LoyaltyMemberRepository loyaltyMemberRepository;

    ExecutorService executorService = DataApiUtil.newBlockingThreadPool(ConfigurationCenterUtil.THREAD_CORE_SUM, ConfigurationCenterUtil.THREAD_QUEUE_SUM);
    @Override
    public void memberRegister() {
        try {
            Long start = System.currentTimeMillis();
            //分批处理
            final int pageSize = 2000;
            String index = "1";
            int completedNum = 0;

            while (true) {
                List<WechatRegisterRequest> historyMember = loyaltyMemberRepository.selectSendPoint(index, pageSize, completedNum);
                //封装
                List<WechatRegisterRequest> wechatRegister = JSON.parseArray(JSON.toJSONString(historyMember), WechatRegisterRequest.class);
                //记录该批次中最大mobile值
                index = wechatRegister.get(wechatRegister.size()-1).getMobile();
                log.info("最大mobile值:{}",JSON.toJSONString(index));
                completedNum += wechatRegister.size();
                UpdataRunnable sendRunnable = new UpdataRunnable(index, completedNum, wechatRegister);
                executorService.execute(sendRunnable);
                if (wechatRegister.size() < pageSize) {
                    break;
                }
            }
            log.info("历史会员注册任务分发完成total:{}+,分发耗时:{}ms,待子线程执行完毕...", completedNum, System.currentTimeMillis() - start);
        } catch (Exception e) {
            log.error("历史会员注册任务异常终止!", e);
        }
    }

    class UpdataRunnable implements Runnable {
        String index;
        int completedNum;
        List<WechatRegisterRequest> wechatRegisterRequests;

        private UpdataRunnable(String index, int completedNum, List<WechatRegisterRequest> wechatRegisterRequests) {
            this.index = index;
            this.completedNum = completedNum;
            this.wechatRegisterRequests = wechatRegisterRequests;
        }

        @Override
        public void run() {
            Long start = System.currentTimeMillis();
            updataMemberTable(wechatRegisterRequests);
            log.info("历史会员注册批次completedNum:{},index:{},耗时:{}ms", completedNum,index, System.currentTimeMillis() - start);
        }
    }

    public void updataMemberTable(List<WechatRegisterRequest> wechatRegisterRequests) {
        for (WechatRegisterRequest st : wechatRegisterRequests) {
            WechatRegisterRequest wechatRegisterRequest = new WechatRegisterRequest();
            wechatRegisterRequest.setUnionId(st.getUnionId());
            wechatRegisterRequest.setAppId(st.getAppId());
            wechatRegisterRequest.setOpenId(st.getOpenId());
            wechatRegisterRequest.setChannelType(st.getChannelType());
            wechatRegisterRequest.setGender(st.getGender());
            if (StringUtils.isNotBlank(st.getNickname())){
                wechatRegisterRequest.setNickname(st.getNickname());
            }
            if (StringUtils.isNotBlank(st.getBirthDay())){
                wechatRegisterRequest.setBirthDay(st.getBirthDay());
            }
            if (StringUtils.isNotBlank(st.getBirthYear())){
                wechatRegisterRequest.setBirthYear(st.getBirthYear());
            }
            wechatRegisterRequest.setMobile(st.getMobile());
            //wechatRegisterRequest.setCity(st.getCity());
            wechatRegisterRequest.setRegisterTime(getStartTimeDate(st.getRegisterTime()));
            if (StringUtils.isNotBlank(st.getHeadImgUrl())){
                wechatRegisterRequest.setHeadImgUrl(st.getHeadImgUrl());
            }
            wechatRegisterRequest.setMemberType(st.getMemberType());
            wechatRegisterRequest.setAppType(st.getAppType());
            if (StringUtils.isNotBlank(st.getEmail())){
                wechatRegisterRequest.setEmail(st.getEmail());
            }
            HashMap<String, Object> map = new HashMap<>();
            map.put("memberSource",st.getDistrict());
            if (StringUtils.isNotBlank(st.getProvince())){
                map.put("memberSourceDetail",st.getProvince());
            }
            Map<String, Object> hashMap = new HashMap<>();
            if ("false".equals(st.getShopCode())){
                hashMap.put("isKoMember",false);
            }else {
                hashMap.put("isKoMember",true);
            }
            wechatRegisterRequest.setCustomizedProperties(hashMap);
            wechatRegisterRequest.setBindingExtProperties(map);
            st.setDistrict("");
            st.setProvince("");
            st.setShopCode("");
            log.info("封装对象:{}",JSON.toJSONString(wechatRegisterRequest));
            try {
                iWechatClient.register(wechatRegisterRequest);
            } catch (Exception e) {
                historyPoint.updateHistoryMember("500", wechatRegisterRequest, e.getMessage());
            }
        }
    }

    public static String getStartTimeDate(String time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeStrart = time.substring(0, 19).replace("T", " ");
        return timeStrart;
    }
}