package com.shuyun.kylin.customized.behavior.resource.po;

import com.alibaba.druid.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class CheckPO {
    /** 会员ID（APPID+OPENID） */
    private String membershipId;
    /** 受邀请者的ID */
    private String invitee;
    /** 互动场景值 */
    private String scene;
    /**
     * 互动渠道类型
     */
    private String channelType;

    /** 答题是否正确，只有在博物馆答题场景才会传递 */
    private String isRight;

    /** 暂不使用的字段列表，也不知道是啥意思 */
    private String transactionId;
    private String campaignCode;
    private Integer transactionAmount;
    private Double orderAmount;
    private Double transactionPoint;
    private String brand;
    private List<Object> extraData;

    @Getter
    @AllArgsConstructor
    public enum SceneType {

        REGISTER("注册成功"),
        COMPLETE_INFO("完善资料"),
        CHECK_IN("签到打卡"),
        CONSECUTIVE_CHECK_IN("连续签到"),
        INVITE_REGISTER("邀请好友");

        //ADD_BUBBLE("打汽"),
        //QUESTION_ANSWER("博物馆答题");

        private String desc;

        public static Boolean isOnceOrConsecutiveCheckIn(String s) { return StringUtils.equals(SceneType.CHECK_IN.name(), s)
                || StringUtils.equals(SceneType.CONSECUTIVE_CHECK_IN.name(), s); }
    }

    @Override
    public String toString() {
        return "{" +
                "membershipId='" + membershipId + '\'' +
                ", scene=" + scene +
                '}';
    }
}
