package com.shuyun.kylin.customized.cdp.service.impl;

import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import com.shuyun.kylin.customized.base.util.DataApiUtil;
import com.shuyun.kylin.customized.base.util.HttpClientHelper;
import com.shuyun.kylin.customized.cdp.service.CdpMemberPointService;
import com.shuyun.pip.component.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


import java.util.HashMap;
import java.util.Map;


@Slf4j
@Service
public class CdpMemberPointServiceImpl implements CdpMemberPointService {

    @Override
    public Map<String, Object> retrySynchronousPoint(Map<String, String> request) {
        //更新重试状态
        HashMap<String, Object> map = new HashMap<>();
        map.put("id",request.get("id"));
        map.put("retryCount",Integer.valueOf(request.get("retryCount")));
        try {
            HashMap<String, String> header = new HashMap<>();
            header.put("CampaignId", "40000000");
            header.put("Content-Type", "application/json");
            header.put("Ocp-Apim-Subscription-Key", ConfigurationCenterUtil.MIDDLEGROUND_KEY);
            String body = HttpClientHelper.doCdpPostJSON(ConfigurationCenterUtil.OCP_INTERACTIVE_RECORD, request.get("newRequest"), header);
            map.put("state",true);
        } catch (Exception e) {
            log.warn("重试积分同步失败重推cdp失败:{}",e.getMessage());
            map.put("state",false);
        }
        return map;
    }
}
