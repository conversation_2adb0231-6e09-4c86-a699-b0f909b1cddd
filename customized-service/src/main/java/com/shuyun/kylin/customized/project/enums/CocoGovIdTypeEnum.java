package com.shuyun.kylin.customized.project.enums;

import lombok.Getter;

/**
 * 券核销所需的证件类型
 */
@Getter
public enum CocoGovIdTypeEnum {
    GOVERNMENT_ID("GovernmentId", "身份证"),
    CHINA_PR("ChinaPR", "外国人永久居留证"),
    TRAVEL_PERMIT("TravelPermit", "港澳台居民来往内地通行证"),
    RESIDENCE_PERMIT("ResidencePermit", "港澳台居民居住证"),
    PASSPORT("Passport", "护照;缺省为身份证");

    private final String code;
    private final String desc;

    CocoGovIdTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
