package com.shuyun.kylin.customized.coupon.resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.dm.api.response.DMLResponse;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.enums.ResponseCodeEnum;
import com.shuyun.kylin.customized.base.stream.kafka.messaging.KafkaSink;
import com.shuyun.kylin.customized.base.util.DataApiUtil;
import com.shuyun.kylin.customized.base.util.DateHelper;
import com.shuyun.kylin.customized.project.request.GrantCouponRequest;
import com.shuyun.kylin.customized.project.response.GrantCouponResponse;
import com.shuyun.kylin.customized.project.service.KoCouponService;
import com.shuyun.kylin.customized.swire.dto.SwireMemberRecordsDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;

@Slf4j
@Component
public class CouponListener {

    @Resource
    private KoCouponService koCouponService;

    @StreamListener(KafkaSink.KAFKA_CUSTOM_MEMBER_COUPON_INPUT)
    public void syncSendMemberCouponInput(GrantCouponRequest grantCouponRequest) {
        log.info("消费外部发券发放:{}", JSON.toJSON(grantCouponRequest));
        GrantCouponResponse couponResponse = koCouponService.instanceGrant(grantCouponRequest);
        log.info("消费外部发券发放返回结果:{}",JSON.toJSONString(couponResponse));
        HashMap<String, Object> map = new HashMap<>();
        map.put("lastSync", DateHelper.getNowZone());
        map.put("code",couponResponse.getCode());
        map.put("msg",couponResponse.getMsg());
        DMLResponse response = DataApiUtil.upsertIsData(ModelConstants.ROSEN_EQUITY_GRANT, grantCouponRequest.getTransactionId(), map);
        if (!response.getIsSuccess()) {
            log.error("消费外部发券发放返回结果保存失败:{}",response.getOperation());
        }
    }
}
