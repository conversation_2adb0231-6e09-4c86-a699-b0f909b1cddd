package com.shuyun.kylin.customized.member.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CepMemberDeductionDto {

    @NotBlank(message = "场景值不能为空")
    private String scene;
    @NotBlank(message = "客户编号不能为空，格式：appid_openid")
    private String customerNo;
    @NotBlank(message = "businessId不为空")
    private String businessId;
    @NotBlank(message = "会员渠道不能为空: TAOBAO：天猫,KO_MP：KO小程序,EC_SHOPPING：EC购物商城,EC_POINT：EC积分商城,H5：H5活动")
    private String channelType;
    @NotBlank(message = "会员类型不能为空,固定值KO")
    private String memberType;
    private String costCenter;
    // @NotBlank(message = "是否成本中心映射值不能为空")
    private Boolean isCostMapping;
    private String changeSource;
    private String changeSourceDetail;
    private String costTracing;
    private String desc;
    private String sendLBSProvince;
    private String sendLBSCity;
    private String sendLBSDistrict;
}
