package com.shuyun.kylin.customized.spi.resource;

import com.shuyun.kylin.crm.openapi.core.dto.common.RegisterMemberDto;
import com.shuyun.kylin.crm.openapi.core.dto.common.SaveDataRequestDto;
import com.shuyun.kylin.crm.openapi.core.dto.common.UpdateMemberDto;
import com.shuyun.kylin.crm.openapi.core.dto.spi.DataTransferValidationResultDto;
import com.shuyun.kylin.crm.openapi.core.dto.spi.ValidationResultDto;
import com.shuyun.kylin.crm.openapi.core.dto.tmall.TaobaoRegisterRequest;
import com.shuyun.kylin.crm.openapi.core.dto.tmall.TaobaoUnbindRequest;
import com.shuyun.kylin.crm.openapi.core.dto.wechat.WechatRegisterRequest;
import com.shuyun.kylin.crm.openapi.core.dto.youzan.YouZanRegisterRequest;
import com.shuyun.kylin.crm.openapi.spi.IValidationService;
import com.shuyun.pip.component.json.JsonUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


/**
 * 用于定制化 Openapi  参数验证
 * 前提:  /customized-service/customized-service.openapi.spi.enabled=true
 */
@Tag(name = "openapi定制参数校验", description = "用于定制化 Openapi  参数验证")
@RestController
@RequestMapping("/openapi")
@ConditionalOnExpression("${customized-service.openapi.spi.enabled}")
public class CustomizedValidationSpiResource implements IValidationService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    @PostMapping("/common/register/validation")
    @Operation(summary = "全渠道会员注册校验", description = "应用场景: 用于全渠道会员注册定制校验")
    public ValidationResultDto validateCommonRegister(@Valid @RequestBody RegisterMemberDto request) {
        logger.info("接收到来自于openapi的全渠道会员注册校验请求，参数:{}", JsonUtils.toJson(request));
        //TODO
        return null;
    }

    @Override
    @PostMapping("/tmall/register/validation")
    @Operation(summary = "天猫会员通会员注册校验", description = "应用场景: 用于天猫会员通会员注册定制校验")
    public ValidationResultDto validateTmallRegister(@Valid @RequestBody TaobaoRegisterRequest request) {
        logger.info("接收到来自于openapi的天猫会员通会员注册校验请求，参数:{}", JsonUtils.toJson(request));
        //TODO
        return null;
    }

    @Override
    @PostMapping("/tmall/unbind/validation")
    @Operation(summary = "天猫会员通会员解绑校验", description = "应用场景: 用于天猫会员通会员解绑定制校验")
    public ValidationResultDto validateTmallUnbind(@Valid @RequestBody TaobaoUnbindRequest request) {
        logger.info("接收到来自于openapi的天猫会员通会员解绑校验请求，参数:{}", JsonUtils.toJson(request));
        //TODO
        return null;
    }

    @Override
    @PostMapping("/wechat/register/validation")
    @Operation(summary = "微信会员注册校验", description = "应用场景: 用于微信会员注册定制校验")
    public ValidationResultDto validateWechatRegistration(@Valid @RequestBody WechatRegisterRequest request) {
        logger.info("接收到来自于openapi的微信会员注册校验请求，参数:{}", JsonUtils.toJson(request));
        //TODO
        return null;
    }

    @Override
    @PostMapping("/dataTransfer/saveData/validation")
    @Operation(summary = "数据接入自定义校验", description = "应用场景: 用于数据对接服务接入数据自定义校验")
    public DataTransferValidationResultDto validateDataTransferInputData(@Valid @RequestBody SaveDataRequestDto request) {
        logger.info("接收到来自于openapi的数据接入自定义校验请求，参数:{}", JsonUtils.toJson(request));
        //TODO  默认校验通过
        return DataTransferValidationResultDto.Companion.success(request.getData());
    }

    @Override
    @PostMapping("/youzan/register/validation")
    @Operation(summary = "有赞会员注册校验", description = "应用场景: 用于有赞会员注册定制校验")
    public ValidationResultDto validateYouZanRegister(@Valid @RequestBody YouZanRegisterRequest request) {
        logger.info("接收到来自于openapi的有赞会员注册校验请求，参数:{}", JsonUtils.toJson(request));
        //TODO
        return null;
    }

    @Override
    @PostMapping("/common/modifyMember/validation")
    @Operation(summary = "更新全渠道会员信息自定义校验", description = "应用场景: 用于全渠道会员更新信息时的校验逻辑")
    public ValidationResultDto validateUpdateCommonMember(@Valid @RequestBody UpdateMemberDto request) {
        logger.info("接收到来自于openapi的更新全渠道会员信息自定义校验请求，参数:{}", JsonUtils.toJson(request));
        //TODO
        return null;
    }

}
