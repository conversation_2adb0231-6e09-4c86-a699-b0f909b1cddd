package com.shuyun.kylin.customized.coupon.strategy;

import com.shuyun.kylin.customized.coupon.dto.CustomerCouponDto;
import com.shuyun.kylin.customized.coupon.dto.CustomerDto;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 用于第三方卡劵系统发劵
 * @date 2021/3/19
 */
@Component("thirdCouponSendStrategy")
public class ThirdCouponSendStrategy implements CouponSendStrategy{
    @Override
    public List<CustomerDto> sendCoupon(CustomerCouponDto customerCouponDto) {
        // TODO --需要项目定制
        return null;
    }
}
