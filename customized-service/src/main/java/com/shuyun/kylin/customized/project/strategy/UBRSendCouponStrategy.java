package com.shuyun.kylin.customized.project.strategy;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.shuyun.kylin.customized.base.feign.dao.MemberBingDto;
import com.shuyun.kylin.customized.base.feign.dao.OfferProjectDetailClientResponse;
import com.shuyun.kylin.customized.project.dto.SendCouponResultDto;
import com.shuyun.kylin.customized.project.request.GrantCouponRequest;
import com.shuyun.kylin.customized.rpc.dto.UBRCouponCodeRequestDto;
import com.shuyun.kylin.customized.rpc.dto.UBRCouponResponseDto;
import com.shuyun.kylin.customized.rpc.enums.UbrCoupoonCreatedStatusEnum;
import com.shuyun.kylin.customized.rpc.template.UbrRequestTemplate;
import lombok.extern.slf4j.Slf4j;

import java.util.UUID;

@Slf4j
public class UBRSendCouponStrategy extends SendCouponStrategy {
    @Override
    public SendCouponResultDto sendCoupon(String templateId, OfferProjectDetailClientResponse projectDetail, GrantCouponRequest grantCouponRequest, MemberBingDto memberBindInfo) {
        log.info("ubr发券策略入参，templateId={}，projectDetail={}，grantCouponRequest={}", templateId, JSON.toJSONString(projectDetail), JSON.toJSONString(grantCouponRequest));
        OfferProjectDetailClientResponse.RspData data = projectDetail.getData();
        if(ObjectUtil.isNull(data)) {
            log.error("内部接口返回失败，data is empty!");
            return new SendCouponResultDto(Boolean.FALSE, null, null,null);
        }
        OfferProjectDetailClientResponse.RspData.ExtData extData = data.getExtData();
        if(ObjectUtil.isNull(extData)) {
            log.error("内部接口返回失败，extData is empty!");
            return new SendCouponResultDto(Boolean.FALSE, null, null,null);
        }
        String externalProjectId = extData.getExternalProjectId();
        if(ObjectUtil.isNull(externalProjectId)) {
            log.error("内部接口返回失败，externalProjectId is empty!");
            return new SendCouponResultDto(Boolean.FALSE, null, null,null);
        }
        UBRCouponCodeRequestDto ubrCouponCodeRequestDto = assembleRequestData(grantCouponRequest);
        log.info("ubr发券以原始入参，externalProjectId={}，ubrCouponCodeRequestDto={}", externalProjectId, JSON.toJSONString(ubrCouponCodeRequestDto));
        UBRCouponResponseDto ubrCouponResponseDto = UbrRequestTemplate.sendCoupon(externalProjectId, ubrCouponCodeRequestDto, grantCouponRequest.getTransactionId());
        if(ObjectUtil.isNull(ubrCouponResponseDto)) {
            log.error("urb发券返回结果为空");
            return new SendCouponResultDto(Boolean.FALSE, null, null,null);
        }
        String status = ubrCouponResponseDto.getStatus();
        if(ObjectUtil.notEqual(status, UbrCoupoonCreatedStatusEnum.DISTRIBUTED.getCode())) {
            log.error("urb发券返回结果状态异常，status={}", status);
            return new SendCouponResultDto(Boolean.FALSE, null, null,null);
        }
        return new SendCouponResultDto(Boolean.TRUE, null, ubrCouponResponseDto.getUniqueCode(),null);
    }

    private UBRCouponCodeRequestDto assembleRequestData(GrantCouponRequest grantCouponRequest) {
        UBRCouponCodeRequestDto ubrCouponCodeRequestDto = new UBRCouponCodeRequestDto();
        ubrCouponCodeRequestDto.setGovId(grantCouponRequest.getGovId());
        ubrCouponCodeRequestDto.setGovIdType(grantCouponRequest.getGovIdType());
        ubrCouponCodeRequestDto.setGuestName(grantCouponRequest.getGuestName());
        ubrCouponCodeRequestDto.setMobile(grantCouponRequest.getMobile());
        ubrCouponCodeRequestDto.setRequestId(UUID.randomUUID().toString().replace("-", ""));
        return ubrCouponCodeRequestDto;
    }
}
