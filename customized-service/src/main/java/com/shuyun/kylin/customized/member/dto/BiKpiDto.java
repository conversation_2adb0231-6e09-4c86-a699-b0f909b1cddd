package com.shuyun.kylin.customized.member.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2022/7/21 14:01
 * @Version 1.0
 */
@Data
public class BiKpiDto {
    @NotBlank(message = "data不能为空")
    private String date;
    @NotBlank(message = "type不能为空")
    private String type;
    @NotNull(message = "kpi值不能为空")
    private Double value;
    @NotBlank(message = "创建时间不能为空")
    private String created;
    @NotBlank(message = "更新时间不能为空")
    private String lastSync;
}
