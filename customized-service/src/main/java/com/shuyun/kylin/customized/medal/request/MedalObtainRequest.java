package com.shuyun.kylin.customized.medal.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class MedalObtainRequest {
    //会员ID
    @NotBlank(message = "会员id不能为空")
    private String memberId;

    //勋章类型
//    @NotBlank(message = "勋章类型不能为空")
    private String medalType;

    //勋章ID
    private Integer medalDefinitionId;

    //体系id
    private Integer medalHierarchyId;

    //勋章过期时间
    private String overdueDate;

    //渠道
    private String channelType;

    //描述
    private String description;


}
