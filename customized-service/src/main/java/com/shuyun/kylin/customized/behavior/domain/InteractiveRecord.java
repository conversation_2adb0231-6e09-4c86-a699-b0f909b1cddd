package com.shuyun.kylin.customized.behavior.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.base.Joiner;
import com.shuyun.kylin.customized.behavior.resource.po.CheckPO;
import com.shuyun.kylin.customized.behavior.service.impl.AbstractInteractionSceneResolver;
import com.shuyun.kylin.customized.behavior.util.DateUtils;
import com.shuyun.kylin.customized.behavior.util.StringUtils;
import com.shuyun.pip.component.json.JsonUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.shuyun.kylin.customized.behavior.util.DateUtils.YYYY_MM_DD;

@Slf4j
@Setter
@Getter
@Data
public class InteractiveRecord {

    public static final ThreadLocal<Consumer<InteractiveRecord>> CONSUMER_THREAD_LOCAL = new ThreadLocal<>();

    private String id;                  //id	Id
    private String customerNo;          //会员渠道编号	String
    private String mobile;                  //手机号
    private String invitee;                  //被邀请者的渠道编号
    private String inviteeMember;                  //手机号
    private String inviteeMobile;                  //手机号
    private String member;                  //会员ID
    private String memberIDStr;         //会员ID	String
    private String channelType;            //渠道	Enum
    private String interactive;                  //互动ID
    private String interactiveId;       //互动Code	String
    //    private String interactiveName;     //互动名称	String
    private String interactiveRecordId; //参与互动记录id	String
    private String interactiveScene;    //行为场景值	String
    private String interactiveType;     //参与行为类型	String
    private String rewardsType;         //获奖类型
    private Boolean isGetRewards;       //获奖	Boolean
    private Integer grantStatus;        //发放状态	Enum
    private String refuseReason;        //拒绝原因	String
    private String coupon;              //本次互动发放的券	String
    private String couponList;              //本次互动发放的券id	String
    private String couponDetailList;  //cep返回结果明细id
    private Integer memberIntegral;     //本次互动发放的积分数	Number
    private ZonedDateTime joinTime;              //参与时间	DateTime
    private Integer signTime;           //连续签到次数	Integer
    private ZonedDateTime created;               //创建时间	DateTime
    private ZonedDateTime lastSync;              //更新时间	DateTime

    @Data
    @AllArgsConstructor
    public static class Member {
        private String id;
    }

    @JsonIgnore
    public boolean isGetRewardsToday() {
        return Objects.equals(DateUtils.formatDateYYYYMMDD(new Date()), joinTime.format(DateTimeFormatter.ofPattern(YYYY_MM_DD)));
    }

    @AllArgsConstructor
    public enum Fields {
        id("id", String.class, "'"),                  //id	Id
        customerNo("customerNo", String.class, "'"),          //会员渠道编号	String
        invitee("invitee", String.class, "'"),          //会员渠道编号	String
        inviteeMember("inviteeMember", String.class, "'"),          //会员渠道编号	String
        inviteeMobile("inviteeMobile", String.class, "'"),          //会员渠道编号	String
        mobile("mobile", String.class, "'"),   //渠道	Enum
        member("member", String.class, "'"),         //会员ID	String
        memberIDStr("memberIDStr", String.class, "'"),         //会员ID	String
        memberIntegral("memberIntegral", Integer.class, ""),     //本次互动发放的积分数	Number
        interactive("interactive", String.class, "'"),       //互动Id
        interactiveScene("interactiveScene", String.class, "'"),    //行为场景值	String
        interactiveId("interactiveId", String.class, "'"),       //互动Code	String
        //        interactiveName("interactiveName", String.class, "'"),     //互动名称	String
        interactiveRecordId("interactiveRecordId", String.class, "'"), //参与互动记录id	String
        interactiveType("interactiveType", String.class, "'"),     //参与行为类型	String
        channelType("channelType", String.class, "'"),   //渠道	Enum
        coupon("coupon", String.class, "'"),             //本次互动发放的券	String
        couponList("couponList", String.class, "'"),             //本次互动发放的券id	List<String>
        couponDetailList("couponDetailList", String.class, "'"),  //cep返回结果明细id	List<String>
        grantStatus("grantStatus", Integer.class, ""),        //发放状态	Enum
        isGetRewards("isGetRewards", Boolean.class, ""),       //获奖	Boolean
        joinTime("joinTime", Date.class, "'"),            //参与时间	DateTime
        refuseReason("refuseReason", String.class, "'"),        //拒绝原因	String
        signTime("signTime", Date.class, "'"),           //连续签到次数	Integer
        created("created", Date.class, "'"),               //创建时间	DateTime
        lastSync("lastSync", Date.class, "'");              //更新时间	DateTime

        private String fieldName;
        private Class<?> javaType;
        private String sqlFieldPrefix;

        public static String getAllFields() {
            return Joiner.on(",").join(Arrays.stream(values()).map(x -> x.fieldName).collect(Collectors.toList()));
        }

        public static List<String> collectEditableFields() {
            List<String> fields = Arrays.stream(values()).map(x -> x.fieldName).collect(Collectors.toList());
            //以下字段为衍生字段，不可编辑
            fields.removeIf("interactiveScene"::equals);
            fields.removeIf("interactiveId"::equals);
            fields.removeIf("interactiveName"::equals);
            return fields;
        }

        public static Predicate<String> getEditablePredicate() {
            return x -> collectEditableFields().contains(x);
        }

        public String eq(Object value) {
            return genSqlStatement("=", value);
        }

        public String le(Object value) {
            return genSqlStatement("<=", value);
        }

        public String ge(Object value) {
            return genSqlStatement(">=", value);
        }

        public String not(Object value) { return genSqlStatement("<>", value); }

        public String in(List<Object> values) { return genSqlStatement("IN", values); }

        private String genSqlStatement(String operator, Object value) {
            String formatValue = value.toString();
            //日期需要特殊处理
            if (value instanceof Date && javaType == Date.class) {
                formatValue = DateUtils.formatDateTime((Date) value);
            }

            return Joiner.on(" ").join(fieldName, operator, StringUtils.wrapperWith(this.sqlFieldPrefix, formatValue));
        }

        private String genSqlStatement(String operator, List<Object> values) {
            return Joiner.on(" ").join(fieldName,
                    operator,
                    "(",
                    StringUtils.wrapperWith(this.sqlFieldPrefix, values.stream()
                            .map(String::valueOf)
                            .collect(Collectors.toList())),
                    ")");
        }
    }

    public static final String fqn = "data.prctvmkt.KO.interactiveRecords";

    public static String genALlFieldQuery(String where) {
        if (where == null) {
            where = "";
        }

        //如果没有加where，自动给它加上
        if (!where.toUpperCase().trim().startsWith("WHERE")) {
            where = "WHERE " + where;
        }

        return Joiner.on(" ").join("Select", Fields.getAllFields(), "From", fqn, where).trim();
    }

    public static InteractiveRecord initRecord(String customerNo,
                                               String memberId,
                                               String mobile,
                                               String channelType,
                                               Interactive interactive,
                                               AbstractInteractionSceneResolver.RewardContext rewardContext,
                                               int grantStatus,
                                               InteractiveRecord lastRecord,
                                               InteractiveRecord nextRecord,
                                               boolean otherChannelCheckIn) {
        InteractiveRecord interactiveRecord = new InteractiveRecord();
        interactiveRecord.setId(StringUtils.uuid());
        interactiveRecord.setCustomerNo(customerNo);
        interactiveRecord.setCouponList(nextRecord.getCouponList());
        interactiveRecord.setCouponDetailList(nextRecord.getCouponDetailList());
        //参与时间
        interactiveRecord.setJoinTime(ZonedDateTime.now());

        AbstractInteractionSceneResolver.RewardInfo rewardInfo = rewardContext.getRewardInfo();
        //拒绝原因
        interactiveRecord.setRefuseReason(rewardInfo.getRefuseReason());

        //连续签到次数  不是签到行为不设置
        if (!CheckPO.SceneType.CHECK_IN.name().equals(interactive.getScene()) && !CheckPO.SceneType.CONSECUTIVE_CHECK_IN.name().equals(interactive.getScene())) {
            interactiveRecord.setSignTime(null);
        } else {
            interactiveRecord.setSignTime(1);
            if (lastRecord != null && !otherChannelCheckIn) {
                Date lastJoinTime = Date.from(lastRecord.getJoinTime().toInstant());
                //如果是连续的，则 +1，否则，置为 1
                if (DateUtils.isConsecutiveDates(lastJoinTime, new Date())) {
                    log.info("连续签到，签到次数加1");
                    interactiveRecord.setSignTime(lastRecord.getSignTime() + 1);
                } else {
                    log.info("非连续签到，签到次数置为1");
                    interactiveRecord.setSignTime(1);
                }
            }
        }

        //在上下文中，修改对象，如连续签到会在这里修改
        try {
            Consumer<InteractiveRecord> interactiveRecordConsumer = CONSUMER_THREAD_LOCAL.get();
            log.info("对象修改函数：{}", interactiveRecordConsumer);
            if (interactiveRecordConsumer != null) {
                log.info("修改前：{}", JsonUtils.toJson(interactiveRecord));
                interactiveRecordConsumer.accept(interactiveRecord);
                log.info("修改后：{}", JsonUtils.toJson(interactiveRecord));
            }
        } finally {
            CONSUMER_THREAD_LOCAL.remove();
        }
        String scene = interactive.getScene();
        if (rewardInfo.isConsecutiveCheckin()) {
            scene = CheckPO.SceneType.CONSECUTIVE_CHECK_IN.name();
        }
        //参与行为的类型
        interactiveRecord.setInteractiveType(scene);
        //渠道
        interactiveRecord.setChannelType(channelType);
        //参与互动记录id
        interactiveRecord.setInteractiveRecordId(StringUtils.isNullOrEmpty(nextRecord.getInteractiveRecordId()) ? interactive.getId() + "_" + StringUtils.uuid() : nextRecord.getInteractiveRecordId());
        //发放状态
        if(rewardInfo.isRefuse() && rewardInfo.isInternalError()) {
            //内部调用发放积分接口错误
            grantStatus = 1;
        }
        interactiveRecord.setGrantStatus(grantStatus);
        //本次互动发放的积分数
        interactiveRecord.setMemberIntegral((int) rewardInfo.getPoint()); //如果发送积分接口调用失败，此处在发送接口失败时，会被更新成0
        //本次互动发放的券 --暂时不实现
        interactiveRecord.setCoupon(interactive.getCouponList());
        //创建时间
        interactiveRecord.setCreated(ZonedDateTime.now());
        //是否获奖
        interactiveRecord.setIsGetRewards(grantStatus == 0);
        //获奖类型
        interactiveRecord.setRewardsType(nextRecord.getRewardsType());
        //会员ID
        interactiveRecord.setMemberIDStr(memberId);
        //手机号
        interactiveRecord.setMobile(mobile);

        //保存引用字段
        interactiveRecord.setMember(memberId);
        interactiveRecord.setInteractive(interactive.getId());

        return interactiveRecord;
    }
}
