package com.shuyun.kylin.customized.behavior.util;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;
import java.util.TimeZone;

@Slf4j
public class DateUtils {

    public final static String YYYY_MM_DD_T_HH_MM_SS_SSS = "yyyy-MM-dd'T'HH:mm:ss.SSS";
    public final static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public final static String YYYY_MM_DD = "yyyy-MM-dd";
    public final static String YYYY_MM = "yyyy-MM";
    public final static String BEGIN_OF_DAY = "T00:00:00.000";
    public final static String END_OF_DAY = "T23:59:59.999";


    public static String format(Date date, String pattern) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        return simpleDateFormat.format(date);
    }

    public static Date parse(String s, String pattern) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        try {
            return simpleDateFormat.parse(s);
        } catch (ParseException e) {
            log.error("日期格式不合法，传入字符串：{}", s);
            return null;
        }
    }

    public static String formatDateTime(Date s) {
        return format(s, YYYY_MM_DD_T_HH_MM_SS_SSS);
    }

    public static String formatDateYYYYMMDD(Date date) {
        return format(date, YYYY_MM_DD);
    }

    public static Date parseDateTime(String s) {
        return parse(s, YYYY_MM_DD_T_HH_MM_SS_SSS);
    }

    public static boolean isConsecutiveDates(Date dateBefore, Date dateAfter) {
        //任意为空，则不连续
        if (dateAfter == null || dateBefore == null) {
            return false;
        }

        //后一天不能早于前一天
        if (dateAfter.before(dateBefore)) {
            return false;
        }

        return (trunc(dateAfter).getTime() - trunc(dateBefore).getTime()) == 86400000; //86400000 即一天的毫秒数
    }

    public static String getMonthStart(Date date) {
        String format = format(date, YYYY_MM);
        format += ("-01" + BEGIN_OF_DAY);
        return format;
    }

    public static String getMonthEnd(Date date) {
        Date date1 = parseDateYYYYMMDD(format(date, YYYY_MM) + "-01");
        date1 = addMonths(date1, 1);
        date1 = addDays(date1, -1);
        return dateEndStr(date1);
    }

    private static Date addDays(Date date1, int amount) {
        return addDateUnit(date1, amount, Calendar.DATE);
    }

    private static Date addMonths(Date date1, int amount) {
        return addDateUnit(date1, amount, Calendar.MONTH);
    }

    @NotNull
    private static Date addDateUnit(Date date1, int i, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date1);
        calendar.add(month, i);
        return calendar.getTime();
    }

    public static class GMT8 {
        public static String toGMT0(String time) {
            return timeZoneTransfer(time, "+8", "0");
        }

        public static String toGMT0(Date time) {
            String formatDateTime = formatDateTime(time);
            return timeZoneTransfer(formatDateTime, "+8", "0");
        }

        public static Date parseDateTime(String dateStr) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(YYYY_MM_DD_T_HH_MM_SS_SSS);
            simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT+8"));
            try {
                return simpleDateFormat.parse(dateStr);
            } catch (ParseException e) {
                log.error("日期格式不合法，传入字符串：{}", dateStr);
                return null;
            }
        }
    }

    public static class GMT0 {
        public static String toGMT8(String time) {
            return timeZoneTransfer(time, "0", "+8");
        }

        public static Date toGMT8Date(Object start) {
            if (start == null) {
                return null;
            }

            String s = start.toString();
            String toGMT8DateStr = toGMT8(s);

            return GMT8.parseDateTime(toGMT8DateStr);
        }
    }

    private static String timeZoneTransfer(String time, String nowTimeZone, String targetTimeZone) {
        if (StringUtils.isNullOrEmpty(time)) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(YYYY_MM_DD_T_HH_MM_SS_SSS);
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT" + nowTimeZone));
        Date date;
        try {
            date = simpleDateFormat.parse(time);
        } catch (ParseException e) {
            return null;
        }
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT" + targetTimeZone));
        return simpleDateFormat.format(date);
    }


    public static Date parseDateYYYYMMDD(String yyymmdd) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(YYYY_MM_DD);
        try {
            return simpleDateFormat.parse(yyymmdd);
        } catch (ParseException e) {
            log.error("日期格式不合法，传入字符串：{}", yyymmdd);
            return null;
        }
    }

    public static Date trunc(Date date) {
        return parseDateYYYYMMDD(formatDateYYYYMMDD(date));
    }

    public static String dateEndStr(Date date) {
        return formatDateYYYYMMDD(date) + END_OF_DAY;
    }

    public static String dateStartStr(Date date) {
        return formatDateYYYYMMDD(date) + BEGIN_OF_DAY;
    }
    public static String getTodayStart() {
        return dateStartStr(new Date());
    }

    public static String getTodayEnd() {
        return dateEndStr(new Date());
    }

    public static final DateTimeFormatter dateTimeFormatter = new DateTimeFormatterBuilder()
            .parseCaseInsensitive()
            .appendPattern("yyyy-MM-dd'T'HH:mm:ss.SSS")
            .appendOffset("+HH:MM", "Z")
            .toFormatter();

    public static class DataModel {
        public static String format(ZonedDateTime invoke) {
            return invoke.format(dateTimeFormatter);
        }
    }

    public static void main(String[] args) {
        ZonedDateTime now = ZonedDateTime.now();
        String format = dateTimeFormatter.format(now);
        System.out.println(format);

        System.out.println(GMT0.toGMT8("2021-12-31T00:00:00.000Z"));

        //将0时区的时间转换到 8时区
        System.out.println(GMT0.toGMT8Date(getTodayStart()).toLocaleString());
        System.out.println(GMT0.toGMT8(getTodayStart()));

        //将当前日期转换到 0时区
        Date x = new Date();
        System.out.println(GMT8.toGMT0(x));


        System.out.println(isConsecutiveDates(parseDateYYYYMMDD("2022-01-03"), new Date()));
        System.out.println(isConsecutiveDates(parseDateYYYYMMDD("2022-01-05"), new Date()));


        LocalDateTime localDateTime = LocalDateTime.parse("2022-06-30T14:56:25.205");
        Date from = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());

        System.out.println(Objects.equals(DateUtils.GMT8.toGMT0(DateUtils.getMonthStart(from)), "2022-05-31T16:00:00.000"));
        System.out.println(Objects.equals(DateUtils.GMT8.toGMT0(DateUtils.getMonthEnd(from)), "2022-06-30T15:59:59.999"));

        System.out.println(getMonthStart(from) + "\t" + DateUtils.GMT8.toGMT0(DateUtils.getMonthStart(from)));
        System.out.println(getMonthEnd(from) + "\t" + DateUtils.GMT8.toGMT0(DateUtils.getMonthEnd(from)));
    }
}
