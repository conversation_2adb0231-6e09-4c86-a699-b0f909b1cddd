package com.shuyun.kylin.customized.medal.service;

import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.medal.request.CampaignRecordRequest;
import com.shuyun.kylin.customized.medal.request.MedalObtainRequest;
import com.shuyun.kylin.customized.medal.request.MedalProgressRequest;
import com.shuyun.kylin.customized.medal.response.*;

import java.text.ParseException;
import java.util.List;

public interface MedalService {

    ResponseResult<List<MedalListResponse>>  medal();

    ResponseResult<String> campaignRecord(CampaignRecordRequest campaignRecord);

    ResponseResult<List<MemberMedalListResponse>> getMemberMadelList(String memberId);

    ResponseResult<MedalDetailsResponse> getMedalDetails(Integer medalDefinitionId,String memberId) throws ParseException;

    ResponseResult<MedalGradeResponse> grade(String memberId);

    ResponseResult<List<MedalProgressResponse>> progress(String memberId);

    ResponseResult<String> obtain(MedalObtainRequest medalObtainRequest);

//    void  medalProgressByCampaignConsumer(MedalProgressRequest medalProgressRequest);
//
//    void medalProgressByPointRecordConsumer(MedalProgressRequest medalProgressRequest);
}
