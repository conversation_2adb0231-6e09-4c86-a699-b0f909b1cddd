package com.shuyun.kylin.customized.base.util;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.kylin.starter.redis.ICache;
import com.shuyun.pip.component.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.apache.commons.codec.binary.Hex;


import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.Map;

@Slf4j
public class AuthenticationUtil {


    public static Map methodPostJson(String url,String token,Object params) {
        //获取签名
        String sign = AuthenticationUtil.createSign(url, JsonUtils.toJson(params));
        log.info("cepSign:{}",sign);
        //HttpHeaders headers = new HttpHeaders();
        HashMap<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", token);
        headers.put("X-TW-SIGNATURE", sign);

        return headers;
    }

    //生成签名
    public static String createSign(String url, String body) {
         String str = url + body + ConfigurationCenterUtil.CEP_SECRED;
        //String str ="'" + url + body + "Aa109qbADuzPu" + "'";
        log.info("cepStr:{}",str);
        MessageDigest messageDigest;
        String encdeStr = "";
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            byte[] hash = messageDigest.digest(str.getBytes("UTF-8"));
            encdeStr = Hex.encodeHexString(hash);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return encdeStr;
    }

    public static String SHA256(final String str) {
        String strText = str + ConfigurationCenterUtil.MEMBER_SIDEBAR_KEY;
        log.info("strText:{}",strText);
        return SHA(strText, "SHA-256");
    }
    public static String SHA512(final String strText) {
        return SHA(strText, "SHA-512");
    }

    /**
     * 字符串 SHA 加密
     *
     * @param
     * @return
     */
    private static String SHA(final String strText, final String strType) {
        // 返回值
        String strResult = null;

        try {
            MessageDigest md = MessageDigest.getInstance(strType);
            byte[] bytes = md.digest(strText.getBytes("UTF-8"));
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < bytes.length; i++) {
                sb.append(Integer.toString((bytes[i] & 0xff) + 0x100, 16).substring(1));
            }
            strResult = sb.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error("NoSuchAlgorithmException",e);
        } catch (UnsupportedEncodingException e) {
            log.error("UnsupportedEncodingException",e);
        }
        return strResult;
    }

    public static HttpHeaders methodPostJsonHttpHeader(String url, String koken, String toJSONString) {
        //获取签名
        String sign = AuthenticationUtil.createSign(url, toJSONString);
        log.info("Sign:{}",sign);
        HttpHeaders header = new HttpHeaders();
        header.add("Content-Type", "application/json");
        header.add("Authorization", koken);
        header.add("X-TW-SIGNATURE", sign);
        return header;
    }
 /*   public static void main(String[] args) {

        String url = "api/test?testid=MIS0002&member_no=WBHBS099999999999999&version=1.0.0&mode=123212321";
        //String url = "api/common/sidebar/fetch?appid=936&user_id=shilang";
        LinkedHashMap<Object, Object> hashMap = new LinkedHashMap<>();
        hashMap.put("member_no","WBHBS099999999999999");
        hashMap.put("beat_time","1490787356");
        hashMap.put("rand_num","w55798x2vnir38oy");
        System.out.println(JSON.toJSONString(hashMap));
        String sign = createSign(url, JSON.toJSONString(hashMap));

        System.out.println(sign);

    }*/

}
