package com.shuyun.kylin.customized.coupon.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("tb_coupon_config")
public class CouponConfig {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String shopCode;

    private String memberType;

    private String appId;

    private String apiKey;

    private String apiSecret;

    private Date created;

    private Date lastSync;

}
