package com.shuyun.kylin.customized.project.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.kylin.customized.base.common.Constants;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.base.enums.ResponseCodeEnum;
import com.shuyun.kylin.customized.base.feign.client.BenefitClient;
import com.shuyun.kylin.customized.base.feign.client.OpenApiFeignClientV3;
import com.shuyun.kylin.customized.base.feign.dao.*;
import com.shuyun.kylin.customized.base.util.DataapiSdkUtil;
import com.shuyun.kylin.customized.base.util.DateHelper;
import com.shuyun.kylin.customized.project.dto.McdCallbackRequestDto;
import com.shuyun.kylin.customized.project.entity.InnerGrantCouponFailureLog;
import com.shuyun.kylin.customized.project.enums.CouponUseStatusEnum;
import com.shuyun.kylin.customized.project.enums.GrantStatusEnum;
import com.shuyun.kylin.customized.project.enums.InnerGrantFailureType;
import com.shuyun.kylin.customized.project.enums.IsDeductPointEnum;
import com.shuyun.kylin.customized.project.request.GrantCouponRequest;
import com.shuyun.kylin.customized.project.request.OfferProjectRequest;
import com.shuyun.kylin.customized.project.response.*;
import com.shuyun.kylin.customized.project.service.KoCouponService;
import com.shuyun.kylin.customized.project.service.SendCouponService;
import com.shuyun.kylin.customized.project.strategy.IQiYiSendCouponStrategy;
import com.shuyun.kylin.customized.project.strategy.router.SendCouponEnum;
import com.shuyun.kylin.customized.rpc.dto.IQiYiRequestDto;
import com.shuyun.kylin.customized.rpc.dto.IQiYiResponseDto;
import com.shuyun.kylin.customized.rpc.template.IQiYiRequestTemplate;
import com.shuyun.kylin.customized.rpc.template.McdRequestTemplate;
import com.shuyun.kylin.starter.exception.model.ThirdPartException;
import com.shuyun.motor.common.cons.PropsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

/**
 * 可口可乐优惠券服务
 */
@Slf4j
@Service
public class KoCouponServiceImpl implements KoCouponService {

    static final DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();

    private static final String LOYALTY_MEMBER_ACCOUNT_POINT_MODEL = PropsUtil.getSysOrEnv("loyalty.account.point");

    static final String COUPON_INSTANCE_MODEL = PropsUtil.getSysOrEnv("ko.coupon.instance.model");

    /**
     * 卡券项目列表内部接口地址
     */
    @Resource
    private OpenApiFeignClientV3 openApiFeignClientV3;

    /**
     * 优惠券权益内部接口地址
     */
    @Resource
    private BenefitClient benefitClient;

    @Resource
    private SendCouponService sendCouponService;

    /**
     * 卡券项目列表（可口可乐）
     *
     * @param offerProjectRequest
     * @return
     */
    @Override
    public CrmProjectResponse projectList(OfferProjectRequest offerProjectRequest) {
        CrmProjectResponse crmProjectResponse = new CrmProjectResponse();
        if(ObjectUtil.isEmpty(offerProjectRequest.getBrand())) {
            crmProjectResponse.setCode(ResponseCodeEnum.ILLEGAL_ARGUMENT.getCode());
            crmProjectResponse.setMsg("品牌不能为空");
            return crmProjectResponse;
        }
        if(ObjectUtil.isEmpty(offerProjectRequest.getChannelType())) {
            crmProjectResponse.setCode(ResponseCodeEnum.ILLEGAL_ARGUMENT.getCode());
            crmProjectResponse.setMsg("渠道类型不能为空");
            return crmProjectResponse;
        }
        crmProjectResponse = assembleProjectListData(offerProjectRequest);
        return crmProjectResponse;
    }

    /**
     * 构建项目列表数据
     * @param offerProjectRequest
     */
    private CrmProjectResponse assembleProjectListData(OfferProjectRequest offerProjectRequest) {
        CrmProjectResponse crmProjectResponse = new CrmProjectResponse();
        try {
            CouponProjectResponseDto couponProjectResponseDto = openApiFeignClientV3.getProjectList(offerProjectRequest);

            log.info("请求内部接口，method=[getProjectList]，couponProjectResponseDto={}", JSON.toJSONString(couponProjectResponseDto));
            if (ObjectUtil.isEmpty(couponProjectResponseDto)) {
                log.info("内部接口返回失败，couponProjectResponseDto is empty!");
                crmProjectResponse.setCode(ResponseCodeEnum.OPENAPI_FAILED.getCode());
                crmProjectResponse.setMsg(ResponseCodeEnum.OPENAPI_FAILED.getMsg());
                return crmProjectResponse;
            }

            String errorCode = couponProjectResponseDto.getErrorCode();
            if (ObjectUtil.isNotEmpty(errorCode)) {
                log.info("内部接口返回失败，errorCode={}, msg={}", errorCode, couponProjectResponseDto.getMsg());
                crmProjectResponse.setCode(errorCode);
                crmProjectResponse.setMsg(couponProjectResponseDto.getMsg());
                return crmProjectResponse;
            }

            crmProjectResponse.setCode(ResponseCodeEnum.SUCCESS.getCode());
            crmProjectResponse.setMsg(ResponseCodeEnum.SUCCESS.getMsg());
            CouponProjectResponseDto.RspData rspData = couponProjectResponseDto.getData();
            crmProjectResponse.setPage(rspData.getPage());
            crmProjectResponse.setPageSize(rspData.getPageSize());
            crmProjectResponse.setTotal(rspData.getTotal());
            if (ObjectUtil.isEmpty(rspData.getTotal()) || rspData.getTotal() == 0) {
                crmProjectResponse.setData(Collections.emptyList());
                return crmProjectResponse;
            }
            List<CouponProjectResponseDto.RspData.ProjectData> projectDataList = rspData.getData();
            List<ProjectDto> projectDtos = new ArrayList<>();
            for (CouponProjectResponseDto.RspData.ProjectData pd : projectDataList) {
                ProjectDto projectDto = new ProjectDto();
                projectDto.setMaxQuantity(pd.getMaxQuantity());
                // 分两种情况：作为虚拟权益/实物权益;
                String type = pd.getType();
                projectDto.setType(type);
                projectDto.setDescription(pd.getDescription());
                CouponProjectResponseDto.RspData.ProjectData.ExtData extData = pd.getExtData();
                if (ObjectUtil.isNotEmpty(extData)) {
                    projectDto.setProjectBusiness(extData.getProjectBusiness());
                    projectDto.setProjectGrantType(extData.getProjectGrantType());
                    projectDto.setCost(extData.getCost());
                    projectDto.setExternalProjectId(extData.getExternalProjectId());
                    projectDto.setVirtualType(extData.getVirtualType());
                    projectDto.setCouponType(extData.getCouponType());
                    projectDto.setIsPointExchange(extData.getIsPointExchange());
                    projectDto.setPoint(extData.getPoint());
                    projectDto.setAmount(extData.getAmount());
                }
                CouponProjectResponseDto.RspData.ProjectData.GrantRestrict grantRestrict = pd.getGrantRestrict();
                if (ObjectUtil.isNotEmpty(grantRestrict)) {
                    projectDto.setGrantPlatforms(grantRestrict.getPlatformsRef());
                }
                projectDto.setProjectId(pd.getId());

                projectDtos.add(projectDto);
            }
            crmProjectResponse.setData(projectDtos);
            return crmProjectResponse;
        } catch (Exception e) {
            log.error("券项目列表内部接口返回异常", e);
            crmProjectResponse.setCode(ResponseCodeEnum.OPENAPI_FAILED.getCode());
            crmProjectResponse.setMsg(ResponseCodeEnum.OPENAPI_FAILED.getMsg());
            return crmProjectResponse;
        }
    }

    /**
     * 发券
     * @param request
     * @return
     */
    @Override
    public GrantCouponResponse instanceGrant(GrantCouponRequest request) {
        GrantCouponResponse grantCouponResponse = new GrantCouponResponse();
        //根据originTransactionId 查询卡券是否发送成功
        GrantCouponResponse couponResponse = isIdempotent(request.getTransactionId());
        if(couponResponse != null){
            log.info("幂等性检查通过，返回之前的响应: {}", JSON.toJSONString(couponResponse));
            return couponResponse;
        }
        try {
            // 根据projectId查券项目信息
            String projectId = request.getProjectId();
            OfferProjectDetailClientResponse projectDetail = getCouponInfoByProjectId(projectId);
            // 1. 校验积分是否足够
            boolean checkPoint = checkPoint(request, projectDetail);
            if(!checkPoint) {
                grantCouponResponse.setCode(ResponseCodeEnum.POINT_NOT_ENOUGH.getCode());
                grantCouponResponse.setMsg(ResponseCodeEnum.POINT_NOT_ENOUGH.getMsg());
                grantCouponResponse.setStatus(GrantStatusEnum.FAIL.getCode());
                return grantCouponResponse;
            }
            if (null != request.getPoint() && null != projectDetail.getData().getExtData().getPoint()){
                if (!projectDetail.getData().getExtData().getPoint().equals(request.getPoint())){
                    grantCouponResponse.setCode(ResponseCodeEnum.EQUITY_POINT_FAILED.getCode());
                    grantCouponResponse.setMsg(ResponseCodeEnum.EQUITY_POINT_FAILED.getMsg());
                    grantCouponResponse.setStatus(GrantStatusEnum.FAIL.getCode());
                    return grantCouponResponse;
                }
            }
            // 校验券项目发券渠道与传入的渠道参数是否匹配
            OfferProjectDetailClientResponse.RspData.GrantRestrict grantRestrict = projectDetail.getData().getGrantRestrict();
            if(ObjectUtil.isEmpty(grantRestrict) || ObjectUtil.notEqual(grantRestrict.getPlatformsRef(), request.getChannelType())) {
                log.info("发券渠道限制不匹配，projectId={}，grantRestrict={}", projectId, JSON.toJSONString(grantRestrict));
                grantCouponResponse.setCode(ResponseCodeEnum.GRANT_CHANNEL_NOT_MATCH.getCode());
                grantCouponResponse.setMsg(ResponseCodeEnum.GRANT_CHANNEL_NOT_MATCH.getMsg());
                return grantCouponResponse;
            }
            // 2.发外部券
            String templateId = getTemplateIdByDb(request.getProjectId());
            ResponseResult<GrantCouponResponse> sendCouponResult = sendCouponService.doSendCoupon(templateId, request, projectDetail);
            log.info("doSendCoupon发券结果，sendCouponResult={}", JSON.toJSONString(sendCouponResult));
            grantCouponResponse.setCode(sendCouponResult.getCode());
            grantCouponResponse.setMsg(sendCouponResult.getMsg());
            if(ObjectUtil.notEqual(sendCouponResult.getCode(), ResponseCodeEnum.SUCCESS.getCode())) {
                log.info("外部发券失败，templateId={}, request={}", templateId, JSON.toJSONString(request));
                grantCouponResponse.setStatus(GrantStatusEnum.FAIL.getCode());
                return grantCouponResponse;
            }
            GrantCouponResponse data = sendCouponResult.getData();
            Optional.ofNullable(data).ifPresent(d -> grantCouponResponse.setCouponCode(d.getCouponCode()));
            Optional.ofNullable(data).ifPresent(d -> grantCouponResponse.setExternalCode(d.getExternalCode()));
            // 内部调核销接口
            innerCallIQiYi(templateId, data.getCouponCode());
            grantCouponResponse.setStatus(GrantStatusEnum.SUCCESS.getCode());
            return grantCouponResponse;
        } catch (Exception e) {
            log.error("发券异常", e);
            grantCouponResponse.setCode(ResponseCodeEnum.OPENAPI_FAILED.getCode());
            grantCouponResponse.setMsg(e.getMessage());
            if ("未获取到券项目模版ID".equals(e.getMessage())){
                grantCouponResponse.setCode(ResponseCodeEnum.EQUITY_PROJECT_FAILED.getCode());
                grantCouponResponse.setMsg(ResponseCodeEnum.EQUITY_PROJECT_FAILED.getMsg());
            }
            if ("未获取到券项目模版类型".equals(e.getMessage())){
                grantCouponResponse.setCode(ResponseCodeEnum.EQUITY_PROJECTTYPE_FAILED.getCode());
                grantCouponResponse.setMsg(ResponseCodeEnum.EQUITY_PROJECTTYPE_FAILED.getMsg());
            }
            grantCouponResponse.setStatus(GrantStatusEnum.FAIL.getCode());
            return grantCouponResponse;
        }
    }

    /**
     *
     * @param templateId
     * @param couponCode
     */
    public void innerCallIQiYi(String templateId, String couponCode) {
        if(ObjectUtil.isEmpty(templateId) || ObjectUtil.isEmpty(couponCode)) {
            log.info("innerCallIQiYi参数不全，templateId={}, couponCode={}", templateId, couponCode);
            return;
        }
        String templateType = sendCouponService.getTemplateTypeById(templateId);
        if(ObjectUtil.notEqual(templateType, SendCouponEnum.IQIYI.getCode())) {
            return;
        }
        log.info("innerCallIQiYi开始执行，templateId={}, couponCode={}", templateId, couponCode);
        try {
            String sql = String.format("select code,holder,grantPlatform,projectId,memberName,province,city,district,address,postalCode,recipientmobile from %s where code='%s'", COUPON_INSTANCE_MODEL, couponCode);
            BaseResponse<Map> execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
            log.info("innerCallIQiYi内部接口返回结果，execute={}", JSON.toJSONString(execute));
            List<Map> data = execute.getData();
            if(CollectionUtils.isEmpty(data)) {
                log.info("innerCallIQiYi内部接口返回结果为空，couponCode={}", couponCode);
                return;
            }
            Map map = data.get(0);
            InstanceUseRequest instanceUseRequest = assembleInstanceUseForInnerCall(map);
            instanceUse(instanceUseRequest);
        } catch (Exception e) {
            log.error("innerCallIQiYi内部接口异常", e);
        }
    }

    private GrantCouponResponse isIdempotent (String originTransactionId){

        try {
            String sql = String.format("select code,externalCode from %s where originTransactionId='%s'", COUPON_INSTANCE_MODEL, originTransactionId);
            BaseResponse<Map<String,String>> execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
            log.info("权益发放返回结果，execute={}", JSON.toJSONString(execute));
            List<Map<String,String>> data = execute.getData();
            if(CollectionUtils.isEmpty(data)) {
                log.info("权益发放返回结果为空:{}", originTransactionId);
                return null;
            }
            Map<String,String> map = data.get(0);
            GrantCouponResponse grantCouponResponse = new GrantCouponResponse();
            grantCouponResponse.setCode(ResponseCodeEnum.SUCCESS.getCode());
            grantCouponResponse.setMsg("发券成功");
            grantCouponResponse.setStatus(GrantStatusEnum.SUCCESS.getCode());
            grantCouponResponse.setCouponCode(map.get("code"));
            grantCouponResponse.setExternalCode(map.get("externalCode"));
            return grantCouponResponse;
        } catch (Exception e) {
            log.error("权益发放返回异常:{},:{}",originTransactionId, e);
            return null;
        }
    }

    private InstanceUseRequest assembleInstanceUseForInnerCall(Map map) {
        InstanceUseRequest request = new InstanceUseRequest();
        request.setBrand("KO");
        request.setChannelType(map.get("grantPlatform").toString());
        request.setTransactionId(UUID.randomUUID().toString().replace("-", ""));
        request.setCouponCodes(map.get("code").toString());
        request.setProjectId(map.get("projectId").toString());
        Optional.ofNullable(map.get("memberName")).ifPresent(memberName -> request.setMemberName(memberName.toString()));
        Optional.ofNullable(map.get("province")).ifPresent(province -> request.setProvince(province.toString()));
        Optional.ofNullable(map.get("city")).ifPresent(city -> request.setCity(city.toString()));
        Optional.ofNullable(map.get("district")).ifPresent(district -> request.setDistrict(district.toString()));
        Optional.ofNullable(map.get("address")).ifPresent(address -> request.setAddress(address.toString()));
        Optional.ofNullable(map.get("postalCode")).ifPresent(postalCode -> request.setPostalCode(postalCode.toString()));
        String mobile = fetchMemberPhone(map.get("holder").toString(), map.get("grantPlatform").toString());
        request.setRecipientmobile(mobile);
        return request;
    }

    /**
     * 校验会员积分是否充足
     *
     * @param request
     * @return true-充足；false-不足够
     */
    private boolean checkPoint(GrantCouponRequest request, OfferProjectDetailClientResponse projectDetail) {
        OfferProjectDetailClientResponse.RspData projectDetailData = projectDetail.getData();
        if(ObjectUtil.isEmpty(projectDetailData)) {
            log.info("内部接口返回失败，projectDetailData is empty!");
            throw new RuntimeException("调用内部积分查询失败");
        }
        OfferProjectDetailClientResponse.RspData.ExtData extData = projectDetailData.getExtData();
        if(ObjectUtil.isEmpty(extData)) {
            log.info("内部接口返回失败，extData is empty!");
            throw new RuntimeException("调用内部积分查询失败");
        }
        Boolean isPointExchange = extData.getIsPointExchange();
        if(Boolean.FALSE.equals(isPointExchange)) {
            // 不需要积分兑换，则直接返回true
           return true;
        }
        Integer couponNeedPoint = extData.getPoint();
        String sql = String.format("select memberId,point from data.loyalty.member.account.Point%s where memberId = '%s'", LOYALTY_MEMBER_ACCOUNT_POINT_MODEL, request.getMemberGrantIdentify());
        BaseResponse<Map> execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
        List<Map> data = execute.getData();
        log.info("查询会员积分原始结果，data={}", JSON.toJSONString(data));
        if (CollectionUtils.isEmpty(data)) {
            log.info("未查询到数据库积分结果，execute is empty!");
            throw new RuntimeException("内部积分查询失败");
        }
        BigDecimal leftPoint = (BigDecimal) data.get(0).get("point");
        return leftPoint.compareTo(new BigDecimal(couponNeedPoint)) >= 0;
    }

    private OfferProjectDetailClientResponse getCouponInfoByProjectId(String projectId) {
        OfferProjectDetailClientRequest offerProjectDetailClientRequest = new OfferProjectDetailClientRequest();
        offerProjectDetailClientRequest.setProjectId(projectId);
        try {
            log.info("getProjectDetail内部接口原始入参，request={}", JSON.toJSONString(offerProjectDetailClientRequest));
            OfferProjectDetailClientResponse projectDetail = openApiFeignClientV3.getProjectDetail(offerProjectDetailClientRequest);
            log.info("getProjectDetail内部接口返回结果，projectDetail={}", JSON.toJSONString(projectDetail));
            if(ObjectUtil.isEmpty(projectDetail)) {
                log.info("内部接口返回失败，projectDetail is empty!");
                throw new RuntimeException(ResponseCodeEnum.EQUITY_PROJECT_FAILED.getMsg());
            }
            return projectDetail;
        } catch (Exception e) {
            log.error("内部接口异常", e);
            throw new RuntimeException(ResponseCodeEnum.EQUITY_PROJECT_FAILED.getMsg());
        }
    }

    private String getTemplateIdByDb(String projectId) {
        String sql = String.format("select id,template from data.offer.v3.Project where id = '%s'", projectId);
        BaseResponse<Map> execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
        List<Map> data = execute.getData();
        log.info("查询券模板id原始结果，data={}", JSON.toJSONString(data));
        if(CollectionUtils.isEmpty(data)) {
            log.info("查询券模板id不存在，projectId={}", projectId);
            throw new RuntimeException(ResponseCodeEnum.EQUITY_PROJECTTYPE_FAILED.getMsg());
        }
        return  (String)data.get(0).get("template");
    }

    /**
     * 券实例列表
     * @param request
     * @return
     */
    @Override
    public InstanceListResponse instanceList(OfferProjectRequest request) {
        InstanceListResponse responseResult = new InstanceListResponse();
        if(ObjectUtil.isEmpty(request.getIdentify())) {
            responseResult.setCode(ResponseCodeEnum.ILLEGAL_ARGUMENT.getCode());
            responseResult.setMsg("会员Id不能为空");
            return responseResult;
        }
        responseResult = assembleInstanceList(request);
        return responseResult;
    }

    @Override
    public ResponseResult<ProjectBalance> getBalance(String projectId) {
        ResponseResult<ProjectBalance> responseResult = new ResponseResult<>();
        try {
            ProjectCountResponse projectCountResponse = benefitClient.internalCount(projectId);
            log.info("内部接口查询结果，projectCountResponse={}", JSON.toJSONString(projectCountResponse));
            if(ObjectUtil.isEmpty(projectCountResponse)) {
                log.error("projectCountResponse is empty!");
                responseResult.setCode(ResponseCodeEnum.OPENAPI_FAILED.getCode());
                responseResult.setMsg(ResponseCodeEnum.OPENAPI_FAILED.getMsg());
                return responseResult;
            }
            ProjectCountResponse.ProjectCountBasicResponse projectCountBasicResponse = projectCountResponse.getProjectCountBasicResponse();
            if(ObjectUtil.isEmpty(projectCountBasicResponse)) {
                log.error("projectCountBasicResponse is empty!");
                responseResult.setCode(ResponseCodeEnum.OPENAPI_FAILED.getCode());
                responseResult.setMsg(ResponseCodeEnum.OPENAPI_FAILED.getMsg());
                return responseResult;
            }
            responseResult.setCode(ResponseCodeEnum.SUCCESS.getCode());
            responseResult.setMsg(ResponseCodeEnum.SUCCESS.getMsg());
            int balanceSum = projectCountBasicResponse.getInstanceCountLimit() - projectCountBasicResponse.getGrantedCount();
            ProjectBalance projectBalance = new ProjectBalance();
            projectBalance.setBalanceSum(balanceSum);
            responseResult.setData(projectBalance);
            return responseResult;
        } catch (Exception e) {
            log.error("内部接口查询异常", e);
            responseResult.setCode(ResponseCodeEnum.OPENAPI_FAILED.getCode());
            responseResult.setMsg(ResponseCodeEnum.OPENAPI_FAILED.getMsg());
            return responseResult;
        }
    }

    /**
     *
     * @param request
     * @return
     */
    @Override
    public InstanceUseResponse instanceUse(InstanceUseRequest request) {
        InstanceUseResponse instanceUseResponse = new InstanceUseResponse();
        // CRM券码和三方券码不能同时为空
        if(ObjectUtil.isAllEmpty(request.getCouponCodes(), request.getExternalCode())) {
            log.info("券核销参数异常，couponCodes={}, externalCode={}", request.getCouponCodes(), request.getExternalCode());
            instanceUseResponse.setCode(ResponseCodeEnum.ILLEGAL_ARGUMENT.getCode());
            instanceUseResponse.setMsg("CRM券码和外部券码不能同时为空");
            instanceUseResponse.setStatus(CouponUseStatusEnum.FAIL.getCode());
            return instanceUseResponse;
        }
        if(ObjectUtil.isNotEmpty(request.getExternalCode()) && ObjectUtil.isEmpty(request.getExternalProjectId())) {
            log.info("券核销参数异常，externalCode={}, externalProjectId={}", request.getExternalCode(), request.getExternalProjectId());
            instanceUseResponse.setCode(ResponseCodeEnum.ILLEGAL_ARGUMENT.getCode());
            instanceUseResponse.setMsg("三方券项目id不能为空");
            instanceUseResponse.setStatus(CouponUseStatusEnum.FAIL.getCode());
            return instanceUseResponse;
        }
        // 如果传了crm券码，说明三方知道crm信息，则需要保证接口其他必填参数都不为空
        if(ObjectUtil.isNotEmpty(request.getCouponCodes())
                && !ObjectUtil.isAllNotEmpty(request.getBrand(), request.getChannelType(), request.getProjectId(), request.getTransactionId())) {
            log.info("券核销参数异常，brand={}, channelType={}, projectId={}, transactionId={}", request.getBrand(), request.getChannelType(), request.getProjectId(), request.getTransactionId());
            instanceUseResponse.setCode(ResponseCodeEnum.ILLEGAL_ARGUMENT.getCode());
            instanceUseResponse.setMsg("品牌/渠道类型/项目ID/transactionId为空");
            instanceUseResponse.setStatus(CouponUseStatusEnum.FAIL.getCode());
            return instanceUseResponse;
        }
        // 如果三方券码不为空，通过三方券码获取核销必要信息
        if(ObjectUtil.isNotEmpty(request.getExternalCode()) && ObjectUtil.isEmpty(request.getCouponCodes())) {
            try {
                assembleInstanceUseDataByExternalCode(request);
                log.info("wx券核销参数组装成功，request={}", JSON.toJSONString(request));
            } catch (Exception e) {
                log.error("券核销参数组装异常，externalCode={}", request.getExternalCode(), e);
                instanceUseResponse.setCode(ResponseCodeEnum.ILLEGAL_ARGUMENT.getCode());
                instanceUseResponse.setMsg(e.getMessage());
                instanceUseResponse.setStatus(CouponUseStatusEnum.FAIL.getCode());
                return instanceUseResponse;
            }
        }
        String sql = String.format("select id,code,projectId,grantPlatform,businessId,templateId,holder from %s where code = '%s'", COUPON_INSTANCE_MODEL, request.getCouponCodes());
        log.info("instanceUse-sql查询原始sql={}", sql);
        BaseResponse execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
        List<Map> data = execute.getData();
        log.info("instanceUse-sql查询原始结果，data={}", JSON.toJSONString(data));
        if(CollectionUtils.isEmpty(data)) {
            log.info("券实例不存在，couponCode={}", request.getCouponCodes());
            instanceUseResponse.setCode(ResponseCodeEnum.USE_COUPON_FAILED.getCode());
            instanceUseResponse.setMsg("券实例不存在");
            instanceUseResponse.setStatus(CouponUseStatusEnum.FAIL.getCode());
            return instanceUseResponse;
        }
        Map map = data.get(0);
        String templateType = sendCouponService.getTemplateTypeById(String.valueOf(map.get("templateId")));
        if(ObjectUtil.equal(templateType, SendCouponEnum.IQIYI.getCode())) {
//            if(ObjectUtil.isEmpty(request.getRecipientmobile())) {
//                instanceUseResponse.setCode(ResponseCodeEnum.USE_COUPON_FAILED.getCode());
//                instanceUseResponse.setMsg("收货手机号不能为空");
//                instanceUseResponse.setStatus(CouponUseStatusEnum.FAIL.getCode());
//                return instanceUseResponse;
//            }
            OfferProjectDetailClientRequest projectDetailRequest = new OfferProjectDetailClientRequest();
            projectDetailRequest.setProjectId(request.getProjectId());
            try {
                log.info("getProjectDetail内部接口原始入参，request={}", JSON.toJSONString(projectDetailRequest));
                OfferProjectDetailClientResponse projectDetail = openApiFeignClientV3.getProjectDetail(projectDetailRequest);
                if(ObjectUtil.isEmpty(projectDetail)) {
                    log.error("内部接口查询失败，projectDetail is empty!");
                    instanceUseResponse.setCode(ResponseCodeEnum.USE_COUPON_FAILED.getCode());
                    instanceUseResponse.setMsg("券项目查询空");
                    instanceUseResponse.setStatus(CouponUseStatusEnum.FAIL.getCode());
                    return instanceUseResponse;
                }
                OfferProjectDetailClientResponse.RspData projectData = projectDetail.getData();
                if(ObjectUtil.isEmpty(projectData)) {
                    log.error("内部接口查询失败，projectData is empty!");
                    instanceUseResponse.setCode(ResponseCodeEnum.USE_COUPON_FAILED.getCode());
                    instanceUseResponse.setMsg("券项目查询空");
                    instanceUseResponse.setStatus(CouponUseStatusEnum.FAIL.getCode());
                    return instanceUseResponse;
                }
                OfferProjectDetailClientResponse.RspData.ExtData projectExtData = projectData.getExtData();
                if(ObjectUtil.isEmpty(projectExtData)) {
                    log.error("内部接口查询失败，projectExtData is empty!");
                    instanceUseResponse.setCode(ResponseCodeEnum.USE_COUPON_FAILED.getCode());
                    instanceUseResponse.setMsg("券项目查询空");
                    instanceUseResponse.setStatus(CouponUseStatusEnum.FAIL.getCode());
                    return instanceUseResponse;
                }
                if(ObjectUtil.isEmpty(projectExtData.getExternalProjectId())) {
                    log.error("内部接口查询失败，externalProjectId is empty!");
                    instanceUseResponse.setCode(ResponseCodeEnum.USE_COUPON_FAILED.getCode());
                    instanceUseResponse.setMsg("外部券项目id空");
                    instanceUseResponse.setStatus(CouponUseStatusEnum.FAIL.getCode());
                    return instanceUseResponse;
                }
                IQiYiResponseDto iQiYiResponseDto = callIQiYiApi(projectExtData.getExternalProjectId(), map.get("holder").toString(), map, request.getChannelType());
                if(ObjectUtil.isNull(iQiYiResponseDto)) {
                    log.error("爱奇艺内部接口返回失败，iQiYiResponseDto is empty!");
                    instanceUseResponse.setCode(ResponseCodeEnum.USE_COUPON_FAILED.getCode());
                    instanceUseResponse.setMsg("调用爱奇艺返回失败");
                    instanceUseResponse.setStatus(CouponUseStatusEnum.FAIL.getCode());
                    return instanceUseResponse;
                }
                if(ObjectUtil.isNotEmpty(iQiYiResponseDto.getErrMsg())) {
                    log.error("爱奇艺内部接口返回失败，errMsg={}", iQiYiResponseDto.getErrMsg());
                    instanceUseResponse.setCode(ResponseCodeEnum.USE_COUPON_FAILED.getCode());
                    instanceUseResponse.setMsg("调用爱奇艺返回失败");
                    instanceUseResponse.setStatus(CouponUseStatusEnum.FAIL.getCode());
                    return instanceUseResponse;
                }
                if(ObjectUtil.notEqual(iQiYiResponseDto.getGameState(), Constants.ZERO_STR)) {
                    log.error("爱奇艺内部接口返回失败，gameState={}", iQiYiResponseDto.getGameState());
                    instanceUseResponse.setCode(ResponseCodeEnum.USE_COUPON_FAILED.getCode());
                    instanceUseResponse.setMsg("调用爱奇艺返回失败");
                    instanceUseResponse.setStatus(CouponUseStatusEnum.FAIL.getCode());
                    return instanceUseResponse;
                }
                // 如果成功，更新三方券码编号
                String id = (String)map.get("id");
                if(ObjectUtil.isNotEmpty(id)) {
                    Map<String, Object> updateMap = new HashMap<>();
                    updateMap.put("externalCode", iQiYiResponseDto.getOrderId());
                    updateMap.put("id", id);
                    dataapiHttpSdk.update(COUPON_INSTANCE_MODEL, id, updateMap, false);
                }
            } catch (Exception e) {
                log.error("爱奇艺流程异常", e);
                instanceUseResponse.setCode(ResponseCodeEnum.USE_COUPON_FAILED.getCode());
                instanceUseResponse.setMsg(ResponseCodeEnum.USE_COUPON_FAILED.getMsg());
                instanceUseResponse.setStatus(CouponUseStatusEnum.FAIL.getCode());
                return instanceUseResponse;
            }
        }
        OfferInstanceUseClientRequestDto offerInstanceUseClientRequestDto = assembleInstanceUse(request);
        try {
            log.info("券核销原始参数，offerInstanceUseClientRequestDto={}", JSON.toJSONString(offerInstanceUseClientRequestDto));
            ClientCommonResponseDto<OfferInstanceUseClientResponseDto> clientCommonResponseDto = openApiFeignClientV3.instanceUse(offerInstanceUseClientRequestDto);
            log.info("券核销原始结果，clientCommonResponseDto={}", JSON.toJSONString(clientCommonResponseDto));
            if(ObjectUtil.isNotEmpty(clientCommonResponseDto.getErrorCode())) {
                instanceUseResponse.setCode(clientCommonResponseDto.getErrorCode());
                instanceUseResponse.setMsg(clientCommonResponseDto.getMsg());
                instanceUseResponse.setStatus(CouponUseStatusEnum.FAIL.getCode());
                return instanceUseResponse;
            }
            instanceUseResponse.setCode(ResponseCodeEnum.SUCCESS.getCode());
            instanceUseResponse.setStatus(clientCommonResponseDto.getData().getStatus());
            instanceUseResponse.setMsg(clientCommonResponseDto.getMsg());
            return instanceUseResponse;
        } catch (ThirdPartException e) {
            log.error("券核销异常-ThirdPartException", e);
            // 保存爱奇艺失败记录，以便重新调用
            saveIQiYiFailureLog(templateType, request.getTransactionId(), offerInstanceUseClientRequestDto);
            instanceUseResponse.setCode(ResponseCodeEnum.USE_COUPON_FAILED.getCode());
            instanceUseResponse.setMsg(e.getMessage());
            instanceUseResponse.setStatus(CouponUseStatusEnum.FAIL.getCode());
            return instanceUseResponse;
        } catch (Exception e) {
            // 保存爱奇艺失败记录，以便重新调用
            saveIQiYiFailureLog(templateType, request.getTransactionId(), offerInstanceUseClientRequestDto);
            instanceUseResponse.setCode(ResponseCodeEnum.USE_COUPON_FAILED.getCode());
            instanceUseResponse.setMsg(ResponseCodeEnum.USE_COUPON_FAILED.getMsg());
            instanceUseResponse.setStatus(CouponUseStatusEnum.FAIL.getCode());
            log.error("券核销异常-exception", e);
            return instanceUseResponse;
        }
    }

    /**
     * 根据三方券id和三方券码组装核销参数
     * @param request
     */
    private void assembleInstanceUseDataByExternalCode(InstanceUseRequest request) {
        String sql = String.format("select code,holder,grantPlatform,projectId,memberName,province,city,district,address,postalCode,recipientmobile from %s where externalCode='%s' and externalProjectId='%s'", COUPON_INSTANCE_MODEL, request.getExternalCode(), request.getExternalProjectId());
        log.info("根据externalProjectId和三方券项目id查询券项目，sql={}", sql);
        BaseResponse<Map> execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
        log.info("根据externalProjectId和三方券项目id查询券项目原始结果，data={}", JSON.toJSONString(execute));
        List<Map> data = execute.getData();
        if(CollectionUtils.isEmpty(data)) {
            log.info("根据projectId和三方券项目id查询券项目不存在，externalCode={}, externalProjectId={}", request.getExternalCode(), request.getExternalProjectId());
            throw new RuntimeException("券实例不存在");
        }
        Map map = data.get(0);
        // 组装发券请求参数
        InstanceUseRequest instanceUseRequest = assembleInstanceUseForInnerCall(map);
        CopyOptions copyOptions = new CopyOptions();
        copyOptions.setIgnoreNullValue(true);
        BeanUtil.copyProperties(instanceUseRequest, request, copyOptions);
    }

    /**
     * 请求爱奇艺接口
     */
    private static IQiYiResponseDto callIQiYiApi(String externalProjectId,String memberId, Map instanceMap, String channelType) {
        IQiYiRequestDto iQiYiRequestDto
                = IQiYiSendCouponStrategy.assembleRequestParams(externalProjectId, fetchMemberPhone(memberId, channelType), String.format("%s_%s", instanceMap.get("projectId"), instanceMap.get("code")));
        return IQiYiRequestTemplate.sendCoupon(iQiYiRequestDto, String.format("%s_%s", instanceMap.get("projectId"), instanceMap.get("code")));
    }

    private static String fetchMemberPhone(String memberId, String channelType) {
        String sql = String.format("select id,memberId,appId,openId,channelType,memberType,mobile from data.prctvmkt.KO.MemberBinding where memberId='%s' and channelType='%s'", memberId, channelType);
        log.info("获取会员绑定关系原始sql={}", sql);
        BaseResponse<Map> execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
        log.info("获取会员绑定关系原始结果，data={}", JSON.toJSONString(execute));
        List<Map> data = execute.getData();
        if (CollectionUtils.isEmpty(data)) {
            log.info("未查询到数据库会员绑定关系结果，execute is empty!");
            throw new RuntimeException("会员渠道绑定信息不存在");
        }
        return data.get(0).get("mobile").toString();
    }

    private OfferInstanceUseClientRequestDto assembleInstanceUse(InstanceUseRequest request) {
        OfferInstanceUseClientRequestDto offerInstanceUseClientRequestDto = new OfferInstanceUseClientRequestDto();
        offerInstanceUseClientRequestDto.setBrand(request.getBrand());
        offerInstanceUseClientRequestDto.setChannelType(request.getChannelType());
        offerInstanceUseClientRequestDto.setTransactionId(request.getTransactionId());
        offerInstanceUseClientRequestDto.setCouponCodes(Collections.singletonList(request.getCouponCodes()));
        OfferInstanceUseClientRequestDto.Extension extension = getInstanceUseExtension(request);
        offerInstanceUseClientRequestDto.setExtension(extension);
        return offerInstanceUseClientRequestDto;
    }

    /**
     * 保存爱奇艺失败记录，用作重新调用
     *
     * @param templateType
     * @param transactionId
     * @param instanceUseClientRequestDto
     */
    public void saveIQiYiFailureLog(String templateType, String transactionId, OfferInstanceUseClientRequestDto instanceUseClientRequestDto) {
        if(ObjectUtil.notEqual(templateType, SendCouponEnum.IQIYI.getCode())) {
            log.info("非爱奇艺流程，不保存请求db记录");
            return;
        }
        try {
            InnerGrantCouponFailureLog innerGrantCouponFailureLog = new InnerGrantCouponFailureLog();
            innerGrantCouponFailureLog.setTransactionId(transactionId);
            innerGrantCouponFailureLog.setRequestParams(JSON.toJSONString(instanceUseClientRequestDto));
            innerGrantCouponFailureLog.setFailureType(InnerGrantFailureType.IQIYI_FAILURE.getCode());
            // 因为爱奇艺是在核销时处理逻辑，不关注积分，只关注核销状态，所以积分为否
            innerGrantCouponFailureLog.setIsPointExchange(IsDeductPointEnum.NO.getCode());
            String dataStr = DateHelper.formatZonedDateTime(DateUtil.date(), DateHelper.DATE_FORMAT_T);
            innerGrantCouponFailureLog.setCreateTime(dataStr);
            innerGrantCouponFailureLog.setUpdateTime(dataStr);
            log.info("saveIQiYiFailureLog ====> save inner grant coupon failure log params:{}", JSON.toJSONString(innerGrantCouponFailureLog));
            Map<String, Object> failureLogMapForPoint = BeanUtil.beanToMap(innerGrantCouponFailureLog, false, true);
            dataapiHttpSdk.insert("data.prctvmkt.KO.innerGrantCouponFailureLog", failureLogMapForPoint, false, true);
        } catch (Exception ex) {
            log.error("saveIQiYiFailureLog ====> save inner grant coupon failure log error !,msg:", ex);
        }
    }

    /**
     * 核销接口扩展字段封装
     * @param request
     * @return
     */
    private OfferInstanceUseClientRequestDto.Extension getInstanceUseExtension(InstanceUseRequest request) {
        OfferInstanceUseClientRequestDto.Extension extension = new OfferInstanceUseClientRequestDto.Extension();
        extension.setMemberName(request.getMemberName());
        extension.setProvince(request.getProvince());
        extension.setCity(request.getCity());
        extension.setDistrict(request.getDistrict());
        extension.setAddress(request.getAddress());
        extension.setPostalCode(request.getPostalCode());
        extension.setRecipientmobile(request.getRecipientmobile());
        matchLbsCode(extension, request);
        return extension;
    }

    private void matchLbsCode(OfferInstanceUseClientRequestDto.Extension extension, InstanceUseRequest request) {
        log.info("匹配成本中心code-use，extension={}", JSON.toJSONString(extension));
        OfferProjectDetailClientResponse projectDetailClientResponse = getCouponInfoByProjectId(request.getProjectId());
        log.info("成本中心匹配-use-查询项目详情，projectDetailClientResponse={}", JSON.toJSONString(projectDetailClientResponse));
        if(ObjectUtil.isEmpty(projectDetailClientResponse)) {
            log.info("成本中心匹配-use-查询项目详情，projectDetailClientResponse is empty");
            throw new RuntimeException("券项目信息查询失败");
        }
        OfferProjectDetailClientResponse.RspData rspData = projectDetailClientResponse.getData();
        if(ObjectUtil.isEmpty(rspData)) {
            log.info("成本中心匹配-use-查询项目详情，rspData is empty");
            throw new RuntimeException("券项目信息查询失败");
        }
        OfferProjectDetailClientResponse.RspData.ExtData extData = rspData.getExtData();
        if(ObjectUtil.isEmpty(extData)) {
            log.info("成本中心匹配-use-查询项目详情，extData is empty");
            throw new RuntimeException("券项目信息查询失败");
        }
        // 固定逻辑
        if(!extData.getIsLBSusedCostCenterCode() || ObjectUtil.isEmpty(request.getUsedLBSCity())) {
            String usedCostCenterCodeJSONStr = extData.getUsedCostCenterCode();
            List<String> usedCostCenterCodeList = JSON.parseArray(usedCostCenterCodeJSONStr, String.class);
            log.info("成本中心usedCostCenterCodeList={}", usedCostCenterCodeList);
            String usedCostCenterCode = usedCostCenterCodeList.get(0);
            extension.setUsedCostCenterCode(usedCostCenterCode);
            // 取固定成本中心字段
            String sql = String.format("select costCenterCode,bottlerFactoryName,city,district from data.prctvmkt.KO.LbsOuMapping where costCenterCode = '%s' limit 1",
                    usedCostCenterCode);
            log.info("固定-use-匹配成本中心原始sql={}", sql);
            BaseResponse<Map> execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
            List<Map> data = execute.getData();
            log.info("固定-use-查询成本中心省市区匹配结果，data={}", JSON.toJSONString(data));
            if(!CollectionUtils.isEmpty(data)) {
                extension.setUsedLBSProvince((String)data.get(0).get("bottlerFactoryName"));
                extension.setUsedLBSCity((String)data.get(0).get("city"));
                extension.setUsedLBSDistrict((String)data.get(0).get("district"));
            }
            return;
        }
        // 否则走转化逻辑，根据city查询库里的成本中心code
        extension.setUsedLBSProvince(request.getUsedLBSProvince());
        extension.setUsedLBSCity(request.getUsedLBSCity());
        extension.setUsedLBSDistrict(request.getUsedLBSDistrict());
        String sql = String.format("select costCenterCode from data.prctvmkt.KO.LbsOuMapping where bottlerFactoryName = '%s' and city = '%s' and district = '%s' limit 1",
                extension.getUsedLBSProvince(),extension.getUsedLBSCity(),extension.getUsedLBSDistrict());
        log.info("匹配成本中心原始sql-use={}", sql);
        BaseResponse<Map> execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
        List<Map> data = execute.getData();
        log.info("查询成本中心省市区匹配结果-use，data={}", JSON.toJSONString(data));
        if(CollectionUtils.isEmpty(data)) {
            String usedCostCenterCodeJSONStr = extData.getUsedCostCenterCode();
            List<String> usedCostCenterCodeList = JSON.parseArray(usedCostCenterCodeJSONStr, String.class);
            log.info("成本中心usedCostCenterCodeList={}", usedCostCenterCodeList);
            String usedCostCenterCode = usedCostCenterCodeList.get(0);
            extension.setUsedCostCenterCode(usedCostCenterCode);
            return;
        }
        String costCenterCode = (String)data.get(0).get("costCenterCode");
        extension.setUsedCostCenterCode(costCenterCode);
    }

    private InstanceListResponse assembleInstanceList(OfferProjectRequest offerProjectRequest) {
        handleDefaultRequestParams(offerProjectRequest);
        InstanceListResponse instanceListResponse = new InstanceListResponse();
        try {
            OfferProjectClientRequest offerProjectClientRequest = new OfferProjectClientRequest();
            BeanUtil.copyProperties(offerProjectRequest, offerProjectClientRequest, "identify");
            OfferProjectClientRequest.Identify identify = new OfferProjectClientRequest.Identify();
            identify.setMemberId(offerProjectRequest.getIdentify());
            offerProjectClientRequest.setIdentify(identify);
            if (StringUtils.isNotEmpty(offerProjectRequest.getCampaignId())){
                HashMap<String, String> map = new HashMap<>();
                map.put("EQ",offerProjectRequest.getCampaignId());
                HashMap<String, Object> queryParamsMap = new HashMap<>();
                queryParamsMap.put("campaignId",map);
                offerProjectClientRequest.setQueryParams(JSON.toJSONString(queryParamsMap));
            }
            log.info("券实例列表查询，request={}", JSON.toJSONString(offerProjectClientRequest));
            CouponInstanceResponseDto couponInstanceResponseDto = openApiFeignClientV3.getInstanceList(offerProjectClientRequest);
            log.info("内部券实例列表查询结果，couponInstanceResponseDto={}", JSON.toJSONString(couponInstanceResponseDto));
            if(ObjectUtil.isEmpty(couponInstanceResponseDto)) {
                log.info("内部接口返回异常，couponInstanceResponseDto is empty!");
                instanceListResponse.setCode(ResponseCodeEnum.OPENAPI_FAILED.getCode());
                instanceListResponse.setMsg(ResponseCodeEnum.OPENAPI_FAILED.getMsg());
                return instanceListResponse;
            }
            String errorCode = couponInstanceResponseDto.getErrorCode();
            if(ObjectUtil.isNotEmpty(errorCode)) {
                log.info("内部接口返回异常，errorCode={}, msg={}", errorCode, couponInstanceResponseDto.getMsg());
                instanceListResponse.setCode(couponInstanceResponseDto.getErrorCode());
                instanceListResponse.setMsg(couponInstanceResponseDto.getMsg());
                return instanceListResponse;
            }
            instanceListResponse.setCode(ResponseCodeEnum.SUCCESS.getCode());
            instanceListResponse.setMsg(ResponseCodeEnum.SUCCESS.getMsg());
            CouponInstanceResponseDto.RspData data = couponInstanceResponseDto.getData();
            if(ObjectUtil.isEmpty(data)) {
                log.info("内部接口返回结束，data is empty");
                instanceListResponse.setData(Collections.emptyList());
                return instanceListResponse;
            }
            List<CouponInstanceResponseDto.RspData.CardList> cardList = data.getCardList();
            if(CollectionUtils.isEmpty(cardList)) {
                log.info("内部接口返回结束，cardList is empty");
                instanceListResponse.setData(Collections.emptyList());
                return instanceListResponse;
            }
            List<InstanceListResponse.Instance> instanceList = new ArrayList<>();
            for(CouponInstanceResponseDto.RspData.CardList card : cardList) {
                InstanceListResponse.Instance instanceResponse = new InstanceListResponse.Instance();
                instanceResponse.setId(card.getId());
                instanceResponse.setProjectId(card.getProjectId());
                instanceResponse.setStatus(card.getStatus());
                instanceResponse.setCouponCode(card.getCouponCode());
                instanceResponse.setEffectiveAt(card.getEffectiveAt());
                instanceResponse.setExpiredAt(card.getExpiredAt());
                InstanceListResponse.Instance.ExtData extData = new InstanceListResponse.Instance.ExtData();
                extData.setGrantAt(card.getGrantAt());
                extData.setUseAt(card.getUseAt());
                CouponInstanceResponseDto.RspData.CardList.Extension cardExtension = card.getExtension();
                Optional.ofNullable(cardExtension).ifPresent(ext -> instanceResponse.setExternalCode(ext.getExternalCode()));
                Optional.ofNullable(cardExtension).ifPresent(ext -> extData.setCampaignId(ext.getCampaignId()));
                Optional.ofNullable(cardExtension).ifPresent(ext -> extData.setCampaignName(ext.getCampaignName()));
                Optional.ofNullable(cardExtension).ifPresent(ext -> extData.setRedeemType(ext.getRedeemType()));
                Optional.ofNullable(cardExtension).ifPresent(ext -> extData.setJumpParameter(ext.getJumpParameter()));
                CouponInstanceResponseDto.RspData.CardList.Project project = card.getProject();
                if(ObjectUtil.isNotEmpty(project)) {
                    extData.setType(project.getType());
                    extData.setTitle(project.getTitle());
                    CouponInstanceResponseDto.RspData.CardList.Project.ExtensionProject projectExtData = project.getExtData();
                    log.info("projectExtData={}", JSON.toJSONString(projectExtData));
                    if(ObjectUtil.isNotEmpty(projectExtData)) {
                        extData.setVirtualType(projectExtData.getVirtualType());
                        extData.setProjectBusiness(projectExtData.getProjectBusiness());
                    }
                }
                instanceResponse.setExtData(extData);

                instanceList.add(instanceResponse);
            }
            instanceListResponse.setData(instanceList);
            instanceListResponse.setTotal(data.getTotal());
            instanceListResponse.setPage(data.getPage());
            instanceListResponse.setPageSize(data.getPageSize());
            return instanceListResponse;
        } catch (Exception e) {
            log.error("券实例列表内部接口异常", e);
            instanceListResponse.setCode(ResponseCodeEnum.OPENAPI_FAILED.getCode());
            instanceListResponse.setMsg(ResponseCodeEnum.OPENAPI_FAILED.getMsg());
            return instanceListResponse;
        }
    }

    private void handleDefaultRequestParams(OfferProjectRequest request) {
        if(ObjectUtil.isNotEmpty(request.getShowProject())) {
            return;
        }
        log.info("默认展示项目信息");
        request.setShowProject(Boolean.TRUE);
    }

    /**
     *
     * @param requestDto
     * @param request
     * @return
     */
    @Override
    public ResponseResult<String> notifyForCouponUse(McdCallbackRequestDto requestDto, HttpServletRequest request) {
        // 保存回调日志
        sendCouponService.saveCallbackLog(SendCouponEnum.MCD.getCode(), String.format("%s_%s", requestDto.getCouponId(),requestDto.getCouponCode()), JSON.toJSONString(requestDto));
        if(!ObjectUtil.isAllNotEmpty(requestDto.getCouponId(), requestDto.getCouponCode())) {
            log.info("mcd回调参数异常，requestDto={}", JSON.toJSONString(requestDto));
            return new ResponseResult<>(ResponseCodeEnum.ILLEGAL_ARGUMENT);
        }
        if(!mcdCallbackSignValid(requestDto, request)) {
            log.info("mcd回调签名校验失败，requestDto={}", JSON.toJSONString(requestDto));
            new ResponseResult<>(ResponseCodeEnum.SIGN_VALID_FAILED);
        }
        // 券核销参数组装
        try {
            InstanceUseRequest instanceUseRequest = assembleMcdCallbackCouponUseParam(requestDto);
            // 调用内部核销接口
            InstanceUseResponse instanceUseResponse = instanceUse(instanceUseRequest);
            log.info("mcd券核销内部调用返回，instanceUseResponse={}", JSON.toJSONString(instanceUseResponse));
            if(ObjectUtil.notEqual(ResponseCodeEnum.SUCCESS.getCode(), instanceUseResponse.getCode())) {
                log.info("mcd券核销内部调用失败，instanceUseResponse={}", JSON.toJSONString(instanceUseResponse));
                return new ResponseResult<>(ResponseCodeEnum.USE_COUPON_FAILED);
            }
            return new ResponseResult<>(ResponseCodeEnum.SUCCESS);
        } catch (Exception e) {
            log.error("mcd券核销异常，requestDto={}", JSON.toJSONString(requestDto), e);
            return new ResponseResult<>(ResponseCodeEnum.USE_COUPON_FAILED.getCode(),e.getMessage());
        }
    }

    private InstanceUseRequest assembleMcdCallbackCouponUseParam(McdCallbackRequestDto requestDto) {
        String sql = String.format("select code,holder,grantPlatform,projectId,memberName,province,city,district,address,postalCode,recipientmobile from %s where externalCode='%s' and externalProjectId='%s'",
                COUPON_INSTANCE_MODEL, requestDto.getCouponCode(), requestDto.getCouponId());
        log.info("mcd根据三方券项目id和三方券编号查询券项实例，sql={}", sql);
        BaseResponse<Map> execute = dataapiHttpSdk.execute(sql, Collections.emptyMap());
        log.info("mcd根据三方券项目id和三方券编号查询券项目原始结果，data={}", JSON.toJSONString(execute));
        List<Map> data = execute.getData();
        if(CollectionUtils.isEmpty(data)) {
            log.info("mcd-根据三方券项目id和三方券编号查询券项实例不存在，externalCode={}, externalProjectId={}", requestDto.getCouponCode(), requestDto.getCouponId());
            throw new RuntimeException("券实例不存在");
        }
        Map map = data.get(0);
        // 组装发券请求参数
        return assembleInstanceUseForInnerCall(map);
    }

    public boolean mcdCallbackSignValid(McdCallbackRequestDto requestDto, HttpServletRequest request) {
        String sign = getMcdCallbackSign(requestDto);
        return ObjectUtil.equal(sign, request.getHeader("Sign"));
    }

    private String getMcdCallbackSign(McdCallbackRequestDto requestDto) {
        return McdRequestTemplate.getCallbackSign(requestDto);
    }
}
