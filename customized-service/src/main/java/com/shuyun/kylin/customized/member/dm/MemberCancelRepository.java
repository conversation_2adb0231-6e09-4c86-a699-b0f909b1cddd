package com.shuyun.kylin.customized.member.dm;

import com.shuyun.kylin.crm.openapi.core.dto.common.MemberDto;
import com.shuyun.kylin.customized.base.common.ModelConstants;
import com.shuyun.kylin.customized.base.dm.BaseDsRepository;
import com.shuyun.kylin.customized.member.dto.CostCenterDto;
import com.shuyun.kylin.customized.member.dto.MemberCancelDto;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class MemberCancelRepository extends BaseDsRepository<MemberCancelDto> {

    /**
     * 查询是否在黑名单
     * @param s
     * @return
     */
    public List<MemberCancelDto> getMemberCancelState(String s) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("member", s);
        queryMap.put("status", "Y");
        String queryMemberSql = " select operator,reason from " + ModelConstants.MEMBER_CANCEL +" where member = :member and status =:status ";
        List<MemberCancelDto> list =  executeSQL(queryMemberSql, queryMap);
        return list;
    }
}
