package com.shuyun.kylin.customized.base.dm;

import com.shuyun.kylin.customized.base.stream.kafka.dao.WechatSubActionOutputDto;
import com.shuyun.kylin.customized.base.util.ConfigurationCenterUtil;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
@Component
public class WechatSubActionRepository extends BaseDsRepository<WechatSubActionOutputDto>{

    /**
     * 查询小程序订阅会员
     * @return
     */
    public List<WechatSubActionOutputDto> getMemberWechatSubNew(String taskId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId",taskId);
        String queryOrderSql = " SELECT id,objectId ,customerTheme,templateId,sceneCampaign,sceneCommen,type,choiceType,landingPage as page,fields1 as thing1 ,fields2 as thing2 ,fields3 as thing3,fields4 as thing4,fields5 as thing5,fields6 as thing6,fields7 as thing7,taskId,occId,nodeId,nodeName  FROM  " + ConfigurationCenterUtil.MEMBER_CAMPAIGN_ACTION_NEW + "  where taskId = :taskId ";
        List<WechatSubActionOutputDto> list = executeSQL(queryOrderSql, queryMap);
        return list;
    }



    /**
     * new更新小程序订阅消息状态
     * @return
     */
    public void updateWechatSubNew(String taskId,String objectId,String state,String koResultDesc) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId",taskId);
        queryMap.put("objectId",objectId);
        queryMap.put("koResult",state);
        queryMap.put("koResultDesc",koResultDesc);
        queryMap.put("executeTime", ZonedDateTime.now());
        String queryOrderSql = " UPDATE " + ConfigurationCenterUtil.MEMBER_CAMPAIGN_ACTION_NEW + " SET koResult = :koResult,koResultDesc =:koResultDesc,executeTime =:executeTime WHERE taskId = :taskId and objectId = :objectId ";
        execute(queryOrderSql, queryMap);
    }


    /**
     * 批量查询小程序订阅会员
     * @return
     */
    public List<WechatSubActionOutputDto> getWechatSubListNew(String taskId,String index, int pageSize) {
        String change = " id > '" + index + "' order by id limit " + pageSize;
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("taskId",taskId);
        String queryOrderSql = " SELECT id,objectId , customerTheme,templateId,sceneCampaign,sceneCommen,type,choiceType,landingPage as page,fields1 as thing1 ,fields2 as thing2 ,fields3 as thing3,fields4 as thing4,fields5 as thing5,fields6 as thing6,fields7 as thing7,taskId,occId,nodeId,nodeName  FROM  " + ConfigurationCenterUtil.MEMBER_CAMPAIGN_ACTION_NEW + "  where taskId = :taskId  and " + change + " ";
        List<WechatSubActionOutputDto> list = executeSQL(queryOrderSql, queryMap);
        return list;
    }

}
