package com.shuyun.kylin.customized.project.service;

import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.project.dto.McdCallbackRequestDto;
import com.shuyun.kylin.customized.project.request.GrantCouponRequest;
import com.shuyun.kylin.customized.project.request.OfferProjectRequest;
import com.shuyun.kylin.customized.project.response.*;

import javax.servlet.http.HttpServletRequest;

public interface KoCouponService {

    /**
     * 卡券项目列表
     *
     * @param offerProjectRequest
     * @return
     */
    CrmProjectResponse projectList(OfferProjectRequest offerProjectRequest);

    /**
     * 发券
     *
     * @param request
     * @return
     */
    GrantCouponResponse instanceGrant(GrantCouponRequest request);

    /**
     * 券实例列表
     * @param request
     * @return
     */
    InstanceListResponse instanceList(OfferProjectRequest request);

    /**
     * 获取未使用量
     * @param projectId
     * @return
     */
    ResponseResult<ProjectBalance> getBalance(String projectId);

    /**
     * 券实例核销
     * @param request
     * @return
     */
    InstanceUseResponse instanceUse(InstanceUseRequest request);

    /**
     * 麦当劳券实例核销回调
     * @param requestDto
     * @param request
     * @return
     */
    ResponseResult<String> notifyForCouponUse(McdCallbackRequestDto requestDto, HttpServletRequest request);
}
