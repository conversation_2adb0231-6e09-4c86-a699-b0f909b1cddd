package com.shuyun.kylin.customized.rpc.threadpool;

import jodd.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @author: Jingwei
 * @date: 2024-12-17
 */
@Configuration
public class CustomizedThreadPool {

    @Bean(name = "rpcLogThreadPool")
    public ThreadPoolExecutor rpcLogThreadPool() {
        ThreadFactory threadFactory = ThreadFactoryBuilder.create().setNameFormat("rpc-log-thread-%d").get();
        return new ThreadPoolExecutor(
                5,
                8,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(500),
                threadFactory,
                new ThreadPoolExecutor.CallerRunsPolicy());
    }
}
