package com.shuyun.kylin.customized.coupon.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shuyun.kylin.customized.base.common.ResponseResult;
import com.shuyun.kylin.customized.coupon.domain.Coupon;
import com.shuyun.kylin.customized.coupon.domain.CouponConfig;
import com.shuyun.kylin.customized.coupon.dto.*;

import java.util.List;

public interface ICouponService  extends IService<Coupon> {
    CouponCodeResponseDto generateCode(CouponCodeRequestDto couponCodeRequestDto);
//    void receive(CustomerCouponDto customerAndCouponDto);
    BaseResponseDto preCoupnModel(PreCouponModelDto preCouponModelDto);
    BaseResponseDto preEquityProject(PreCouponProjectModelDto preCouponProjectModelDto);
    BaseResponseDto pregenerateCode(OutCouponCodeNoDto outCouponCodeNoDto);
    List<Coupon> assemblyData(CustomerCouponDto customerCouponDto, List<CustomerDto> customerDtoList);
    void searchDataList(List<Coupon> couponDatas);
    void initCouponConfig(CouponConfigDto couponConfigDto);
    void sendKafka(CustomerCouponDto customerCouponDto);
    CouponConfig getCouponConfigByMemberType(String memberType);
    ResponseResult saveOtherBenefits(OtherBenefitsDto otherBenefitsDto);
}

