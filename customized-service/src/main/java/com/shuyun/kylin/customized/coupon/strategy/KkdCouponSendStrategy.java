package com.shuyun.kylin.customized.coupon.strategy;

import com.google.common.collect.Lists;
import com.shuyun.kylin.crm.openapi.core.base.enums.ModelTypeEnum;
import com.shuyun.kylin.crm.openapi.core.dto.admin.ModelMappingDto;
import com.shuyun.kylin.crm.openapi.sdk.client.common.IConfigClient;
import com.shuyun.kylin.customized.base.common.KukeduoConstant;
import com.shuyun.kylin.customized.base.util.ObgUtil;
import com.shuyun.kylin.customized.coupon.domain.CouponConfig;
import com.shuyun.kylin.customized.coupon.dto.CustomerCouponDto;
import com.shuyun.kylin.customized.coupon.dto.CustomerDto;
import com.shuyun.kylin.customized.coupon.dto.kkd.SendCouponDto;
import com.shuyun.kylin.customized.coupon.enums.StatusEnum;
import com.shuyun.kylin.customized.coupon.service.ICouponService;
import com.shuyun.kylin.customized.coupon.service.impl.ModelService;
import com.shuyun.motor.common.cons.PropsUtil;
import com.shuyun.pip.component.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description  此类仅用于发酷客多卡劵
 * @date 2021/3/19
 */
@Slf4j
@Component("kkdCouponSendStrategy")
public class KkdCouponSendStrategy  implements CouponSendStrategy{

    @Autowired
    private ModelService modelService;

    @Autowired
    private IConfigClient iConfigClient;

    @Autowired
    private ICouponService couponService;


    @Override
    public List<CustomerDto> sendCoupon(CustomerCouponDto customerCouponDto) {
        //1.通过权益模型memberType获取实际MemberBinding模型fqn
        List<ModelMappingDto> modelMappingDtos = iConfigClient.queryModelMapping(customerCouponDto.getMemberType());
        if(CollectionUtils.isEmpty(modelMappingDtos)){
            log.info("无法查到模型配置关系，参数{}，请检查参数！",customerCouponDto.getMemberType());
            return null;
        }
        String fqn = modelMappingDtos.stream().filter(
                t-> t.getModelName() == ModelTypeEnum.MEMBER_BINDING
        ).findFirst().orElse(new ModelMappingDto()).getFqnName();
        log.info("MemberBinding对应模型fqn:{}", fqn);

        //2.查询MemberBinding模型中的KKD渠道customerNo和memberId
        List<Map<String,String>> customerNoList = modelService.getKkdCustomerNos(fqn,customerCouponDto);
        log.info("会员查询条数：{}",customerNoList.size());
        if(customerNoList.isEmpty()){
            log.info("没有查到会员，跳过执行");
            return null;
        }
        Map<String,String> customerMemberMap = new HashMap<>();
        customerNoList.forEach(map ->
                customerMemberMap.put(map.get(KukeduoConstant.PARAM_CUSTOMERNO),map.get(KukeduoConstant.PARAM_MEMBERID))
        );
        log.info("customerNo和memberId关系Map建立完毕");

        //3.获取发券appiId，apiKey,apiSecret
        CouponConfig couponConfig = couponService.getCouponConfigByMemberType(customerCouponDto.getMemberType());
        log.info("couponConfig参数：{}", JsonUtils.toJson(couponConfig));

        //4.执行发券逻辑
        int totalSize = customerNoList.size();
        int pageIndex = Integer.parseInt(PropsUtil.getSysOrEnv("customized.customer.transfer.size", "500"));
        int pageNo = 0;
        log.info("本次发放总数：{}",totalSize);
        Map<String,String> sendResultMap = new HashMap<>();
        Map<String,String> errorResultMap = new HashMap<>();

        while(true){
            SendCouponDto sendCouponDto = new SendCouponDto();
            sendCouponDto.setAppId(couponConfig.getAppId());
            sendCouponDto.setApiKey(couponConfig.getApiKey());
            sendCouponDto.setApiSecret(couponConfig.getApiSecret());
            List<String> memberIds = Lists.newArrayList();
            customerNoList.stream().skip((long) pageNo *pageIndex).limit(pageIndex).collect(Collectors.toList()).forEach(
                    t -> memberIds.add(t.get(KukeduoConstant.PARAM_CUSTOMERNO))
            );
            ++pageNo;
            if(CollectionUtils.isEmpty(memberIds)){
                log.info("userIds已遍历完成，跳出循环");
                break;
            }
            sendCouponDto.setMemberIds(memberIds);
            sendCouponDto.setSendCount(1);
            sendCouponDto.setCouponIds(ObgUtil.listOf(customerCouponDto.getProjectId()));
            log.info("执行第{}次循环,本次提交{}条。", pageNo , memberIds.size());
            ObgUtil.sendKkdCoupon(sendCouponDto,sendResultMap,errorResultMap);
        }

        //5.转化发放成功customerNo和券关系为memberId和券关系，返回人券关系
        List<CustomerDto> customerDtoList = Lists.newArrayList();
        for (String s : sendResultMap.keySet()) {
            CustomerDto customerDto = new CustomerDto();
            customerDto.setCustomerNo(customerMemberMap.get(s));
            customerDto.setCoupon(sendResultMap.get(s));
            customerDto.setStatus(StatusEnum.GRANT_SUCCESS.getValue());
            customerDtoList.add(customerDto);
        }
        //6.转化发放失败的customerNo和券关系为memberId和券关系，返回人券关系
        for (String s : errorResultMap.keySet()) {
            CustomerDto customerDto = new CustomerDto();
            customerDto.setCustomerNo(customerMemberMap.get(s));
            customerDto.setCoupon(sendResultMap.get(s));
            customerDto.setStatus(StatusEnum.GRANT_FAILED.getValue());
            customerDtoList.add(customerDto);
        }
        //7.把未执行发放的客户也放入返回值数组
        for(CustomerDto customerDto : customerCouponDto.getData() ){
            if(!customerDtoList.contains(customerDto)){
                customerDto.setStatus(StatusEnum.GRANT_FAILED.getValue());
                customerDtoList.add(customerDto);
            }
        }

        return customerDtoList;
    }
}
