kind: Service
specVersion: v4
metadata:
  name: customized-service
  apiVersion: 'v1'
  accessPoint:
    container: coke/customized-service
  middleware:
    mysql:
containers:
  - name: coke/customized-service
    imagePullPolicy: PullIfNotPresent
    ports:
      - name: customized-service
        protocol: tcp
        targetPort: 0
        containerPort: 8080
profiles:
  - name: default
    cpu: 0.5
    mem: 1024
    replicas: 1
    containers:
      - name: coke/customized-service
        cpu: 0.5
        mem: 1024