language: java 
jdk:
 - oraclejdk8
docker:
 - docker1.12
build:
 script:
  - mvn clean install -U -Dmaven.test.skip=true
 app_version: "mvn -q -Dexec.executable='echo' -Dexec.args='${projects.version}' --non-recursive org.codehaus.mojo:exec-maven-plugin:1.3.1:exec"
package:
 - type: docker
   docker_file: customized-task/Dockerfile
   registry_uri: hub.shuyun.com
   image_name: coke/customized-task
   image_tag: ${APP_VERSION}-${TIMESTAMP}
 - type: docker
   docker_file: customized-service/Dockerfile
   registry_uri: hub.shuyun.com
   image_name: coke/customized-service
   image_tag: ${APP_VERSION}-${TIMESTAMP}
# - type: docker
 #  docker_file: demo-node/Dockerfile
 #  registry_uri: hub.shuyun.com
  # image_name: coke/demo-node
 #  image_tag: ${APP_VERSION}-${TIMESTAMP}
compose:
 - type: service
   service_file: customized-task/.service.yml
 - type: service
   service_file: customized-service/.service.yml
 #- type: service
   #service_file: demo-node/.service.yml